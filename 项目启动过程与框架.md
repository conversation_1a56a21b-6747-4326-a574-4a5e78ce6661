# App启动至首页加载业务流程及模块框架分析

## 一、项目概述

### 1. 项目版本说明
- **核心版本**：`uqbike_thailand`（泰国版本）
- **其他版本**：
  - 中国大陆版本：`uqbike`
  - 海外版本：`uqbike_overseas`
  - 日本版本：`uqbike_japan`
  - 展示版本：`uqbike_show`

### 2. 技术架构
- **开发模式**：原生与 H5 混合开发
- **架构模式**：MVP 架构
- **主要技术栈**：
  - 原生开发 (Native Android - Kotlin)
  - H5 开发 (WebView)
  - 事件总线 (EventBus)
  - 响应式编程 (RxJava)

## 二、应用启动流程

### 1. 启动入口 (`SplashActivity`)
- **启动顺序**：
  1. 应用启动，进入 `SplashActivity` (`app/src/main/java/com/tbit/uqbike/activity/SplashActivity.kt`)
  2. 读取本地广告缓存
  3. 展示开屏广告
  4. 跳转至主界面

- **广告加载逻辑**：
  * 优先检查自家广告 (`SP_SPLAD`)
  * 若自家广告存在：显示加载动画 (GIF: `R.drawable.img_spal`)、广告容器，加载并展示自家广告图片，启动倒计时 (`Glob.open_screen`)
  * 若自家广告不存在：检查第三方广告缓存 (`SP_SPLAD_THIRD`)
  * 若第三方广告存在：显示加载动画，预加载 App Open Ad，延迟后调用 `AdUtil.showSpal` 尝试展示第三方开屏广告，设置超时
  * 若均无缓存：直接进入主界面

### 2. 主界面加载 (`HomeActivity`)
- **界面结构**：
  * 使用 `ViewPager2` (`binding.viewPagerMain`) 和 `HomePageAdapter` (`app/src/main/java/com/tbit/uqbike/adapter/HomePageAdapter.kt`) 管理多个 Fragment
  * 底部导航栏 (`LinearLayout` 容器如 `binding.lyMenuHome` 等) 控制页面切换
  * 包含四个主要标签页：`HomeFrag` (首页)、`ActFrag` (活动)、`MsgFrag` (消息)、`MineFrag` (我的)

- **初始化流程**：
  1. 初始化各个 Fragment
  2. 设置 ViewPager2 适配器
  3. 配置页面切换监听
  4. 加载地图和广告数据

## 三、首页模块分析

### 1. 整体架构
- **页面结构**：
  * 顶部区域 (`HomeAdFragment`)
  * 地图区域 (`MainMapFragment`)
  * 扫码区域
  * 底部功能区
  * 广告展示区

- **核心组件**：
  * `HomeFrag`：首页主 Fragment，负责展示应用的核心首页内容
  * `MainMapFragment`：地图功能，负责初始化地图并获取区域 ID
  * `HomeAdFragment`：广告展示，负责首页顶部的广告展示和用户状态显示
  * `MainBusinessFragment`：业务功能，处理用户认证、订单管理等
  * `MainAdFullFragment`：全屏广告，包含开屏广告缓存数据获取与更新逻辑

### 2. 功能模块详解

#### a) 地图功能 (`MainMapFragment`)
- **核心功能**：
  * 地图展示和定位
  * 获取区域 ID (`area_id`)
  * 车辆位置显示
  * 禁行区域显示
  * 路线规划

- **回调机制**：
  * 在获取 `area_id` 成功或失败时，分别调用 `HomeFrag` 中设置的回调
  * `onAreaIdSucListener`：成功回调
  * `onAreaIdFailListener`：失败回调

#### b) 广告模块 (`HomeAdFragment`)
- **广告类型**：
  * Banner 广告
  * 弹窗广告
  * 第三方广告
  * 全屏广告

- **广告管理**：
  * 广告缓存机制 (`SP_SPLAD`, `SP_SPLAD_THIRD`)
  * 广告展示控制
  * 点击跳转处理

- **关键方法**：
  * `getData()`：加载首页Banner广告、弹窗广告及第三方Banner广告
  * `setTopState()`：设置顶部状态

#### c) 业务功能 (`MainBusinessFragment`)
- **主要功能**：
  * 用户认证
  * 订单管理
  * 骑行状态
  * 邀请功能

### 3. 交互流程

#### a) 数据加载流程
1. 获取区域 ID
2. 加载广告数据
3. 获取用户状态
4. 更新 UI 显示

#### b) 用户操作流程
1. 扫码解锁
2. 查看订单
3. 充值钱包
4. 联系客服
5. 查看计费规则

## 四、特色功能

### 1. 多语言支持
- **支持语言**：
  * 泰文 (核心语言)
  * 英文 (基础语言)
  * 简体中文
  * 韩语
  * 印尼语
  * 马来语

- **实现方式**：
  * 使用 Android 标准资源目录结构
  * 根据用户设置自动切换语言
  * 不同版本使用不同的语言资源

### 2. 多版本支持
- **版本类型**：
  * 泰国版本 (核心版本)
  * 中国大陆版本
  * 海外版本
  * 日本版本
  * 展示版本

- **版本特点**：
  * 每个版本都有特定的语言资源
  * 针对不同市场进行本地化
  * 共享核心功能代码

### 3. 技术特点

#### a) UI 组件
- 自定义 View
- 圆角布局
- 底部弹出框
- 自定义滚动视图

#### b) 数据管理
- 本地缓存
- 网络请求
- 状态管理
- 数据同步

## 五、开发规范

### 1. 代码组织
- 遵循 MVP 架构
- 使用 Contract 接口定义
- 统一的命名规范
- 清晰的代码注释

### 2. 资源管理
- 统一的资源命名
- 多语言资源分离
- 版本特定资源管理
- 资源复用原则

### 3. 性能优化
- 广告加载优化
- 地图性能优化
- 内存管理
- 启动速度优化

## 六、技术栈分析

### 1. 原生开发 (Native Android - Kotlin)
- **核心应用框架**：
  * `SplashActivity`, `HomeActivity`, `MainActivity` 等 Activity
  * 应用的主要导航结构 (底部导航栏 + ViewPager2)

- **核心功能 Fragment**：
  * `HomeFrag`, `MainMapFragment`, `MainAdFullFragment`, `MainBusinessFragment` 等

- **原生 UI 组件**：
  * 各种 Dialog (`CommDialog`, `InviteDialog`, `MainAdDialog`, `RentalNoteDialog` 等)
  * BottomSheet (`UserHelpBottomSheep`, `kfBottomSheep`)

- **核心业务逻辑**：
  * 网络请求
  * 数据存储
  * 广告集成逻辑 (`AdUtil`, `AdBannelUtil`)
  * 地图 SDK 集成

### 2. H5 开发 (通过 WebView 加载)
- **首页部分 UI**：
  * `HomeAdFragment` 顶部区域加载的 `menus` H5 页面

- **特定内容页面**：
  * 通过 `WebActivity` / `WebActionActivity` 加载的页面
  * 计费规则
  * 广告落地页
  * 活动说明等

- **部分弹窗内容**：
  * `RentalNoteDialog` 中加载的长租说明 H5 页面

## 七、多语言支持分析

### 1. 语言资源文件
- **泰文 (Thai) - 核心语言**
   - 文件位置：`app/src/uqbike_thailand/res/values-th-rTH/strings.xml`
   - 特点：作为核心版本（泰国版本）的主要语言，所有界面文本都翻译成了泰文

- **英语 (English) - 基础语言**
   - 文件位置：`app/src/main/res/values/strings.xml`
   - 特点：作为默认语言，所有字符串都是英文

- **简体中文 (Simplified Chinese)**
   - 文件位置：`app/src/main/res/values-zh-rCN/strings.xml`
   - 特点：针对中国大陆市场，所有界面文本都翻译成了简体中文

- **韩语 (Korean)**
   - 文件位置：`app/src/main/res/values-ko-rKR/strings.xml`
   - 特点：针对韩国市场，所有界面文本都翻译成了韩文

- **印尼语 (Indonesian)**
   - 文件位置：`app/src/main/res/values-in-rID/strings.xml`
   - 特点：针对印尼市场，所有界面文本都翻译成了印尼文

- **马来语 (Malay)**
   - 文件位置：`app/src/main/res/values-ms-rMY/strings.xml`
   - 特点：针对马来西亚市场，所有界面文本都翻译成了马来文

### 2. 资源文件结构
- **目录结构**：
   - 基础语言：`values/`
   - 特定语言：`values-语言代码-地区代码/`

- **资源类型**：
   - 字符串资源：`strings.xml`
   - 图片资源：`drawable-语言代码-地区代码-分辨率/`

- **语言代码和地区代码**：
   - 泰文：th-rTH（核心语言）
   - 英语：en（基础语言）
   - 简体中文：zh-rCN
   - 韩语：ko-rKR
   - 印尼语：in-rID
   - 马来语：ms-rMY

### 3. 版本与语言对应关系
- **核心版本**：`uqbike_thailand`（泰国版本）
- **中国大陆版本**：`uqbike`
- **海外版本**：`uqbike_overseas`
- **日本版本**：`uqbike_japan`
- **展示版本**：`uqbike_show`

## 八、首页布局与功能模块分析

### 1. 整体架构
- **技术架构**：
  - 采用 ViewPager2 + Fragment 的架构
  - 底部导航栏包含4个主要标签页：首页、活动、消息、我的
  - 使用 MVP 架构模式，通过 Contract 和 Presenter 处理业务逻辑

### 2. 首页布局结构 (`frag_home.xml`)
- **整体布局**：
  - 采用 `androidx.constraintlayout.widget.ConstraintLayout` 作为根布局。
  - 内部包含 `CameraPermissionFragment` 和 `MainBusinessFragment`，它们通常覆盖整个屏幕但可能根据业务逻辑动态调整。
  - 主要内容通过一个 `com.tbit.uqbike.widget.CustomNestedScrollView` 包裹，允许页面滚动。
  - `CustomNestedScrollView` 内部是一个 `LinearLayout` (id: `frag_home`)，按垂直方向排列各个模块。

- **模块组成**：
  - **顶部区域** (`HomeAdFragment`, id: `main_ad_fragment`)
    - 背景图片
    - 网络状态提示
    - Banner 广告区域
  - **广告和地图区域容器** (id: `layout_ad_area`)
    - 包含地图和扫码功能，位于顶部区域下方，并通过 `android:layout_marginTop="-20dp"` 实现部分重叠效果。
    - **地图区域** (`MainMapFragment`, id: `main_map_fragment`)
      - 地图显示
      - 定位功能
      - 区域 ID 获取
    - **扫码区域** (id: `ry_scan` / `ly_scan`)
      - 扫码按钮
      - 骑行状态显示 (通过 `ly_riding_btn` 控制可见性)
  - **底部功能区** (包含在 `frag_home` 的 `LinearLayout` 中，但具体 ID 散布在 `HomeFrag.kt` 中通过 `MyView?.findViewById<LinearLayout>(R.id.ly_menu_...)` 引用)
    - 钱包充值 (`ly_menu_balance`)
    - 客服热线 (`ly_menu_hotline`)
    - 计费规则 (`ly_menu_cost`)
    - 更多功能 (`ly_menu_more`)
  - **广告展示区** (id: `im_image0` ~ `im_image4`)
    - 多个广告位，支持图片广告展示。
  - **H5 推荐页面区域** (id: `layout_h5_area`)
    - 新增的 `FrameLayout` 容器，用于加载新的 H5 业务页面。
    - 默认可见性为 `android:visibility="gone"`。
    - 位于 `im_image4` (广告展示区最后一个广告位) 之后。

- **可见性控制**：
  - **H5 推荐页面区域 (`layout_h5_area`)**：
    - **后端控制**：其可见性主要由后端配置接口 `/1.0/common/config/self/taokeisopen` 返回的 `is_open` 标志（1 表示开放，0 表示关闭）动态控制。
    - **默认状态**：在布局文件中设置为 `gone`。
  - **其他区域**：
    - 首页的各个功能区域（如地图、扫码、底部功能区、广告展示区等）的可见性通常是**独立控制**的。
    - 这些控制基于各自的业务逻辑、数据加载状态或用户交互（例如骑行状态会影响扫码区域的显示）。
    - Fragment 内部的元素可见性由其对应的 Kotlin 代码（如 `HomeFrag.kt`、`HomeAdFragment.kt`、`MainMapFragment.kt`）通过编程方式进行管理。

### 3. 主要功能模块

#### a) 地图功能 (`MainMapFragment`)
- **核心功能**：
  - 地图展示和定位
  - 获取区域 ID
  - 车辆位置显示
  - 禁行区域显示
  - 路线规划

#### b) 广告模块 (`HomeAdFragment`)
- **广告类型**：
  - Banner 广告展示
  - 弹窗广告
  - 第三方广告集成
  - 广告点击跳转

#### c) 业务功能 (`MainBusinessFragment`)
- **主要功能**：
  - 用户认证
  - 订单管理
  - 骑行状态
  - 邀请功能

#### d) 全屏广告 (`MainAdFullFragment`)
- **功能特点**：
  - 开屏广告缓存
  - 广告展示控制

### 4. 交互流程

#### a) 启动流程
- `SplashActivity` 显示开屏广告
- 跳转到 `HomeActivity`
- 初始化各个 Fragment
- 加载地图和广告数据

#### b) 数据加载流程
- 获取区域 ID
- 加载广告数据
- 获取用户状态
- 更新 UI 显示

#### c) 用户操作流程
- 扫码解锁
- 查看订单
- 充值钱包
- 联系客服
- 查看计费规则

## 九、环境配置与切换机制

### 1. 环境配置定义
- 每个 flavor（如中国大陆、泰国、海外等）目录下均有独立的 `FlavorConfig.kt`，定义该版本的后端 API 基础地址。例如（以泰国为例）：

  ```kotlin
  object NET {
      const val BASE_URL = "https://api.gogoep.io/"
      var USER_URL = "https://my.gogoep.com/"
      var COM_URL = "https://api.gogoep.com/"
      var EVENT_URL = "https://ci.gogoep.com/"
      var H5_URL = "https://h5.gogoep.com/"
  }
  ```
- `build.gradle` 通过 `productFlavors` 区分不同国家/地区/环境的包，指定不同的 `applicationId`、渠道、密钥等。

### 2. API服务连接配置
- `ApiService.kt` 通过 `FlavorConfig.NET` 中的 URL 常量，定义各类 API 服务的基础地址。
- `RetrofitInit.kt` 初始化 Retrofit 时，动态传入不同的 baseUrl（如 USER_URL、COM_URL 等），并配置通用请求头（如 token、设备ID、区域ID、语言、时区等），支持 HTTPS。
- 各 API 客户端（如 `UserRClient`、`ComRClient`、`EventRClient`）分别用不同的 URL 初始化 Retrofit 实例，实现多服务分离。

### 3. 环境切换机制
- **构建时切换**：
  - 通过选择不同的 `productFlavors` 和 `buildTypes`，在打包时确定 API 地址和相关配置。
  - 例如：`uqbike_thailandDebug`、`uqbike_thailandRelease` 等。

- **运行时切换**：
  - 在登录页面下方隐藏区域，连续点击可弹出环境切换面板（如 DEV/TEST/PROD）。
  - 切换后会持久化环境标志到本地（`SpUtil` 中的 `SP_TESTNET` 标志）。
  - **核心切换机制**：App 启动时（`App.kt` 的 `onCreate` 方法）统一读取 `SP_TESTNET` 标志，根据标志值动态设置所有环境变量：
    ```kotlin
    if (!SpUtil.getInstance().find(Constant.SpKey.SP_TESTNET).isNullOrEmpty()) {
        // 测试环境
        FlavorConfig.NET.USER_URL = "https://my.gogoep.io/"
        FlavorConfig.NET.COM_URL = "https://api.gogoep.io/"
        FlavorConfig.NET.EVENT_URL = "https://ci.gogoep.io/"
        FlavorConfig.NET.H5_URL = "https://h5.gogoep.io/"
        FlavorConfig.NET.GOSHOP_WEB_HOST = "https://www.gogo-shop.tech"
    } else {
        // 生产环境
        FlavorConfig.NET.USER_URL = "https://my.gogoep.com/"
        FlavorConfig.NET.COM_URL = "https://api.gogoep.com/"
        FlavorConfig.NET.EVENT_URL = "https://ci.gogoep.com/"
        FlavorConfig.NET.H5_URL = "https://h5.gogoep.com/"
        FlavorConfig.NET.GOSHOP_WEB_HOST = "https://www.gogo-shop.com"
    }
    ```
  - 切换后需重启 App 以使新环境生效。

- **安全措施**：
  - 生产包通常隐藏或禁用环境切换功能。
  - 切换时弹出确认提示，防止误操作。
  - 切换后清理缓存、重置登录状态，确保数据隔离。

### 4. 总结
- 多环境支持：通过 `FlavorConfig` 和 `productFlavors` 实现多国家/多环境的灵活配置。
- API分离：不同服务（用户、通用、事件等）独立配置基础 URL，便于后端分布式部署。
- 运行时切换：开发/测试阶段可一键切换环境，极大提升调试效率。
- 统一管理：所有环境变量（包括 API 地址、H5 地址、GOSHOP 地址等）统一由 `SP_TESTNET` 标志控制，保证环境切换的一致性和可维护性。
- 安全与隔离：生产环境下切换受限，切换后自动重启和数据隔离，保障线上安全。
