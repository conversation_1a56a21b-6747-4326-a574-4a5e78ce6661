<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分享返利测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .product-info {
            text-align: center;
            margin-bottom: 20px;
        }
        .product-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .product-price {
            font-size: 24px;
            color: #ff4444;
            font-weight: bold;
        }
        .share-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-top: 20px;
        }
        .share-btn:hover {
            background: linear-gradient(45deg, #ff5252, #ff7979);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-info">
            <h3>分享返利功能测试</h3>
            <p>点击下方按钮测试分享返利弹窗功能</p>
        </div>
        
        <div class="product-info">
            <div class="product-title">测试商品 - 智能电动车</div>
            <div class="product-price">¥2,999</div>
        </div>
        
        <button class="share-btn" onclick="testShareRebate()">
            🎁 邀请好友赚佣金
        </button>
    </div>

    <script>
        function testShareRebate() {
            console.log("开始测试分享返利功能");

            // 检查Android接口是否可用
            if (!window.Android) {
                alert("错误：window.Android 不存在，请在App中打开此页面");
                return;
            }

            if (!window.Android.shareRebate) {
                alert("错误：window.Android.shareRebate 方法不存在");
                return;
            }

            // 模拟分享返利数据
            const shareData = {
                type: "shareRebate",
                data: {
                    url: "https://example.com/product/123?id=user123&username=testuser",
                    text: "推荐一款超棒的智能电动车，邀请好友购买还能赚佣金！",
                    product_id: 123,
                    rebates: ["10", "5", "3", "2", "1"],
                    all_rebate: 21
                }
            };

            console.log("准备调用 shareRebate，数据:", shareData);

            try {
                // 调用原生方法
                window.Android.shareRebate(JSON.stringify(shareData), function(result) {
                    console.log("分享返利结果:", result);
                    alert("分享返利调用成功，结果: " + result);
                });
            } catch (error) {
                console.error("调用 shareRebate 出错:", error);
                alert("调用出错: " + error.message);
            }
        }

        // 页面加载完成后的提示
        window.onload = function() {
            console.log("分享返利测试页面已加载");

            // 检查接口可用性
            const status = document.createElement('div');
            status.style.cssText = 'position: fixed; top: 10px; left: 10px; background: #f0f0f0; padding: 10px; border: 1px solid #ccc; font-size: 12px; z-index: 9999;';

            if (window.Android && window.Android.shareRebate) {
                status.innerHTML = '✅ Android.shareRebate 接口可用';
                status.style.backgroundColor = '#d4edda';
            } else {
                status.innerHTML = '❌ Android.shareRebate 接口不可用';
                status.style.backgroundColor = '#f8d7da';
            }

            document.body.appendChild(status);
        };
    </script>
</body>
</html>
