package com.tbit.uqbike.adapter

import android.graphics.Paint
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.RechargeItem
import com.tbit.uqbike.entity.RideCardDataItem
import com.tbit.uqbike.qrcode.CommonUtils
import com.tbit.uqbike.roundview.RoundLinearLayout
import com.tbit.uqbike.roundview.RoundRelativeLayout
import com.tbit.uqbike.roundview.RoundTextView

class RentalAdapter  : BaseAdapter<RideCardDataItem>() {
    var onClickListener = {_: String ->}
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }
    inner class ViewHolder(parent: ViewGroup) :
        BaseAdapter<RideCardDataItem>.AbsViewHolder(parent, R.layout.item_rental) {

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return

            itemView.setOnClickListener {
//                if(!data.isSatisfy) return@setOnClickListener
                source.forEach { it.isSel = false}
                data.isSel = true
                notifyDataSetChanged()
                onClickListener(data.id.toString())
            }
            if (position == 0){
                if (data.tags.isNullOrEmpty()){
                    itemView.findViewById<RelativeLayout>(R.id.ry_card_top)?.visibility = View.GONE
                    itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.visibility = View.INVISIBLE
                    itemView.findViewById<RoundRelativeLayout>(R.id.item_ry_rech)?.delegate?.strokeColor = ContextUtil.getContext().resources.getColor(R.color.c_line)
                    if(data.isSel){
                        itemView.findViewById<RoundRelativeLayout>(R.id.item_ry_rech)?.delegate?.strokeColor = ContextUtil.getContext().resources.getColor(R.color.c_F86125)
                        itemView.findViewById<RoundLinearLayout>(R.id.item_rental)?.elevation = CommonUtils.dip2px(ContextUtil.getContext(),3f).toFloat()
                        itemView.findViewById<ImageView>(R.id.img_card_sel)?.visibility = View.VISIBLE
//                itemView.findViewById<TextView>(R.id.tv_rental_title)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.c_F86125))
                    }else{
                        itemView.findViewById<RoundRelativeLayout>(R.id.item_ry_rech)?.delegate?.strokeColor = ContextUtil.getContext().resources.getColor(R.color.c_line)
                        itemView.findViewById<RoundLinearLayout>(R.id.item_rental)?.elevation = CommonUtils.dip2px(ContextUtil.getContext(),0f).toFloat()
                        itemView.findViewById<ImageView>(R.id.img_card_sel)?.visibility = View.GONE
//                itemView.findViewById<TextView>(R.id.tv_rental_title)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.black_hint))
                    }
                }else{
                    itemView.findViewById<RelativeLayout>(R.id.ry_card_top)?.visibility = View.VISIBLE
                    itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.visibility = View.INVISIBLE
                    if (data.tags.contains(",")){
                        var tagsData = data.tags.split(",")
                        itemView.findViewById<TextView>(R.id.tv_item_top)?.text = tagsData[0]
                    }else if (data.tags.contains("，")){
                        var tagsData = data.tags.split("，")
                        itemView.findViewById<TextView>(R.id.tv_item_top)?.text = tagsData[0]
                    }else{
                        itemView.findViewById<TextView>(R.id.tv_item_top)?.text = data.tags
                    }
                    itemView.findViewById<RoundRelativeLayout>(R.id.item_ry_rech)?.delegate?.strokeColor = ContextUtil.getContext().resources.getColor(R.color.c_F86125)
                }
                if(data.isSel){
                    itemView.findViewById<RoundLinearLayout>(R.id.item_rental)?.elevation = CommonUtils.dip2px(ContextUtil.getContext(),3f).toFloat()
                    itemView.findViewById<ImageView>(R.id.img_card_sel)?.visibility = View.VISIBLE
                }else{
                    itemView.findViewById<RoundLinearLayout>(R.id.item_rental)?.elevation = CommonUtils.dip2px(ContextUtil.getContext(),0f).toFloat()
                    itemView.findViewById<ImageView>(R.id.img_card_sel)?.visibility = View.GONE
                }
            }else{
                itemView.findViewById<RelativeLayout>(R.id.ry_card_top)?.visibility = View.GONE
                if(data.isSel){
                    itemView.findViewById<RoundRelativeLayout>(R.id.item_ry_rech)?.delegate?.strokeColor = ContextUtil.getContext().resources.getColor(R.color.c_F86125)
                    itemView.findViewById<RoundLinearLayout>(R.id.item_rental)?.elevation = CommonUtils.dip2px(ContextUtil.getContext(),3f).toFloat()
                    itemView.findViewById<ImageView>(R.id.img_card_sel)?.visibility = View.VISIBLE
//                itemView.findViewById<TextView>(R.id.tv_rental_title)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.c_F86125))
                }else{
                    itemView.findViewById<RoundRelativeLayout>(R.id.item_ry_rech)?.delegate?.strokeColor = ContextUtil.getContext().resources.getColor(R.color.c_line)
                    itemView.findViewById<RoundLinearLayout>(R.id.item_rental)?.elevation = CommonUtils.dip2px(ContextUtil.getContext(),0f).toFloat()
                    itemView.findViewById<ImageView>(R.id.img_card_sel)?.visibility = View.GONE
//                itemView.findViewById<TextView>(R.id.tv_rental_title)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.black_hint))
                }
                if (data.tags.isNullOrEmpty()){
                    itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.visibility = View.INVISIBLE
                }else{
                    itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.visibility = View.VISIBLE
                    if (data.tags.contains(",")){
                        var tagsData = data.tags.split(",")
                        itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.text = tagsData[0]
                    }else if (data.tags.contains("，")){
                        var tagsData = data.tags.split("，")
                        itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.text = tagsData[0]
                    }else{
                        itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.text = data.tags
                    }
                }
            }

            itemView.findViewById<TextView>(R.id.tv_rental_title)?.text = data.title
            itemView.findViewById<TextView>(R.id.tv_rental_amount)?.text = data.price.toInt().toString()
            itemView.findViewById<TextView>(R.id.tv_rental_amount_unit)?.text = data.currency

            if (data.discount_value == 0){
                itemView.findViewById<RoundTextView>(R.id.rv_item_tran_1)?.visibility = View.GONE
            }else{
                itemView.findViewById<RoundTextView>(R.id.rv_item_tran_1)?.visibility = View.VISIBLE
                itemView.findViewById<RoundTextView>(R.id.rv_item_tran_1)?.text = ResUtil.getString(R.string.s_card_off,data.discount_value.toString()+"%")
            }

            if (data.original_price == data.price){
                itemView.findViewById<TextView>(R.id.tv_rental_amount_z)?.visibility = View.INVISIBLE
            }else{
                itemView.findViewById<TextView>(R.id.tv_rental_amount_z)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_rental_amount_z)?.text = AppUtil.getFloat2(data.original_price)+data.currency
                itemView.findViewById<TextView>(R.id.tv_rental_amount_z)?.paint?.flags = Paint.STRIKE_THRU_TEXT_FLAG
//            itemView.findViewById<TextView>(R.id.tv_rental_amount_z)?.paint?.flags = Paint.DITHER_FLAG
            }

            //1次卡 2时间卡
            if (data.type == 1 || data.type == 0){
                itemView.findViewById<RoundTextView>(R.id.rv_item_tran_2)?.text = AppUtil.getFloat2(data.per_unit_cost_value)+data.currency+"/"+ResUtil.getString(R.string.min)
            }else{
                // 1分钟 2小时 3天
                when (data.free_unit){
                    1->{
                        itemView.findViewById<RoundTextView>(R.id.rv_item_tran_2)?.text = AppUtil.getFloat2(data.per_unit_cost_value)+data.currency+"/"+ResUtil.getString(R.string.min)
                    }
                    2->{
                        itemView.findViewById<RoundTextView>(R.id.rv_item_tran_2)?.text = AppUtil.getFloat2(data.per_unit_cost_value)+data.currency+"/"+ResUtil.getString(R.string.s_unit_hour)
                    }
                    3->{
                        itemView.findViewById<RoundTextView>(R.id.rv_item_tran_2)?.text = AppUtil.getFloat2(data.per_unit_cost_value)+data.currency+"/"+ResUtil.getString(R.string.s_unit_day)
                    }
                }
            }

        }
    }

}