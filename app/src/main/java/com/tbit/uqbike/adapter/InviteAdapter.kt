package com.tbit.uqbike.adapter

import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.RxUtil.timeOld
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.Coupon
import com.tbit.uqbike.entity.MsgListDataItem
import com.tbit.uqbike.entity.type_coupon_money
import com.tbit.uqbike.entity.type_coupon_ridecard
import com.tbit.uqbike.utils.ImageLoad

class InviteAdapter : BaseAdapter<Coupon>() {

    companion object {
        private const val TYPE_NORMAL = 0
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        val viewHolder = ViewHolder(parent)
        return viewHolder
    }

    override fun getItemViewType(position: Int): Int {
        var type = TYPE_NORMAL
        return type
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<Coupon>.AbsViewHolder(parent, R.layout.item_invite) {

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            itemView.findViewById<TextView>(R.id.tv_item_invite_title)?.text = data.name
            if (data.typeData.equals(type_coupon_money)){
                itemView.findViewById<RelativeLayout>(R.id.rl_other)?.visibility = View.VISIBLE
                itemView.findViewById<RelativeLayout>(R.id.rl_invite)?.visibility = View.GONE
                ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_invite_money),itemView.findViewById<ImageView>(R.id.img_item_invite))
                itemView.findViewById<TextView>(R.id.tv_item_invite_time)?.visibility = View.GONE
            }else if(data.typeData.equals(type_coupon_ridecard)){
                itemView.findViewById<RelativeLayout>(R.id.rl_other)?.visibility = View.VISIBLE
                itemView.findViewById<RelativeLayout>(R.id.rl_invite)?.visibility = View.GONE
                ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_invite_ridecard),itemView.findViewById<ImageView>(R.id.img_item_invite))
                itemView.findViewById<TextView>(R.id.tv_item_invite_time)?.visibility = View.GONE
            }else{
                itemView.findViewById<RelativeLayout>(R.id.rl_other)?.visibility = View.GONE
                itemView.findViewById<RelativeLayout>(R.id.rl_invite)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_item_invite_time)?.visibility = View.VISIBLE

                itemView.findViewById<TextView>(R.id.tv_item_invite_amount)?.text = AppUtil.getFloat2(data.amount)
                itemView.findViewById<TextView>(R.id.tv_item_invite_unit)?.text = Glob.CurrencyUnit
                if (data.is_min_spend == 1){
                    itemView.findViewById<TextView>(R.id.tv_item_invite_amounthint)?.text =
                        ResUtil.getString(R.string.s_invite_ismin,AppUtil.getFloat2(data.min_spend_amount))
                }else{
                    itemView.findViewById<TextView>(R.id.tv_item_invite_amounthint)?.text =
                        ResUtil.getString(R.string.s_invite_umin)
                }

                if(TimeFormatUtil.isSameDay(TimeFormatUtil.transToStringBysqDataInfo_Long(data.end_time),System.currentTimeMillis())){
                    var timeData = TimeFormatUtil.transToStringBysqDataInfo_min(data.end_time).split(" ")
                    itemView.findViewById<TextView>(R.id.tv_item_invite_time)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.c_F86125))
                    itemView.findViewById<TextView>(R.id.tv_item_invite_time)?.text =
                        ResUtil.getString(R.string.s_today)+ ResUtil.getString(R.string.s_datedown,timeData[1])
                }else{
                    itemView.findViewById<TextView>(R.id.tv_item_invite_time)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.c_838588))
                    itemView.findViewById<TextView>(R.id.tv_item_invite_time)?.text =
                        ResUtil.getString(R.string.s_datedown,TimeFormatUtil.transToStringBysqDataInfo_min(data.end_time))
                }

            }
        }
    }
}