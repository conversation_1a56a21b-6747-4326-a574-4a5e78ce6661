package com.tbit.uqbike.adapter

import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.recyclerview.widget.RecyclerView
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.MsgListDataItem
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.MDUtil
import org.json.JSONObject
import org.jetbrains.anko.layoutInflater

class MessageAdapter: RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val TYPE_NORMAL = 0
        private const val TYPE_FOOTER = 1
    }

    var source = emptyList<MsgListDataItem>()
    private var onMyItemClickListener: ((MsgListDataItem, Int) -> Unit)? = null

    var showFooter = false

    override fun getItemCount(): Int {
        return source.size + if (showFooter) 1 else 0
    }

    override fun getItemViewType(position: Int): Int {
        return if (showFooter && position == source.size) {
            TYPE_FOOTER
        } else {
            TYPE_NORMAL
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == TYPE_FOOTER) {
            val view = LayoutInflater.from(parent.context).inflate(R.layout.layout_list_footer_no_more, parent, false)
            FooterViewHolder(view)
        } else {
            val view = parent.context.layoutInflater.inflate(R.layout.item_message, parent, false)
            ViewHolder(view)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is ViewHolder -> {
                val data = source.getOrNull(position) ?: return
                holder.bind(data, position)
            }
            is FooterViewHolder -> {
                // No binding needed
            }
        }
    }

    fun setOnMyItemClickListener(listener: (MsgListDataItem, Int) -> Unit) {
        this.onMyItemClickListener = listener
    }

    inner class FooterViewHolder(view: View) : RecyclerView.ViewHolder(view)

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        @RequiresApi(Build.VERSION_CODES.O)
        fun bind(data: MsgListDataItem, position: Int) {
            itemView.setOnClickListener {
                onMyItemClickListener?.invoke(data, position)
            }

            val propertiesSea = JSONObject()
            propertiesSea.put("tag", "internal_message")
            propertiesSea.put("message_id", data.id)
            MDUtil.SaeEvent(itemView, propertiesSea)

            itemView.findViewById<TextView>(R.id.text_title)?.text = AppUtil.getMstType(data.type)
            val textState = itemView.findViewById<RoundTextView>(R.id.text_state)
            if (data.read_time == null || data.read_time == 0) {
                textState?.text = ResUtil.getString(R.string.unread_message)
                textState?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.c_E00000))
                textState?.delegate?.backgroundColor = ContextUtil.getContext().resources.getColor(R.color.c_E00000_10)
            } else {
                textState?.text = ResUtil.getString(R.string.have_read_message)
                textState?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.c_C0C1C3))
                textState?.delegate?.backgroundColor = ContextUtil.getContext().resources.getColor(R.color.c_838588_tra)
            }
            itemView.findViewById<TextView>(R.id.text_time)?.text = TimeFormatUtil.transToStringBysq(data.create_time.toLong())
            val textContent = itemView.findViewById<TextView>(R.id.text_content)
            if (data.type == 10) {
                textContent?.visibility = View.VISIBLE
                textContent?.text = ResUtil.getString(R.string.s_msg_invitetype)
            } else {
                if (data.response.isNullOrEmpty()) {
                    textContent?.visibility = View.GONE
                } else {
                    textContent?.visibility = View.VISIBLE
                    textContent?.text = data.response
                }
            }
        }
    }
}