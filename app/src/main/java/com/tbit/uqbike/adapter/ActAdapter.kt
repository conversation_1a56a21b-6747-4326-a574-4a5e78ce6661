package com.tbit.uqbike.adapter

import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.MsgListDataItem
import com.tbit.uqbike.entity.getAdData
import com.tbit.uqbike.entity.getAdDataItem
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import org.json.JSONObject

class ActAdapter: BaseAdapter<getAdDataItem>() {

    companion object {
        private const val TYPE_NORMAL = 0
        private const val TYPE_LAST = 1
        private const val TYPE_FIRST = 2
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        val viewHolder = ViewHolder(parent)
//        if(viewType and TYPE_LAST != 0) {
//            viewHolder.itemView.findViewById<TextView>(R.id.view_driver.layoutParams.height = parent.dip(1)
//            viewHolder.itemView.findViewById<TextView>(R.id.view_driver.requestLayout()
//        }
//        if(viewType and TYPE_FIRST != 0) {
////            (viewHolder.itemView.findViewById<TextView>(R.id.layoutParams as ViewGroup.MarginLayoutParams).topMargin = parent.dip(0)
//            viewHolder.itemView.findViewById<TextView>(R.id.requestLayout()
//        }
        return viewHolder
    }

    override fun getItemViewType(position: Int): Int {
        var type = TYPE_NORMAL
//        if (position == 0) type = type or TYPE_FIRST
//        if (position == source.size - 1) type = type or TYPE_LAST
        return type
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<getAdDataItem>.AbsViewHolder(parent, R.layout.item_act) {

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return

            val propertiesSea = JSONObject()
            propertiesSea.put("tag", "internal_message") // 设置商品
//            itemView.findViewById<TextView>(R.id.custom_textview)?.text = data.title
            itemView.findViewById<ImageView>(R.id.act_image)?.let { it1 ->
                ImageLoad.loadimgWithCorner(data.images[0],
                    itemView.findViewById<ImageView>(R.id.act_image)!!,10
                )
            }

            propertiesSea.put("message_id", data.id) // 设置商品 ID
            MDUtil.SaeEvent(itemView,propertiesSea)
//
//            itemView.findViewById<TextView>(R.id.text_title)?.text = AppUtil.getMstType(data.type)
//            if(data.read_time == null || data.read_time == 0){
//                itemView.findViewById<RoundTextView>(R.id.text_state)?.text = ResUtil.getString(R.string.unread_message)
//                itemView.findViewById<RoundTextView>(R.id.text_state)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.c_E00000))
//                itemView.findViewById<RoundTextView>(R.id.text_state)?.delegate?.backgroundColor = ContextUtil.getContext().resources.getColor(R.color.c_E00000_10)
//            }else{
//                itemView.findViewById<RoundTextView>(R.id.text_state)?.text = ResUtil.getString(R.string.have_read_message)
//                itemView.findViewById<RoundTextView>(R.id.text_state)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.c_C0C1C3))
//                itemView.findViewById<RoundTextView>(R.id.text_state)?.delegate?.backgroundColor = ContextUtil.getContext().resources.getColor(R.color.c_838588_tra)
//            }
//            itemView.findViewById<TextView>(R.id.text_time)?.text = TimeFormatUtil.transToStringBysq(data.create_time.toLong())
//            if (data.type == 10){
//                itemView.findViewById<TextView>(R.id.text_content)?.visibility = View.VISIBLE
//                itemView.findViewById<TextView>(R.id.text_content)?.text = ResUtil.getString(R.string.s_msg_invitetype)
//            }else{
//                if (data.response.isNullOrEmpty()){
//                    itemView.findViewById<TextView>(R.id.text_content)?.visibility = View.GONE
//                }else{
//                    itemView.findViewById<TextView>(R.id.text_content)?.visibility = View.VISIBLE
//                    itemView.findViewById<TextView>(R.id.text_content)?.text = data.response
//                }
//            }
        }
    }
}