package com.tbit.uqbike.adapter

import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.MyRideCardDataItem
import com.tbit.uqbike.roundview.RoundLinearLayout
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import org.json.JSONObject

class MyRideCardAdapter : BaseAdapter<MyRideCardDataItem>() {
    var onLoseCardListener = {_: Int ->}
    var onRefundListener = {_: String ->}
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<MyRideCardDataItem>.AbsViewHolder(parent, R.layout.item_myridecard) {

        init {
//            itemView.findViewById<TextView>(R.id.text_money_unit.text = Glob.symbol
        }

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return

            val propertiesSea = JSONObject()
            propertiesSea.put("tag", "my_cycling_card") // 设置商品
            propertiesSea.put("card_id", data.ride_card_no) // 设置商品 ID
            MDUtil.SaeEvent(itemView,propertiesSea)

            if(data.is_support == 1){
                ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_ridcard_logo),itemView.findViewById<ImageView>(R.id.img_item_myridcard_hint))
                itemView.findViewById<RoundLinearLayout>(R.id.ry_item_myridecard)?.delegate?.backgroundColor = ContextUtil.getContext().resources.getColor(R.color.c_F3FAFF)
            }else{
                ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_uridcard_logo),itemView.findViewById<ImageView>(R.id.img_item_myridcard_hint))
                itemView.findViewById<RoundLinearLayout>(R.id.ry_item_myridecard)?.delegate?.backgroundColor = ContextUtil.getContext().resources.getColor(R.color.c_grey)
            }

            if(data.isSupportHint.isNullOrEmpty()){
                itemView.findViewById<TextView>(R.id.tv_item_myridecard_tip)?.visibility = View.GONE
            }else{
                itemView.findViewById<TextView>(R.id.tv_item_myridecard_tip)?.text = data.isSupportHint
                itemView.findViewById<TextView>(R.id.tv_item_myridecard_tip)?.visibility = View.VISIBLE
            }
//            itemView.findViewById<TextView>(R.id.tv_item_myridcard_validity.text = ResUtil.getString(R.string.expiration_date_with_title,
//                TimeFormatUtil.transToStringBysqDataInfo(data.create_time.toLong())+"-"+TimeFormatUtil.transToStringBysqDataInfo(data.expire_time.toLong()))
            itemView.findViewById<TextView>(R.id.tv_item_myridcard_validity)?.text = ResUtil.getString(R.string.s_datedown,TimeFormatUtil.transToStringBysqDataInfo(data.expire_time.toLong()))
            itemView.findViewById<TextView>(R.id.tv_item_myridcard_title)?.text = data.title
            itemView.findViewById<TextView>(R.id.tv_item_myridcard_area)?.text = ResUtil.getString(R.string.s_onlearea_use,data.areaName)

            itemView.findViewById<RoundTextView>(R.id.rv_mycard_state)?.delegate?.backgroundColor = ContextUtil.getContext().resources.getColor(R.color.blue_namal)
            itemView.findViewById<RoundTextView>(R.id.rv_mycard_state)?.text = ResUtil.getString(R.string.effectiving)

            //1次卡 2时间卡
            if (data.type == 1 || data.type == 0){
                itemView.findViewById<TextView>(R.id.tv_item_myridcard_alone)?.text = ResUtil.getString(R.string.s_free_ridealone)+"："+data.deduction_time.toString()+" "+ResUtil.getString(R.string.min)
                itemView.findViewById<TextView>(R.id.tv_item_myridcard_dayall)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_item_myridcard_daynum)?.visibility = View.VISIBLE
                //1单日次数骑行卡，2总次数骑行卡，3无限卡
                if(data.ride_card_type == 3){
                    itemView.findViewById<TextView>(R.id.tv_item_myridcard_daynum)?.text = ResUtil.getString(R.string.s_remaining_day_num)+"："+ResUtil.getString(R.string.s_ulimit_num)
                }else{
                    itemView.findViewById<TextView>(R.id.tv_item_myridcard_daynum)?.text = ResUtil.getString(R.string.s_remaining_day_num)+"："+(data.times + data.present_times -data.used_times) +ResUtil.getString(R.string.s_unit_times)+
                            "("+data.used_times+"/"+(data.times+data.present_times)+")"
                }
                if (data.day == 0){
                    itemView.findViewById<TextView>(R.id.tv_item_myridcard_dayall)?.text = ResUtil.getString(R.string.s_Remaining_time)+"："+ResUtil.getString(R.string.s_ulimit_time)
                }else{
                    itemView.findViewById<TextView>(R.id.tv_item_myridcard_dayall)?.text = ResUtil.getString(R.string.s_Remaining_time)+"："+data.remaining_day +ResUtil.getString(R.string.s_unit_day)+
                            "("+data.remaining_day+"/"+(data.day+data.present_day)+")"
                }
            }else{
                itemView.findViewById<TextView>(R.id.tv_item_myridcard_dayall)?.visibility = View.GONE
                itemView.findViewById<TextView>(R.id.tv_item_myridcard_daynum)?.visibility = View.GONE
                if (data.invalid_time == 0){
                    // 1分钟 2小时 3天
                    when (data.free_unit){
                        1->itemView.findViewById<TextView>(R.id.tv_item_myridcard_alone)?.text = ResUtil.getString(R.string.s_ride_freetime)+"："+data.free_count.toString()+" "+ResUtil.getString(R.string.min)
                        2->itemView.findViewById<TextView>(R.id.tv_item_myridcard_alone)?.text = ResUtil.getString(R.string.s_ride_freetime)+"："+data.free_count.toString()+" "+ ResUtil.getString(R.string.s_unit_hour)
                        3->itemView.findViewById<TextView>(R.id.tv_item_myridcard_alone)?.text = ResUtil.getString(R.string.s_ride_freetime)+"："+data.free_count.toString()+" "+ResUtil.getString(R.string.s_unit_day)
                    }
                }else{
                    itemView.findViewById<TextView>(R.id.tv_item_myridcard_alone)?.text = ResUtil.getString(R.string.s_free_ridetime,TimeFormatUtil.transToStringBysqDataInfo(data.invalid_time.toLong()))
                }
            }

            if (data.is_support_refund == 1){
                itemView.findViewById<RoundTextView>(R.id.btn_refun)?.visibility = View.VISIBLE
            }else{
                itemView.findViewById<RoundTextView>(R.id.btn_refun)?.visibility = View.GONE
            }
            itemView.findViewById<RoundTextView>(R.id.btn_refun)?.setOnClickListener { onRefundListener(data.ride_card_no.toString()) }

            itemView.findViewById<ImageView>(R.id.img_item_myridcard)?.setOnClickListener {
                data.isExpend = !data.isExpend
                notifyItemChanged(position)
            }
            if(data.isExpend){
                //说明是否展开
                ImageLoad.loadimg(ContextUtil.getContext().getDrawable(R.drawable.picture_icon_arrow_up)!!,itemView.findViewById<ImageView>(R.id.img_item_myridcard))
                itemView.findViewById<TextView>(R.id.tv_item_myridcard_expend)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_item_myridcard_expend)?.text = data.notes
            }else{
                ImageLoad.loadimg(ContextUtil.getContext().getDrawable(R.drawable.picture_icon_arrow_down)!!,itemView.findViewById<ImageView>(R.id.img_item_myridcard))
                itemView.findViewById<TextView>(R.id.tv_item_myridcard_expend)?.visibility = View.GONE
            }

            if(position == source.size-1){
                itemView.findViewById<LinearLayout>(R.id.ly_item_losecard)?.visibility = View.VISIBLE
                itemView.findViewById<LinearLayout>(R.id.ly_item_losecard)?.setOnClickListener { onLoseCardListener(position) }
            }else{
                itemView.findViewById<LinearLayout>(R.id.ly_item_losecard)?.visibility = View.GONE
            }
        }
    }
}