package com.tbit.uqbike.adapter

import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.ViewGroup
import android.widget.CheckedTextView
import android.widget.EditText
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.tbit.maintanenceplus.utils.InputMethodUtils
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.RechargeConfig
import com.tbit.uqbike.utils.MoneyUtil

class RechargeAdapter: BaseAdapter<RechargeConfig>() {

    companion object {
        private const val TYPE_NORMAL = 0
        const val TYPE_MANUAL = 1
    }

    private var selectedIndex = 0
    var money: Int? = null
        private set

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return if (viewType == TYPE_NORMAL) NormalViewHolder(parent) else ManualViewHolder(parent)
    }

    override fun getItemViewType(position: Int): Int {
        val data = source.get(position)
        return if (data.money >= 0) TYPE_NORMAL else TYPE_MANUAL
    }

    override fun onItemClick(position: Int) {
        super.onItemClick(position)
        if (position == RecyclerView.NO_POSITION) return
        selectedIndex = position
        notifyDataSetChanged()
    }

    inner class NormalViewHolder(parent: ViewGroup)
        : BaseAdapter<RechargeConfig>.AbsViewHolder(parent, R.layout.item_charge) {

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            val isSelected = selectedIndex == position
            if (isSelected) money = data.money
            itemView.isSelected = isSelected
            itemView.findViewById<CheckedTextView>(R.id.check)?.isSelected = isSelected
            itemView.findViewById<CheckedTextView>(R.id.text_content)?.isSelected = isSelected
            itemView.findViewById<CheckedTextView>(R.id.check)?.text = itemView.context.getString(R.string.money_with_unit, Glob.symbol,
                MoneyUtil.standard2Show(data.money.toFloat()).toString())
            itemView.findViewById<CheckedTextView>(R.id.text_content)?.text = data.desc
            itemView.findViewById<CheckedTextView>(R.id.text_content)?.visibility = if (data.desc.isNullOrEmpty()) View.GONE else View.VISIBLE
        }
    }

    inner class ManualViewHolder(parent: ViewGroup)
        : BaseAdapter<RechargeConfig>.AbsViewHolder(parent, R.layout.item_manual_charge), TextWatcher {

        init {
            itemView.findViewById<EditText>(R.id.edit_charge_manual)?.setOnClickListener { onItemClick(adapterPosition) }
            itemView.findViewById<EditText>(R.id.edit_charge_manual)?.addTextChangedListener(this)
            itemView.findViewById<TextView>(R.id.text_charge_manual)?.text = itemView.context.getString(R.string.charge_customize, Glob.symbol)
        }

        override fun onBindViewHolder(position: Int) {
            val isSelected = selectedIndex == position
            if (!isSelected) recyclerView?.let { InputMethodUtils.hideInput(itemView.context, it) }
            itemView.isSelected = isSelected
            itemView.findViewById<EditText>(R.id.edit_charge_manual)?.isSelected = isSelected
            itemView.findViewById<TextView>(R.id.text_charge_manual)?.isSelected = isSelected
            itemView.findViewById<EditText>(R.id.edit_charge_manual)?.requestFocus()
            updateMoney()
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        override fun afterTextChanged(s: Editable?) {
            onEditChange()
        }

        private fun onEditChange() {
            val edit = itemView.findViewById<EditText>(R.id.edit_charge_manual)?.text.toString()
            itemView.findViewById<TextView>(R.id.text_charge_manual)?.text = itemView.context.getString(R.string.money_with_unit, Glob.symbol, edit)
            updateMoney()
        }

        private fun updateMoney() {
            if (!itemView.isSelected) return
            val edit = itemView.findViewById<EditText>(R.id.edit_charge_manual)?.text.toString()
            money = edit.toFloatOrNull()?.let { MoneyUtil.show2Standard(it).toInt() }
        }
    }
}