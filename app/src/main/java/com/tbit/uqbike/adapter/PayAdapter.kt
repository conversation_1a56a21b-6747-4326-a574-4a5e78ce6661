package com.tbit.uqbike.adapter

import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.SysPaymethodData
import com.tbit.uqbike.utils.ImageLoad

class PayAdapter : BaseAdapter<SysPaymethodData>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }
    inner class ViewHolder(parent: ViewGroup) :
        BaseAdapter<SysPaymethodData>.AbsViewHolder(parent, R.layout.item_pay) {

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return

            itemView.findViewById<TextView>(R.id.tv_rech_amout)?.text = data.name
            ImageLoad.loadimg(data.icon,itemView.findViewById<ImageView>(R.id.item_img_pay))
        }
    }

}