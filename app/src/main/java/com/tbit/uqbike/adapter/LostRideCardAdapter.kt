package com.tbit.uqbike.adapter

import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.App
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.MyRideCardDataItem
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import org.json.JSONObject

class LostRideCardAdapter : BaseAdapter<MyRideCardDataItem>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<MyRideCardDataItem>.AbsViewHolder(parent, R.layout.item_myridecard) {

        init {
//            itemView.text_money_unit.text = Glob.symbol
        }

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return

            val propertiesSea = JSONObject()
            propertiesSea.put("tag", "expired_card") // 设置商品
            propertiesSea.put("card_id", data.id) // 设置商品 ID
            MDUtil.SaeEvent(itemView,propertiesSea)

            itemView.findViewById<TextView>(R.id.tv_item_myridecard_tip)?.visibility = View.GONE

//            itemView.findViewById<TextView>(R.id.tv_item_myridcard_validity.text = ResUtil.getString(R.string.uexpiration_date_with_title,TimeFormatUtil.transToStringBysqDataInfo(data.expire_time.toLong()))
            itemView.findViewById<TextView>(R.id.tv_item_myridcard_validity)?.text = ResUtil.getString(R.string.s_datedown,TimeFormatUtil.transToStringBysqDataInfo(data.expire_time.toLong()))
            itemView.findViewById<TextView>(R.id.tv_item_myridcard_title)?.text = data.title
            itemView.findViewById<TextView>(R.id.tv_item_myridcard_area)?.text = ResUtil.getString(R.string.s_onlearea_use,data.areaName)

            itemView.findViewById<TextView>(R.id.tv_item_myridcard_dayall)?.visibility = View.GONE
            itemView.findViewById<TextView>(R.id.tv_item_myridcard_daynum)?.visibility = View.GONE


            if (data.refund_status == 1){
                itemView.findViewById<RoundTextView>(R.id.rv_mycard_state)?.delegate?.backgroundColor = ContextUtil.getContext().getColor(R.color.c_y)
                itemView.findViewById<RoundTextView>(R.id.rv_mycard_state)?.setTextColor(ContextUtil.getContext().getColor(R.color.white))
            }else{
                itemView.findViewById<RoundTextView>(R.id.rv_mycard_state)?.delegate?.backgroundColor = ContextUtil.getContext().resources.getColor(R.color.c_E3E4E6)
                itemView.findViewById<RoundTextView>(R.id.rv_mycard_state)?.setTextColor(ContextUtil.getContext().getColor(R.color.c_838588))
            }
            //0, 1次卡 2时间卡
            if (data.type == 1 || data.type == 0){
                itemView.findViewById<TextView>(R.id.tv_item_myridcard_alone)?.text = ResUtil.getString(R.string.s_free_ridealone)+"："+data.deduction_time.toString()+" "+ResUtil.getString(R.string.min)
                if (data.refund_status == 2){
                    itemView.findViewById<RoundTextView>(R.id.rv_mycard_state)?.text = ResUtil.getString(R.string.s_isrefund)
                }else if (data.refund_status == 1){
                    itemView.findViewById<RoundTextView>(R.id.rv_mycard_state)?.text = ResUtil.getString(R.string.s_isrefund_ing)
                }else{
                    itemView.findViewById<RoundTextView>(R.id.rv_mycard_state)?.text = ResUtil.getString(R.string.s_refund_expired)
                }

//                if (data.used_times == 0){
//                    itemView.findViewById<TextView>(R.id.rv_mycard_state.text = ResUtil.getString(R.string.expired)
//                }else{
//                    itemView.findViewById<TextView>(R.id.rv_mycard_state.text = ResUtil.getString(R.string.s_use_y)
//                }
            }else{
                if (data.refund_status == 2){
                    itemView.findViewById<RoundTextView>(R.id.rv_mycard_state)?.text = ResUtil.getString(R.string.s_isrefund)
                }else if (data.refund_status == 1){
                    itemView.findViewById<RoundTextView>(R.id.rv_mycard_state)?.text = ResUtil.getString(R.string.s_isrefund_ing)
                }else {
                    itemView.findViewById<RoundTextView>(R.id.rv_mycard_state)?.text = ResUtil.getString(R.string.s_refund_expired)
                }

//                if (data.first_use_time == 0L){
//                    itemView.findViewById<TextView>(R.id.rv_mycard_state.text = ResUtil.getString(R.string.expired)
//                }else{
//                    itemView.findViewById<TextView>(R.id.rv_mycard_state.text = ResUtil.getString(R.string.s_use_y)
//                }
                if (data.invalid_time == 0){
                    // 1分钟 2小时 3天
                    when (data.free_unit){
                        1->itemView.findViewById<TextView>(R.id.tv_item_myridcard_alone)?.text = ResUtil.getString(R.string.s_ride_freetime)+"："+data.free_count.toString()+" "+ResUtil.getString(R.string.min)
                        2->itemView.findViewById<TextView>(R.id.tv_item_myridcard_alone)?.text = ResUtil.getString(R.string.s_ride_freetime)+"："+data.free_count.toString()+" "+ ResUtil.getString(R.string.s_unit_hour)
                        3->itemView.findViewById<TextView>(R.id.tv_item_myridcard_alone)?.text = ResUtil.getString(R.string.s_ride_freetime)+"："+data.free_count.toString()+" "+ResUtil.getString(R.string.s_unit_day)
                    }
                }else{
                    itemView.findViewById<TextView>(R.id.tv_item_myridcard_alone)?.text = ResUtil.getString(R.string.s_free_ridetime,TimeFormatUtil.transToStringBysqDataInfo(data.invalid_time.toLong()))
                }
            }

            itemView.findViewById<ImageView>(R.id.img_item_myridcard)?.setOnClickListener {
                data.isExpend = !data.isExpend
                notifyItemChanged(position)
            }
            if(data.isExpend){
                //说明是否展开
                ImageLoad.loadimg(ContextUtil.getContext().getDrawable(R.drawable.picture_icon_arrow_up)!!,itemView.findViewById<ImageView>(R.id.img_item_myridcard))
                itemView.findViewById<TextView>(R.id.tv_item_myridcard_expend)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_item_myridcard_expend)?.text = data.notes
            }else{
                ImageLoad.loadimg(ContextUtil.getContext().getDrawable(R.drawable.picture_icon_arrow_down)!!,itemView.findViewById<ImageView>(R.id.img_item_myridcard))
                itemView.findViewById<TextView>(R.id.tv_item_myridcard_expend)?.visibility = View.GONE
            }
        }
    }
}