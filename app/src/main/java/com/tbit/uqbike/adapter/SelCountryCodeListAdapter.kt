package com.tbit.uqbike.adapter

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.CountryDataItem
import com.tbit.uqbike.utils.ImageLoad
import org.jetbrains.anko.layoutInflater

class SelCountryCodeListAdapter : RecyclerView.Adapter<SelCountryCodeListAdapter.MyHolder>() {

    var source: List<CountryDataItem> = emptyList()
    var onItemClickListener = { _: CountryDataItem -> }
    var selQH = ""

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyHolder {
        return MyHolder(parent.context.layoutInflater.inflate(R.layout.item_selcountry_code, parent, false))
    }

    override fun onBindViewHolder(holder: MyHolder, position: Int) {
        val countryCodeInfo = source.getOrNull(position) ?: return
        val context = holder.itemView.context
        holder.itemView.findViewById<TextView>(R.id.text_name)?.text = countryCodeInfo.title
//        holder.itemView.findViewById<TextView>(R.id.text_code)?.text =
//            context.getString(R.string.country_code_format, countryCodeInfo.country_code.toInt().toString())
        try {
            ImageLoad.loadimg(countryCodeInfo.icon,holder.itemView.findViewById(R.id.image_country))
        }catch (e : NullPointerException){}
    }

    override fun getItemCount(): Int {
        return source.size
    }

    private fun onItemClick(position: Int) {
        val countryCodeInfo = source.getOrNull(position) ?: return
        selQH = countryCodeInfo.country_code
        onItemClickListener(countryCodeInfo)
    }

    inner class MyHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        init {
            itemView.setOnClickListener {
                onItemClick(adapterPosition)
            }
        }
    }
}