package com.tbit.uqbike.adapter

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.tbit.uqbike.R
import org.jetbrains.anko.layoutInflater

class RefundKhhAdapter : RecyclerView.Adapter<RefundKhhAdapter.MyHolder>() {

    var source: List<String> = emptyList()
    var onItemClickListener = { _: String -> }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyHolder {
        return MyHolder(parent.context.layoutInflater.inflate(R.layout.item_refund_khh, parent, false))
    }

    override fun onBindViewHolder(holder: MyHolder, position: Int) {
        val dataItem = source.getOrNull(position) ?: return
//        val context = holder.itemView.findViewById<TextView>(R.id.context
        holder.itemView.findViewById<TextView>(R.id.text_name)?.text = dataItem
//        if (countryCodeInfo.url.isNullOrEmpty()) {
//            holder.itemView.image_country.visibility = View.GONE
//        } else {
//            holder.itemView.image_country.visibility = View.VISIBLE
//            Glide.with(context).load(countryCodeInfo.url).into(holder.itemView.image_country)
//        }
    }

    override fun getItemCount(): Int {
        return source.size
    }

    private fun onItemClick(position: Int) {
        val countryCodeInfo = source.getOrNull(position) ?: return
        onItemClickListener(countryCodeInfo)
    }

    inner class MyHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        init {
            itemView.setOnClickListener {
                onItemClick(adapterPosition)
            }
        }
    }
}