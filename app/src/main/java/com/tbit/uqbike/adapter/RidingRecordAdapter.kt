package com.tbit.uqbike.adapter

import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.App
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.RidingRecordDataItem
import com.tbit.uqbike.utils.MDUtil
import org.json.JSONObject
import java.util.*

class RidingRecordAdapter: BaseAdapter<RidingRecordDataItem>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<RidingRecordDataItem>.AbsViewHolder(parent, R.layout.item_riding_record) {

        init {
//            itemView.findViewById<TextView>(R.id.text_fee_unit.text = Glob.symbol
        }

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return

            val propertiesSea = JSONObject()
            propertiesSea.put("tag", "cycling_records") // 设置商品
            propertiesSea.put("cycling_records_id", data.id) // 设置商品 ID
            propertiesSea.put("order_status", data.status) // 设置商品 ID
            MDUtil.SaeEvent(itemView,propertiesSea)

            if (data.type == Constant.OrderType.RENTAL_Y){
                itemView.findViewById<TextView>(R.id.tv_item_ridingtime)?.text = ResUtil.getString(R.string.riding_time)+"："+"-" + ResUtil.getString(R.string.min)
                itemView.findViewById<TextView>(R.id.tv_item_ridingmileage)?.text = ResUtil.getString(R.string.mileage)+"："+"-" + "km"
            }else{
                itemView.findViewById<TextView>(R.id.tv_item_ridingtime)?.text = ResUtil.getString(R.string.riding_time)+"："+data.order_time + ResUtil.getString(R.string.min)
                itemView.findViewById<TextView>(R.id.tv_item_ridingmileage)?.text = ResUtil.getString(R.string.mileage)+"："+data.mileage + "km"
            }
            itemView.findViewById<TextView>(R.id.tv_item_ridingstarttime)?.text = ResUtil.getString(R.string.s_start_time)+"："+TimeFormatUtil.transToStringBysq(data.create_time.toLong())
            if(data.finish_time == null || data.finish_time == 0){
                itemView.findViewById<TextView>(R.id.tv_item_ridingendtime)?.text = ResUtil.getString(R.string.s_end_time)+"：-"
            }else{
                itemView.findViewById<TextView>(R.id.tv_item_ridingendtime)?.text = ResUtil.getString(R.string.s_end_time)+"："+TimeFormatUtil.transToStringBysq(data.finish_time.toLong())
            }
            itemView.findViewById<TextView>(R.id.tv_item_ridingno)?.text = ResUtil.getString(R.string.order_no)+"："+data.order_no
            //订单状态：1进行中,2待支付，3已支付
            when (data.status) {
                1 -> {
                    itemView.findViewById<TextView>(R.id.tv_item_ridingamount)?.visibility = View.GONE
                    if (data.type == Constant.OrderType.RENTAL_Y){
                        itemView.findViewById<TextView>(R.id.tv_item_ridingstate)?.text = ResUtil.getString(R.string.s_rentaling)
                    }else{
                        itemView.findViewById<TextView>(R.id.tv_item_ridingstate)?.text = ResUtil.getString(R.string.riding)
                    }
                    itemView.findViewById<TextView>(R.id.tv_item_ridingstate)?.setTextColor(App.context.resources.getColor(R.color.blue_namal))
                }
                2 -> {
                    itemView.findViewById<TextView>(R.id.tv_item_ridingamount)?.visibility = View.VISIBLE
                    itemView.findViewById<TextView>(R.id.tv_item_ridingamount)?.text = ResUtil.getString(R.string.s_tobepay)+"："+AppUtil.getFloat2(data.amount-data.paid_amount).toString()+data.currency
                    itemView.findViewById<TextView>(R.id.tv_item_ridingstate)?.text = ResUtil.getString(R.string.s_order_npay)
                    itemView.findViewById<TextView>(R.id.tv_item_ridingstate)?.setTextColor(App.context.resources.getColor(R.color.c_y))
                }
                3 -> {
                    itemView.findViewById<TextView>(R.id.tv_item_ridingamount)?.visibility = View.VISIBLE
                    itemView.findViewById<TextView>(R.id.tv_item_ridingamount)?.text = ResUtil.getString(R.string.s_paymoney)+"："+AppUtil.getFloat2(data.amount).toString()+data.currency
                    if (data.type == Constant.OrderType.RENTAL_Y){
                        itemView.findViewById<TextView>(R.id.tv_item_ridingstate)?.text = ResUtil.getString(R.string.s_paycomp)
                    }else{
                        itemView.findViewById<TextView>(R.id.tv_item_ridingstate)?.text = ResUtil.getString(R.string.s_order_comp)
                    }
                    itemView.findViewById<TextView>(R.id.tv_item_ridingstate)?.setTextColor(App.context.resources.getColor(R.color.c_C0C1C3))
                }
                5 -> {
                    itemView.findViewById<TextView>(R.id.tv_item_ridingamount)?.visibility = View.VISIBLE
                    itemView.findViewById<TextView>(R.id.tv_item_ridingamount)?.text = ResUtil.getString(R.string.s_paymoney)+"："+AppUtil.getFloat2(data.amount).toString()+data.currency
                    itemView.findViewById<TextView>(R.id.tv_item_ridingstate)?.text = ResUtil.getString(R.string.s_order_comp)
                    itemView.findViewById<TextView>(R.id.tv_item_ridingstate)?.setTextColor(App.context.resources.getColor(R.color.c_C0C1C3))
                }
            }
        }
    }
}