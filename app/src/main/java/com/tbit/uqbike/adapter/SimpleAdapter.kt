package com.tbit.uqbike.adapter

import android.view.ViewGroup
import android.widget.TextView
import com.tbit.uqbike.R

class SimpleAdapter: BaseAdapter<String>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<String>.AbsViewHolder(parent, R.layout.item_simple) {

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            itemView.findViewById<TextView>(R.id.text)?.text =data
        }
    }
}