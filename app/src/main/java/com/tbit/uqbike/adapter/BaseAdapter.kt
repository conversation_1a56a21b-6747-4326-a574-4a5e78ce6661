package com.tbit.uqbike.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import org.jetbrains.anko.layoutInflater

abstract class BaseAdapter<T>: RecyclerView.Adapter<BaseAdapter<T>.AbsViewHolder>() {

    var source = emptyList<T>()
    protected var recyclerView: RecyclerView? = null
    private var onItemClickListener = {_: T -> }
    private var onItemLongClickListener = {_: T -> false }
    private var onMyItemClickListener = {_: T,_: Int -> }

    override fun onBindViewHolder(holder: A<PERSON>ViewHolder, position: Int) {
        holder.onBindViewHolder(position)
    }

    override fun getItemCount(): Int {
        return source.size
    }

    protected open fun onItemClick(position: Int) {
        val data = source.getOrNull(position) ?: return
        onItemClickListener(data)
        onMyItemClickListener(data,position)
    }

    fun setOnItemClickListener(listener: (T) -> Unit) {
        this.onItemClickListener = listener
    }

    fun setOnMyItemClickListener(listener: (T,Int) -> Unit) {
        this.onMyItemClickListener = listener
    }

    private fun onItemLongClick(position: Int): Boolean {
        val data = source.getOrNull(position) ?: return false
        return onItemLongClickListener(data)
    }

    fun setOnItemLongClickListener(listener: (T) -> Boolean) {
        this.onItemLongClickListener = listener
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        this.recyclerView = null
    }

    abstract inner class AbsViewHolder: RecyclerView.ViewHolder {
        init {
            itemView.setOnClickListener { onItemClick(adapterPosition) }
            itemView.setOnLongClickListener { onItemLongClick(adapterPosition) }
        }

        constructor(parent: ViewGroup, layoutId: Int)
                :super(parent.context.layoutInflater.inflate(layoutId, parent, false))

        abstract fun onBindViewHolder(position: Int)
    }
}