package com.tbit.uqbike.adapter

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.RechargeItem
import com.tbit.uqbike.roundview.RoundRelativeLayout
import com.tbit.uqbike.roundview.RoundTextView

class RechargeNewAdapter : BaseAdapter<RechargeItem>() {
    var onClickListener = {_: String,_: String ->}
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }
    inner class ViewHolder(parent: ViewGroup) :
        BaseAdapter<RechargeItem>.AbsViewHolder(parent, R.layout.item_rechargenew) {

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return

            itemView.setOnClickListener {
                if(!data.isSatisfy) return@setOnClickListener
                source.forEach {
                    it.isSel = false}
                data.isSel = true
                notifyDataSetChanged()
                if (data.id.isNullOrEmpty()){
                    onClickListener(data.recharge_amount.toString(),"")
                }else{
                    onClickListener(data.recharge_amount.toString(),data.id)
                }
            }
            if(data.isSatisfy){
                itemView.findViewById<RoundRelativeLayout>(R.id.v_item_rechange)?.visibility = View.GONE
            }else{
                itemView.findViewById<RoundRelativeLayout>(R.id.v_item_rechange)?.visibility = View.VISIBLE
            }
            if(data.isSel){
                itemView.findViewById<RoundRelativeLayout>(R.id.item_ry_rech)?.visibility = View.VISIBLE
                itemView.findViewById<ImageView>(R.id.item_img_recharge)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_rech_amout)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.blue_namal))
            }else{
                itemView.findViewById<RoundRelativeLayout>(R.id.item_ry_rech)?.visibility = View.GONE
                itemView.findViewById<ImageView>(R.id.item_img_recharge)?.visibility = View.GONE
                itemView.findViewById<TextView>(R.id.tv_rech_amout)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.black_namal))
            }
            if(data.present_amount == null || data.present_amount.toFloat() == 0F){
                itemView.findViewById<RoundTextView>(R.id.tv_rech_present)?.visibility = View.GONE
            }else{
                itemView.findViewById<RoundTextView>(R.id.tv_rech_present)?.visibility = View.VISIBLE
                itemView.findViewById<RoundTextView>(R.id.tv_rech_present)?.text = ResUtil.getString(R.string.s_send)+data.present_amount+Glob.CurrencyUnit
            }
//            itemView.findViewById<TextView>(R.id.tv_rech_amout)?.text = data.recharge_amount.toString()+Glob.CurrencyUnit
            itemView.findViewById<TextView>(R.id.tv_rech_amout)?.text = AppUtil.formatAmount(kotlin.math.ceil(data.recharge_amount).toLong())+Glob.CurrencyUnit
        }
    }

}