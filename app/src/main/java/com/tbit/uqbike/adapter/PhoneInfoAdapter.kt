package com.tbit.uqbike.adapter

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.PhoneInfo
import com.tbit.uqbike.utils.ImageLoad

class PhoneInfoAdapter: BaseAdapter<PhoneInfo>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }
    fun String.isLong(): Boolean = toLongOrNull() != null
    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<PhoneInfo>.AbsViewHolder(parent, R.layout.item_phone_info) {

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            if (data.type == Constant.CustomerServiceType.PHONE) {
                itemView.findViewById<TextView>(R.id.text_content)?.text = data.text.toString()
                itemView.findViewById<TextView>(R.id.btn)?.text = ResUtil.getString(R.string.str_call)
                itemView.findViewById<ImageView>(R.id.image_icon)?.visibility = View.VISIBLE
                ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_local_phone),itemView.findViewById<ImageView>(R.id.image_icon))
            }else if (data.type == Constant.CustomerServiceType.FACEBOOK) {
                itemView.findViewById<TextView>(R.id.text_content)?.text = "Facebook"
                itemView.findViewById<ImageView>(R.id.image_icon)?.visibility = View.VISIBLE
                ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_facebook),itemView.findViewById<ImageView>(R.id.image_icon))
            }else if (data.type == Constant.CustomerServiceType.LINE) {
                itemView.findViewById<TextView>(R.id.text_content)?.text = "LINE"
                itemView.findViewById<ImageView>(R.id.image_icon)?.visibility = View.VISIBLE
                ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_line),itemView.findViewById<ImageView>(R.id.image_icon))
            } else {
                itemView.findViewById<TextView>(R.id.text_content)?.text = data.text.toString()
                itemView.findViewById<TextView>(R.id.btn)?.text = ResUtil.getString(R.string.str_copy)
                itemView.findViewById<ImageView>(R.id.image_icon)?.visibility = View.GONE
            }
        }
    }
}