package com.tbit.uqbike.adapter

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.tbit.maintenance.config.Constant
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.RideCard
import com.tbit.uqbike.utils.MoneyUtil

class RideCardAdapter: BaseAdapter<RideCard>() {

    private var selectedRideCardIndex = 0
    val selectedRideCard get() = source.getOrNull(selectedRideCardIndex)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    override fun onItemClick(position: Int) {
        super.onItemClick(position)
        if (position == RecyclerView.NO_POSITION) return
        selectedRideCardIndex = position
        notifyDataSetChanged()
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<RideCard>.AbsViewHolder(parent, R.layout.item_ride_card) {

        init {
            itemView.findViewById<TextView>(R.id.text_price_unit)?.text = Glob.symbol
        }

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            val context = itemView.context
            itemView.findViewById<TextView>(R.id.text_name)?.text = data.name
            itemView.findViewById<TextView>(R.id.text_price)?.text = String.format("%.2f", MoneyUtil.standard2Show(data.money))
            if (data.type == Constant.CardType.DURATION) {
                itemView.findViewById<TextView>(R.id.text_max_ride)?.visibility = View.GONE
                itemView.findViewById<TextView>(R.id.text_time_count)?.text = context.getString(R.string.str_can_ride_duration_format, data.timeCount)
            } else {
                itemView.findViewById<TextView>(R.id.text_max_ride)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.text_max_ride)?.text = context.getString(R.string.ride_card_max_ride, data.maxRide, parseType(context, data.type))
                itemView.findViewById<TextView>(R.id.text_time_count)?.text = context.getString(R.string.ride_card_time_count, data.timeCount)
            }
            itemView.isSelected = position == selectedRideCardIndex
        }

        private fun parseType(context: Context, type: Int): String? {
            return when (type) {
//                Constant.CardType.DAY -> context.getString(R.string.by_day)
//                Constant.CardType.WEEK -> context.getString(R.string.by_week)
//                Constant.CardType.MONTH -> context.getString(R.string.by_month)
//                Constant.CardType.SEASON -> context.getString(R.string.by_season)
//                Constant.CardType.HALF_YEAR -> context.getString(R.string.by_half_year)
//                Constant.CardType.YEAR -> context.getString(R.string.by_year)
                else -> null
            }
        }
    }
}