package com.tbit.uqbike.adapter

import android.view.ViewGroup
import android.widget.TextView
import com.tbit.uqbike.R
import com.tbit.uqbike.map.bean.SuggestionResult

class SuggestionAdapter: BaseAdapter<SuggestionResult>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<SuggestionResult>.AbsViewHolder(parent, R.layout.item_suggestion) {

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            itemView.findViewById<TextView>(R.id.text_name)?.text = data.title
            itemView.findViewById<TextView>(R.id.text_snippet)?.text = data.snippet
        }
    }
}