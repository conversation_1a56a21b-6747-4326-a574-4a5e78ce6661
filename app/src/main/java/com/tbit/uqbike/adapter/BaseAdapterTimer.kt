package com.tbit.uqbike.adapter

import android.os.CountDownTimer
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import org.jetbrains.anko.layoutInflater

abstract class BaseAdapterTimer <T>: RecyclerView.Adapter<BaseAdapterTimer<T>.AbsViewHolder>() {

    var source = emptyList<T>()
    protected var recyclerView: RecyclerView? = null
    private var onItemClickListener = {_: T -> }
    private var onItemLongClickListener = {_: T -> false }

    override fun onBindViewHolder(holder: AbsViewHolder, position: Int) {
        holder.onBindViewHolder(holder,position)
    }

    override fun getItemCount(): Int {
        return source.size
    }

    protected open fun onItemClick(position: Int) {
        val data = source.getOrNull(position) ?: return
        onItemClickListener(data)
    }

    fun setOnItemClickListener(listener: (T) -> Unit) {
        this.onItemClickListener = listener
    }

    private fun onItemLongClick(position: Int): Boolean {
        val data = source.getOrNull(position) ?: return false
        return onItemLongClickListener(data)
    }

    fun setOnItemLongClickListener(listener: (T) -> Boolean) {
        this.onItemLongClickListener = listener
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        this.recyclerView = null
    }

    abstract inner class AbsViewHolder: RecyclerView.ViewHolder {
        var countDownTimer : CountDownTimer? = null
        init {
            itemView.setOnClickListener { onItemClick(adapterPosition) }
            itemView.setOnLongClickListener { onItemLongClick(adapterPosition) }
        }

        constructor(parent: ViewGroup, layoutId: Int)
                :super(parent.context.layoutInflater.inflate(layoutId, parent, false))

        abstract fun onBindViewHolder(holder: AbsViewHolder,position: Int)
    }
}