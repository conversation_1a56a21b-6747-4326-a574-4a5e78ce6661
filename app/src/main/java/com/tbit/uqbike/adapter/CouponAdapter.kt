package com.tbit.uqbike.adapter

import android.content.Context
import android.graphics.Color
import android.text.Html
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.Coupon
import com.tbit.uqbike.utils.CacheFactory
import com.tbit.uqbike.utils.MoneyUtil
import com.tbit.uqbike.databinding.ItemCouponBinding
import com.tbit.uqbike.dialog.CommDialog
import androidx.core.text.HtmlCompat
import org.jetbrains.anko.dip
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import java.util.TimeZone
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.activity.ScanForBorrowActivity
import com.tbit.uqbike.dialog.util.RidingDialogUtil
import com.tbit.uqbike.activity.LoginActivity
import com.tbit.uqbike.activity.MainActivity
import android.content.Intent
import com.tbit.maintenance.utils.AppUtil

class CouponAdapter: BaseAdapter<Coupon>() {

    companion object {
        private const val TYPE_NORMAL = 0
        private const val TYPE_LAST = 1
    }

    private enum class CouponDisplayStatus { ACTIVE, USED, EXPIRED, LOCKED }

    private fun getDisplayStatus(data: Coupon): CouponDisplayStatus {
        if (data.isLocked) return CouponDisplayStatus.LOCKED
        
        // Handle explicit statuses next
        if (data.internalStatus == 1) return CouponDisplayStatus.USED // API says used

        if (data.endTimeLong <= 0L) return CouponDisplayStatus.EXPIRED // No valid end time or already past for unlocked coupons

        // If the coupon's UTC end time is before the current UTC time, it's expired.
        if (data.endTimeLong < System.currentTimeMillis()) {
            return CouponDisplayStatus.EXPIRED
        } else {
            // Otherwise, it's active
            return CouponDisplayStatus.ACTIVE
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        val viewHolder = ViewHolder(parent)
        if(viewType == TYPE_LAST) {
            (viewHolder.itemView.layoutParams as ViewGroup.MarginLayoutParams).bottomMargin = parent.context.dip(16)
        }
        return viewHolder
    }

    override fun getItemViewType(position: Int): Int {
        return if(position == source.size - 1) TYPE_LAST else TYPE_NORMAL
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<Coupon>.AbsViewHolder(parent, R.layout.item_coupon) {

        private val leftPanelBg: View? = itemView.findViewById(R.id.view_coupon_left_panel_bg)
        private val tvCouponValue: TextView? = itemView.findViewById(R.id.tv_coupon_value)
        private val tvCouponUnit: TextView? = itemView.findViewById(R.id.tv_coupon_currency)
        private val tvCouponTitle: TextView? = itemView.findViewById(R.id.tv_coupon_title)
        private val tvCouponExpiry: TextView? = itemView.findViewById(R.id.tv_coupon_expiry)
        private val tvCouponThreshold: TextView? = itemView.findViewById(R.id.tv_coupon_threshold)
        private val tvCouponStatusWatermark: TextView? = itemView.findViewById(R.id.tv_coupon_status_watermark)
        private val btnCouponUse: Button? = itemView.findViewById(R.id.btn_coupon_use)
        private val btnCouponUnlock: Button? = itemView.findViewById(R.id.btn_coupon_unlock)
        private val tvCouponRulesLink: TextView? = itemView.findViewById(R.id.tv_coupon_rules_link)
        private val containerRulesDescription: View? = itemView.findViewById(R.id.container_rules_description)

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            val context = itemView.context
            val displayStatus = getDisplayStatus(data)

            // Reset button visibility initially
            btnCouponUse?.visibility = View.GONE
            btnCouponUnlock?.visibility = View.GONE

            // Left Panel Background and Watermark
            when (displayStatus) {
                CouponDisplayStatus.ACTIVE -> {
                    leftPanelBg?.setBackgroundColor(ContextCompat.getColor(context, R.color.coupon_active_red))
                    tvCouponStatusWatermark?.visibility = View.GONE
                    btnCouponUse?.visibility = View.VISIBLE
                }
                CouponDisplayStatus.LOCKED -> {
                    leftPanelBg?.setBackgroundColor(ContextCompat.getColor(context, R.color.coupon_active_red))
                    tvCouponStatusWatermark?.visibility = View.GONE
                    btnCouponUnlock?.visibility = View.VISIBLE 
                }
                CouponDisplayStatus.USED -> {
                    leftPanelBg?.setBackgroundColor(ContextCompat.getColor(context, R.color.coupon_inactive_gray))
                    tvCouponStatusWatermark?.visibility = View.VISIBLE
                    tvCouponStatusWatermark?.text = context.getString(R.string.s_vip_coupon_btn_used)
                }
                CouponDisplayStatus.EXPIRED -> {
                    leftPanelBg?.setBackgroundColor(ContextCompat.getColor(context, R.color.coupon_inactive_gray))
                    tvCouponStatusWatermark?.visibility = View.VISIBLE
                    tvCouponStatusWatermark?.text = context.getString(R.string.s_vip_coupon_btn_expire)
                }
            }

            // Logging for debug
            val visibilityInt = tvCouponStatusWatermark?.visibility
            val visibilityString = when(visibilityInt) {
                View.VISIBLE -> "VISIBLE (0)"
                View.GONE -> "GONE (8)"
                View.INVISIBLE -> "INVISIBLE (4)"
                else -> "UNKNOWN ($visibilityInt)"
            }
            android.util.Log.d("CouponAdapterVisibility", "Coupon: '${data.name}', isLocked: ${data.isLocked}, displayStatus: $displayStatus, watermarkVisibility: $visibilityString, endTimeLong: ${data.endTimeLong}, currentTimeMillis: ${System.currentTimeMillis()}")

            if (data.couponType == 1) {
                tvCouponValue?.text = AppUtil.getFloat2(data.couponMoney)
                tvCouponUnit?.text = context.getString(R.string.s_currency_thb)
            } else {
                tvCouponValue?.text = data.couponTime.toString()
                tvCouponUnit?.text = context.getString(R.string.s_time_unit_min)
            }

            tvCouponTitle?.text = data.name
            tvCouponExpiry?.text = formatExpiryDate(data.endTimeLong, context)
            tvCouponThreshold?.text = data.thresholdDescription

            btnCouponUse?.setOnClickListener {
                if (!LocationUtil.isGpsPermiss()) {
                    return@setOnClickListener
                }
                RidingDialogUtil.showRideCardDialog(context as BaseActivity) {
                    val intent = Intent(context, MainActivity::class.java)
                    context.startActivity(intent)
                }
            }

            tvCouponRulesLink?.setOnClickListener {
                Toast.makeText(context, "Show rules for: ${data.name}", Toast.LENGTH_LONG).show()
            }

            containerRulesDescription?.setOnClickListener {
                val couponRemark = data.remark ?: ""
                val spannedRemark: Spanned = HtmlCompat.fromHtml(couponRemark, HtmlCompat.FROM_HTML_MODE_LEGACY)
                CommDialog.Builder(itemView.context)
                    .setTitle(itemView.context.getString(R.string.s_rule_desc))
                    .setSpanContent(SpannableString(spannedRemark))
                    .setRightText(itemView.context.getString(R.string.confirm))
                    .setClickListen(object : CommDialog.OneSelDialog {
                        override fun sureClick() {
                            // Dialog dismisses automatically
                        }
                    })
                    .setLeftText(null) // Ensure left button is hidden
                    .setCanceledOnOutside(true)
                    .build()
                    .show()
            }
        }
    }

    private fun formatExpiryDate(endTimeLong: Long, context: Context): String {
        if (endTimeLong <= 0L) {
            return "" // Return empty string if endTimeLong is 0 or negative
        } else {
            val couponCalendar = Calendar.getInstance().apply { timeInMillis = endTimeLong } // Local time of expiry
            val todayCalendar = Calendar.getInstance() // Current local time

            return if (couponCalendar.get(Calendar.YEAR) == todayCalendar.get(Calendar.YEAR) &&
                couponCalendar.get(Calendar.DAY_OF_YEAR) == todayCalendar.get(Calendar.DAY_OF_YEAR)) {
                // 到期日是今天
                val sdfTime = SimpleDateFormat("HH:mm", Locale.getDefault())
                val timeStr = sdfTime.format(couponCalendar.time)
                context.getString(R.string.s_today_time_expires, timeStr)
            } else {
                // 到期日不是今天
                val sdfDate = SimpleDateFormat("yyyy.MM.dd", Locale.getDefault())
                val dateStr = sdfDate.format(couponCalendar.time)
                dateStr + "到期" // Hardcoded for now, recommend using R.string.s_expires_suffix
            }
        }
    }
}