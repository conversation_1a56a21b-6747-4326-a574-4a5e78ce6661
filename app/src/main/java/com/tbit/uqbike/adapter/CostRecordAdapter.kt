package com.tbit.uqbike.adapter

import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.App
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.FaultListDataItem
import com.tbit.uqbike.entity.RidingRecordDataItem
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.MDUtil
import org.json.JSONObject

class CostRecordAdapter : BaseAdapter<FaultListDataItem>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<FaultListDataItem>.AbsViewHolder(parent, R.layout.item_cost_record) {

        init {
//            itemView.findViewById<TextView>(R.id.text_fee_unit.text = Glob.symbol
        }

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return

            itemView.findViewById<TextView>(R.id.tv_item_name)?.text = ResUtil.getString(R.string.s_ride_order)+":  "+data.order_no
            itemView.findViewById<RoundTextView>(R.id.rtv_item_state)?.text = data.status_name
            //0待处理，1已处理（审核）-通过，2已处理（审核）-不通过；部分列表需前端自行判断，=0未处理，>0已处理
            when(data.status){
                0 ->{
                    itemView.findViewById<RoundTextView>(R.id.rtv_item_state)?.setTextColor(
                        ContextUtil.getContext().getResources().getColor(R.color.c_F86125))
                    itemView.findViewById<RoundTextView>(R.id.rtv_item_state)?.getDelegate()?.backgroundColor = ContextUtil.getContext().getResources().getColor(R.color.c_F86125_10)
                }
                1 ->{
                    itemView.findViewById<RoundTextView>(R.id.rtv_item_state)?.setTextColor(
                        ContextUtil.getContext().getResources().getColor(R.color.blue_namal))
                    itemView.findViewById<RoundTextView>(R.id.rtv_item_state)?.getDelegate()?.backgroundColor = ContextUtil.getContext().getResources().getColor(R.color.blue_namal10)
                }
                2 ->{
                    itemView.findViewById<RoundTextView>(R.id.rtv_item_state)?.setTextColor(
                        ContextUtil.getContext().getResources().getColor(R.color.c_838588))
                    itemView.findViewById<RoundTextView>(R.id.rtv_item_state)?.getDelegate()?.backgroundColor = ContextUtil.getContext().getResources().getColor(R.color.c_838588_tra)
                }
            }
            itemView.findViewById<TextView>(R.id.tv_item_time)?.text = ResUtil.getString(R.string.s_appeal_time)+"："+TimeFormatUtil.transToStringBysq(data.create_time)
        }
    }
}