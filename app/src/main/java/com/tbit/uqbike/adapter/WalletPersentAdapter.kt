package com.tbit.uqbike.adapter

import android.graphics.PorterDuff
import android.os.Build
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.App
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.WalletPersentDataItem
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.MDUtil
import org.json.JSONObject


class WalletPersentAdapter : BaseAdapter<WalletPersentDataItem>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        val viewHolder = ViewHolder(parent)
        return viewHolder
    }
    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<WalletPersentDataItem>.AbsViewHolder(parent, R.layout.item_walletpersent) {

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return

            val propertiesSea = JSONObject()
            propertiesSea.put("tag", "gift_balance") // 设置商品
            propertiesSea.put("wallet_balance", data.amount) // 设置商品 ID
            MDUtil.SaeEvent(itemView,propertiesSea)

            itemView.findViewById<TextView>(R.id.tv_item_walletpersent_name)?.text = data.name
            itemView.findViewById<TextView>(R.id.tv_item_walletpersent_money)?.text = AppUtil.getFloat2Double(data.amount).toString()
            itemView.findViewById<TextView>(R.id.tv_item_walletpersent_unit)?.text = data.currency
            if(data.is_usable == 1){
                itemView.findViewById<RoundTextView>(R.id.tv_item_walletpersent_state)?.setTextColor(App.context.resources.getColor(R.color.blue_namal))
                itemView.findViewById<TextView>(R.id.tv_item_walletpersent_money)?.setTextColor(App.context.resources.getColor(R.color.black_namal))
                itemView.findViewById<TextView>(R.id.tv_item_walletpersent_unit)?.setTextColor(App.context.resources.getColor(R.color.black_namal))
                itemView.findViewById<RoundTextView>(R.id.tv_item_walletpersent_state)?.delegate?.backgroundColor = App.context.resources.getColor(R.color.c_F3FAFF)
                itemView.findViewById<RoundTextView>(R.id.tv_item_walletpersent_state)?.text = ResUtil.getString(R.string.s_area_use)
                val tintedDrawable = App.context.resources.getDrawable(R.drawable.ic_go).constantState!!.newDrawable().mutate()
                tintedDrawable.setColorFilter(ContextCompat.getColor(App.context, R.color.black_namal), PorterDuff.Mode.SRC_IN)
                itemView.findViewById<ImageView>(R.id.img_item_walletpersent)?.setImageDrawable(tintedDrawable)
            }else{
                itemView.findViewById<RoundTextView>(R.id.tv_item_walletpersent_state)?.setTextColor(App.context.resources.getColor(R.color.c_838588))
                itemView.findViewById<TextView>(R.id.tv_item_walletpersent_money)?.setTextColor(App.context.resources.getColor(R.color.c_838588))
                itemView.findViewById<TextView>(R.id.tv_item_walletpersent_unit)?.setTextColor(App.context.resources.getColor(R.color.c_838588))
                itemView.findViewById<RoundTextView>(R.id.tv_item_walletpersent_state)?.delegate?.backgroundColor = App.context.resources.getColor(R.color.c_grey)
                itemView.findViewById<RoundTextView>(R.id.tv_item_walletpersent_state)?.text = ResUtil.getString(R.string.s_area_uuse)
                val tintedDrawable = App.context.resources.getDrawable(R.drawable.ic_go).constantState!!.newDrawable().mutate()
                tintedDrawable.setColorFilter(ContextCompat.getColor(App.context, R.color.c_838588), PorterDuff.Mode.SRC_IN)
                itemView.findViewById<ImageView>(R.id.img_item_walletpersent)?.setImageDrawable(tintedDrawable)
            }
            itemView.setOnClickListener { onGoPersentInfoListener(position) }
        }
    }

    var onGoPersentInfoListener = {_: Int ->}
}