package com.tbit.uqbike.adapter

//import com.luck.picture.lib.PictureSelector

import android.annotation.SuppressLint
import android.app.Activity.RESULT_OK
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.doule.database.CoroutinesUtil
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.luck.picture.lib.config.SelectorConfig
import com.luck.picture.lib.permissions.PermissionConfig
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.preview.GlideEngine
import com.tbit.preview.ImageConfig
import com.tbit.preview.ImageInfo
import com.tbit.preview.ImagePreviewActivity
import com.tbit.uqbike.App
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.dialog.PicSumitDialog
import com.tbit.uqbike.file.FileUtil
import com.tbit.uqbike.utils.AliyunUploadFile
import com.tbit.uqbike.utils.HashTools
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import kotlinx.coroutines.delay
import top.zibin.luban.Luban
import top.zibin.luban.OnCompressListener
import java.io.File
import java.io.Serializable
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger


/**
 * Created by yankaibang on 2018/10/8.
 */
open class SelectPhotoAdapter(protected val activity: BaseActivity, private val spanCount: Int, private val maxCount: Int,
                              protected val source: MutableList<ImageInfo>, private val photoPath: String) : RecyclerView.Adapter<SelectPhotoAdapter.Holder>() {

    private val TYPE_NORMAL = 0
    private val TYPE_ADD_MORE = 1
    private val statusHeight by lazy { getStatusBarHeight(activity) }
    protected val selectorImageFragment: SelectorImageFragment
    protected val REQUEST_CODE_CHOOSE = count.getAndIncrement() % 0xFFFF
    private val SELECTOR_IMAGE_TAG = "SELECTOR_IMAGE_TAG"
    //    protected open val userCache = true
    protected open val userCache = false
    var onDelListener = {_: Int ->}
    var onSucListener = {_: Int ->}
    var onclickListener = {_: Int ->}

    var editable: Boolean = true
    var isCam = false //是否只拍照
    var isWate = false// 是否水印
    var carnum = ""// 车辆编号
    var adress = ""// 定位地址
    var showTypeBig = false// 展示大小
    init {
        val addedSelectorImageFragment = activity.supportFragmentManager.findFragmentByTag(SELECTOR_IMAGE_TAG) as? SelectorImageFragment
        selectorImageFragment = addedSelectorImageFragment ?: SelectorImageFragment()
        if(addedSelectorImageFragment == null) {
            activity.supportFragmentManager.beginTransaction().add(selectorImageFragment, SELECTOR_IMAGE_TAG).commitNowAllowingStateLoss()
        }
        selectorImageFragment.adapters.add(this)

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
        return if(viewType == TYPE_ADD_MORE){
            if (showTypeBig){
                AddMoreHolder(activity.layoutInflater.inflate(R.layout.item_camera_big, parent, false))
            }else{
                AddMoreHolder(activity.layoutInflater.inflate(R.layout.item_camera, parent, false))
            }
        } else{
            if (showTypeBig){
                NormalHolder(activity.layoutInflater.inflate(R.layout.item_select_imgage_big, parent, false))
            }else{
                NormalHolder(activity.layoutInflater.inflate(R.layout.item_select_image, parent, false))
            }
        }
    }

    override fun onBindViewHolder(holder: Holder, position: Int) {
        when(holder) {
            is NormalHolder-> onBindViewHolder(holder, position)
            is AddMoreHolder -> onBindViewHolder(holder, position)
        }
    }

    override fun getItemCount(): Int {
        return if(isShowAdd()) source.size + 1 else source.size
    }

    override fun getItemViewType(position: Int): Int {
        return if(isShowAdd() && position == itemCount - 1) TYPE_ADD_MORE else TYPE_NORMAL
    }

    private fun onBindViewHolder(holder: NormalHolder, position: Int) {
        if (source[position].bigImageUrl != null){
            onDisplayImage(activity, holder.iv_image, source[position].bigImageUrl)
        }
        holder.itemView.setOnClickListener {
            updateImageLocation(holder.itemView, holder.iv_image, position)
            startImagePreviewActivity(position)
        }
        holder.iv_delete.setOnClickListener {
            delImage(position)
        }
        holder.iv_delete.visibility = if (isShowDel()) View.VISIBLE else View.GONE
    }

    private fun onDisplayImage(context: Context, imageView: ImageView, url: String) {
        if (userCache) {
            ImageConfig.getImageLoader().onDisplayImage(context, imageView, url)
        } else {
            Glide.with(context).load(url)
                .apply(RequestOptions().skipMemoryCache(true)
                    .diskCacheStrategy(DiskCacheStrategy.NONE))
                .into(imageView)
        }
    }

    private fun onBindViewHolder(holder: AddMoreHolder, position: Int) {
        holder.itemView.setOnClickListener {
//            selectImage()
//            selectCamera()
            onclickListener(position)
            var picDialog = PicSumitDialog(isCam)
            picDialog.onBtnListener = {
                when (it) {
                    0 -> selectCamera()
                    1 -> selectImage()
                }
            }
            activity.lifecycleDialogHelper.show(picDialog)
        }
    }

    private fun updateImageLocation(itemView: View, imageView: ImageView, position: Int) {
        val itemViewWidth = itemView.width
        val itemViewHeight = itemView.height
        val imageViewWidth = imageView.width
        val imageViewHeight = imageView.height

        //获取当前imageView的位置
        val points = IntArray(2)
        imageView.getLocationInWindow(points)
        val positionIamgeViewX = points[0]
        val positionIamgeViewY = points[1] - statusHeight
        //计算第一个imageView的位置
        val firstImageViewX = positionIamgeViewX - position % spanCount * itemViewWidth
        val firstImageViewY = positionIamgeViewY - position / spanCount * itemViewHeight

        for (i in source.indices) {
            val info = source.get(i)
            info.imageViewWidth = imageViewWidth
            info.imageViewHeight = imageViewHeight
            info.imageViewX = firstImageViewX + i % spanCount * itemViewWidth
            info.imageViewY = firstImageViewY + i / spanCount * itemViewHeight/2
        }
    }

    private fun startImagePreviewActivity(position: Int) {
        val intent = Intent(activity, ImagePreviewActivity::class.java)
        val bundle = Bundle()
        bundle.putSerializable(ImagePreviewActivity.IMAGE_INFO, source as Serializable)
        bundle.putInt(ImagePreviewActivity.CURRENT_ITEM, position)
        bundle.putBoolean(ImagePreviewActivity.USER_CACHE, userCache)
        intent.putExtras(bundle)
        activity.startActivity(intent)
        activity.overridePendingTransition(0, 0)
    }

    protected open fun selectImage() {
        PictureSelector.create(selectorImageFragment)
            .openGallery(SelectMimeType.ofImage())
            .setMaxSelectNum(maxCount - source.count())// 最大图片选择数量
            .setMinSelectNum(1)// 最小选择数量
            .setImageSpanCount(3)// 每行显示个数
            .setSelectionMode(SelectModeConfig.MULTIPLE)// 多选 or 单选
            .setImageEngine(GlideEngine.createGlideEngine())
            .isPreviewImage(false)// 是否可预览图片
            .isSelectZoomAnim(true)// 图片列表点击 缩放效果 默认true
            .isDisplayCamera(false)//是否显示相机入口
//            .setCompressEngine(ImageFileCompressEngine())// 是否压缩
            .forResult(REQUEST_CODE_CHOOSE)//结果回调onActivityResult code
    }

    protected open fun selectCamera() {
        PictureSelector.create(selectorImageFragment)
            .openCamera(SelectMimeType.ofImage())// 全部.PictureMimeType.ofAll()、图片.ofImage()、视频.ofVideo()、音频.ofAudio()
            .forResultActivity(REQUEST_CODE_CHOOSE)//结果回调onActivityResult code
    }

    protected open fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == REQUEST_CODE_CHOOSE && resultCode == RESULT_OK) {
            val selectList = PictureSelector.obtainSelectorList(data)
            selectList.map {
                val localMedia = it
                if (localMedia.isCompressed) localMedia.compressPath else localMedia.realPath
            }.let { addImage(it) }
        }
    }
    var sumitNum = 0
    var sumitMax = 0
    protected fun addImage(paths: List<String>) {
        sumitNum = 0
        sumitMax = paths.size
        activity.runOnUiThread {activity.loadingDialogHelper.show {  }}

        paths.forEach {
            source.add(ImageInfo().apply {
                try {
                    Luban.with(activity)
                        .load(File(it))
                        .ignoreBy(100)
                        .setCompressListener(object : OnCompressListener {
                            override fun onStart() {}
                            override fun onSuccess(file: File) {
                                MyLogUtil.Log("1111","=====drawTextToBitmap===onSuccess======"+it)
                                if (isWate){
                                    var newPath = drawTextToBitmap(activity,file,carnum,adress)
                                    bigImageUrl = newPath
                                }else{
                                    bigImageUrl = file.absolutePath
                                }
                                sumitImg()
                            }
                            override fun onError(e: Throwable) {
                                MyLogUtil.Log("1111","=====drawTextToBitmap===onError======"+it)
                                if (isWate){
                                    var newPath = drawTextToBitmap(activity,File(it),carnum,adress)
                                    bigImageUrl = newPath
                                }else{
                                    bigImageUrl = it
                                }
                                sumitImg()
                            }
                        }).launch()
                }catch (e : Exception){
                    activity.loadingDialogHelper.dismiss()
                    MyToastUtil.toast(ResUtil.getString(R.string.s_sumitimg_fail))
                }

            })
        }


    }
    fun sumitImg(){
        Log.e("1111","==========选择图片==="+source.size)
//        activity.runOnUiThread {activity.loadingDialogHelper.show {  }}
        source.forEach {
            if (!it.bigImageUrl.contains("http") && it.sumitState == 0){
                it.sumitState = 1
                var sale = UUID.randomUUID().toString().substring(0,4);//
                var fileName = Glob.IMG_PREFIX+"/"+photoPath+"/"+ HashTools.digestBySHA1(""+System.currentTimeMillis() + sale)
                MyLogUtil.Log("1111","==== 图片上传 fileName=="+fileName)
                AliyunUploadFile(object : AliyunUploadFile.AliyunUploadView {
                    override fun UploadSuccess(url: String?) {
                        MyLogUtil.Log("1111","==== 图片上传成功 =="+url)
                        sumitNum++
                        if (sumitNum == sumitMax) activity.runOnUiThread { activity.loadingDialogHelper.dismiss() }
                        it.bigImageUrl = url
                        it.sumitState = 2
                        onSucListener(0)
                    }
                    override fun Uploaddefeated(error: String?) {
                        sumitNum++
                        activity.runOnUiThread {
                            it.sumitState = 0
                            if (sumitNum == sumitMax) activity.loadingDialogHelper.dismiss()
                            MyToastUtil.toast(ResUtil.getString(R.string.s_sumitimg_fail))
                        }
                        MyLogUtil.Log("1111","==== 图片上传失败 =="+error)
                    }
                }).UploadFile(
                    App.context,Glob.ACCESS_ID,Glob.ACCESS_KEY,Glob.ACCESS_TOKEN,Glob.ACCESS_ENDPOINT,Glob.ACCESS_BUCKET_NAME,
                    fileName,it.bigImageUrl)
            }
        }
        CoroutinesUtil.launchMain {
            delay(500)
            notifyDataSetChanged()
        }
    }

    private fun delImage(index: Int) {
        if (source.size > 0){
            source.removeAt(index)
            notifyDataSetChanged()
            onDelListener(index)
        }
    }

    private fun isShowAdd(): Boolean {
        return source.size < maxCount && editable
    }

    private fun isShowDel(): Boolean {
        return editable
    }

    /**
     * 获取状态栏高度(px)
     *
     * @return 状态栏高度px
     */
    private fun getStatusBarHeight(context: Context): Int {
        val resources = context.resources
        val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
        return resources.getDimensionPixelSize(resourceId)
    }

    abstract inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)

    inner class NormalHolder(itemView: View): Holder(itemView){
        val iv_image = itemView.findViewById<ImageView>(R.id.iv_image)
        val iv_delete = itemView.findViewById<ImageView>(R.id.iv_delete)
    }

    inner class AddMoreHolder(itemView: View): Holder(itemView)

    companion object {

        const val TYPE_FAULT = "fault"//故障上报
        const val TYPE_APPEAL = "appeal"//费用申诉
        const val TYPE_FEEDBACK = "feedback"//意见反馈
        const val TYPE_PROFILE = "user/profile"//用户认证
        const val TYPE_USER = "user"//用户头像
        const val TYPE_LEAVEMSG = "msg"//留言

        val count = AtomicInteger()
        class SelectorImageFragment: Fragment() {
            var adapters = mutableListOf<SelectPhotoAdapter>()

            override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
                super.onActivityResult(requestCode, resultCode, data)
                adapters.forEach {
                    it.onActivityResult(requestCode, resultCode, data)
                }
            }
        }
    }

    /**
     * 添加水印并保存到手机新目录
     */
//    fun drawTextToBitmap(gContext: Context, gBitmap: Bitmap, gText: String): String? {
    @SuppressLint("MissingInflatedId", "NewApi")
    fun drawTextToBitmap(gContext: Context, file: File, carNum: String,adress: String): String? {
        val resources = gContext.resources
        val scale = resources.displayMetrics.density
//        val bitmap = gBitmap.copy(Bitmap.Config.ARGB_8888, true)
        val bitmap = fileToBitmap(file).copy(Bitmap.Config.ARGB_8888, true)
        val srcWidth: Int = bitmap.getWidth()
        val srcHeight: Int = bitmap.getHeight()

        val rootView = LinearLayout(gContext)
//        val density = ContextUtil.getContext().resources.displayMetrics.density//防止不同屏幕密度下UI效果不同做的一个缩放
//        MyLogUtil.Log("1111","======density========="+density)
        val view = LayoutInflater.from(gContext).inflate(R.layout.layout_watemark,rootView)
        view.findViewById<TextView>(R.id.tv_wate_carnum).text = carNum

        view.findViewById<TextView>(R.id.tv_wate_time).text = TimeFormatUtil.transToStringTime(System.currentTimeMillis())
        view.findViewById<TextView>(R.id.tv_wate_adress).text = adress
        val watermark = viewToBitmap(view)

        val watermarkWidth: Int = watermark.getWidth()
        val watermarkHeight: Int = watermark.getHeight()

        val bm = Bitmap.createBitmap(srcWidth, srcHeight, bitmap.config)
        val canvas = Canvas(bm)
        // 将原图绘制到新的Bitmap上
        canvas.drawBitmap(bitmap, 0f, 0f, null)
        // 将水印绘制到右下角
//        canvas.drawBitmap(watermark, (srcWidth - watermarkWidth).toFloat(), (srcHeight - watermarkHeight).toFloat(), null)
        canvas.drawBitmap(watermark, 40f, (srcHeight - watermarkHeight).toFloat()-40f, null)
        return FileUtil.saveImageToGallery(activity,bm)
    }
    fun viewToBitmap(view: View): Bitmap {
        val measureSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        view.measure(measureSpec, measureSpec)
//        val density = ContextUtil.getContext().resources.displayMetrics.density//防止不同屏幕密度下UI效果不同做的一个缩放
        //由于是生成的，未进行测量，所有需要进行measure操作
//        var measuredWidth = (view.measuredWidth * density).toInt()
//        var measuredHeight = (view.measuredHeight * density).toInt()
        var measuredWidth = (view.measuredWidth).toInt()
        var measuredHeight = (view.measuredHeight).toInt()
        view.layout(0, 0, measuredWidth, measuredHeight)
        // 创建一个和给定View相同大小的空Bitmap
//        val bitmap = Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
        val bitmap = Bitmap.createBitmap(measuredWidth, measuredHeight, Bitmap.Config.ARGB_8888)
        // 使用Canvas来将View的内容绘制到Bitmap上
        val canvas = Canvas(bitmap)
        // 确保View被重绘
        view.draw(canvas)
        return bitmap
    }
    fun fileToBitmap(file: File): Bitmap {
        val options = BitmapFactory.Options()
        options.inPreferredConfig = Bitmap.Config.ARGB_8888
        return BitmapFactory.decodeFile(file.absolutePath, options)
    }
}