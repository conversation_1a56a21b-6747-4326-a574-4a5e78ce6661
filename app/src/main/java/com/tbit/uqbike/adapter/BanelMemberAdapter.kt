package com.tbit.uqbike.adapter

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.CustomBean
import com.tbit.uqbike.utils.ImageLoad
import com.zhpan.bannerview.BaseBannerAdapter
import com.zhpan.bannerview.BaseViewHolder
import com.tbit.uqbike.activity.MainActivity
import java.util.*

class BanelMemberAdapter : BaseBannerAdapter<CustomBean>() {

    @RequiresApi(Build.VERSION_CODES.O)
    override fun bindData(holder: BaseViewHolder<CustomBean>, data: CustomBean?, position: Int, pageSize: Int) {
        if (data == null) return

        holder.setImageResource(R.id.banner_image, data.imageBg)
        holder.setImageResource(R.id.banner_image_hg, data.imageRes)
        holder.setImageResource(R.id.item_img_member_ques, data.imageQues)
        holder.setImageResource(R.id.item_img_member_go, data.imageGo)

        val context = ContextUtil.getContext() // Cache context

        if (data.isLocked) {
            // 修改判断条件：青铜会员且里程小于0.01km
            if (data.memberLevel == 1 && data.distance < 0.01f) {
                holder.findViewById<TextView>(R.id.item_tv_member_name).visibility = View.VISIBLE
                holder.findViewById<TextView>(R.id.item_tv_member_level).visibility = View.VISIBLE
                
                // 直接使用0.01作为升级里程
                val nextLevelDistanceString = "0.01"
                val upgradeMilestoneText = context.getString(R.string.s_vip_upgrade_milestone_needed, nextLevelDistanceString)
                holder.findViewById<TextView>(R.id.item_tv_member_level).text = upgradeMilestoneText

                // Show "Ride to upgrade" button
                holder.findViewById<LinearLayout>(R.id.ll_go_ride_clickable_area).visibility = View.VISIBLE
                holder.findViewById<TextView>(R.id.item_tv_member_level_ride).text = context.getString(R.string.s_vip_goride_update)

                // Hide time, distance, ques icon for this specific case as well
                holder.findViewById<TextView>(R.id.item_tv_member_time).visibility = View.INVISIBLE
                holder.findViewById<TextView>(R.id.item_tv_member_disance).visibility = View.INVISIBLE
                holder.findViewById<ImageView>(R.id.item_img_member_ques).visibility = View.INVISIBLE

            } else {
                // 其他未达等级，取min_mileage字段
                holder.findViewById<TextView>(R.id.item_tv_member_name).visibility = View.VISIBLE
                holder.findViewById<TextView>(R.id.item_tv_member_level).visibility = View.VISIBLE
                
                val minMileage = data.min_mileage
                val minMileageString = String.format(Locale.getDefault(), "%.2f", minMileage)
                val upgradeTip = context.getString(R.string.s_vip_nextleveUpdate, minMileageString)
                holder.findViewById<TextView>(R.id.item_tv_member_level).text = upgradeTip

                holder.findViewById<TextView>(R.id.item_tv_member_time).visibility = View.INVISIBLE
                holder.findViewById<TextView>(R.id.item_tv_member_disance).visibility = View.INVISIBLE
                holder.findViewById<ImageView>(R.id.item_img_member_ques).visibility = View.INVISIBLE
                holder.findViewById<LinearLayout>(R.id.ll_go_ride_clickable_area).visibility = View.INVISIBLE
            }
        } else {
            // Original logic for "isLocked == false" (unlocked state)
            holder.findViewById<TextView>(R.id.item_tv_member_name).visibility = View.VISIBLE
            holder.findViewById<TextView>(R.id.item_tv_member_time).visibility = View.VISIBLE
            holder.findViewById<TextView>(R.id.item_tv_member_disance).visibility = View.VISIBLE
            holder.findViewById<ImageView>(R.id.item_img_member_ques).visibility = View.VISIBLE
            holder.findViewById<TextView>(R.id.item_tv_member_level).visibility = View.VISIBLE
            holder.findViewById<LinearLayout>(R.id.ll_go_ride_clickable_area).visibility = View.VISIBLE

            try {
                val nextLevelDistanceString = String.format(Locale.getDefault(), "%.2f", data.nextLevel)
                val nextLevelText = context.getString(R.string.s_vip_nextLevelforMile, nextLevelDistanceString)
                holder.findViewById<TextView>(R.id.item_tv_member_level).text = nextLevelText
            } catch (e: Exception) {
                Log.e("BanelMemberAdapter", "Error formatting next level string for unlocked", e)
                var fallbackText = ""
                try { fallbackText = context.getString(R.string.s_vip_nextLevelforMile) } catch (e2: Exception) { }
                // Ensure data.nextLevel is also formatted if used in fallback
                val nextLevelDistanceStringFallback = String.format(Locale.getDefault(), "%.2f", data.nextLevel)
                holder.findViewById<TextView>(R.id.item_tv_member_level).text = fallbackText + " " + nextLevelDistanceStringFallback
            }
        }

        holder.findViewById<TextView>(R.id.item_tv_member_name).text = data.name
        val timeText = String.format("%s%s-%s", 
            ContextUtil.getContext().getString(R.string.s_ridecard_time_new),
            TimeFormatUtil.transToStringBysqData(data.starTime),
            TimeFormatUtil.transToStringBysqData(data.endTime)
        )
        holder.findViewById<TextView>(R.id.item_tv_member_time).text = timeText

        val distanceText = String.format("%s%.2fkm",
            ContextUtil.getContext().getString(R.string.s_lc),
            data.distance
        )
        holder.findViewById<TextView>(R.id.item_tv_member_disance).text = distanceText
        
        val quesIcon: ImageView = holder.findViewById(R.id.item_img_member_ques)
        quesIcon.setOnClickListener { anchorView ->
            val inflater = LayoutInflater.from(anchorView.context)
            val popupView = inflater.inflate(R.layout.popup_mileage_hint, null)

            val popupWindow = PopupWindow(
                popupView,
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                true // Focusable
            )
            popupWindow.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            popupWindow.isOutsideTouchable = true
            
            // Measure the popupView to get its dimensions
            popupView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
            val popupWidth = popupView.measuredWidth
            val popupHeight = popupView.measuredHeight

            // Get the distanceTextView to align the left edge of the popup
            val distanceTextView: TextView = holder.findViewById(R.id.item_tv_member_disance) // holder is available here

            // Calculate xOffset to align popup's left with distanceTextView's left
            // This assumes anchorView (quesIcon) and distanceTextView share a common relevant parent for 'left' coordinate, 
            // or we need to use getLocationOnScreen for more complex hierarchies.
            // For simplicity with showAsDropDown, we calculate offset relative to anchorView's position.
            
            val anchorScreenPos = IntArray(2)
            anchorView.getLocationOnScreen(anchorScreenPos)
            
            val distanceTextScreenPos = IntArray(2)
            distanceTextView.getLocationOnScreen(distanceTextScreenPos)

            val targetPopupScreenX = distanceTextScreenPos[0] // Align popup left with distanceText left
            val xOffset = targetPopupScreenX - anchorScreenPos[0]

            // Calculate yOffset to position popup above the anchorView
            val yOffset = -(anchorView.height + popupHeight + 16) // 16px margin above

            popupWindow.showAsDropDown(anchorView, xOffset, yOffset)

            // Auto-dismiss after 3 seconds
            Handler(Looper.getMainLooper()).postDelayed({
                if (popupWindow.isShowing) {
                    popupWindow.dismiss()
                }
            }, 3000) // 3000 milliseconds = 3 seconds
        }

        var textColorData = 0
        when(position % 5) {
            0 -> {
                textColorData = R.color.black_namal
            }
            1 -> {
                textColorData = R.color.c_003967
            }
            2 -> {
                textColorData = R.color.c_673E00
            }
            3 -> {
                textColorData = R.color.c_000E67
            }
            4 -> {
                textColorData = R.color.c_662400
            }
            else -> {
                 textColorData = R.color.black_namal
            }
        }

        holder.findViewById<TextView>(R.id.item_tv_member_name).setTextColor(ContextUtil.getContext().getColor(textColorData))
        holder.findViewById<TextView>(R.id.item_tv_member_time).setTextColor(ContextUtil.getContext().getColor(textColorData))
        holder.findViewById<TextView>(R.id.item_tv_member_disance).setTextColor(ContextUtil.getContext().getColor(textColorData))
        holder.findViewById<TextView>(R.id.item_tv_member_level).setTextColor(ContextUtil.getContext().getColor(textColorData))
        
        val rideToUpgradeTextView: TextView = holder.findViewById(R.id.item_tv_member_level_ride)
        rideToUpgradeTextView.setTextColor(ContextUtil.getContext().getColor(textColorData))
        
        // Find the new clickable LinearLayout
        val clickableArea: LinearLayout = holder.findViewById(R.id.ll_go_ride_clickable_area)
        
        // Set click listener on the new clickable area
        clickableArea.setOnClickListener { view ->
            val context = view.context
            // User is assumed to be logged in when on Member Center page
            val intent = android.content.Intent(context, MainActivity::class.java)
            // Add any flags if MainActivity requires them for this specific launch, e.g.:
            // intent.addFlags(android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP or android.content.Intent.FLAG_ACTIVITY_SINGLE_TOP)
            context.startActivity(intent)
        }
    }

    override fun getLayoutId(viewType: Int): Int {
        return R.layout.item_custom_view;
    }
}
