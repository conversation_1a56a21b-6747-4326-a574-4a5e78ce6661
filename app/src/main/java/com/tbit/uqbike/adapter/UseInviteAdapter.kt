package com.tbit.uqbike.adapter

import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.Coupon
import com.tbit.uqbike.entity.couponType
import com.tbit.uqbike.entity.type_coupon_money
import com.tbit.uqbike.entity.type_coupon_ridecard
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import org.json.JSONObject

class UseInviteAdapter : BaseAdapter<Coupon>() {

    companion object {
        private const val TYPE_NORMAL = 0
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        val viewHolder = ViewHolder(parent)
        return viewHolder
    }

    override fun getItemViewType(position: Int): Int {
        var type = TYPE_NORMAL
        return type
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<Coupon>.AbsViewHolder(parent, R.layout.item_invite_sel) {

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return

            val propertiesSea = JSONObject()
            propertiesSea.put("tag", "ride_card_select_coupon") // 设置商品
            propertiesSea.put("coupon_id", data.user_coupon_id) // 设置商品 ID
            MDUtil.SaeEvent(itemView,propertiesSea)

            itemView.findViewById<TextView>(R.id.tv_item_invite_title)?.text = data.name
            itemView.findViewById<RelativeLayout>(R.id.rl_other)?.visibility = View.GONE
            itemView.findViewById<RelativeLayout>(R.id.rl_invite)?.visibility = View.VISIBLE
            itemView.findViewById<TextView>(R.id.tv_item_invite_time)?.visibility = View.VISIBLE

            itemView.findViewById<TextView>(R.id.tv_item_invite_amount)?.text = AppUtil.getStr_small(AppUtil.getFloat2(data.amount)+Glob.CurrencyUnit,
                Glob.CurrencyUnit.length)
            itemView.findViewById<TextView>(R.id.tv_item_invite_unit)?.visibility =View.GONE
//            itemView.findViewById<TextView>(R.id.tv_item_invite_unit)?.text = Glob.CurrencyUnit

            if (data.type == couponType){
                itemView.findViewById<LinearLayout>(R.id.ly_coupon_type1)?.visibility = View.GONE
                itemView.findViewById<LinearLayout>(R.id.ly_coupon_type2)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_item_invite_typename)?.text = data.type_name
            }else{
                itemView.findViewById<LinearLayout>(R.id.ly_coupon_type1)?.visibility = View.VISIBLE
                itemView.findViewById<LinearLayout>(R.id.ly_coupon_type2)?.visibility = View.GONE
            }

            if (data.is_min_spend == 1){
                itemView.findViewById<TextView>(R.id.tv_item_invite_amounthint)?.text =
                    ResUtil.getString(R.string.s_invite_ismin, AppUtil.getFloat2(data.min_spend_amount))
            }else{
                itemView.findViewById<TextView>(R.id.tv_item_invite_amounthint)?.text =
                    ResUtil.getString(R.string.s_invite_umin)
            }
            if (data.isSel){
                ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_sel_y),itemView.findViewById<ImageView>(R.id.img_item_invite_sel))
            }else{
                ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_sel_n),itemView.findViewById<ImageView>(R.id.img_item_invite_sel))
            }

            if (data.is_superposed == 0){
                itemView.findViewById<TextView>(R.id.tv_item_invite_super)?.visibility = View.VISIBLE
            }else{
                itemView.findViewById<TextView>(R.id.tv_item_invite_super)?.visibility = View.GONE
            }

            if(TimeFormatUtil.isSameDay(TimeFormatUtil.transToStringBysqDataInfo_Long(data.end_time),System.currentTimeMillis())){
                var timeData = TimeFormatUtil.transToStringBysqDataInfo_min(data.end_time).split(" ")
                itemView.findViewById<TextView>(R.id.tv_item_invite_time)?.setTextColor(
                    ContextUtil.getContext().resources.getColor(R.color.c_F86125))
                itemView.findViewById<TextView>(R.id.tv_item_invite_time)?.text =
                    ResUtil.getString(R.string.s_today)+ ResUtil.getString(R.string.s_datedown,timeData[1])
            }else{
                itemView.findViewById<TextView>(R.id.tv_item_invite_time)?.setTextColor(
                    ContextUtil.getContext().resources.getColor(R.color.c_838588))
                itemView.findViewById<TextView>(R.id.tv_item_invite_time)?.text =
                    ResUtil.getString(R.string.s_datedown, TimeFormatUtil.transToStringBysqDataInfo_min(data.end_time))
            }

            // 会员专属券标识
            val tvMemberTag = itemView.findViewById<TextView>(R.id.tv_member_tag)
            if (data.is_membership == true) {
                tvMemberTag?.visibility = View.VISIBLE
                tvMemberTag?.text = ResUtil.getString(R.string.s_member_tag)
            } else {
                tvMemberTag?.visibility = View.GONE
            }
        }
    }
}