package com.tbit.uqbike.adapter

import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.GiftCard
import com.tbit.uqbike.utils.CacheFactory
import com.tbit.uqbike.utils.MoneyUtil

class GiftCardAdapter: BaseAdapter<GiftCard>() {

    companion object {
        private const val TYPE_NORMAL = 0
        private const val TYPE_LAST = 1
    }

    private val expirationDateFactory = CacheFactory(Int.MAX_VALUE, ::createExpiration)

    init {
        registerAdapterDataObserver(object: RecyclerView.AdapterDataObserver() {
            override fun onChanged() {
                expirationDateFactory.clean()
            }
        })
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        val viewHolder = ViewHolder(parent)
        if(viewType == TYPE_LAST) {
            val layoutParams = viewHolder.itemView.layoutParams as ViewGroup.MarginLayoutParams
            layoutParams.bottomMargin = layoutParams.topMargin
        }
        return viewHolder
    }

    override fun getItemViewType(position: Int): Int {
        return if(position == source.size - 1) TYPE_LAST else TYPE_NORMAL
    }

    private fun createExpiration(giftCard: GiftCard): String {
        return ContextUtil.getContext().getString(R.string.use_time_with_title, giftCard.formatUseTime)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<GiftCard>.AbsViewHolder(parent, R.layout.item_gift_card) {

        init {
            itemView.findViewById<TextView>(R.id.text_value_unit)?.text = Glob.symbol
        }

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            itemView.findViewById<TextView>(R.id.text_expiration_date)?.text = expirationDateFactory.create(data)
            itemView.findViewById<TextView>(R.id.text_value)?.text = MoneyUtil.standard2Show(data.money.toFloat()).toInt().toString()
        }
    }
}