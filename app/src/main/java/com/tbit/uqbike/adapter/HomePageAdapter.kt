package com.tbit.uqbike.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.tbit.uqbike.base.BaseFragment

class HomePageAdapter (activity: FragmentActivity, private var type: Array<BaseFragment>) : FragmentStateAdapter(activity) {

    override fun getItemCount(): Int {
        return type.size
    }

    override fun createFragment(position: Int): Fragment {
        return type.get(position)
    }
}