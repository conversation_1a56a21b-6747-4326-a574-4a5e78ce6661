package com.tbit.uqbike.adapter

import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.TextView
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.langEntity

class LangAdapter : BaseAdapter<langEntity>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }
    inner class ViewHolder(parent: ViewGroup) :
        BaseAdapter<langEntity>.AbsViewHolder(parent, R.layout.item_lang) {

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            val checkBox= itemView.findViewById<CheckBox>(R.id.item_cbt_lang)
            itemView.findViewById<TextView>(R.id.item_tv_lang)?.text = data.name
            itemView.findViewById<TextView>(R.id.item_tv_lang_hint)?.text = data.hint
            if(data.isUse){
                checkBox.isChecked = true
            }else{
                checkBox.isChecked = false
            }
//            itemView.item_cbt_lang.setOnClickListener { onMyItemClickListener(data,position) }
            itemView.findViewById<CheckBox>(R.id.item_cbt_lang)?.setOnClickListener { onItemClick(position) }
        }
    }

}