package com.tbit.uqbike.adapter

import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import com.tbit.uqbike.R
import org.jetbrains.anko.imageResource

class SelectAdapter<Value>: BaseAdapter<SelectAdapter.Data<Value>>() {

    private var checkedPosition = 0

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    override fun onItemClick(position: Int) {
        setCheckedPosition(position)
        super.onItemClick(position)
    }

    fun getSelect(): Data<Value>? {
        return source.getOrNull(checkedPosition)
    }

    private fun setCheckedPosition(position: Int) {
        if (position == checkedPosition) return

        val lastCheckPosition = checkedPosition
        checkedPosition = position
        if (isValidPosition(lastCheckPosition)) notifyItemChanged(lastCheckPosition, Unit)
        if (isValidPosition(position)) notifyItemChanged(position, Unit)
    }

    private inline fun isValidPosition(position: Int): Boolean {
        return position in 0 until itemCount
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<Data<Value>>.AbsViewHolder(parent, R.layout.item_select) {

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            val imageId = data.imageId
            if (imageId != null) itemView.findViewById<ImageView>(R.id.image_icon)?.imageResource = imageId
            itemView.findViewById<ImageView>(R.id.image_icon)?.visibility = if (imageId != null) View.VISIBLE else View.GONE
            itemView.findViewById<TextView>(R.id.text_content)?.text = data.message
            itemView.findViewById<CheckBox>(R.id.checkbox)?.isChecked = position == checkedPosition
        }
    }

    class Data<Value>(val imageId: Int?, val message: String, val value: Value)
}