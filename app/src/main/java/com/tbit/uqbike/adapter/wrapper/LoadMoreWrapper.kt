package com.tbit.tbituser.adapter.wrapper

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.tbit.uqbike.adapter.wrapper.WrapperUtils

/**
 * Created by zhy on 16/6/23.
 */
class LoadMoreWrapper<T : RecyclerView.ViewHolder>(private val mInnerAdapter: RecyclerView.Adapter<T>) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private var mLoadMoreView: View? = null
    private var mLoadMoreLayoutId: Int = 0

    private var mOnLoadMoreListener: (() -> Unit)? = null

    override fun registerAdapterDataObserver(observer: RecyclerView.AdapterDataObserver) {
        super.registerAdapterDataObserver(observer)
        mInnerAdapter.registerAdapterDataObserver(observer)
    }

    override fun unregisterAdapterDataObserver(observer: RecyclerView.AdapterDataObserver) {
        super.unregisterAdapterDataObserver(observer)
        mInnerAdapter.unregisterAdapterDataObserver(observer)
    }

    private fun hasLoadMore(): Boolean {
        return mLoadMoreView != null || mLoadMoreLayoutId != 0
    }

    private fun isShowLoadMore(position: Int): Boolean {
        return hasLoadMore() && position >= mInnerAdapter.itemCount
    }

    override fun getItemViewType(position: Int): Int {
        return if (isShowLoadMore(position)) {
            ITEM_TYPE_LOAD_MORE
        } else mInnerAdapter.getItemViewType(position)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        if (viewType == ITEM_TYPE_LOAD_MORE) {
            val loadMoreView = mLoadMoreView
            val holder: LoadMoreViewHolder
            if (loadMoreView != null) {
                holder = LoadMoreViewHolder(loadMoreView)
            } else {
                val view = LayoutInflater.from(parent.context).inflate(mLoadMoreLayoutId, parent, false)
                holder = LoadMoreViewHolder(view)
            }
            return holder
        }
        return mInnerAdapter.onCreateViewHolder(parent, viewType)
    }

    class LoadMoreViewHolder(view: View) : RecyclerView.ViewHolder(view)

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (isShowLoadMore(position)) {
            mOnLoadMoreListener?.invoke()
            return
        }
        mInnerAdapter.onBindViewHolder(holder as T, position)
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        WrapperUtils.onAttachedToRecyclerView(
            mInnerAdapter,
            recyclerView,
            object : WrapperUtils.SpanSizeCallback {
                override fun getSpanSize(
                    layoutManager: GridLayoutManager,
                    oldLookup: GridLayoutManager.SpanSizeLookup,
                    position: Int
                ): Int {
                    return if (isShowLoadMore(position)) {
                        layoutManager.spanCount
                    } else oldLookup?.getSpanSize(position) ?: 1
                }
            })
    }


    override fun onViewAttachedToWindow(holder: RecyclerView.ViewHolder) {
        mInnerAdapter.onViewAttachedToWindow(holder as T)

        if (isShowLoadMore(holder.layoutPosition)) {
            setFullSpan(holder)
        }
    }

    private fun setFullSpan(holder: RecyclerView.ViewHolder) {
        val lp = holder.itemView.layoutParams

        if (lp != null && lp is StaggeredGridLayoutManager.LayoutParams) {

            lp.isFullSpan = true
        }
    }

    override fun getItemCount(): Int {
        return mInnerAdapter.itemCount + if (hasLoadMore()) 1 else 0
    }

    fun setOnLoadMoreListener(loadMoreListener: () -> Unit): LoadMoreWrapper<*> {
        if (loadMoreListener != null) {
            mOnLoadMoreListener = loadMoreListener
        }
        return this
    }

    fun setLoadMoreView(loadMoreView: View?): LoadMoreWrapper<*> {
        mLoadMoreView = loadMoreView
        return this
    }

    fun setLoadMoreView(layoutId: Int): LoadMoreWrapper<*> {
        mLoadMoreLayoutId = layoutId
        return this
    }

    companion object {
        val ITEM_TYPE_LOAD_MORE = Integer.MAX_VALUE - 2
    }
}
