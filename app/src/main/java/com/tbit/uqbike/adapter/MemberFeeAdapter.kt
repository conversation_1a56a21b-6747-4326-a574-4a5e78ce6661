package com.tbit.uqbike.adapter

import android.content.Context
import android.view.ViewGroup
import android.widget.TextView
import com.tbit.maintenance.config.Constant
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.MemberFee
import com.tbit.uqbike.utils.MoneyUtil

class MemberFeeAdapter: BaseAdapter<MemberFee>() {

    var selectedMemberFee = source.getOrNull(0)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<MemberFee>.AbsViewHolder(parent, R.layout.item_member_fee) {

        init {
            itemView.findViewById<TextView>(R.id.text_price_unit)?.text = Glob.symbol
        }

        override fun onBindViewHolder(position: Int) {
            val data = source.getOr<PERSON>ull(position) ?: return
            itemView.findViewById<TextView>(R.id.text_name)?.text = data.name
            itemView.findViewById<TextView>(R.id.text_content)?.text = parseType(itemView.context, data.type)
            itemView.findViewById<TextView>(R.id.text_price)?.text = String.format("%.2f", MoneyUtil.standard2Show(data.money))
            itemView.isSelected = data.memberFeeId == selectedMemberFee?.memberFeeId
        }

        private fun parseType(context: Context, type: Int): String? {
            return when (type) {
//                Constant.MemberFeeType.DAY -> context.getString(R.string.by_day)
//                Constant.MemberFeeType.WEEK -> context.getString(R.string.by_week)
//                Constant.MemberFeeType.MONTH -> context.getString(R.string.by_month)
//                Constant.MemberFeeType.SEASON -> context.getString(R.string.by_season)
//                Constant.MemberFeeType.HALF_YEAR -> context.getString(R.string.by_half_year)
//                Constant.MemberFeeType.YEAR -> context.getString(R.string.by_year)
                else -> null
            }
        }
    }
}