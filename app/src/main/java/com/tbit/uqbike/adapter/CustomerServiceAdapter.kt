package com.tbit.uqbike.adapter

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.tbit.uqbike.R
import org.jetbrains.anko.imageResource

class CustomerServiceAdapter: BaseAdapter<Triple<Int, Int?, String?>>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<Triple<Int, Int?, String?>>.AbsViewHolder(parent, R.layout.item_custom_service) {

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            val imageId = data.second
            val message = data.third
            if (imageId != null) itemView.findViewById<ImageView>(R.id.image_icon)?.imageResource = imageId
            itemView.findViewById<ImageView>(R.id.image_icon)?.visibility = if (imageId != null) View.VISIBLE else View.GONE
            itemView.findViewById<TextView>(R.id.text_content)?.text = message
        }
    }
}