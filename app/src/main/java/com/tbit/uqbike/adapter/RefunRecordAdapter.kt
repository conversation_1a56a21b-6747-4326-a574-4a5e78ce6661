package com.tbit.uqbike.adapter

import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.RefundRecordDataItem
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.MDUtil
import org.json.JSONObject

class RefunRecordAdapter : BaseAdapter<RefundRecordDataItem>() {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        val viewHolder = ViewHolder(parent)
        return viewHolder
    }
    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<RefundRecordDataItem>.AbsViewHolder(parent, R.layout.item_refunrecord) {

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return

            val propertiesSea = JSONObject()
            propertiesSea.put("tag", "refund_history") // 设置商品
            propertiesSea.put("refund_history_id", data.id) // 设置商品 ID
            MDUtil.SaeEvent(itemView,propertiesSea)

            itemView.findViewById<TextView>(R.id.tv_Refund_record_time)?.text = ResUtil.getString(R.string.s_refund_appletime)+TimeFormatUtil.transToStringBysqDataInfo(data.create_time)
            //0待审核1已审核2审过不通过|
            when(data.status){
                0->{
                    itemView.findViewById<RoundTextView>(R.id.tv_Refund_record_state)?.text = ResUtil.getString(R.string.s_check_u)
                    itemView.findViewById<RoundTextView>(R.id.tv_Refund_record_state)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.blue_namal))
                }
                1->{
                    itemView.findViewById<RoundTextView>(R.id.tv_Refund_record_state)?.text = ResUtil.getString(R.string.s_suc)
                    itemView.findViewById<RoundTextView>(R.id.tv_Refund_record_state)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.c_C0C1C3))
                }
                2->{
                    itemView.findViewById<RoundTextView>(R.id.tv_Refund_record_state)?.text = ResUtil.getString(R.string.s_fail)
                    itemView.findViewById<RoundTextView>(R.id.tv_Refund_record_state)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.c_C0C1C3))
                }
            }
            itemView.findViewById<TextView>(R.id.tv_Refund_record_money)?.text = AppUtil.getFloat2(data.amount)+data.currency
            if (data.verify_time == 0L){
                itemView.findViewById<TextView>(R.id.tv_Refund_record_verifytime)?.text = ResUtil.getString(R.string.s_checktime)+"--"
            }else{
                itemView.findViewById<TextView>(R.id.tv_Refund_record_verifytime)?.text = ResUtil.getString(R.string.s_checktime)+TimeFormatUtil.transToStringBysqDataInfo(data.verify_time)
            }
            if (data.bank_deposit.isNullOrEmpty()){
                itemView.findViewById<TextView>(R.id.tv_Refund_record_deposit)?.visibility = View.GONE
            }else{
                itemView.findViewById<TextView>(R.id.tv_Refund_record_deposit)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_Refund_record_deposit)?.text = ResUtil.getString(R.string.s_refund_deposit)+data.bank_deposit
            }
            if (data.bank_account_name.isNullOrEmpty()){
                itemView.findViewById<TextView>(R.id.tv_Refund_record_name)?.visibility = View.GONE
            }else{
                itemView.findViewById<TextView>(R.id.tv_Refund_record_name)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_Refund_record_name)?.text = ResUtil.getString(R.string.s_refund_name)+data.bank_account_name
            }
            if (data.bank_account.isNullOrEmpty()){
                itemView.findViewById<TextView>(R.id.tv_Refund_record_account)?.visibility = View.GONE
            }else{
                itemView.findViewById<TextView>(R.id.tv_Refund_record_account)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_Refund_record_account)?.text = ResUtil.getString(R.string.s_refund_account)+data.bank_account
            }

            if (data.verify_remark.isNullOrEmpty()){
                itemView.findViewById<TextView>(R.id.tv_Refund_record_remark)?.visibility = View.GONE
            }else{
                itemView.findViewById<TextView>(R.id.tv_Refund_record_remark)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_Refund_record_remark)?.text = ResUtil.getString(R.string.s_refund_remark)+"\n"+data.verify_remark
            }
        }
    }

    var onGoPersentInfoListener = {_: Int ->}
}