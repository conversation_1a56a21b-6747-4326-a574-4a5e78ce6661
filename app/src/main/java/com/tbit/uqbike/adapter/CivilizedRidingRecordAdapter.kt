package com.tbit.uqbike.adapter

import android.view.ViewGroup
import android.widget.TextView
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.CivilizedRidingRecord
import com.tbit.uqbike.mvp.model.CivilizedRidingModel

class CivilizedRidingRecordAdapter : BaseAdapter<CivilizedRidingRecord>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup) : AbsViewHolder(parent, R.layout.item_civilized_riding_record) {
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            itemView.findViewById<TextView>(R.id.tv_type)?.text = CivilizedRidingModel.getTypeStr(data.type)
            itemView.findViewById<TextView>(R.id.tv_time)?.text = data.formatAddTime
        }

    }

}