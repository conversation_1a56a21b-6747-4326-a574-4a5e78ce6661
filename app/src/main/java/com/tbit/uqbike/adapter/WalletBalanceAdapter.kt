package com.tbit.uqbike.adapter

import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.Item
import com.tbit.uqbike.roundview.RoundTextView

class WalletBalanceAdapter(var viewType : Int) : BaseAdapter<Item>() {
    companion object {
        const val TYPE_NORMAL = 0
        const val TYPE_HEAD = 1
        const val TYPE_VIEW_WALLET = 1 //钱包余额流水
        const val TYPE_VIEW_PRESENT = 2 //赠送余额流水
    }
    var balance = 0.0
    var AmountUnit = ""
    fun setWalletBalance(balance : Double,AmountUnit : String){
        this.balance = balance
        this.AmountUnit = AmountUnit
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
//        val viewHolder = ViewHolder(parent)
//        return viewHolder

        return if (viewType == TYPE_NORMAL) ViewHolder(parent) else ViewHolderHead(parent)
    }

    override fun getItemViewType(position: Int): Int {
        return if (position == 0) TYPE_HEAD else TYPE_NORMAL
    }
    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<Item>.AbsViewHolder(parent, R.layout.item_walletbalance) {

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            if (position == 1){
                itemView.findViewById<TextView>(R.id.tv_balance_hint)?.visibility = View.VISIBLE
            }else{
                itemView.findViewById<TextView>(R.id.tv_balance_hint)?.visibility = View.GONE
            }

//            itemView.findViewById<TextView>(R.id.tv_item_walletbalance_title)?.text = AppUtil.getBalanceType(data.type)
            itemView.findViewById<TextView>(R.id.tv_item_walletbalance_title)?.text = data.type_name
            itemView.findViewById<TextView>(R.id.tv_item_walletbalance_time)?.text = TimeFormatUtil.transToStringBysq(data.create_time.toLong())
            if (data.amount > 0){
                itemView.findViewById<TextView>(R.id.tv_item_walletbalance_num)?.text = "+"+AppUtil.getFloat2Double(data.amount).toString()
            }else{
                itemView.findViewById<TextView>(R.id.tv_item_walletbalance_num)?.text = AppUtil.getFloat2Double(data.amount).toString()
            }
        }
    }

    inner class ViewHolderHead(parent: ViewGroup)
        : BaseAdapter<Item>.AbsViewHolder(parent, R.layout.head_walletbalance) {

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
//            val data = source.getOrNull(position) ?: return
            itemView.findViewById<TextView>(R.id.heat_walletbalance_money)?.text = AppUtil.getStr_small(AppUtil.getFloat2Double(balance).toString()+AmountUnit,
                AmountUnit.length)
//            itemView.findViewById<TextView>(R.id.heat_walletbalance_unit)?.text = AmountUnit
            if(viewType == TYPE_VIEW_WALLET){
                itemView.findViewById<RoundTextView>(R.id.heat_walletbalance_pay)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.heat_walletbalance_title)?.text = ResUtil.getString(R.string.s_walletamount)
            }else{
                itemView.findViewById<RoundTextView>(R.id.heat_walletbalance_pay)?.visibility = View.GONE
                itemView.findViewById<TextView>(R.id.heat_walletbalance_title)?.text = ResUtil.getString(R.string.s_persentamount)
            }
            itemView.findViewById<RoundTextView>(R.id.heat_walletbalance_pay)?.setOnClickListener { onGoPayListener(position) }
        }
    }
    var onGoPayListener = {_: Int ->}
}