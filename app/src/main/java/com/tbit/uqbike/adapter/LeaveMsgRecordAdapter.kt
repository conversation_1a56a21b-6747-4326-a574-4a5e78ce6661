package com.tbit.uqbike.adapter

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.google.android.material.imageview.ShapeableImageView
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.preview.ImageInfo
import com.tbit.preview.ImagePreviewActivity
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.entity.Comment
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MyLogUtil
import java.io.Serializable

class LeaveMsgRecordAdapter(var act : BaseActivity) : BaseAdapter<Comment>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<Comment>.AbsViewHolder(parent, R.layout.item_leavemsg_record) {

        init {
//            itemView.findViewById<TextView>(R.id.text_fee_unit.text = Glob.symbol
        }

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            itemView.findViewById<TextView>(R.id.tv_leavemsg_time)?.text = TimeFormatUtil.transToStringBysq(data.create_time)
            if (data.user.is_admin_user){
                itemView.findViewById<LinearLayout>(R.id.ly_leavemsg_l)?.visibility = View.VISIBLE
                itemView.findViewById<LinearLayout>(R.id.ly_leavemsg_r)?.visibility = View.GONE
                itemView.findViewById<TextView>(R.id.tv_leavemsg_name_l)?.text = data.user.user_name

                if (data.content.comment.isNullOrEmpty()){
                    itemView.findViewById<TextView>(R.id.tv_leavemsg_cont_l)?.visibility = View.GONE
                }else{
                    itemView.findViewById<TextView>(R.id.tv_leavemsg_cont_l)?.visibility = View.VISIBLE
                    itemView.findViewById<TextView>(R.id.tv_leavemsg_cont_l)?.text = data.content.comment
                }

                if (data.user.avatar.isNullOrEmpty()){
                    ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_gogo),itemView.findViewById<ShapeableImageView>(R.id.item_faultinfo_img_l))
                }else{
                    ImageLoad.loadimg(data.user.avatar,itemView.findViewById<ShapeableImageView>(R.id.item_faultinfo_img_l))
                }

                setImageView(data.content.image_url,itemView.findViewById<ShapeableImageView>(R.id.img_leavemsg_1_l),
                    itemView.findViewById<ShapeableImageView>(R.id.img_leavemsg_2_l),itemView.findViewById<ShapeableImageView>(R.id.img_leavemsg_3_l))
            }else{
                itemView.findViewById<LinearLayout>(R.id.ly_leavemsg_l)?.visibility = View.GONE
                itemView.findViewById<LinearLayout>(R.id.ly_leavemsg_r)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_leavemsg_name_r)?.text = data.user.user_name

                if (data.content.comment.isNullOrEmpty()){
                    itemView.findViewById<TextView>(R.id.tv_leavemsg_cont_r)?.visibility = View.GONE
                }else{
                    itemView.findViewById<TextView>(R.id.tv_leavemsg_cont_r)?.visibility = View.VISIBLE
                    itemView.findViewById<TextView>(R.id.tv_leavemsg_cont_r)?.text = data.content.comment
                }

                if (data.user.avatar.isNullOrEmpty()){
                    ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_head),itemView.findViewById<ShapeableImageView>(R.id.item_faultinfo_img_r))
                }else{
                    ImageLoad.loadimg(data.user.avatar,itemView.findViewById<ShapeableImageView>(R.id.item_faultinfo_img_r))
                }

                setImageView(data.content.image_url,itemView.findViewById<ShapeableImageView>(R.id.img_leavemsg_1_r),
                    itemView.findViewById<ShapeableImageView>(R.id.img_leavemsg_2_r),itemView.findViewById<ShapeableImageView>(R.id.img_leavemsg_3_r))
            }
        }
    }
    fun setImageView(image_url: List<String>?,img1 : ImageView,img2 : ImageView,img3 : ImageView){
        if (image_url != null && image_url.size > 0){
            when(image_url.size){
                1->{
                    if (image_url.get(0) != null) ImageLoad.loadimg(image_url.get(0),img1)
                    img1.visibility = View.VISIBLE
                    img2.visibility = View.GONE
                    img3.visibility = View.GONE
                    img1.setOnClickListener { startImagePreviewActivity(image_url.get(0)) }
                }
                2->{
                    if (image_url.get(0) != null) ImageLoad.loadimg(image_url.get(0),img1)
                    if (image_url.get(1) != null) ImageLoad.loadimg(image_url.get(1),img2)
                    img1.visibility = View.VISIBLE
                    img2.visibility = View.VISIBLE
                    img3.visibility = View.GONE
                    img1.setOnClickListener { startImagePreviewActivity(image_url.get(0)) }
                    img2.setOnClickListener { startImagePreviewActivity(image_url.get(1)) }
                }
                3->{
                    if (image_url.get(0) != null) ImageLoad.loadimg(image_url.get(0),img1)
                    if (image_url.get(1) != null) ImageLoad.loadimg(image_url.get(1),img2)
                    if (image_url.get(2) != null) ImageLoad.loadimg(image_url.get(2),img3)
                    img1.visibility = View.VISIBLE
                    img2.visibility = View.VISIBLE
                    img3.visibility = View.VISIBLE
                    img1.setOnClickListener { startImagePreviewActivity(image_url.get(0)) }
                    img2.setOnClickListener { startImagePreviewActivity(image_url.get(1)) }
                    img3.setOnClickListener { startImagePreviewActivity(image_url.get(2)) }
                }
            }
        }else{
            img1.visibility = View.GONE
            img2.visibility = View.GONE
            img3.visibility = View.GONE
        }
    }
    private fun startImagePreviewActivity(image_url : String) {
        if (!image_url.isNullOrEmpty()){
            var imgsource = ArrayList<ImageInfo>()
            var imgData = ImageInfo()
            imgData.bigImageUrl = image_url
            imgsource.add(imgData)

            val intent = Intent(act, ImagePreviewActivity::class.java)
            val bundle = Bundle()
            bundle.putSerializable(ImagePreviewActivity.IMAGE_INFO, imgsource as Serializable)
            bundle.putInt(ImagePreviewActivity.CURRENT_ITEM, 0)
            bundle.putBoolean(ImagePreviewActivity.USER_CACHE, true)
            intent.putExtras(bundle)
            act.startActivity(intent)
            act.overridePendingTransition(0, 0)
        }
    }
}