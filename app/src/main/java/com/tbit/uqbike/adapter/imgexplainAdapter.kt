package com.tbit.uqbike.adapter

import android.content.Intent
import android.os.Bundle
import android.view.ViewGroup
import com.bumptech.glide.Glide
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.preview.ImageInfo
import com.tbit.preview.ImagePreviewActivity
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import java.io.Serializable

class imgexplainAdapter(protected val activity: BaseActivity) : BaseAdapter<ImageInfo>() {
    var onClickListener = {_: String ->}
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }
    inner class ViewHolder(parent: ViewGroup) :
        BaseAdapter<ImageInfo>.AbsViewHolder(parent, R.layout.item_costappealimg) {

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            Glide.with(ContextUtil.getContext()).load(data.bigImageUrl).into(itemView.findViewById(R.id.iv_image))
            itemView.setOnClickListener {
                startImagePreviewActivity(position)
            }
        }
    }
    private fun startImagePreviewActivity(position: Int) {
        val intent = Intent(activity, ImagePreviewActivity::class.java)
        val bundle = Bundle()
        bundle.putSerializable(ImagePreviewActivity.IMAGE_INFO, source as Serializable)
        bundle.putInt(ImagePreviewActivity.CURRENT_ITEM, position)
        bundle.putBoolean(ImagePreviewActivity.USER_CACHE, false)
        intent.putExtras(bundle)
        activity.startActivity(intent)
        activity.overridePendingTransition(0, 0)
    }
}