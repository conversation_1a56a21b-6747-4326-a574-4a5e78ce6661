package com.tbit.uqbike.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.tbit.uqbike.fragment.RidCardFrag
import com.tbit.uqbike.fragment.TransactionFrag

/**
 * <AUTHOR>
 * @date 2022/9/13
 * @desc
 */
class PropsPageAdapter (activity: FragmentActivity, private var type: Array<String>) : FragmentStateAdapter(activity) {
    private var mallType = ""
    override fun getItemCount(): Int {
        return type.size
    }

    override fun createFragment(position: Int): Fragment {
        when (position) {
            0 -> return TransactionFrag()
            1 -> return RidCardFrag()
        }
        return TransactionFrag()
    }
}