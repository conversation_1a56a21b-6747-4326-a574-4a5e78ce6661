package com.tbit.uqbike.adapter

import android.os.Build
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.tbit.maintenance.utils.AppUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.CouponUseRecdItem

class CouponRecdAdaper : BaseAdapter<CouponUseRecdItem>() {

    companion object {
        private const val TYPE_NORMAL = 0
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        val viewHolder = ViewHolder(parent)
        return viewHolder
    }

    override fun getItemViewType(position: Int): Int {
        var type = TYPE_NORMAL
        return type
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<CouponUseRecdItem>.AbsViewHolder(parent, R.layout.item_couponrecd) {

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            itemView.findViewById<TextView>(R.id.tv_item_coupon_title)?.text = data.name+"("+data.currency+")"
            itemView.findViewById<TextView>(R.id.tv_item_coupon_amount)?.text = AppUtil.getFloat2(data.coupon_deduction)
        }
    }
}