package com.tbit.uqbike.adapter

import android.app.Activity
import android.os.Build
import android.os.CountDownTimer
import android.util.SparseArray
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.google.gson.Gson
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.App
import com.tbit.uqbike.R
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.entity.ChargeOrderDataItem
import com.tbit.uqbike.entity.OrderCannelData
import com.tbit.uqbike.mvp.model.TransactionLogModel
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.MDUtil
import io.reactivex.rxkotlin.subscribeBy
import org.json.JSONObject


class TransactionLogFragAdapter(act : Activity) : BaseAdapterTimer<ChargeOrderDataItem>() {
    var onGoPayListener = {_: Int ->}
    //记录每次刷新时的时间
    private var tempTime: Long = 0
    fun setGetTime(tempTime: Long) {
        this.tempTime = tempTime
    }
    private val countDownMap: SparseArray<CountDownTimer> = SparseArray<CountDownTimer>()

    // 清空资源
    fun cancelAllTimers() {
        if (countDownMap == null) {
            return
        }
        var i = 0
        val length = countDownMap.size()
        while (i < length) {
            val cdt = countDownMap[countDownMap.keyAt(i)]
            cdt?.cancel()
            i++
        }
    }
    lateinit var act : Activity
    init {
        this.act = act
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapterTimer<ChargeOrderDataItem>.AbsViewHolder(parent, R.layout.item_transaction_frag) {

        init {
//            itemView.text_money_unit.text = Glob.symbol
        }

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(holder: AbsViewHolder,position: Int) {
            val data = source.getOrNull(position) ?: return

            val propertiesSea = JSONObject()
            propertiesSea.put("tag", "order_history") // 设置商品
            propertiesSea.put("order_id", data.id) // 设置商品 ID
            propertiesSea.put("order_type", "1") // 1=充值 2=骑行卡
            MDUtil.SaeEvent(itemView,propertiesSea)

            itemView.findViewById<TextView>(R.id.tv_item_charge_order)?.text = data.order_no
            itemView.findViewById<TextView>(R.id.tv_item_charge_creattime)?.text = ResUtil.getString(R.string.s_creatorder_time)+"："+TimeFormatUtil.transToStringBysq(data.create_time.toLong())
            itemView.findViewById<TextView>(R.id.tv_item_charge_chargemoney)?.text = ResUtil.getString(R.string.s_rech_wallet)+"："+AppUtil.getFloat2(data.recharge_amount).toString()+data.currency
            //订单状态：2待支付，3已支付，4已关闭
            when (data.status) {
                2 -> {
                    itemView.findViewById<TextView>(R.id.tv_item_charge_state)?.text = ResUtil.getString(R.string.s_order_npay)
                    itemView.findViewById<TextView>(R.id.tv_item_charge_state)?.setTextColor(App.context.resources.getColor(R.color.c_y))
                    itemView.findViewById<TextView>(R.id.tv_item_charge_paytime)?.visibility = View.GONE
                    itemView.findViewById<TextView>(R.id.tv_item_charge_paymoney)?.visibility = View.VISIBLE
                    itemView.findViewById<TextView>(R.id.tv_item_charge_paymoney)?.text = ResUtil.getString(R.string.s_tobepay)+"："+AppUtil.getFloat2(data.amount).toString()+data.currency
                    itemView.findViewById<View>(R.id.view_driver)?.visibility = View.VISIBLE


                    itemView.findViewById<LinearLayout>(R.id.ly_item_charge_pay)?.visibility = View.VISIBLE
//                    itemView.btn_item_charge_pay.text = ""+secondsToTimeFormat(data.countdown)+" 去支付"
                    //记录时间点
                    val timeStamp = System.currentTimeMillis() / 1000 - tempTime
                    val time: Long = (data.countdown - timeStamp) * 1000
                    //将前一个缓存清除
                    if (holder.countDownTimer != null) {
                        holder.countDownTimer!!.cancel();
                    }
                    if (time > 0) { //判断倒计时是否结束
                        holder.countDownTimer = object : CountDownTimer(time, 1000) {
                            override fun onTick(millisUntilFinished: Long) {
//                                MyLogUtil.Log("1111","======== 倒计时  ======")
                                itemView.findViewById<RoundTextView>(R.id.btn_item_charge_pay)?.text = ""+secondsToTimeFormat(millisUntilFinished.toInt()/1000)+" "+ResUtil.getString(R.string.s_gopay)
                            }
                            override fun onFinish() {
                                //倒计时结束
                                TransactionLogModel.cancelRechargeOrder(data.order_no)
                                    .subscribeBy(
                                        onNext = {
                                            var resultData = Gson().fromJson(it.toString(), OrderCannelData::class.java)
                                            data.cancel_time = resultData.cancel_time
                                            data.status = 4
                                            notifyItemChanged(position)
                                        }
                                    ).toCancelable()
                            }
                        }.start()
                        countDownMap.put(itemView.findViewById<RoundTextView>(R.id.btn_item_charge_pay)!!.hashCode(), holder.countDownTimer)
                    } else {
                        itemView.findViewById<RoundTextView>(R.id.btn_item_charge_pay)?.text = "00"+ResUtil.getString(R.string.s_gopay)
                    }
                    itemView.findViewById<RoundTextView>(R.id.btn_item_charge_pay)?.setOnClickListener {
                        //去支付
                        onGoPayListener(position)
                    }
                }
                3 -> {
                    itemView.findViewById<TextView>(R.id.tv_item_charge_state)?.text = ResUtil.getString(R.string.s_paycomp)
                    itemView.findViewById<TextView>(R.id.tv_item_charge_state)?.setTextColor(App.context.resources.getColor(R.color.c_C0C1C3))
                    itemView.findViewById<TextView>(R.id.tv_item_charge_paytime)?.visibility = View.VISIBLE
                    itemView.findViewById<TextView>(R.id.tv_item_charge_paytime)?.text = ResUtil.getString(R.string.s_paytime)+"："+TimeFormatUtil.transToStringBysq(data.paid_time.toLong())
                    itemView.findViewById<TextView>(R.id.tv_item_charge_paymoney)?.visibility = View.VISIBLE
                    itemView.findViewById<TextView>(R.id.tv_item_charge_paymoney)?.text = ResUtil.getString(R.string.s_paymoney)+"："+AppUtil.getFloat2(data.paid_amount).toString()+data.currency
                    itemView.findViewById<View>(R.id.view_driver)?.visibility = View.GONE
                    itemView.findViewById<LinearLayout>(R.id.ly_item_charge_pay)?.visibility = View.GONE
                }
                4 -> {
                    itemView.findViewById<TextView>(R.id.tv_item_charge_state)?.text = ResUtil.getString(R.string.s_canldown)
                    itemView.findViewById<TextView>(R.id.tv_item_charge_state)?.setTextColor(App.context.resources.getColor(R.color.c_C0C1C3))
                    itemView.findViewById<TextView>(R.id.tv_item_charge_paytime)?.visibility = View.VISIBLE
                    itemView.findViewById<TextView>(R.id.tv_item_charge_paytime)?.text = ResUtil.getString(R.string.s_canl_time)+"："+TimeFormatUtil.transToStringBysq(data.cancel_time.toLong())
                    itemView.findViewById<TextView>(R.id.tv_item_charge_paymoney)?.visibility = View.GONE
                    itemView.findViewById<View>(R.id.view_driver)?.visibility = View.GONE
                    itemView.findViewById<LinearLayout>(R.id.ly_item_charge_pay)?.visibility = View.GONE
                }
            }
            if(data.present_amount == null || data.present_amount == 0f){
                itemView.findViewById<TextView>(R.id.tv_item_charge_giftmoney)?.visibility = View.GONE
            }else{
                itemView.findViewById<TextView>(R.id.tv_item_charge_giftmoney)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_item_charge_giftmoney)?.text = ResUtil.getString(R.string.s_persentamount)+"："+AppUtil.getFloat2(data.present_amount).toString()+data.currency
            }
            itemView.findViewById<RoundTextView>(R.id.btn_item_charge_cannel)?.setOnClickListener {
                CommDialog.Builder(act).setTitle(ResUtil.getString(R.string.dialog_tip)).setContent(ResUtil.getString(R.string.s_pay_cannel))
                    .setLeftText(ResUtil.getString(R.string.cancel)).setRightText(ResUtil.getString(R.string.ok)).setCanceledOnOutside(true)
                    .setClickListen(object : CommDialog.TwoSelDialog {
                        override fun leftClick() {}
                        override fun rightClick() {
                            TransactionLogModel.cancelRechargeOrder(data.order_no)
                                .subscribeBy(
                                    onNext = {
                                        var resultData = Gson().fromJson(it.toString(), OrderCannelData::class.java)
                                        data.cancel_time = resultData.cancel_time
                                        data.status = 4
                                        notifyItemChanged(position)
                                    }
                                ).toCancelable()
                        }
                    }).build().show()
            }
        }
    }
    fun secondsToTimeFormat(seconds: Int): String {
//        val hours = (seconds / 3600).toString().padStart(2, '0') // 计算小时部分并补全前导零
//        val minutes = ((seconds % 3600) / 60).toString().padStart(2, '0') // 计算分钟部分并补全前导零
        val minutes = (seconds / 60).toString().padStart(2, '0') // 计算分钟部分并补全前导零
        val second = (seconds % 60).toString().padStart(2, '0') // 计算秒钟部分并补全前导零
        return "$minutes:$second" // 返回结果字符串
    }
}