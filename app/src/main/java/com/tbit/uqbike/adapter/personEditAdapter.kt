package com.tbit.uqbike.adapter

import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.personItemSelData

class personEditAdapter : BaseAdapter<personItemSelData>() {

    companion object {
        private const val TYPE_NORMAL = 0
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        val viewHolder = ViewHolder(parent)
        return viewHolder
    }

    override fun getItemViewType(position: Int): Int {
        var type = TYPE_NORMAL
        return type
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<personItemSelData>.AbsViewHolder(parent, R.layout.item_person_edit) {

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            itemView.findViewById<TextView>(R.id.text_name)?.text = data.value

            if (position == source.size - 1){
                itemView.findViewById<LinearLayout>(R.id.ly_line)?.visibility = View.GONE
            }else{
                itemView.findViewById<LinearLayout>(R.id.ly_line)?.visibility = View.VISIBLE
            }
        }
    }
}