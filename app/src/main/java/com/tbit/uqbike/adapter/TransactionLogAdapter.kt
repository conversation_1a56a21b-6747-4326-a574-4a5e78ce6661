package com.tbit.uqbike.adapter

import android.graphics.Paint
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.App
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.RideCardDataItem
import com.tbit.uqbike.roundview.RoundRelativeLayout
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import org.json.JSONObject

class TransactionLogAdapter: BaseAdapter<RideCardDataItem>() {
    var onBuyCardListener = {_: Int ->}
    var onBuyNoteListener = {}
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<RideCardDataItem>.AbsViewHolder(parent, R.layout.item_transaction) {

        init {
//            itemView.text_money_unit.text = Glob.symbol
        }

        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return

            val propertiesSea = JSONObject()
            propertiesSea.put("tag", "cycling_card") // 设置商品
            propertiesSea.put("card_id", data.id) // 设置商品 ID
            MDUtil.SaeEvent(itemView,propertiesSea)

            if(position == 0){
                if (data.tags.isNullOrEmpty()){
                    itemView.findViewById<RelativeLayout>(R.id.ry_card_top)?.visibility = View.GONE
                    itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.visibility = View.INVISIBLE
                    itemView.findViewById<RoundRelativeLayout>(R.id.item_ry_rech)?.delegate?.strokeColor = ContextUtil.getContext().resources.getColor(R.color.c_line)
                }else{
                    itemView.findViewById<RelativeLayout>(R.id.ry_card_top)?.visibility = View.VISIBLE
                    itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.visibility = View.INVISIBLE
                    if (data.tags.contains(",")){
                        var tagsData = data.tags.split(",")
                        itemView.findViewById<TextView>(R.id.tv_item_top)?.text = tagsData[0]
                    }else if (data.tags.contains("，")){
                        var tagsData = data.tags.split("，")
                        itemView.findViewById<TextView>(R.id.tv_item_top)?.text = tagsData[0]
                    }else{
                        itemView.findViewById<TextView>(R.id.tv_item_top)?.text = data.tags
                    }
                    itemView.findViewById<RoundRelativeLayout>(R.id.item_ry_rech)?.delegate?.strokeColor = ContextUtil.getContext().resources.getColor(R.color.c_F86125)
                }
                itemView.findViewById<LinearLayout>(R.id.ly_item_ridcardtop)?.visibility = View.VISIBLE
            }else{
                itemView.findViewById<RoundRelativeLayout>(R.id.item_ry_rech)?.delegate?.strokeColor = ContextUtil.getContext().resources.getColor(R.color.c_line)
                itemView.findViewById<LinearLayout>(R.id.ly_item_ridcardtop)?.visibility = View.GONE
                itemView.findViewById<RelativeLayout>(R.id.ry_card_top)?.visibility = View.GONE
                if (data.tags.isNullOrEmpty()){
                    itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.visibility = View.INVISIBLE
                }else{
                    itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.visibility = View.VISIBLE
                    if (data.tags.contains(",")){
                        var tagsData = data.tags.split(",")
                        itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.text = tagsData[0]
                    }else if (data.tags.contains("，")){
                        var tagsData = data.tags.split("，")
                        itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.text = tagsData[0]
                    }else{
                        itemView.findViewById<RoundTextView>(R.id.rv_card_state)?.text = data.tags
                    }
                }
            }
            itemView.findViewById<ImageView>(R.id.item_img_ridecard_buynotes)?.setOnClickListener { onBuyNoteListener() }
            itemView.findViewById<TextView>(R.id.item_rv_ridecard_buynotes)?.setOnClickListener { onBuyNoteListener() }

            itemView.findViewById<TextView>(R.id.tv_rental_title)?.text = data.title
            itemView.findViewById<TextView>(R.id.tv_rental_amount)?.text = AppUtil.getFloat2(data.final_price)
            itemView.findViewById<TextView>(R.id.tv_rental_amount_unit)?.text = data.currency

            if (data.ride_card_tags !=null && data.ride_card_tags.size > 0){
                if (data.ride_card_tags.get(0).is_show){
                    itemView.findViewById<TextView>(R.id.item_tv_coupon_tag)?.text = data.ride_card_tags.get(0).name
                    itemView.findViewById<TextView>(R.id.item_tv_coupon_tag)?.visibility = View.VISIBLE
                }else{
                    itemView.findViewById<TextView>(R.id.item_tv_coupon_tag)?.visibility = View.GONE
                }
            }else{
                itemView.findViewById<TextView>(R.id.item_tv_coupon_tag)?.visibility = View.GONE
            }

            if (data.discount_value == 0){
                itemView.findViewById<RoundTextView>(R.id.rv_item_tran_1)?.visibility = View.GONE
            }else{
                itemView.findViewById<RoundTextView>(R.id.rv_item_tran_1)?.visibility = View.VISIBLE
                itemView.findViewById<RoundTextView>(R.id.rv_item_tran_1)?.text = ResUtil.getString(R.string.s_card_off,data.discount_value.toString()+"%")
            }

            if (data.original_price == data.price){
                itemView.findViewById<TextView>(R.id.tv_rental_amount_z)?.visibility = View.INVISIBLE
            }else{
                itemView.findViewById<TextView>(R.id.tv_rental_amount_z)?.visibility = View.VISIBLE
                itemView.findViewById<TextView>(R.id.tv_rental_amount_z)?.text = AppUtil.getFloat2(data.original_price)+data.currency
                itemView.findViewById<TextView>(R.id.tv_rental_amount_z)?.paint?.flags = Paint.STRIKE_THRU_TEXT_FLAG
//            itemView.findViewById<TextView>(R.id.tv_rental_amount_z)?.paint?.flags = Paint.DITHER_FLAG
            }

            //1次卡 2时间卡
            if (data.type == 1 || data.type == 0){
//                itemView.findViewById<TextView>(R.id.tv_item_ridcard_alone)?.text = ResUtil.getString(R.string.s_free_ridealone_new)+" "+data.deduction_time.toString()+" "+
//                        ResUtil.getString(R.string.min)+" | "+ResUtil.getString(R.string.s_onlearea_use,data.areaName)
                itemView.findViewById<TextView>(R.id.tv_item_ridcard_alone)?.text = ResUtil.getString(R.string.s_onlearea_use,data.areaName)
                itemView.findViewById<RoundTextView>(R.id.rv_item_tran_2)?.text = AppUtil.getFloat2(data.per_unit_cost_value)+data.currency+"/"+ResUtil.getString(R.string.s_unit_day)
            }else{
                // 1分钟 2小时 3天
                when (data.free_unit){
                    1->{
//                        itemView.findViewById<TextView>(R.id.tv_item_ridcard_alone)?.text = ResUtil.getString(R.string.s_ride_freetime_new)+" "+
//                                data.free_count.toString()+" "+ResUtil.getString(R.string.min)+" | "+ResUtil.getString(R.string.s_onlearea_use,data.areaName)
                        itemView.findViewById<TextView>(R.id.tv_item_ridcard_alone)?.text = ResUtil.getString(R.string.s_onlearea_use,data.areaName)
                        itemView.findViewById<RoundTextView>(R.id.rv_item_tran_2)?.text = AppUtil.getFloat2(data.per_unit_cost_value)+data.currency+"/"+ResUtil.getString(R.string.min)
                    }
                    2->{
//                        itemView.findViewById<TextView>(R.id.tv_item_ridcard_alone)?.text = ResUtil.getString(R.string.s_ride_freetime_new)+" "+
//                                data.free_count.toString()+" "+ResUtil.getString(R.string.s_unit_hour)+" | "+ResUtil.getString(R.string.s_onlearea_use,data.areaName)
                        itemView.findViewById<TextView>(R.id.tv_item_ridcard_alone)?.text = ResUtil.getString(R.string.s_onlearea_use,data.areaName)
                        itemView.findViewById<RoundTextView>(R.id.rv_item_tran_2)?.text = AppUtil.getFloat2(data.per_unit_cost_value)+data.currency+"/"+ResUtil.getString(R.string.s_unit_hour)
                    }
                    3->{
//                        itemView.findViewById<TextView>(R.id.tv_item_ridcard_alone)?.text = ResUtil.getString(R.string.s_ride_freetime_new)+" "+
//                                data.free_count.toString()+" "+ResUtil.getString(R.string.s_unit_day)+" | "+ResUtil.getString(R.string.s_onlearea_use,data.areaName)
                        itemView.findViewById<TextView>(R.id.tv_item_ridcard_alone)?.text = ResUtil.getString(R.string.s_onlearea_use,data.areaName)
                        itemView.findViewById<RoundTextView>(R.id.rv_item_tran_2)?.text = AppUtil.getFloat2(data.per_unit_cost_value)+data.currency+"/"+ResUtil.getString(R.string.s_unit_day)
                    }
                }
            }

            itemView.setOnClickListener { onBuyCardListener(position) }
        }
    }
}