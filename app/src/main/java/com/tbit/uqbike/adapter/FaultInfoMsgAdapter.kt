package com.tbit.uqbike.adapter

import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.google.android.material.imageview.ShapeableImageView
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.Comment
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.ImageLoad

class FaultInfoMsgAdapter : BaseAdapter<Comment>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AbsViewHolder {
        return ViewHolder(parent)
    }

    inner class ViewHolder(parent: ViewGroup)
        : BaseAdapter<Comment>.AbsViewHolder(parent, R.layout.item_faultinfo_msg) {

        init {
//            itemView.findViewById<TextView>(R.id.text_fee_unit.text = Glob.symbol
        }

        @RequiresApi(Build.VERSION_CODES.O)
        override fun onBindViewHolder(position: Int) {
            val data = source.getOrNull(position) ?: return
            itemView.findViewById<TextView>(R.id.tv_info_name)?.text = data.user.user_name
            itemView.findViewById<TextView>(R.id.tv_info_time)?.text = TimeFormatUtil.transToStringBysq(data.create_time)
            itemView.findViewById<RoundTextView>(R.id.tv_info_cont)?.text = data.content.comment
            if (data.user.is_admin_user){
                if (data.user.avatar.isNullOrEmpty()){
                    ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_gogo),itemView.findViewById<ShapeableImageView>(R.id.item_faultinfo_img))
                }else{
                    ImageLoad.loadimg(data.user.avatar,itemView.findViewById<ShapeableImageView>(R.id.item_faultinfo_img))
                }
            }else{
                if (data.user.avatar.isNullOrEmpty()){
                    ImageLoad.loadimg(ContextUtil.getContext().resources.getDrawable(R.drawable.ic_head),itemView.findViewById<ShapeableImageView>(R.id.item_faultinfo_img))
                }else{
                    ImageLoad.loadimg(data.user.avatar,itemView.findViewById<ShapeableImageView>(R.id.item_faultinfo_img))
                }
            }
            if (position == source.size-1){
                itemView.findViewById<LinearLayout>(R.id.item_ly_sx)?.visibility = View.INVISIBLE
            }else{
                itemView.findViewById<LinearLayout>(R.id.item_ly_sx)?.visibility = View.VISIBLE
            }
        }
    }
}