package com.tbit.uqbike.apppay

import com.tbit.uqbike.pay.bean.PayResult
import io.reactivex.Observable

interface IAppPay {
    fun payCharge(deposit: Boolean, money: Int?, adAccountId: String?): Observable<PayResult>
    fun payReturnCharge(money: Int?, adAccountId: String?): Observable<PayResult>
    fun payMemberFee(memberFeeId: Int, adAccountId: String?): Observable<PayResult>
    fun payVipCard(vipCardId: Int, adAccountId: String?): Observable<PayResult>
    fun payRideCard(rideCardId: Int, adAccountId: String?): Observable<PayResult>
}