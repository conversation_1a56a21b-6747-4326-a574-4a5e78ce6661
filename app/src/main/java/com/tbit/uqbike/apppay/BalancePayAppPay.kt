package com.tbit.uqbike.apppay

import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.addToComposite
import com.tbit.uqbike.R
import com.tbit.uqbike.mvp.model.RideCardModel
import com.tbit.uqbike.pay.bean.PayResult
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.subjects.PublishSubject

class BalancePayAppPay(activity: AppCompatActivity) : IAppPay, LifecycleObserver {

    private val compositeDisposable = CompositeDisposable()
    private val publishSubject = PublishSubject.create<PayResult>()

    init {
        activity.lifecycle.addObserver(this)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        compositeDisposable.dispose()
    }

    override fun payCharge(deposit: Boolean, money: Int?, adAccountId: String?): Observable<PayResult> {
        return Observable.just(PayResult(false, ResUtil.getString(R.string.str_unsupport_pay)))
    }

    override fun payReturnCharge(money: Int?, adAccountId: String?): Observable<PayResult> {
        return Observable.just(PayResult(false, ResUtil.getString(R.string.str_unsupport_pay)))
    }

    override fun payMemberFee(memberFeeId: Int, adAccountId: String?): Observable<PayResult> {
        return Observable.just(PayResult(false, ResUtil.getString(R.string.str_unsupport_pay)))
    }

    override fun payVipCard(vipCardId: Int, adAccountId: String?): Observable<PayResult> {
        return Observable.just(PayResult(false, ResUtil.getString(R.string.str_unsupport_pay)))
    }

    override fun payRideCard(rideCardId: Int, adAccountId: String?): Observable<PayResult> {
        RideCardModel.createBalanceRideCardOrder(adAccountId, rideCardId)
            .addToComposite(compositeDisposable)
            .subscribeBy(
                onComplete = {
                    publishSubject.onNext(PayResult(true, ResUtil.getString(R.string.str_pay_success)))
                },
                onError = {
                    publishSubject.onNext(PayResult(false, ErrHandler.getErrMsg(it)))
                }
            )
        return publishSubject.hide().take(1)
    }
}