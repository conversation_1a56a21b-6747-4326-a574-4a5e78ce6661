package com.tbit.uqbike.apppay

import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.addToComposite
import com.tbit.uqbike.R
import com.tbit.uqbike.mvp.model.MemberFeeModel
import com.tbit.uqbike.mvp.model.OrderModel
import com.tbit.uqbike.mvp.model.RideCardModel
import com.tbit.uqbike.pay.bean.PayResult
import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.subjects.PublishSubject

class LianLianPayAppPay(activity: AppCompatActivity) : IAppPay, LifecycleObserver {

    private val compositeDisposable = CompositeDisposable()
    private val publishSubject = PublishSubject.create<PayResult>()

    init {
        activity.lifecycle.addObserver(this)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        compositeDisposable.dispose()
    }

    override fun payCharge(deposit: Boolean, money: Int?, adAccountId: String?): Observable<PayResult> {
        OrderModel.createLianLianPayChargeOrder(deposit, money, adAccountId)
            .addToComposite(compositeDisposable)
            .subscribeBy(
                onNext = {
                    if (it.isSuccess && it.data!=null && !it.data.payUrl.isNullOrEmpty() && !it.data.orderId.isNullOrEmpty()) {
                        publishSubject.onNext(PayResult(false, null, lianLianPayConfig = it))
                    } else {
                        publishSubject.onNext(PayResult(false, ResUtil.getString(R.string.str_lianlian_pay_config_error)))
                    }
                },
                onError = {
                    publishSubject.onError(it)
                }
            )
        return publishSubject.hide().take(1)
    }

    override fun payReturnCharge(money: Int?, adAccountId: String?): Observable<PayResult> {
        OrderModel.createLianLianPayReturnChargeOrder(money, adAccountId)
            .addToComposite(compositeDisposable)
            .subscribeBy(
                onNext = {
                    if (it.isSuccess && it.data!=null && !it.data.payUrl.isNullOrEmpty() && !it.data.orderId.isNullOrEmpty()) {
                        publishSubject.onNext(PayResult(false, null, lianLianPayConfig = it))
                    } else {
                        publishSubject.onNext(PayResult(false, ResUtil.getString(R.string.str_lianlian_pay_config_error)))
                    }
                },
                onError = {
                    publishSubject.onError(it)
                }
            )
        return publishSubject.hide().take(1)
    }

    override fun payMemberFee(memberFeeId: Int, adAccountId: String?): Observable<PayResult> {
        MemberFeeModel.createLianLianPayMemberOrder(adAccountId, memberFeeId)
            .addToComposite(compositeDisposable)
            .subscribeBy(
                onNext = {
                    if (it.isSuccess && it.data!=null && !it.data.payUrl.isNullOrEmpty() && !it.data.orderId.isNullOrEmpty()) {
                        publishSubject.onNext(PayResult(false, null, lianLianPayConfig = it))
                    } else {
                        publishSubject.onNext(PayResult(false, ResUtil.getString(R.string.str_lianlian_pay_config_error)))
                    }
                },
                onError = {
                    publishSubject.onError(it)
                }
            )
        return publishSubject.hide().take(1)
    }

    override fun payVipCard(vipCardId: Int, adAccountId: String?): Observable<PayResult> {
        return publishSubject.hide().take(1)
    }

    override fun payRideCard(rideCardId: Int, adAccountId: String?): Observable<PayResult> {
        RideCardModel.createLianLianPayRideCardOrder(adAccountId, rideCardId)
            .addToComposite(compositeDisposable)
            .subscribeBy(
                onNext = {
                    if (it.isSuccess && it.data!=null && !it.data.payUrl.isNullOrEmpty() && !it.data.orderId.isNullOrEmpty()) {
                        publishSubject.onNext(PayResult(false, null, lianLianPayConfig = it))
                    } else {
                        publishSubject.onNext(PayResult(false, ResUtil.getString(R.string.str_lianlian_pay_config_error)))
                    }
                },
                onError = {
                    publishSubject.onError(it)
                }
            )
        return publishSubject.hide().take(1)
    }
}