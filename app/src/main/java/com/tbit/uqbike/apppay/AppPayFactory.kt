package com.tbit.uqbike.apppay

import androidx.appcompat.app.AppCompatActivity
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R

class AppPayFactory(activity: AppCompatActivity) {

//    private val alipayAppPay = if (ResUtil.getBoolean(R.bool.support_alipay)) AlipayAppPay(activity) else null
//    private val wxPayAppPay = if (ResUtil.getBoolean(R.bool.support_wxpay)) WxPayAppPay(activity) else null
//    private val payPalAppPay = if (ResUtil.getBoolean(R.bool.support_paypal)) PayPalAppPay(activity) else null
//    private val stripePalAppPay = if (ResUtil.getBoolean(R.bool.support_stripe)) StripePayAppPay(activity) else null
//    private val paypayAppPay = if (ResUtil.getBoolean(R.bool.support_paypay)) PaypayAppPay(activity) else null
//    private val linePayAppPay = if (ResUtil.getBoolean(R.bool.support_line)) LinePayAppPay(activity) else null
    private val lianLianPayAppPay = if (ResUtil.getBoolean(R.bool.support_lianLian)) LianLianPayAppPay(activity) else null
    private val balancePayAppPay = BalancePayAppPay(activity)

    fun createAppPay(payWay: Int): IAppPay {
        return when (payWay) {
//            Constant.PayWay.ALIPAY -> alipayAppPay
//            Constant.PayWay.WXPAY -> wxPayAppPay
            Constant.PayWay.PAYPAL -> {
                if (Glob.paypalPayConfig == null) {
                    throw Throwable(ResUtil.getString(R.string.str_not_get_paypal_config))
                } else {
                    throw Throwable(ResUtil.getString(R.string.str_not_get_paypal_config))
//                    payPalAppPay
                }
            }
//            Constant.PayWay.STRIPE -> stripePalAppPay
//            Constant.PayWay.PAYPAY -> paypayAppPay
//            Constant.PayWay.LINE -> linePayAppPay
            Constant.PayWay.BALANCE -> balancePayAppPay
            Constant.PayWay.LIANLIAN -> lianLianPayAppPay
            else -> throw Throwable("not support pay way: $payWay")
        } ?: throw Throwable("not support pay way: $payWay")
    }
}