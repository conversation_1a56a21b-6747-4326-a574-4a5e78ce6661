package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import java.util.*

class GiftCard {

    /**礼品卡id */
    @SerializedName("giftcardId")
    val giftCardId: Int? = null

    /**区域id */
    @SerializedName("accountId")
    val accountId: Int? = null

    /**类型 */
    @SerializedName("cardType")
    val cardType: Int? = null

    /**礼品卡编号 */
    @SerializedName("cardNO")
    val cardNO: String? = null

    /**金额 */
    @SerializedName("money")
    val money: Int = 0

    /**失效日期 */
    @SerializedName("expireDate")
    val expireDate: Long? = null

    /**创建时间 */
    @SerializedName("createTime")
    val createTime: Long? = null

    /**使用时间 */
    @SerializedName("useTime")
    val useTime: Long? = null

    /**使用用户id */
    @SerializedName("userId")
    val userId: Int? = null

    /**备注 */
    @SerializedName("remark")
    val remark: String? = null

    val formatUseTime by lazy { useTime?.let { TimeFormatUtil.formatDateTime(Date(it)) } }
}