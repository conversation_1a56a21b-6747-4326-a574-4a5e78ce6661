package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import java.io.Serializable

class AdDeposit : Serializable {

    //实际金额
    @SerializedName("actualMoney")
    val actualMoney: Float = 0f

    //礼品卡金额
    @SerializedName("cardMoney")
    val cardMoney: Float = 0f

    //用户需交押金
    @SerializedName("depositMoney")
    val depositMoney: Float = 0f

    //用户已交押金
    @SerializedName("useDeposit")
    val useDeposit: Float = 0f

    @SerializedName("depositStatus")
    val depositState: Int = 0

    //总金额
    @SerializedName("totalMoney")
    val totalMoney: Float = 0f

    @SerializedName("accountId")
    val accountId: String? = null

    @SerializedName("modelType")
    val modelType: Int = 0

    @SerializedName("nameAuth")
    val nameAuth: Int = 0

    //是否支持预约
    @SerializedName("appoint")
    val appoint: Int = 0

    //货币符号
    @SerializedName("symbol")
    val symbol: String? = null

    //是否显示退款按钮  1显示，0或空不显示
    @SerializedName("showRefund")
    val showRefund: Int? = null

    val needNameAuth get() = nameAuth != 0

    val supportBook get() = appoint != 0
}