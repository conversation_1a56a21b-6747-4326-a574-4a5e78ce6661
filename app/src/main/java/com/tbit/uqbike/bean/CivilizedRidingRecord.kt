package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import java.io.Serializable
import java.util.*

class CivilizedRidingRecord : Serializable {

    @SerializedName("rideId")
    val rideId: Int? = null

    //订单编号
    @SerializedName("orderNo")
    val orderNo: String? = null

    //用户id
    @SerializedName("userId")
    val userId: Int? = null

    @SerializedName("machineId")
    var machineId: String? = null

    @SerializedName("startTime")
    var startTime: Long? = null

    @SerializedName("endTime")
    var endTime: Long? = null

    //手机号码
    @SerializedName("phone")
    val phone: String? = null

    //添加时间
    @SerializedName("addTime")
    val addTime: Long? = null

    //处理时间
    @SerializedName("handelTime")
    val handelTime: Long? = null

    //经度
    @SerializedName("lon")
    val lon: Double = 0.0

    //纬度
    @SerializedName("lat")
    val lat: Double = 0.0

    //类型
    @SerializedName("type")
    val type: Int? = null

    //状态 0未申诉、1申述中、2已处理
    @SerializedName("state")
    val state: Int? = null

    //处理结果 0未处理、1申述通过、2申述失败
    @SerializedName("rspRet")
    val rspRet: Int? = null

    //回复信息
    @SerializedName("rspMsg")
    val rspMsg: String? = null

    //申述理由
    @SerializedName("reason")
    val reason: String? = null

    //备注
    @SerializedName("remark")
    val remark: String? = null

    @SerializedName("photologs")
    val photos: List<SnapshotPhoto>? = null

    val formatAddTime by lazy { addTime?.let { TimeFormatUtil.formatDateTime(Date(it)) } }

    val formatStartTime by lazy { startTime?.let { TimeFormatUtil.formatDateTime(Date(it)) } }

    val formatEndTime by lazy { endTime?.let { TimeFormatUtil.formatDateTime(Date(it)) } }

    class SnapshotPhoto : Serializable {

        @SerializedName("photoId")
        val photoId: Int? = null

        //不文明骑行Id
        @SerializedName("rideId")
        val rideId: Int? = null

        //订单编号
        @SerializedName("machineNO")
        val machineNO: String? = null

        //oss图片url
        @SerializedName("ossUrl")
        val ossUrl: String? = null

        //ftp图片url
        @SerializedName("ftpUrl")
        val ftpUrl: String? = null

    }

}