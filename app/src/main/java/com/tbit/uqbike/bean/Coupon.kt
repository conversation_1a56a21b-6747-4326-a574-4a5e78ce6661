package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName

// API CouponItem status values (assuming based on common patterns - to be confirmed)
// const val COUPON_STATUS_ACTIVE = 0 // Or some other value indicating active/unused
// const val COUPON_STATUS_USED = 1   // Or some other value indicating used
// const val COUPON_STATUS_EXPIRED = 2 // Or some other value indicating expired explicitly by status

data class Coupon(
    @SerializedName("couponuserId") // Keep SerializedName if this data class is ever directly deserialized by Gson
    val couponUserId: Int = 0,
    @SerializedName("couponId")
    val couponId: Int = 0,
    // val accountId: Int = 0, // Not obviously mapped from CouponItem, omitting for now unless needed
    @SerializedName("userId")
    val userId: Int = 0, // Not directly in CouponItem, might be from a higher context or not needed for display
    // val recTime: String? = null, // CouponItem has startTime (Long), not recTime (String)
    
    // For isUse and isValid, we'll rely on a status field from CouponItem and endTimeLong
    val internalStatus: Int = 0, // Represents the raw status from CouponItem (e.g., 0:active, 1:used, 2:expired by API)
    val endTimeLong: Long = 0L, // Raw end time from CouponItem - made public for adapter

    @SerializedName("couponType")
    val couponType: Int? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("couponMoney")
    val couponMoney: Float = 0f, // Changed from Int to Float to preserve decimal precision
    @SerializedName("couponTime")
    val couponTime: Int = 0,
    @SerializedName("remark")
    val remark: String? = null, // Can be used for detailed description from couponInfo.desc
    val thresholdDescription: String? = null, // New field for threshold text
    val isLocked: Boolean = false // New field to indicate if the coupon is part of a locked equity
) {
    // Companion object for status constants if needed by adapter directly, though adapter uses its own enum.
    // companion object {
    //     const val STATUS_API_ACTIVE = 0 // Example, confirm actual values
    //     const val STATUS_API_USED = 1   // Example
    //     const val STATUS_API_EXPIRED_BY_STATUS = 2 // Example
    // }

    val isUse: Boolean
        get() = internalStatus == 1 // Assuming status 1 from API means 'used'

    val isValid: Boolean // isValid often means "not expired AND not used" for an active coupon
        get() {
            if (isUse) return false // If used, it's no longer 'valid' for use
            if (internalStatus == 2) return false // Assuming status 2 from API means 'expired by status'
            return endTimeLong > System.currentTimeMillis() // Valid if not used, not expired by status, and end time is in future
        }
}