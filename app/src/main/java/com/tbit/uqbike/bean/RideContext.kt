package com.tbit.uqbike.bean

class RideContext(val userCode: String?) {

    var onProcessChangeListener = {}

    var secretKeyInfo: SecretKeyInfo? = null

    var dispatchInfo: DispatchInfo? = null

    var lastRidingResult: RidingRecord? = null

    var parkInfo: ParkInfo? = null

    var lat: Double? = null

    var lng: Double? = null

    var payWay: Int = 0

    var firstChangeMoney: Int? = null

    var insufficientMoney: Int? = null

    var arrearsMoney: Int? = null

    var progress: Int = 0
        set(value) {
            field = value
            onProcessChangeListener()
        }

    var netErrorCount: Int = 0

    var bleErrorCount: Int = 0

    var linePayConfig: LinePayConfig? = null

    var lianLianPayConfig: LianLianPayConfig? = null

}