package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import java.io.Serializable

class SupportFunctionInfo : Serializable {

    @SerializedName("functionName")
    val functionName: String? = null

    @SerializedName("functionType")
    val functionType: Int? = null //1：头盔锁 6：RFID

    @SerializedName("functionValue")
    val functionValue: Int? = null //1：有这项功能 0：没有

    @SerializedName("machineId")
    val machineId: Int? = null

}