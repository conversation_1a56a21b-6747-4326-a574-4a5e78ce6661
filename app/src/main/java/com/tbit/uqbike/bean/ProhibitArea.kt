package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import com.tbit.tbituser.map.bean.LatLng
import java.io.Serializable

class ProhibitArea: Serializable {

    /** 禁止区id */
    @SerializedName("prohibitAreaId")
    var prohibitAreaId: Int? = null

    /**  运营区域id */
    @SerializedName("accountId")
    val accountId: Int? = null

    /**名称 */
    @SerializedName("name")
    var name: String? = null

    @SerializedName("region_code")
    var region_code: Int = 0

    /**  类型 */
    @SerializedName("prohibitAreaType")
    val prohibitAreaType: Int? = null

    /**  中心点经度 */
    @SerializedName("lo")
    val lo: Double = 0.0

    /**  中心点纬度 */
    @SerializedName("la")
    val la: Double = 0.0

    /**
     * 校准后经度
     */
    @SerializedName("loC")
    var lonC: Double = 0.0

    /**
     * 校准后纬度
     */
    @SerializedName("laC")
    var latC: Double = 0.0

    /**  允许误差 */
    @SerializedName("allowRange")
    val range: Double = 0.0

    /**  经纬度集合 */
    @SerializedName("points")
    val points: String? = null

    /**
     * 多边行经纬度集合
     */
    @SerializedName("pointsC")
    val pointsCInternal: String? = null

    /**  备注 */
    @SerializedName("remark")
    val remark: String? = null

    //经纬度列表
    var pointsC: List<LatLng> ?= null
//    val pointsC: List<LatLng> by lazy {
//        pointsCInternal?.split(";")
//            ?.mapNotNull { it.split(",").takeIf { it.size == 2 } }
//            ?.map { LatLng().apply {
//                lat = it[1].toDouble()
//                lng = it[0].toDouble()
//            } } ?: emptyList()
//    }
}