package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import java.io.Serializable

/**
 * Created by <PERSON> on 2017/5/9 0009.
 */
class User:Serializable {
    @SerializedName("userId")
    val userId: String? = null
    @SerializedName("accountId")
    val accountId: String? = null
    @SerializedName("phone")
    var phone: String? = null
    @SerializedName("sex")
    val sex: String? = null
    @SerializedName("birthDay")
    val birthDay: String? = null
    @SerializedName("email")
    val email: String? = null
    @SerializedName("depositState")
    val depositState = 0
    @SerializedName("joinTime")
    val joinTime: String? = null
    @SerializedName("updateTime")
    val updateTime: String? = null
    @SerializedName("remark")
    val remark: String? = null
    @SerializedName("imageUrl")
    val avatarUrl: String? = null
    @SerializedName("money")
    val balance = 0.0
    @SerializedName("token")
    val token: String? = null
    @SerializedName("depositMoney")
    val depositMoney = 0.0
    @SerializedName("idNO")
    val idCardNumber: String? = null
    @SerializedName("name")
    val name: String? = null
    @SerializedName("nameAuth")
    val isNameAuth = false
    @SerializedName("depositDate")
    val depositDate: Long? = null
    @SerializedName("rideCardDate")
    val rideCardDate: Long? = null
    @SerializedName("vipDate")
    val vipDate: Long? = null
    @SerializedName("newUserFlag")
    val newUserFlag = false //是否是新用户
    @SerializedName("surplusMinutes")
    val surplusMinutes: Int? = null //剩余可骑行时间

}