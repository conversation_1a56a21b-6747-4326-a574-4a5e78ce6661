package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import java.util.*

class VipCardRecord {

    /**用户id */
    @SerializedName("userId")
    val userId: Int? = null

    /**代理id */
    @SerializedName("accountId")
    val accountId: Int? = null

    /**会员卡id */
    @SerializedName("vipId")
    val vipId: Int? = null

    /**购买时间 */
    @SerializedName("buyTime")
    val buyTime: Long? = null

    /**生效时间 */
    @SerializedName("effectDate")
    val effectDate: Long? = null

    /**到期时间 */
    @SerializedName("expireDate")
    val expireDate: Long? = null

    /**备注 */
    @SerializedName("remark")
    val remark: String? = null

    /**支付状态 */
    @SerializedName("memberState")
    val memberState: Int? = null

    /**订单编号 */
    @SerializedName("orderNO")
    val orderNO: String? = null

    /**支付时间 */
    @SerializedName("payTime")
    val payTime: Long? = null

    /**会员卡名称 */
    @SerializedName("name")
    val name: String? = null

    /**会员卡类型 */
    @SerializedName("type")
    val type: Int = 0

    /**折扣 */
    @SerializedName("discount")
    val discount: Double? = null

    /**是否过期 */
    @SerializedName("isValid")
    val validType: Int = 0

    val formatBuyTime by lazy { buyTime?.let { TimeFormatUtil.formatDateTime(Date(it)) } }

    val formatExpireDate by lazy { expireDate?.let { TimeFormatUtil.formatDate(Date(it)) } }
}