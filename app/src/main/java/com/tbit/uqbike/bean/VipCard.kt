package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName

class VipCard {

    /**代理id */
    @SerializedName("accountId")
    val accountId: Int? = null

    /**会员卡id */
    @SerializedName("vipId")
    val vipId: Int? = null

    /**创建时间 */
    @SerializedName("createTime")
    val createTime: String? = null

    /**更新时间 */
    @SerializedName("updateTime")
    val updateTime: String? = null

    /**备注 */
    @SerializedName("remark")
    val remark: String? = null

    /**是否免押 */
    @SerializedName("freeDeposit")
    val freeDeposit: Int = 0

    /**会员卡名称 */
    @SerializedName("name")
    val name: String? = null

    /**会员卡类型 */
    @SerializedName("type")
    val type: Int = 0

    /**折扣 */
    @SerializedName("discount")
    val discount: Double? = null

    /**是否生效 */
    @SerializedName("enable")
    val enable: Int = 0

    /**金额 */
    @SerializedName("money")
    val money: Float = 0f

    /**描述 */
    @SerializedName("description")
    val description: String? = null

    val isFreeDeposit get() = freeDeposit != 0
}