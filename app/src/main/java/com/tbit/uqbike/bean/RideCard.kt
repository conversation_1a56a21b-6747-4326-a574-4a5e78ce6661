package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName

class RideCard {
    //    rideCardId	骑行会员卡id
//    accountId	品牌id
//    name	骑行会员卡名称
//    type	骑行会员卡类型
//      1：周卡
//      2：月卡
//      3：季卡
//      4：年卡
//    createTime	创建时间
//    money	金额（元）
//    timeCount	骑行时长限制（分钟）
//    maxRide	最大骑行次数
//    enable	是否生效
//    description	描述
//    remark	备注

    @SerializedName("rideCardId")
    var rideCardId = 0

    @SerializedName("accountId")
    var accountId: String? = null

    @SerializedName("name")
    var name: String? = null

    @SerializedName("type")
    var type = 0

    @SerializedName("createTime")
    var createTime: String? = null

    @SerializedName("money")
    var money = 0f

    @SerializedName("timeCount")
    var timeCount = 0

    @SerializedName("maxRide")
    var maxRide = 0

    @SerializedName("enable")
    var isEnable = false

    @SerializedName("description")
    var description: String? = null

    @SerializedName("remark")
    var remark: String? = null
}