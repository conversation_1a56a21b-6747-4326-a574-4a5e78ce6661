package com.tbit.smartbike.bean

import org.simpleframework.xml.ElementList
import org.simpleframework.xml.Root

/**
 * Created by WG on 10/05/2018.
 * Email: <EMAIL>
 * Github: https://github.com/WGwangguan
 * Desc:
 */
@Root(name = "update", strict = false)
class UpdateDesc {

    @field:ElementList(entry = "language", inline=true)
    var desc: List<UpdateDescLanguage>? = null

    override fun toString(): String {
        return "UpdateDesc(desc=$desc)"
    }
}