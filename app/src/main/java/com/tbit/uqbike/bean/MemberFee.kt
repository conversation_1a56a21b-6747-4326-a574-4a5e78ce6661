package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName

class MemberFee {
    //    "accountId": 226,
//            "creatTime": *************,
//            "memberFeeId": 21,
//            "money": 288,
//            "name": "年卡",
//            "remark": "年卡测试",
//            "type": 4

    @SerializedName("accountId")
    var accountId = 0

    @SerializedName("creatTime")
    var createTime: String? = null

    @SerializedName("memberFeeId")
    var memberFeeId = 0

    @SerializedName("money")
    var money = 0f

    @SerializedName("name")
    var name: String? = null

    @SerializedName("remark")
    var remark: String? = null

    @SerializedName("type")
    var type = 0
}