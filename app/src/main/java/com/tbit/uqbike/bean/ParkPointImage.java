package com.tbit.uqbike.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

/**
 * Created by WG on 10/01/2018.
 * Email: <EMAIL>
 * Github: https://github.com/WGwangguan
 * Desc:
 */


public class ParkPointImage implements Parcelable {
    //    imageId
//            图片id
//    parkPointId
//            站点id
//    addTime
//            添加时间
    @SerializedName("imageId")
    private String imageId;
    @SerializedName("parkPointId")
    private int parkPointId;
    @SerializedName("addTime")
    private String addTime;

    protected ParkPointImage(Parcel in) {
        imageId = in.readString();
        parkPointId = in.readInt();
        addTime = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(imageId);
        dest.writeInt(parkPointId);
        dest.writeString(addTime);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<ParkPointImage> CREATOR = new Creator<ParkPointImage>() {
        @Override
        public ParkPointImage createFromParcel(Parcel in) {
            return new ParkPointImage(in);
        }

        @Override
        public ParkPointImage[] newArray(int size) {
            return new ParkPointImage[size];
        }
    };

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public int getParkPointId() {
        return parkPointId;
    }

    public void setParkPointId(int parkPointId) {
        this.parkPointId = parkPointId;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime;
    }

    public static Creator<ParkPointImage> getCREATOR() {
        return CREATOR;
    }

    @Override
    public String toString() {
        return "StationImage{" +
                "imageId='" + imageId + '\'' +
                ", parkPointId=" + parkPointId +
                ", addTime='" + addTime + '\'' +
                '}';
    }
}
