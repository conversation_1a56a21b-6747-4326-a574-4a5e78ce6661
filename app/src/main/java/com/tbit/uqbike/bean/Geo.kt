package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import com.tbit.uqbike.utils.PointUtil

/**
 * Created by ya<PERSON>ibang on 2019/10/14.
 */

class Geo {

    //主键
    @SerializedName("geoId")
    var geoId: Int = 0

    //代理商id
    @SerializedName("accountId")
    var accountId: Int = 0

    //电子围栏类型0：圆形1：多边形
    @SerializedName("geoType")
    var geoType: Int = 0

    //半径
    @SerializedName("radius")
    var radius: Int = 0

    //经纬度列表
    @SerializedName("points")
    var pointsString: String? = null

    //经纬度列表
    @SerializedName("pointsC")
    var pointsCString: String? = null

    val points by lazy { pointsString?.let { PointUtil.pointsString2LatLng(it) } ?: emptyList() }

    val pointsC by lazy { pointsCString?.let { PointUtil.pointsString2LatLng(it) } ?: emptyList() }
}
