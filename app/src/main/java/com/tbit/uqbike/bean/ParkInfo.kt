package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName

/**
 * Created by <PERSON> on 2017/6/26 16:34.
 * Desc：中途停车
 */
class ParkInfo {

    //    accountId	平台账号id
//    userId	用户id
//    machineId	设备id
//    startTime	开始时间
//    endTime	结束时间
//    remark	备注
    @SerializedName("accountId")
    val accountId = 0
    @SerializedName("userId")
    val userId = 0
    @SerializedName("machineId")
    val machineId = 0
    @SerializedName("startTime")
    val startTime: String? = null
    @SerializedName("endTime")
    val endTime: String? = null
    @SerializedName("remark")
    val remark: String? = null
    @SerializedName("money")
    val money = 0.0

    override fun toString(): String {
        return "ParkInfo{" +
                "accountId=" + accountId +
                ", userId=" + userId +
                ", machineId=" + machineId +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", remark='" + remark + '\'' +
                '}'
    }
}