package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import com.tbit.maintenance.config.Constant

class ServicePhoneInfo {

    @SerializedName("name")
    val name: String? = null

    @SerializedName("phone")
    val phone: String? = null

    @SerializedName("adName")
    val adName: String? = null

    @SerializedName("adPhone")
    val adPhone: String? = null

    @SerializedName("email")
    val email: String? = null

    fun toPhoneInfoList(): List<PhoneInfo> {
        val list = mutableListOf<PhoneInfo>()
        if (!adPhone.isNullOrEmpty()) {
            list.add(PhoneInfo(adPhone, Constant.CustomerServiceType.PHONE))
        } else if (!phone.isNullOrEmpty()) {
            list.add(PhoneInfo(phone, Constant.CustomerServiceType.PHONE))
        }
        if (!email.isNullOrEmpty()) {
            list.add(PhoneInfo(email, Constant.CustomerServiceType.EMAIL))
        }
        return list
    }

//    private fun toPhoneInfo(servicePhone: String): PhoneInfo? {
//        val phone = servicePhone.replace(Regex("[（(].*[)）]"), "").trim()
//        return if(phone.isNotEmpty()) PhoneInfo(phone, servicePhone) else null
//    }
}