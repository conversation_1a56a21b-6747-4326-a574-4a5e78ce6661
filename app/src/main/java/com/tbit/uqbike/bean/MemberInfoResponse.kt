package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName

data class MemberInfoResponse(
    @SerializedName("name") val name: String?,
    @SerializedName("member_level") val memberLevel: Int?,
    @SerializedName("min_mileage") val minMileage: Float?,
    @SerializedName("max_mileage") val maxMileage: Float?,
    @SerializedName("user_mileage") val userMileage: Float?,
    @SerializedName("upgrade_mileage") val upgradeMileage: Float?,
    @SerializedName("start_time") val startTime: Long?,
    @SerializedName("end_time") val endTime: Long?,
    @SerializedName("is_config_equity") val isConfigEquity: Boolean?,
    @SerializedName("equity") val equity: Equity?
)

data class Equity(
    @SerializedName("coupons") val coupons: Coupons?,
    @SerializedName("other_park") val otherPark: OtherPark?,
    @SerializedName("ride_card") val rideCard: MemberEquityRideCard?
)

data class Coupons(
    @SerializedName("list") val list: List<CouponItem>?,
    @SerializedName("is_lock") val isLock: Boolean?,
    @SerializedName("desc") val desc: String?
)

data class CouponItem(
    @SerializedName("user_coupon_id") val userCouponId: Int?,
    @SerializedName("amount") val amount: Float?,
    @SerializedName("start_time") val startTime: Long?,
    @SerializedName("end_time") val endTime: Long?,
    @SerializedName("status") val status: Int?,
    @SerializedName("coupon_id") val couponId: Int?,
    @SerializedName("type") val type: Int?,
    @SerializedName("business_type") val businessType: Int?,
    @SerializedName("is_min_spend") val isMinSpend: Int?,
    @SerializedName("min_spend_amount") val minSpendAmount: String?,
    @SerializedName("is_superposed") val isSuperposed: Int?,
    @SerializedName("coupon_info") val couponInfo: CouponInfoDetail?
)

data class CouponInfoDetail(
    @SerializedName("id") val id: Int?,
    @SerializedName("name") val name: String?,
    @SerializedName("desc") val desc: String?,
    @SerializedName("business_type") val businessType: Int?,
    @SerializedName("amount_type") val amountType: Int?,
    @SerializedName("min_amount") val minAmount: String?,
    @SerializedName("max_amount") val maxAmount: String?,
    @SerializedName("amount") val couponAmount: String? // Renamed from amount to avoid conflict if ever used in a context with CouponItem.amount
)

data class OtherPark(
    @SerializedName("park_impunity") val parkImpunity: Int?,
    @SerializedName("is_lock") val isLock: Boolean?,
    @SerializedName("desc") val desc: String?
)

data class MemberEquityRideCard(
    @SerializedName("ride_card_delay") val rideCardDelay: Int?,
    @SerializedName("is_lock") val isLock: Boolean?,
    @SerializedName("desc") val desc: String?
) 