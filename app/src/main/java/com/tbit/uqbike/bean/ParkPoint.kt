package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import com.tbit.tbituser.map.bean.LatLng
import java.io.Serializable

/**
 * Created by Salmon on 2017/5/9 0009.
 */

class ParkPoint: Serializable {
    @SerializedName("parkPointId")
    var parkPointId: Int = 0
    @SerializedName("name")
    var name: String? = null
    @SerializedName("region_code")
    var region_code: Int = 0
    @SerializedName("lo")
    var lon: Double = 0.toDouble()
    @SerializedName("la")
    var lat: Double = 0.toDouble()
    @SerializedName("loC")
    var lonC: Double = 0.toDouble()
    @SerializedName("laC")
    var latC: Double = 0.toDouble()
    @SerializedName("remark")
    val remark: String? = null
    @SerializedName("allowRange")
    val range: Double = 0.toDouble()
    @SerializedName("canBorrowNum")
    val canBorrowNum: Int = 0
    @SerializedName("parkPointType")
    val parkPointType: Int = 0
    @SerializedName("pointsC")
    val pointsCInternal: String? = null

    //经纬度列表
    var pointsC: List<LatLng> ?= null
//    val pointsC: List<LatLng> by lazy {
//        pointsCInternal?.split(";")
//                ?.mapNotNull { it.split(",").takeIf { it.size == 2 } }
//                ?.map { LatLng().apply {
//                    lat = it[1].toDouble()
//                    lng = it[0].toDouble()
//                } } ?: emptyList()
//    }

    override fun toString(): String {
        return "ParkPoint(parkPointId=$parkPointId, name=$name, lon=$lon, lat=$lat, lonC=$lonC, latC=$latC, remark=$remark, range=$range, canBorrowNum=$canBorrowNum, parkPointType=$parkPointType, pointsCInternal=$pointsCInternal)"
    }
}
