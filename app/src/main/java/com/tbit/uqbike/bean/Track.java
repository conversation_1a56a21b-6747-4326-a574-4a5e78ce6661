package com.tbit.uqbike.bean;

import com.google.gson.annotations.SerializedName;

/**
 * Created by <PERSON> on 2017/5/27 14:34.
 * Desc：
 */

public class Track {
    @SerializedName("machineId")
    String machineId;    //设备id
    @SerializedName("machineNO")
    String machineNO;    //设备编号
    @SerializedName("dt")
    String dt;    //定位时间
    @SerializedName("pointType")
    int pointType;    //定位类型
    @SerializedName("lon")
    double lon;    //原始经度
    @SerializedName("lat")
    double lat;    //原始纬度
    @SerializedName("lonC")
    double lonC;    //校准后经度
    @SerializedName("latC")
    double latC;    //校准后纬度
    @SerializedName("exData")
    String exData;    //扩展字段

    public String getMachineId() {
        return machineId;
    }

    public void setMachineId(String machineId) {
        this.machineId = machineId;
    }

    public String getMachineNO() {
        return machineNO;
    }

    public void setMachineNO(String machineNO) {
        this.machineNO = machineNO;
    }

    public String getDt() {
        return dt;
    }

    public void setDt(String dt) {
        this.dt = dt;
    }

    public int getPointType() {
        return pointType;
    }

    public void setPointType(int pointType) {
        this.pointType = pointType;
    }

    public double getLon() {
        return lon;
    }

    public void setLon(double lon) {
        this.lon = lon;
    }

    public double getLat() {
        return lat;
    }

    public void setLat(double lat) {
        this.lat = lat;
    }

    public double getLonC() {
        return lonC;
    }

    public void setLonC(double lonC) {
        this.lonC = lonC;
    }

    public double getLatC() {
        return latC;
    }

    public void setLatC(double latC) {
        this.latC = latC;
    }

    public String getExData() {
        return exData;
    }

    public void setExData(String exData) {
        this.exData = exData;
    }

    @Override
    public String toString() {
        return "Track{" +
                "machineId='" + machineId + '\'' +
                ", machineNO='" + machineNO + '\'' +
                ", dt='" + dt + '\'' +
                ", pointType=" + pointType +
                ", lon=" + lon +
                ", lat=" + lat +
                ", lonC=" + lonC +
                ", latC=" + latC +
                ", exData='" + exData + '\'' +
                '}';
    }
}
