package com.tbit.uqbike.bean

import android.os.Parcel
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import java.io.Serializable

/**
 * Created by <PERSON> on 2017/5/16 9:56.
 * Desc：
 */
class BikeState() : Parcelable, Serializable {

    @SerializedName("machineId")
    var machineId = 0
        internal set

    @SerializedName("userCode")
    var userCode: String? = null
        internal set

    @SerializedName("posDt")
    var posDt: String? = null
        internal set

    @SerializedName("batDt")
    var batDt: String? = null
        internal set

    @SerializedName("lon")
    var lon = 0.0
        internal set

    @SerializedName("lat")
    var lat = 0.0
        internal set

    @SerializedName("lonC")
    var lonC = 0.0
        internal set

    @SerializedName("latC")
    var latC = 0.0
        internal set

    @SerializedName("soc")
    var soc: String? = null
        internal set

    @SerializedName("batteryEI")
    var batteryEI: String? = null
        internal set

    @SerializedName("batteryEU")
    var batteryEU: String? = null
        internal set

    @SerializedName("socPercent")
    var powerRemain = 0
        internal set

    @SerializedName("surplusMileage")
    var mileageRemain: String? = null
        internal set

    @SerializedName("isBorrow")
    var isBorrowInternal = 0
        internal set

    val isBorrow get() = isBorrowInternal == 1

    @SerializedName("generation")
    var generation = 0
        internal set

    constructor(parcel: Parcel) : this() {
        machineId = parcel.readInt()
        userCode = parcel.readString()
        posDt = parcel.readString()
        batDt = parcel.readString()
        lon = parcel.readDouble()
        lat = parcel.readDouble()
        lonC = parcel.readDouble()
        latC = parcel.readDouble()
        soc = parcel.readString()
        batteryEI = parcel.readString()
        batteryEU = parcel.readString()
        powerRemain = parcel.readInt()
        mileageRemain = parcel.readString()
        isBorrowInternal = parcel.readInt()
        generation = parcel.readInt()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(machineId)
        parcel.writeString(userCode)
        parcel.writeString(posDt)
        parcel.writeString(batDt)
        parcel.writeDouble(lon)
        parcel.writeDouble(lat)
        parcel.writeDouble(lonC)
        parcel.writeDouble(latC)
        parcel.writeString(soc)
        parcel.writeString(batteryEI)
        parcel.writeString(batteryEU)
        parcel.writeInt(powerRemain)
        parcel.writeString(mileageRemain)
        parcel.writeInt(isBorrowInternal)
        parcel.writeInt(generation)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<BikeState> {
        override fun createFromParcel(parcel: Parcel): BikeState {
            return BikeState(parcel)
        }

        override fun newArray(size: Int): Array<BikeState?> {
            return arrayOfNulls(size)
        }
    }
}