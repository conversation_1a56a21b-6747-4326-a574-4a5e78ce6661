package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import java.util.*

class RideCardRecord {
    //    userId	用户id
//    rideCardId	骑行卡id
//    startDate	生效日期
//    endDate	失效日期
//    useCount	已使用次数
//    isValid	是否生效
//    orderNO	订单编号
//    payState	支付状态
//    name	骑行卡名称
//    maxRide	骑行卡最大使用次数（次）
//            0：为无限次
//    timeCount	最长单次骑行时长（分）
//            0：无限制

    @SerializedName("userId")
    var userId = 0

    @SerializedName("rideCardId")
    var rideCardId = 0

    @SerializedName("startDate")
    var startDate: Long? = null

    @SerializedName("endDate")
    var endDate: Long? = null

    @SerializedName("useCount")
    var useCount = 0

    @SerializedName("isValid")
    var isValid = false

    @SerializedName("orderNO")
    var orderNO: String? = null

    @SerializedName("payState")
    var payState = 0

    @SerializedName("name")
    var name: String? = null

    @SerializedName("maxRide")
    var maxRide = 0

    @SerializedName("timeCount")
    var timeCount = 0

    @SerializedName("type")
    var type = 0

    @SerializedName("money")
    var money = 0f

    val formatStartDate by lazy { startDate?.let { TimeFormatUtil.formatDate(Date(it)) } }

    val formatEndDate by lazy { endDate?.let { TimeFormatUtil.formatDate(Date(it)) } }
}