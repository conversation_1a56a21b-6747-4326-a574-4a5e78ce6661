package com.tbit.smartbike.bean

import android.os.Parcel
import android.os.Parcelable
import org.simpleframework.xml.Element
import org.simpleframework.xml.Root

/**
 * Created by WG on 09/05/2018.
 * Email: <EMAIL>
 * Github: https://github.com/WGwangguan
 * Desc:
 */
@Root(name = "update", strict = false)
class Update() : Parcelable {
    @field:Element(name = "v")
    var version: Int = 0
    @field:Element(name = "u", required = false)
    var apkPath: String? = null
    @field:Element(name = "f")
    var forceUpdateImpl: Int = 0
    val forceUpdate get() = forceUpdateImpl == 1

    constructor(parcel: Parcel) : this() {
        version = parcel.readInt()
        apkPath = parcel.readString()
        forceUpdateImpl = parcel.readInt()
    }

    override fun writeToParcel(dest: Parcel, flags: Int) {
        dest?.writeInt(version)
        dest?.writeString(apkPath)
        dest?.writeInt(forceUpdateImpl)
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun toString(): String {
        return "Update(version=$version, apkPath=$apkPath, forceUpdateImpl=$forceUpdateImpl)"
    }

    companion object CREATOR : Parcelable.Creator<Update> {
        override fun createFromParcel(parcel: Parcel): Update {
            return Update(parcel)
        }

        override fun newArray(size: Int): Array<Update?> {
            return arrayOfNulls(size)
        }
    }
}