package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName

class BookInfo {

    //平台账号id
    @SerializedName("accountId")
    val accountId: Int? = null

    //用户id
    @SerializedName("userId")
    val userId: Int? = null

    //设备id
    @SerializedName("machineId")
    val machineId: Int? = null

    //车辆编号
    @SerializedName("userCode")
    val userCode: String? = null

    //预约时间
    @SerializedName("appointmentTime")
    val appointmentTime: Long? = null

    //有效时间(分钟)
    @SerializedName("time")
    val time: Long = 0

    //剩余电量
    @SerializedName("socPercent")
    val powerRemain = 0

    //续航里程
    @SerializedName("surplusMileage")
    val mileageRemain: String? = null
}