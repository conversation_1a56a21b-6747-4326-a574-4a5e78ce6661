package com.tbit.uqbike.bean

import android.content.Context
import android.content.Intent
import com.google.gson.annotations.SerializedName
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.uqbike.activity.*
import org.jetbrains.anko.intentFor
import java.io.Serializable

class Advertising: Serializable {

    @SerializedName("accountId")
    val accountId: Int? = null

    @SerializedName("adId")
    val adId: Int? = null

    @SerializedName("adType")
    val adType: Int? = null

    @SerializedName("endTime")
    val endTime: String? = null

    @SerializedName("imageId")
    val imageId: String? = null

    @SerializedName("openURL")
    val openURL: String? = null

    @SerializedName("startTime")
    val startTime: String? = null

    @SerializedName("title")
    val title: String? = null

    @SerializedName("updateTime")
    val updateTime: String? = null

    val isValid get() = (endTime?.let { TimeFormatUtil.parseDateTime(it) }?.time ?: 0) >= System.currentTimeMillis()

    fun urlIntent(context: Context): Intent? {
        return when (openURL) {
            "recharge" -> ChargeNewActivity.createIntent(context,0f)
//            "depositvip" -> context.intentFor<MemberFeeActivity>()
//            "ridecard" -> context.intentFor<RideCardActivity>()
//            "vip" -> context.intentFor<VipCardActivity>()
            null, "" -> null
            else -> context.intentFor<WebActivity>(WebActivity.TITLE to title, WebActivity.URL to openURL)
        }
    }

    fun urlNeedLogin(): Boolean {
        return when (openURL) {
            "recharge" -> true
            "depositvip" -> true
            "ridecard" -> true
            "vip" -> true
            else -> false
        }
    }
}