package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import java.io.Serializable
import java.util.*

class Message: Serializable {

    //用户消息id
    @SerializedName("userMsgId")
    val userMsgId: Int? = null

    //消息类型
    @SerializedName("msgType")
    val msgType: Int? = null

    //用户id
    @SerializedName("userId")
    val userId: String? = null

    //创建时间
    @SerializedName("createTime")
    val createTime: Long? = null

    //阅读时间
    @SerializedName("readTime")
    val readTime: Long? = null

    //消息状态
    @SerializedName("msgState")
    val msgState: Int? = null

    //标题
    @SerializedName("title")
    val title: String? = null

    //内容
    @SerializedName("content")
    val content: String? = null

    //链接
    @SerializedName("linkURL")
    val linkURL: String? = null

    //备注
    @SerializedName("remark")
    val remark: String? = null

    val formatCreateTime by lazy { createTime?.let { TimeFormatUtil.formatDateTime(Date(it)) } }
}