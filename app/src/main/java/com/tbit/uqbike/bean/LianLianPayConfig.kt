package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import java.io.Serializable

class LianLianPayConfig : Serializable {

    @SerializedName("trace_id")
    val traceId: String? = null

    @SerializedName("code")
    val code: Int? = null //200000代表成功

    @SerializedName("message")
    val message: String? = null

    @SerializedName("data")
    val data: Data? = null

    val isSuccess get() = code == 200000

    class Data : Serializable {

        @SerializedName("order_status")
        val orderStatus: String? = null

        @SerializedName("merchant_order_id")
        val merchantOrderId: String? = null

        @SerializedName("create_time")
        val createTime: String? = null

        @SerializedName("order_currency")
        val orderCurrency: String? = null

        @SerializedName("order_amount")
        val orderAmount: String? = null

        @SerializedName("link_url")
        val linkUrl: String? = null

        @SerializedName("payUrl")
        val payUrl: String? = null

        @SerializedName("merchant_id")
        val merchantId: String? = null

        @SerializedName("order_id")
        val orderId: String? = null

    }

}