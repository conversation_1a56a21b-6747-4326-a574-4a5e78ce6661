package com.tbit.smartbike.bean

import org.simpleframework.xml.Attribute
import org.simpleframework.xml.ElementList
import org.simpleframework.xml.Root

@Root(name = "language", strict = false)
class UpdateDescLanguage {

    @field:ElementList(entry = "d", inline=true)
    var desc: List<String>? = null

    @field:Attribute(name = "value")
    var language: String? = null

    override fun toString(): String {
        return "UpdateDescLanguage(desc=$desc, language=$language)"
    }
}