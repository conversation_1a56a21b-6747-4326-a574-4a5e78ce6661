package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintanenceplus.utils.TimeFormatUtil.parseDate
import java.util.*

class MemberFeeRecord {

    @SerializedName("userId")
    var userId = 0

    @SerializedName("memberFeeName")
    var name: String? = null

    @SerializedName("buyTime")
    var buyTime: Long? = null

    @SerializedName("expireTime")
    var expireTime: Long? = null

    @SerializedName("memberState")
    var memberState = 0

    @SerializedName("money")
    var money = 0f

    @SerializedName("isValid")
    val isValid = 0

//    val isValid: Boolean
//        get() {
//            val expireTime = expireTime
//            val expireTimeMillis = if (expireTime == null) 0 else parseDate(expireTime).time
//            return System.currentTimeMillis() <= expireTimeMillis
//        }

    val formatBuyTime by lazy { buyTime?.let { TimeFormatUtil.formatDate(Date(it)) } }

    val formatExpireTime by lazy { expireTime?.let { TimeFormatUtil.formatDate(Date(it)) } }

}