package com.tbit.uqbike.bean

import android.os.Parcel
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import java.util.*

/**
 * Created by Salmon on 2017/5/9 0009.
 */
class RidingRecord() : Parcelable {
    @SerializedName("userId")
    var userId: String? = null
    @SerializedName("accountId")
    var accountId: String? = null
    @SerializedName("orderNO")
    var orderNO: String? = null
    @SerializedName("machineId")
    var machineId: String? = null
    @SerializedName("userCode")
    var machineNO: String? = null
    @SerializedName("startTime")
    var startTime: Long = 0
    @SerializedName("endTime")
    var endTime: Long = 0
    @SerializedName("stopTime")
    var stopTime: Int = 0
    @SerializedName("parkingTime")
    var parkingTime: Int = 0 //临停时长
    @SerializedName("surplusMinutes")
    var surplusMinutes: Int = 0 //剩余可用时长
    @SerializedName("remindThreeMinuteTime")
    var remindThreeMinuteTime: Long = 0 //剩余3分钟提醒时刻，时间戳
    @SerializedName("remindOneMinuteTime")
    var remindOneMinuteTime: Long = 0 //剩余1分钟提醒时刻，时间戳
    @SerializedName("runoutTime")
    var runoutTime: Long = 0 //可骑行时间用尽时刻，时间戳
    @SerializedName("money")
    var fee = 0.0
    @SerializedName("remark")
    var remark: String? = null
    @SerializedName("mileage")
    var mileage = 0.0
    @SerializedName("generation")
    var generation = 0

    val formatStartTime by lazy { TimeFormatUtil.formatDateTime(Date(startTime)) }

    val formatEndTime by lazy { TimeFormatUtil.formatDateTime(Date(endTime)) }

    constructor(parcel: Parcel) : this() {
        userId = parcel.readString()
        accountId = parcel.readString()
        orderNO = parcel.readString()
        machineId = parcel.readString()
        machineNO = parcel.readString()
        startTime = parcel.readLong()
        endTime = parcel.readLong()
        stopTime = parcel.readInt()
        parkingTime = parcel.readInt()
        surplusMinutes = parcel.readInt()
        remindThreeMinuteTime = parcel.readLong()
        remindOneMinuteTime = parcel.readLong()
        runoutTime = parcel.readLong()
        fee = parcel.readDouble()
        remark = parcel.readString()
        mileage = parcel.readDouble()
        generation = parcel.readInt()
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(userId)
        parcel.writeString(accountId)
        parcel.writeString(orderNO)
        parcel.writeString(machineId)
        parcel.writeString(machineNO)
        parcel.writeLong(startTime)
        parcel.writeLong(endTime)
        parcel.writeInt(stopTime)
        parcel.writeInt(parkingTime)
        parcel.writeInt(surplusMinutes)
        parcel.writeLong(remindThreeMinuteTime)
        parcel.writeLong(remindOneMinuteTime)
        parcel.writeLong(runoutTime)
        parcel.writeDouble(fee)
        parcel.writeString(remark)
        parcel.writeDouble(mileage)
        parcel.writeInt(generation)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<RidingRecord> {
        override fun createFromParcel(parcel: Parcel): RidingRecord {
            return RidingRecord(parcel)
        }

        override fun newArray(size: Int): Array<RidingRecord?> {
            return arrayOfNulls(size)
        }
    }
}