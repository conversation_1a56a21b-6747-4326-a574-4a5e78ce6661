package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import java.io.Serializable

class LinePayConfig : Serializable {

    @SerializedName("info")
    val info: Info? = null

    @SerializedName("returnCode")
    val returnCode: String? = null

    @SerializedName("returnMessage")
    val returnMessage: String? = null

    class Info : Serializable {

        @SerializedName("paymentAccessToken")
        val paymentAccessToken: String? = null

        @SerializedName("transactionId")
        val transactionId: String? = null

        @SerializedName("paymentUrl")
        val paymentUrl: PaymentUrl? = null

        class PaymentUrl : Serializable {

            @SerializedName("app")
            val app: String? = null

            @SerializedName("web")
            val web: String? = null

        }
    }

}