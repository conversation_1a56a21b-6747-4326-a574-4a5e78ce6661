package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName

class RideFee {

    //平台账号id
    @SerializedName("accountId")
    val accountId: String? = null

    //起步时间(小时)
    @SerializedName("baseTime")
    val baseTime: Int = 0

    //起步时间内费用（精确到0.01）
    @SerializedName("baseTimeFee")
    val baseTimeFee: Float = 0f

    @SerializedName("areaMoney")
    val areaMoney: Float = 0f

    @SerializedName("baseMile")
    val baseMile: Int = 0

    @SerializedName("baseMileFee")
    val baseMileFee: Float = 0f

    @SerializedName("capMile")
    val capMile: Int? = null

    @SerializedName("capMileFee")
    val capMileFee: Float? = null

    @SerializedName("capTime")
    val capTime: Int? = null

    @SerializedName("capTimeFee")
    val capTimeFee: Float? = null

    @SerializedName("dispatchSwitch")
    val dispatchSwitch: Int? = null

    @SerializedName("freeTime")
    val freeTime: Int? = null

    @SerializedName("newUserFreeTime")
    val newUserFreeTime: Int? = null

    @SerializedName("newUserFreeTimes")
    val newUserFreeTimes: Int? = null

    @SerializedName("newUserRechcrge")
    val newUserRechcrge: Float? = null

    @SerializedName("overMile")
    val overMile: Int? = null

    @SerializedName("overMileFee")
    val overMileFee: Float? = null

    @SerializedName("overTime")
    val overTime: Int = 0

    @SerializedName("overTimeFee")
    val overTimeFee: Float = 0f

    @SerializedName("parkPointMoney")
    val parkPointMoney: Float = 0f

    @SerializedName("rechargeBase")
    val rechargeBase: Float? = null
}