package com.tbit.uqbike.bean

import com.google.gson.annotations.SerializedName
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import java.util.*

/**
 * Created by <PERSON> on 2017/5/26 9:47.
 * Desc：
 */
class TransactionLog {
    /**
     * accountId : 5
     * money : 2
     * operaTime : 2017-05-26 09:42:52
     * remark : 骑行扣费
     * type : 0 消费类型：1：充值；0：消费
     * userId : 6
     */
    @SerializedName("accountId")
    var accountId = 0
    @SerializedName("money")
    var money = 0f
    @SerializedName("operaTime")
    var operaTime: Long? = null
    @SerializedName("remark")
    var remark: String? = null
    @SerializedName("type")
    var type = 0
    @SerializedName("userId")
    var userId = 0

    val formatOperaTime by lazy { operaTime?.let { TimeFormatUtil.formatDateTime(Date(it)) } }

    override fun toString(): String {
        return "TransactionLog{" +
                "accountId=" + accountId +
                ", money=" + money +
                ", operaTime='" + operaTime + '\'' +
                ", remark='" + remark + '\'' +
                ", type=" + type +
                ", userId=" + userId +
                '}'
    }
}