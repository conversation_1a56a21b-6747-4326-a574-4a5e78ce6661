package com.tbit.smartbike.banner

import android.content.Context
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.tbit.uqbike.R
import com.tbit.uqbike.onepasslogin.Utils
import com.youth.banner.loader.ImageLoader


class GlideImageLoader : ImageLoader() {
    val options0: RequestOptions = RequestOptions()
        .placeholder(null) // 设置加载中的图片
        .error(null) // 设置加载失败的图片
    override fun displayImage(context: Context, path: Any?, imageView: ImageView) {
        Glide.with(context).load(path).apply(options0).into(imageView)
    }
}