package com.tbit.uqbike.entity

val RIG_TYPE_NEWUSER = 1 //新手礼包
val RIG_TYPE_SYSCOUPON = 2 //系统奖励
data class RigisterData(
    val type : Int, //1新手礼包2系统奖励
    val is_display: Boolean,
    val present_amount: Int,
    val ride_card : RideCardRigerDatas? = null,
    val coupon: List<Coupon>
)

data class RideCardRigerDatas(
    val name: String,
    val number: Int,
    val ride_card_id: Int
)

class SelCoupon : ArrayList<Coupon>()
data class Coupon(
    val user_coupon_id : Int,
    val amount: Float,
    val is_min_spend: Int,//是否无门槛0: 是. 1: 不是
    val min_spend_amount: Float,//最低消费金额
    val name: String,
    val end_time : Long,
    val is_superposed : Int,//是否支持叠加使用 0:不支持 1:允许叠加使用
    val type : Int,//优惠券类型 1: 满减券 ,2 买一送一
    var isSel : Boolean = false, //是否选中
    var typeData : Int = 0,  // 1骑行金，2骑行卡，3优惠券
    val type_name : String,
    val is_membership: Boolean = false // 是否会员专属券，后端返回 true/false
)
val type_coupon_money = 1 //骑行金
val type_coupon_ridecard = 2 //骑行卡
val type_coupon_invite = 3 //优惠券

data class GiveCoupon(
    val number : Int,
    val coupon : List<Coupon>
)