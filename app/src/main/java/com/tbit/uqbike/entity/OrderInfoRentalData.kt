package com.tbit.uqbike.entity

import com.tbit.tbituser.map.bean.LatLng

data class OrderInfoRentalData(
    val amount: Float,
    val area_id: Int,
    val cancel_time: Int,
    val create_time: Int,
//    val deleted_time: Int,
    val device_uuid: String,
    val dispatch_amount: Float,
//    val dispatch_balance_deduction: Int,
//    val dispatch_present_deduction: Int,
//    val dispatch_type: Int,
//    val estimated_end_time: Int,
    val finish_time: Int,
//    val force_complete_time: Int,
//    val force_operator: Int,
//    val force_reason: Int,
//    val guarantee_deposit: Int,
//    val guarantee_deposit_return: Int,
    val id: Int,
    val is_lock: Boolean,
    val lease_end_time: Long,
    val mileage: Float,
//    val modified_time: Int,
    val no: String,
    val order_amount: Float,
//    val order_balance_deduction: Int,
    val order_no: String,
//    val order_present_deduction: Int,
    val order_time: Int,
//    val overtime_amount: Int,
    val paid_amount: Float,
    val paid_time: Int,
    val park_area: ParkArea,
    val park_area_id: Int,
    val payment_code: String,
    val paymethod: Int,
    val phone_number: String,
//    val refund_amount: Int,
    val region_code: Int,
    val ride_card_item_id: String,
    val sn: String,
    var status: Int,
    val third_no: String,
    val type: Int,
    val uid: Int,
    val ride_card_deduction: Float,
    val vehicle_no: String,
    val balance_deduction: Float,
    val present_deduction: Float,
    var is_short_order: Int,
    val ride_card_time: Int,
    val extra_pay_amount: Float,
    val mileage_point: List<LatLng>,
    val battery_replace_times : Int, //可换电次数
    val use_battery_replace_times : Int, //"已换电次数
    val vehicle_status : Int,  //1 已开锁 2 已关锁 3 换电中
    val coupon_deduction : Float, //优惠金额
    val ride_card_delay: Int? = null, // 会员骑行卡延期时间（min）
    val is_park_impunity: Int? = null // 是否非P免罚款：0否 1是
)
data class ParkArea(
    val flag: LatLng,
    val name: String
)

