package com.tbit.uqbike.entity

/**
 * 分享返利数据模型
 */
data class ShareRebateData(
    val type: String,           // "shareRebate"
    val data: ShareData
)

data class ShareData(
    val url: String,            // 完整分享链接（含id和username）
    val text: String,           // 分享文案
    val product_id: Int,        // 商品ID
    val rebates: List<String>,  // 各级佣金百分比 ["10","5","3","2","1"]
    val all_rebate: Int         // 总收益百分比
)

/**
 * 分享平台枚举
 */
enum class SharePlatform(val platformName: String, val packageName: String) {
    WHATSAPP("WhatsApp", "com.whatsapp"),
    LINE("Line", "jp.naver.line.android"),
    FACEBOOK("Facebook", "com.facebook.katana")
}
