package com.tbit.uqbike.ble;

import android.app.Activity;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.os.Handler;

import com.lsxiao.apollo.core.Apollo;
import com.tbit.maintenance.config.Constant;
import com.tbit.uqbike.Glob;
import com.tbit.uqbike.utils.MyLogUtil;
import com.tbit.uqbike.utils.TypeConversion;
import com.xiaoantech.sdk.XiaoanBleApiClient;
import com.xiaoantech.sdk.ble.model.Response;
import com.xiaoantech.sdk.ble.scanner.ScanResult;
import com.xiaoantech.sdk.listeners.BleCallback;
import com.xiaoantech.sdk.listeners.BleStateChangeListener;
import com.xiaoantech.sdk.listeners.ScanResultCallback;

public class XiaoAnBleSdk implements BleStateChangeListener, ScanResultCallback {
    private XiaoanBleApiClient apiClient;
    private Boolean isConing = false;//是否连接中

    public Boolean getConing() {
        return isConing;
    }

    public Boolean getCon() {
        return isCon;
    }

    private Boolean isCon = false;//是否连接成功
    private Boolean IsAutoSendMsg = false;// 连接成功是否自动发送消息
    private Activity act = null;
    private int writeRestartTime = 0;//重写命令 次数

    public void setAutoSendMsg(Boolean autoSendMsg) {
        IsAutoSendMsg = autoSendMsg;
    }

    /**
     * @param act
     * @param imei  设备编号
     * @param secretKey  密钥  token
     */
    public void Init(Activity act, String imei, String secretKey){
        this.act = act;
        XiaoanBleApiClient.Builder builder = new XiaoanBleApiClient.Builder(act);
        builder.setBleStateChangeListener(this);
        builder.setScanResultCallback(this);
        builder.setReadRssiInterval(0);
        builder.setToken(TypeConversion.hexToDecimal(secretKey));
        builder.setBleMtu(64);
        apiClient = builder.build();
        if (apiClient != null) {
            isConing = true;
            apiClient.connectToIMEI(imei);
            act.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            //
                            if (isCon != null && apiClient != null){
                                isConing = false;
                                if (!isCon){
                                    Apollo.emit(Constant.Event.BLE_CONFAIL);
                                    if (apiClient != null){
                                        apiClient.stopScan();
                                    }
                                }
//                                MyLogUtil.Log("9999","=======是否连接==="+ isCon);
                            }
                        }
                    },30 * 1000);
                }
            });
        }
    }
    public void CloseCar(){
        if (isCon){
            apiClient.setDefend(true, new BleCallback() {
                @Override
                public void onResponse(final Response response) {
//                    MyLogUtil.Log("9999", "======onConnect== response===="+response.toString());
                    if (response != null){
                        Glob.INSTANCE.setXiaoanState(response.code+":"+response.status);
                    }
                    if (response.code == 0){
                        //===成功 ====
                        Apollo.emit(Constant.Event.BLE_CLOSECARSUC);
                        writeRestartTime = 0;
                    }else{
                        writeRestartTime = writeRestartTime + 1;
                        if (writeRestartTime > 2) {
                            if (response.code == 7){
                                //响应超时
                                Apollo.emit(Constant.Event.BLE_CLOSECARFAIL);
                            }else {
                                Apollo.emit(Constant.Event.BLE_CLOSECARFAIL);
                            }
                            writeRestartTime = 0;
                        }else{
                            CloseCar();
                        }
                    }
                }
            });
        }
    }
    public void disCon(){
        isConing = false;
        isCon = false;
        if (apiClient != null) {
            apiClient.disConnect();
            apiClient.onDestroy();
        }
    }

    public void ActivityResult(int requestCode, int resultCode, Intent data){
        if (apiClient != null) {
            apiClient.onActivityResult(requestCode, resultCode, data);
        }
    }
    @Override
    public void onConnect(BluetoothDevice bluetoothDevice) {
        isConing = false;
        isCon = true;
        MyLogUtil.Log("9999", "======onConnect== 连接成功====");
        Apollo.emit(Constant.Event.BLE_CONSUC);
        if (IsAutoSendMsg && act != null){
            act.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (IsAutoSendMsg != null && apiClient != null){
                                CloseCar();
                            }
                        }
                    },2 * 1000);
                }
            });

        }
    }

    @Override
    public void onDisConnect(BluetoothDevice bluetoothDevice) {
        MyLogUtil.Log("9999", "======onConnect== onDisConnect====");
        Apollo.emit(Constant.Event.BLE_CONFAIL_AUTO);
        isConing = false;
        isCon = false;
        if (apiClient != null) {
            apiClient = null;
        }
    }

    @Override
    public void onDeviceReady(BluetoothDevice bluetoothDevice) {
        //表明服务发现完毕，可以进行操作了。
        MyLogUtil.Log("9999", "======onDeviceReady ====");
    }

    @Override
    public void onReadRemoteRssi(int i) {
    }

    @Override
    public void onError(BluetoothDevice bluetoothDevice, String s, int i) {
//        MyLogUtil.Log("9999", "======onError======"+s+","+i);
    }

    @Override
    public void onBleAdapterStateChanged(int i) {
    }

    @Override
    public void onResult(ScanResult scanResult) {
//        MyLogUtil.Log("9999", "======onResult======"+scanResult.toString());
    }
}
