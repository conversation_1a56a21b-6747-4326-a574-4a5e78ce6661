package com.tbit.uqbike.ble

import android.os.Build
import androidx.annotation.RequiresApi
import com.lsxiao.apollo.core.Apollo
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.bean.Command
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil

object XiaoAnBleUtil {

    var onDisMissListen = { }
    @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    fun GoAction(snData :String, secretKey : String, command: Command, isCallBack : Boolean,isSendMsg : Boolean){
        var sn = snData.substring(3,snData.length)
        var snNew = ""
        for (i in sn.length-1 downTo 0 step 2) {
            snNew = snNew + sn.substring(i-1,i+1)+":"
        }
        snNew = snNew.substring(0,snNew.length-1)
        MyLogUtil.Log("1111","===获取蓝牙密钥 信息解密=="+ snNew +","+secretKey)
        BLEManager.getBleIns().setmYComMandCallback(object : BLEManager.ComMandCallback{
            override fun onCallback(RideData: String?) {
//                loadingDialogHelper!!.dismiss()
                MyLogUtil.Log("1111","==RideData===="+RideData)
//                ctrlListener(command, false)
                onDisMissListen()
                var resultData = ""
                if (RideData!!.length > 4){
                    resultData = RideData!!.substring(RideData!!.length-4,RideData!!.length-2)
                }
                MyLogUtil.Log("1111","==RideData===="+resultData)
                if (resultData.equals("00")){
//                    MyToastUtil.toast("操作成功")
//                    MyToastUtil.toast(ContextUtil.getContext().getString(R.string.send_success,command.name))

                    Apollo.emit(Constant.Event.BLE_CLOSECARSUC)

                    if (command.id == Constant.CtrlType.REQUEST_LOCATION){
                        command.id = Constant.CtrlType.REQUEST_LOCATION_VOICE
                        GoAction(snData,secretKey,command,false,false)
                    }

                    if (command.id == Constant.CtrlType.UNLOCK_BATTERY){
                        command.id = Constant.CtrlType.REQUEST_BATERY_OPEN_VOICE
                        GoAction(snData,secretKey,command,false,false)
                    }

                    if (command.id == Constant.CtrlType.LOCK_BATTERY){
                        command.id = Constant.CtrlType.REQUEST_BATERY_CLOSE_VOICE
                        GoAction(snData,secretKey,command,false,false)
                    }
                }else{
//                    MyToastUtil.toast("操作失败")
                    Apollo.emit(Constant.Event.BLE_CLOSECARFAIL)
//                    MyToastUtil.toast(ContextUtil.getContext().getString(R.string.op_failed_with_reason, command.name, resultData))
                }
            }

            override fun onConnectback(isSuc: Boolean) {
//                loadingDialogHelper!!.dismiss()
                MyLogUtil.Log("1111","==onConnectback===="+isSuc)
//                MyToastUtil.toast("连接失败")
                Apollo.emit(Constant.Event.BLE_CONFAIL)
                onDisMissListen()
            }

        })
        XiaoAnCommand.conmandValue = ""
        when(command.id){
            Constant.CtrlType.UNLOCK->{ BLEManager.getBleIns()!!.dataKey = "2C" }
            Constant.CtrlType.LOCK->{ BLEManager.getBleIns()!!.dataKey = "2B" }
            Constant.CtrlType.FIND_BIKE->{
                BLEManager.getBleIns()!!.dataKey = "28"
                XiaoAnCommand.conmandValue = "04"
            }
            Constant.CtrlType.REQUEST_LOCATION_VOICE->{
                BLEManager.getBleIns()!!.dataKey = "28"
                XiaoAnCommand.conmandValue = "05"
            }

            Constant.CtrlType.REQUEST_BATERY_OPEN_VOICE->{
                BLEManager.getBleIns()!!.dataKey = "28"
                XiaoAnCommand.conmandValue = "17"
            }

            Constant.CtrlType.REQUEST_BATERY_CLOSE_VOICE->{
                BLEManager.getBleIns()!!.dataKey = "28"
                XiaoAnCommand.conmandValue = "18"
            }
            Constant.CtrlType.REQUEST_LOCATION->{
                BLEManager.getBleIns()!!.dataKey = "70"
                XiaoAnCommand.conmandValue = "01"
            }

            Constant.CtrlType.UNLOCK_BATTERY->{ BLEManager.getBleIns()!!.dataKey = "34" }
            Constant.CtrlType.LOCK_BATTERY->{ BLEManager.getBleIns()!!.dataKey = "35" }
        }
        BLEManager.getBleIns()!!.tokenData = secretKey
        if (BLEManager.getBleIns()!!.curConnDevice != null && BLEManager.getBleIns()!!.no.equals(snNew) && BLEManager.getBleIns()!!.isConState){
            BLEManager.getBleIns()!!.sendMessage(XiaoAnCommand.getCommand(BLEManager.getBleIns()!!.dataKey,BLEManager.getBleIns()!!.tokenData),isCallBack,isSendMsg)
        }else{
            if (BLEManager.getBleIns()!!.isConnectIng){
                MyLogUtil.Log("1111","=========正在连接中=========")
                BLEManager.getBleIns()!!.setSendMsg(isSendMsg)
            }else{
                BLEManager.getBleIns()!!.disConnectDevice()
                //开始搜索
                BLEManager.getBleIns()!!.startDiscoveryDevice(snNew, secretKey,30000,isSendMsg)
            }
        }
    }
}