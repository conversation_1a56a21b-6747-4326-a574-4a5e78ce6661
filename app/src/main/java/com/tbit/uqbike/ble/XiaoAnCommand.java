package com.tbit.uqbike.ble;

import com.tbit.uqbike.utils.TypeConversion;

public class XiaoAnCommand {
    public static String conmandValue = "";
    public static byte[] getCommand(String dataKey,String tokenData){
//        String dataKey = "28";
//        String tokenData = "0A0A0505";
        String dataCont = tokenData+conmandValue;
        byte[] dataByByres = TypeConversion.hexString2Bytes(dataCont);
        String dataLength_Str = TypeConversion.intToHexString(dataByByres.length,1);
        int dataEnd = 0;
        int dataKey_Str = TypeConversion.hexToDecimal(dataKey);
        dataEnd = dataEnd + (int) dataKey_Str;
        dataEnd = dataEnd + (int) TypeConversion.hexToDecimal(dataLength_Str);
        for (int i = 0; i < dataByByres.length; i ++){
            dataEnd = dataEnd + dataByByres[i];
        }
//        MyLogUtil.Log("1111","===蓝牙命令=="+dataKey + dataLength_Str + dataCont + TypeConversion.intToHexString(dataEnd,1));
        byte[] command = TypeConversion.hexString2Bytes(dataKey + dataLength_Str + dataCont + TypeConversion.intToHexString(dataEnd,1));
        return command;
    }
}
