package com.tbit.uqbike.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.CouponAdapter
import com.tbit.uqbike.base.BaseFragment
import com.tbit.uqbike.bean.Coupon // Assuming Coupon data class exists
import android.widget.TextView

class Member1Frag : BaseFragment() {

    private lateinit var rvCoupons: RecyclerView
    private lateinit var llEmptyState: LinearLayout
    private lateinit var couponAdapter: CouponAdapter
    private lateinit var tvUnlockStatusTitle: TextView
    private lateinit var tvEquityDescContent: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.frag_member1, container, false)
    }

    private var MyView : View? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        this.MyView = view
        super.onViewCreated(view, savedInstanceState)

        rvCoupons = view.findViewById(R.id.rv_coupons_member1)
        llEmptyState = view.findViewById(R.id.ll_empty_state_member1)
        tvUnlockStatusTitle = view.findViewById(R.id.tv_member1_unlock_status_title)
        tvEquityDescContent = view.findViewById(R.id.tv_equity_desc_content_member1)

        setupRecyclerView()
    }

    private fun setupRecyclerView() {
        couponAdapter = CouponAdapter() // Initialize your adapter
        rvCoupons.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = couponAdapter
        }
    }

    fun updateCoupons(newCoupons: List<Coupon>) {
        if (!::couponAdapter.isInitialized) {
            // Adapter might not be initialized if view is not created yet or destroyed.
            // Handle this case, maybe log or queue the update.
            return
        }

        // Replace .clear() and .addAll() with direct assignment
        couponAdapter.source = newCoupons // Assuming 'source' is a 'var' in BaseAdapter
        couponAdapter.notifyDataSetChanged()

        if (newCoupons.isEmpty()) {
            llEmptyState.visibility = View.VISIBLE
            rvCoupons.visibility = View.GONE
        } else {
            llEmptyState.visibility = View.GONE
            rvCoupons.visibility = View.VISIBLE
        }
    }

    fun updateUnlockStatusTitle(textResId: Int, isVisible: Boolean) {
        if (!::tvUnlockStatusTitle.isInitialized) return
        if (isVisible && textResId != 0) {
            tvUnlockStatusTitle.setText(textResId)
            tvUnlockStatusTitle.visibility = View.VISIBLE
        } else {
            tvUnlockStatusTitle.visibility = View.GONE
        }
    }

    /**
     * 设置专属券包权益说明内容
     */
    fun setEquityDesc(desc: String?) {
        if (!::tvEquityDescContent.isInitialized) return
        if (desc.isNullOrEmpty()) {
            tvEquityDescContent.text = ""
        } else {
            tvEquityDescContent.text = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                android.text.Html.fromHtml(desc, android.text.Html.FROM_HTML_MODE_LEGACY)
            } else {
                android.text.Html.fromHtml(desc)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }

}