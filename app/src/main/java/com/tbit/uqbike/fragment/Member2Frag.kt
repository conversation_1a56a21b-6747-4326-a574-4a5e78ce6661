package com.tbit.uqbike.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.ImageView
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseFragment

class Member2Frag : BaseFragment() {
    // private lateinit var tvTitle: TextView
    private lateinit var tvCount: TextView
    private lateinit var tvDescription: TextView
    private lateinit var ivNonParkingIcon: ImageView
    private lateinit var tvUnlockStatusTitle: TextView
    private lateinit var tvNonParkingFormattedDesc: TextView
    private lateinit var tvEquityExplanationButton: TextView
    // ImageView for icon can be omitted if it's static from XML

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.frag_member2, container, false)
    }

    private var MyView : View? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        this.MyView = view
        super.onViewCreated(view, savedInstanceState)

        // tvTitle = view.findViewById(R.id.tv_non_parking_title)
        tvCount = view.findViewById(R.id.tv_non_parking_count)
        tvDescription = view.findViewById(R.id.tv_non_parking_description)
        ivNonParkingIcon = view.findViewById(R.id.iv_non_parking_icon)
        tvUnlockStatusTitle = view.findViewById(R.id.tv_member2_unlock_status_title)
        tvNonParkingFormattedDesc = view.findViewById(R.id.tv_non_parking_formatted_desc)
        tvEquityExplanationButton = view.findViewById(R.id.tv_equity_explanation_button)

        // title and count are likely set directly or via a more specific method if they vary beyond desc/lock
    }
    override fun onDestroy() {
        super.onDestroy()
    }

    fun updateNonParkingPermitInfo(title: String, countText: String, description: String) {
        // if (!::tvTitle.isInitialized) { 
        //     return
        // }
        // tvTitle.text = title
        tvCount.text = countText 
        tvDescription.text = description
    }

    fun updateContent(apiDescription: String, impunityCount: Int, isLocked: Boolean) {
        if (!::tvDescription.isInitialized || !::tvCount.isInitialized) { 
            return
        }
        tvDescription.text = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            android.text.Html.fromHtml(apiDescription, android.text.Html.FROM_HTML_MODE_LEGACY)
        } else {
            android.text.Html.fromHtml(apiDescription)
        }

        // Set the count text using s_vip_times
        val countText = "$impunityCount ${getString(R.string.s_vip_times)}"
        tvCount.text = countText
        tvCount.visibility = View.VISIBLE // Ensure it's visible

        // Always attempt to set and show the formatted description, regardless of isLocked status for this specific TextView
        if (::tvNonParkingFormattedDesc.isInitialized) {
            // If impunityCount is 0, it will display "...享受0次..."
            // If a different behavior is needed for impunityCount == 0 (e.g., hide), add a condition here.
            val formattedDescText = getString(R.string.s_vip_nop_desc, impunityCount)
            tvNonParkingFormattedDesc.text = formattedDescText
            tvNonParkingFormattedDesc.visibility = View.VISIBLE 
        }

        // TODO: Add UI changes based on isLocked for other elements like tvDescription, e.g., show a lock icon or overlay for tvDescription
        // The general description (tvDescription) might still reflect the locked state as per original TODO.
        if (isLocked) {
            // Example: tvDescription.text = getString(R.string.s_vip_locked_equity) // Or a specific lock message for the main description
        } else {
            // tvDescription.text = apiDescription; // Already set at the beginning of the function
        }
    }

    fun updateNonParkingIcon(iconResId: Int) {
        if (::ivNonParkingIcon.isInitialized) {
            if (iconResId != 0) {
                ivNonParkingIcon.setImageResource(iconResId)
                ivNonParkingIcon.visibility = View.VISIBLE
            } else {
                // Optionally hide the ImageView or set a default placeholder if iconResId is invalid (e.g. 0)
                // For now, let's assume 0 means no specific icon, maybe keep placeholder or hide
                 ivNonParkingIcon.visibility = View.GONE // Or set a generic placeholder
            }
        }
    }

    fun updateUnlockStatusTitle(textResId: Int, isVisible: Boolean) {
        if (!::tvUnlockStatusTitle.isInitialized) return
        if (isVisible && textResId != 0) {
            tvUnlockStatusTitle.setText(textResId)
            tvUnlockStatusTitle.visibility = View.VISIBLE
        } else {
            tvUnlockStatusTitle.visibility = View.GONE
        }
    }
}