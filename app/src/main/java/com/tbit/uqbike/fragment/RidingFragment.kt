package com.tbit.uqbike.fragment

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Vibrator
import android.provider.Settings
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.baidu.ar.it
import com.baidu.ar.no
import com.doule.database.CoroutinesUtil
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.gson.Gson
import com.lsxiao.apollo.core.Apollo
import com.lsxiao.apollo.core.annotations.Receive
import com.lsxiao.capa.CapaLayout
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.Glob
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.ChargeNewActivity
import com.tbit.uqbike.activity.MainActivity
import com.tbit.uqbike.activity.PicReturnActivity
import com.tbit.uqbike.activity.SearchPActivity
import com.tbit.uqbike.activity.UserCarActivity
import com.tbit.uqbike.activity.model.RidingModel
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.base.BaseFragment
import com.tbit.uqbike.ble.BleModelNew
import com.tbit.uqbike.ble.XiaoAnBleSdk
import com.tbit.uqbike.dialog.BackCarDialog
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.dialog.PowerPayDialog
import com.tbit.uqbike.dialog.RideCardInfoDialog
import com.tbit.uqbike.dialog.StopBomDialog
import com.tbit.uqbike.dialog.StopCenDialog
import com.tbit.uqbike.dialog.backCarAbnormalDialog
import com.tbit.uqbike.dialog.backCarCountDownDialog
import com.tbit.uqbike.dialog.util.MainDialogUtil
import com.tbit.uqbike.dialog.util.RidingDialogUtil
import com.tbit.uqbike.entity.ArrearsAmountData
import com.tbit.uqbike.entity.ArrearsData
import com.tbit.uqbike.entity.BillRulesData
import com.tbit.uqbike.entity.BleData
import com.tbit.uqbike.entity.CarInfoData
import com.tbit.uqbike.entity.OrderInfoRentalData
import com.tbit.uqbike.entity.RideCardNumData
import com.tbit.uqbike.entity.RidingOrderInfoData
import com.tbit.uqbike.entity.RidingOrderPrecompleteData
import com.tbit.uqbike.entity.RindingRemainTimeData
import com.tbit.uqbike.entity.getAreaDataOrder
import com.tbit.uqbike.entity.getConfigByRidingData
import com.tbit.uqbike.entity.powerAmoutData
import com.tbit.uqbike.map.bean.Location
import com.tbit.uqbike.mvp.model.BikeModel
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.model.ParkPointModel
import com.tbit.uqbike.qrcode.CommonUtils
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.EventModel
import com.tbit.uqbike.resqmodel.OrderModel
import com.tbit.uqbike.roundview.RoundLinearLayout
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.BleUtils
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.NetUtils
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.startActivity
import org.json.JSONObject
import java.lang.NullPointerException
import com.cn.maintenance.network.UserRClient
import com.tbit.uqbike.bean.MemberInfoResponse
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers


@RequiresApi(Build.VERSION_CODES.O)
class RidingFragment : BaseFragment() {
    var onRidingListener = {_: String,_: Boolean,_: Boolean ->} // 订单号，是否用户手动结束订单，长租或短租
    var onRidingBatterListener = {_: String,_: Int ->}
    var onRidingOpenBleListener = {}
    var onRidingKFListener = {}
    var onPowerListener = {_: String ->}
    var orderNo = "";
    var resultDataConfig : getConfigByRidingData ?= null
    var stopState = "1"// 1继续用车 2临停
    var batter = 0//当前电量
    var xiaoAnBleSdk : XiaoAnBleSdk ? = null
    var isOpenUser = true //是否是开锁本人
    var CarLatlng : LatLng? = null //车辆位置
    var ParkLatlng : LatLng? = null //p点位置
    var park_id = 0 //p点ID
    var stopCenDiag : StopCenDialog? = null
    var stopDiag : StopBomDialog? = null
    var powerNum = 0 //可换电次数
    var powerNum_U = 0 //换电次数
    var isPowering = false// 是否正在换电中
    var backCarDiog : BackCarDialog? = null
    var  rentalResultData : OrderInfoRentalData? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.layout_bike_riding, container, false)
    }

    private var rentalTime = 0L// 长租 距离订单结束还需要多长时间
    private var view : View? = null
    fun getHight() : Int{
        var hight = 0
        if (view != null){
//            hight = view!!.height
            try {
                if (activity != null){
                    hight = (CommonUtils.getScreenHeight(activity)/5).toInt()
                }
//                MyLogUtil.Log("1111","========getHight========="+hight+","+ CommonUtils.getScreenHeight(activity))
            }catch (e : UnsupportedOperationException){}
        }
        return hight
    }
    @RequiresApi(Build.VERSION_CODES.O)
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.view = view

//        ComModel.getBleSecretKey("********").subscribeBy(
//            onNext = {
//                MyLogUtil.Log("1111","===获取蓝牙密钥 信息=="+it.toString())
//                var resultDataBleNew = Gson().fromJson(it.toString(), BleData::class.java)
//                var sn = AppUtil.getSicData(resultDataBleNew.sn)!!
//                var secretKey = AppUtil.getSicData(resultDataBleNew.key)!!.replace(" ","")
//                MyLogUtil.Log("1111","===获取蓝牙密钥 信息=="+ sn+","+secretKey)
//                CoroutinesUtil.launchMain {
//                    xiaoAnBleSdk = XiaoAnBleSdk()
//                    xiaoAnBleSdk?.setAutoSendMsg(true)
//                    xiaoAnBleSdk?.Init(activity,sn,secretKey)
//                }
//            }, onError = {}
//        ).toCancelable()

        view?.findViewById<CapaLayout>(R.id.capaLayout)?.toContent()
        view.findViewById<RoundTextView>(R.id.tv_riding_repay)?.clickDelay {
            if (isRental_MyOrder){
                if (!isPowering){
                    val properties = JSONObject()
                    MDUtil.clickEvent("vehicle_return_click",properties)
                    //                MyToastUtil.toast("还车")
                    var MylatLng = OrderModel.getLocalDataByMap()
                    backCar(true,MylatLng,ParkLatlng!!)
                }
            }else{
                loadingDialogHelper!!.show {  }
                val properties = JSONObject()
                MDUtil.clickEvent("return_click",properties)

                var location = LocationModel.lastLocation
                var latLng = LatLng()
                try {
                    latLng.lat = location!!.latitude
                    latLng.lng = location!!.longitude
                }catch (e : NullPointerException){
                    location = Location(0f, 0f, 0.0, 0.0, -1,"","")
                    latLng.lat = 0.0
                    latLng.lng = 0.0
                }
                var MylatLng = latLng
                if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
                    latLng = GPSUtil.bd09_To_gps84(location!!.latitude,location!!.longitude)
                }
                latLng.lat = GPSUtil.retain6(latLng.lat)
                latLng.lng = GPSUtil.retain6(latLng.lng)

                ParkPointModel.getNearParkPointsOrder(orderNo,latLng.lat, latLng.lng)
                    .subscribeBy(
                        onNext = {
                            MyLogUtil.Log("1111","====获取附近停车点=="+it.toString())
                            if (!it.toString().equals("[]")){
                                val resultData = Gson().fromJson(it.toString(), getAreaDataOrder::class.java)
                                if (resultData != null){
                                    var latlngFlag = resultData.flag
                                    if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
                                        var dataFlag = GPSUtil.gps84_To_bd09(latlngFlag.lat,latlngFlag.lng)
                                        latlngFlag.lat = dataFlag.lat
                                        latlngFlag.lng = dataFlag.lng
                                    }else{
                                        var dataFlag = GPSUtil.gps84_To_Gcj02_new(latlngFlag.lat,latlngFlag.lng)
                                        latlngFlag.lat = dataFlag.lat
                                        latlngFlag.lng = dataFlag.lng
                                    }
                                    backCar(true,MylatLng,latlngFlag)
                                }else{
                                    backCar(false,MylatLng,LatLng())
                                }
                            }else{
                                backCar(false,MylatLng,LatLng())
                            }
                        },
                        onError = {
                            val errMsg = ErrHandler.getErrMsg(it)
                            backCar(false,MylatLng,LatLng())
                        }
                    ).toCancelable()
            }
        }
        view.findViewById<RoundTextView>(R.id.btn_riding_stop)?.clickDelay {
            if (isRental_MyOrder){
                if (!isPowering){
                    var carRental = 1  //1开锁  2锁车
                    if (view.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text.toString().equals(getString(R.string.s_clockcar))){
                        carRental = 2
                        val properties = JSONObject()
                        MDUtil.clickEvent("temporary_lock_click",properties)
                    }else{
                        val properties = JSONObject()
                        MDUtil.clickEvent("unlock_click",properties)
                    }
                    loadingDialogHelper!!.show {  }
                    OrderModel.CarlockByRental(orderNo,carRental.toString())
                        .subscribeBy(
                            onNext = {
                                loadingDialogHelper!!.dismiss()
                                MyLogUtil.Log("1111","===获取 开关锁信息=="+it.toString())
                                if(carRental.toString().equals("1")){
                                    view.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text = getString(R.string.s_clockcar)
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.text = getString(R.string.s_car_uclock)
                                    EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!, no,
                                        EventModel.CAR_ULOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_RENTAL_OPEN).subscribeBy(onNext = {}).toCancelable()
                                }else{
                                    view.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text = getString(R.string.unlock)
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.text = getString(R.string.s_car_lock)
                                    EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!, no,
                                        EventModel.CAR_LOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_RENTAL_CLOSE).subscribeBy(onNext = {}).toCancelable()
                                }
                            },
                            onError = {
                                loadingDialogHelper!!.dismiss()
//                            val errCode = ErrHandler.getErrCode(it)
                                val errMsg = ErrHandler.getErrMsg(it)
                                if(carRental.toString().equals("1")){
                                    EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!, no,
                                        EventModel.CAR_ULOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_RENTAL_OPEN_FAIL+"("+errMsg+")")
                                        .subscribeBy(onNext = {}).toCancelable()
                                }else{
                                    EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!, no,
                                        EventModel.CAR_LOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_RENTAL_CLOSE_FAIL+"("+errMsg+")")
                                        .subscribeBy(onNext = {}).toCancelable()
                                }
                            }
                        ).toCancelable()
                }
            }else{
                stopState = "1"
                if (view.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text!!.equals(ResUtil.getString(R.string.temporary_stop))){
                    stopState = "2"
                    if (!SpUtil.getInstance().find(Constant.SpKey.SP_NEWUSERCAR).isNullOrEmpty()){
                        if (activity != null){
                            activity!!.startActivity<UserCarActivity>(UserCarActivity.TYPE to UserCarActivity.TYPE_STOP,
                                UserCarActivity.UNIT to (stopTime/60).toString()+getString(R.string.min))
                        }
                        SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_NEWUSERCAR,"")
                        return@clickDelay
                    }
                }
                if (stopState.equals("2")){
                    stopDiag = StopBomDialog(loadingDialogHelper!!,(stopTime/60).toString())
                    stopDiag?.onSureListener = {
                        useCar()
                        val properties = JSONObject()
                        MDUtil.clickEvent("temporary_lock_car_confirmation",properties)
                    }
                    lifecycleDialogHelper.show(stopDiag!!)
                    val properties = JSONObject()
                    MDUtil.clickEvent("temporary_parking_click",properties)
                }else{
                    useCar()
                    val properties = JSONObject()
                    MDUtil.clickEvent("continue_cycling_click",properties)
                }
            }
        }
        view.findViewById<RoundLinearLayout>(R.id.rly_rantal_xc)?.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("find_car_click",properties)
            loadingDialogHelper!!.show {  }
            OrderModel.CarlockByRental(orderNo,"3")
                .subscribeBy(
                    onNext = { loadingDialogHelper!!.dismiss() },
                    onError = {loadingDialogHelper!!.dismiss()}
                ).toCancelable()
        }
        view.findViewById<RoundLinearLayout>(R.id.rly_rantal_gz)?.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("fault_click",properties)
            MainDialogUtil.kfDig(loadingDialogHelper!!,lifecycleDialogHelper)
        }
        view.findViewById<RoundLinearLayout>(R.id.rly_rantal_zc)?.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("navigation_click",properties)
            if (CarLatlng != null){
                var latLngMy = OrderModel.getLocalDataByMap()
                if (activity == null) return@clickDelay
                AppUtil.startNav(activity!!,latLngMy,CarLatlng!!,false)
            }
        }

        view?.findViewById<ImageView>(R.id.img_rental_go)?.clickDelay {
            if (ParkLatlng != null){
                var latLngMy = OrderModel.getLocalDataByMap()
                if (activity == null) return@clickDelay
                AppUtil.startNav(activity!!,latLngMy,ParkLatlng!!,false)
            }
        }

        view?.findViewById<TextView>(R.id.tv_riding_hint)?.clickDelay {
            if (ParkLatlng != null && isRental_MyOrder){
                var latLngMy = OrderModel.getLocalDataByMap()
                if (activity == null) return@clickDelay
                AppUtil.startNav(activity!!,latLngMy,ParkLatlng!!,false)
            }
        }

        view.findViewById<RoundTextView>(R.id.rt_riding_power)?.clickDelay {
            val properties = JSONObject()
            properties.put("number_battery_replacements",powerNum_U.toString())
            MDUtil.clickEvent("contact_battery_replacement_click",properties)

            if (isPowering){
                MainDialogUtil.kfDig(loadingDialogHelper!!,lifecycleDialogHelper,true)
            }else{
                if (view.findViewById<RoundTextView>(R.id.tv_riding_repay)?.text.toString().equals(getString(R.string.temporary_stop))){
                    MyToastUtil.toast(getString(R.string.s_power_clock))
                    return@clickDelay
                }
                if(rentalResultData!=null&&rentalResultData!!.vehicle_status==1){
                    MyToastUtil.toast(getString(R.string.s_power_clock))
                    return@clickDelay
                }
                loadingDialogHelper!!.show {  }
                OrderModel.getOrderPower(orderNo).subscribeBy(
                    onNext = {
                        loadingDialogHelper!!.dismiss()
                        MyLogUtil.Log("1111","===获取 换电费用信息=="+it.toString())
                        val resultData = Gson().fromJson(it.toString(), powerAmoutData::class.java)
                        if (resultData.amount == 0f){
                            if (activity != null){
                                RidingDialogUtil.showPowerDialog(activity!! as BaseActivity,orderNo,RidingDialogUtil.TYPE_POWER_1,0,"",resultData,
                                    onNotify = {
                                        if (it.equals(Constant.SUC)){
                                            getOrderInfo(orderNo,false)
                                            MainDialogUtil.kfDig(loadingDialogHelper!!,lifecycleDialogHelper,true)
                                        }
                                    })
                            }
                        }else{
                            if (powerNum > 0){
                                if (activity != null){
                                    RidingDialogUtil.showPowerDialog(activity!! as BaseActivity,orderNo,RidingDialogUtil.TYPE_POWER_2,powerNum,
                                        AppUtil.getFloat2(resultData.amount),resultData, onNotify = {})
                                }
                            }else{
                                if (activity != null){
                                    RidingDialogUtil.showPowerDialog(activity!! as BaseActivity,orderNo,RidingDialogUtil.TYPE_POWER_3,powerNum,
                                        AppUtil.getFloat2(resultData.amount),resultData,onNotify = {})
                                }
                            }
                        }
                    },
                    onError = {
                        loadingDialogHelper!!.dismiss()
                    }
                ).toCancelable()
            }
        }


        view.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.clickDelay {
            if (activity == null) return@clickDelay
            activity!!.startActivity<ChargeNewActivity>()
            val properties = JSONObject()
            MDUtil.clickEvent("recharge_overdue_fees_click",properties)
        }
        view.findViewById<RoundTextView>(R.id.rv_riding_searchp)?.clickDelay {
            if (activity == null) return@clickDelay
            activity!!.startActivity<SearchPActivity>()
        }
        getConfigByRiding(false,isRental_MyOrder)
        view.findViewById<RoundTextView>(R.id.rv_riding_searchp)?.postDelayed({AutoTask()},1000)

//        bleColseCar("S1791953",true)
//        MyLogUtil.Log("1111","====蓝牙权限"+hasBluetoothPermission()+","+BleUtils.isBleOpened())
    }

    /**
     * 临停 或 继续用车
     */
    fun useCar(){
        loadingDialogHelper!!.show {  }
        OrderModel.sumitTemplock(orderNo,stopState)
            .subscribeBy(
                onNext = {
                    loadingDialogHelper!!.dismiss()
                    MyLogUtil.Log("1111","===获取 临时停车信息=="+it.toString())
//                        val resultData: RidingOrderPrecompleteData = Gson().fromJson(it.toString(), RidingOrderPrecompleteData::class.java)
                    if(stopState.equals("1")){
                        view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text = ResUtil.getString(R.string.temporary_stop)
//                            BleModelNew.continueRide()

                        carState = 0
                        if(orderState == 0 && carState == 0){
                            view?.findViewById<LinearLayout>(R.id.ly_ridingorder_hint)?.visibility = View.GONE
                        }

                        EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!, no,
                            EventModel.CAR_ULOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_U_LSTC).subscribeBy(onNext = {}).toCancelable()
                    }else{
                        view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text = ResUtil.getString(R.string.s_riding_qx)

                        stopCenDiag = StopCenDialog(stopTime.toString())
                        stopCenDiag?.onreBackListener={
                            view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.performClick()
                        }
                        stopCenDiag?.onRidingListener={
                            view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.performClick()
                        }
                        stopCenDiag?.onDissListener = {
                            endOrder(false)
                        }
                        lifecycleDialogHelper.show(stopCenDiag!!)

//                            BleModelNew.tempStop()
                        EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                            EventModel.CAR_LOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_LSTC).subscribeBy(onNext = {}).toCancelable()
                    }
                    getOrderInfo(orderNo,true)
                },
                onError = {loadingDialogHelper!!.dismiss()}
            ).toCancelable()
    }
    fun backCarInfo(resultData: RidingOrderPrecompleteData, isShowGop: Boolean, MylatLng: LatLng, latlngFlag: LatLng, hasImpunity: Boolean, isInPPoint: Boolean) {
        var content = ResUtil.getString(R.string.s_riding_repay)
        var unit = AppUtil.getFloat2(resultData.dispatch_amount).toString() + resultData.currency
        var spannableString = AppUtil.getSpanStr(unit, content, R.color.c_y)
        // 仅保留异常弹窗的免罚文案切换逻辑
        if (resultData.pre_complete_status != 1 && resultData.pre_complete_status != 11) {
            if (resultData.hasImpunity && !resultData.isInPPoint && isRental_MyOrder) {
                // 异常弹窗专属：有免罚权益且不在P点，切换文案
                content = getString(R.string.s_impunity_tip)
                spannableString = SpannableString(content)
            }
            if (content.contains(unit.toString())) {
                var data1 = spannableString.split(unit)[0].length
                if (activity != null) {
                    spannableString.setSpan(ForegroundColorSpan(activity!!.resources.getColor(R.color.c_y)), data1, data1+unit.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                }
            }
        }
        if (isRental_MyOrder) {
            var btn_sure = ResUtil.getString(R.string.ok)
            var otherPcon = ""
            if (resultData.pre_complete_status == 1 || resultData.pre_complete_status == 11) {
                btn_sure = ResUtil.getString(R.string.ok)
                endOrder(true)
                val properties = JSONObject()
                MDUtil.clickEvent("confirm_return_car_click", properties)
            } else {
                otherPcon = getString(R.string.s_p_cont)
                resultData.orderNo = orderNo
                if (activity != null) {
                    backCarAbnormalDialog.Builder(activity)
                        .setTitle(ResUtil.getString(R.string.dialog_tip))
                        .setSpanContent(spannableString)
                        .setLeftText(ResUtil.getString(R.string.cancel))
                        .setRightText(btn_sure)
                        .setCanceledOnOutside(true)
                        .setContentp(otherPcon)
                        .setOrderData(resultData)
                        .setShowGoP(true)
                        .setOnBomCllickListener {
                            MyLogUtil.Log("1111", "====前往附近P点")
                            val properties = JSONObject()
                            MDUtil.clickEvent("Point_P_retracted_click", properties)
                            AppUtil.startNav(activity!!, MylatLng, latlngFlag, false)
                        }
                        .setClickListen(object : backCarAbnormalDialog.TwoSelDialog {
                            override fun leftClick() {
                                val properties = JSONObject()
                                MDUtil.clickEvent("cancel_return_click", properties)
                            }
                            override fun rightClick(orderstate: Int) {
                                val properties = JSONObject()
                                MDUtil.clickEvent("forced_return_car_click", properties)
                                if (orderstate == 5 || orderstate == 15 || orderstate == 1 || orderstate == 11) {
                                    endOrder(true)
                                } else {
                                    activity!!.startActivity(PicReturnActivity.createIntent(activity!!, SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!, orderNo))
                                }
                            }
                        }).build().show()
                }
            }
        } else {
            if ((resultData.pre_complete_status == 2 || resultData.pre_complete_status == 12) && isShowGop) {
                backCarDiog = BackCarDialog(loadingDialogHelper!!, orderNo, resultData.pre_complete_status, true, hasImpunity, isInPPoint)
                backCarDiog?.onGoPListener = { lat, lng ->
                    val properties = JSONObject()
                    MDUtil.clickEvent("Point_P_retracted_click", properties)
                    MyLogUtil.Log("1111", "====前往附近P点")
                    if (lat == 0.0 || lng == 0.0) {
                        if (activity != null) {
                            AppUtil.startNav(activity!!, MylatLng, latlngFlag, false)
                        }
                    } else {
                        if (activity != null) {
                            AppUtil.startNav(activity!!, MylatLng, LatLng(lat, lng), false)
                        }
                    }
                }
                backCarDiog?.onSureListener = {
                    CouponidList.clear()
                    CouponidList.addAll(it)
                    endOrder(true)
                }
                lifecycleDialogHelper.show(backCarDiog!!)
            } else {
                backCarDiog = BackCarDialog(loadingDialogHelper!!, orderNo, resultData.pre_complete_status, false, hasImpunity, isInPPoint)
                backCarDiog?.onSureListener = {
                    CouponidList.clear()
                    CouponidList.addAll(it)
                    endOrder(true)
                }
                lifecycleDialogHelper.show(backCarDiog!!)
            }
        }
    }
    /**
     * 还车 预结算
     * @param isShowGop 是否显示 附近停车点
     * @param MylatLng 我的位置
     * @param latlngFlag p点位置
     */
    fun backCar(isShowGop: Boolean, MylatLng: LatLng, latlngFlag: LatLng) {
        // 先判断是否在P点
        val isInP = isInPPoint(MylatLng.lat, MylatLng.lng)
        
        if (!isInP) {
            // 不在P点,判断是否有免罚权益
            isNonPImpunityActive { hasImpunity ->
                // 根据权益状态获取预结算信息
                getPrecompleteInfo(isShowGop, MylatLng, latlngFlag, hasImpunity)
            }
        } else {
            // 在P点,直接获取预结算信息
            getPrecompleteInfo(isShowGop, MylatLng, latlngFlag, false)
        }
    }

    private fun getPrecompleteInfo(isShowGop: Boolean, MylatLng: LatLng, latlngFlag: LatLng, hasImpunity: Boolean) {
        val isInP = isInPPoint(MylatLng.lat, MylatLng.lng)
        if (isRental_MyOrder) {
            loadingDialogHelper!!.show { }
            OrderModel.getPrecompleteByRental(orderNo)
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper!!.dismiss()
                        val resultData: RidingOrderPrecompleteData = Gson().fromJson(it.toString(), RidingOrderPrecompleteData::class.java)
                        resultData.hasImpunity = hasImpunity
                        resultData.isInPPoint = isInP
                        backCarInfo(resultData, isShowGop, MylatLng, latlngFlag, hasImpunity, isInP)
                    },
                    onError = {
                        loadingDialogHelper!!.dismiss()
                        val errCode = ErrHandler.getErrCode(it)
                        if(errCode == Constant.ErrCode.TIMEOUT || errCode == Constant.ErrCode.FAILED) {
                            MyToastUtil.toast(getString(R.string.network_error))
                        }
                    }
                ).toCancelable()
        } else {
            OrderModel.getPrecomplete(orderNo)
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper!!.dismiss()
                        val resultData: RidingOrderPrecompleteData = Gson().fromJson(it.toString(), RidingOrderPrecompleteData::class.java)
                        resultData.hasImpunity = hasImpunity
                        resultData.isInPPoint = isInP
                        backCarInfo(resultData, isShowGop, MylatLng, latlngFlag, hasImpunity, isInP)
                    },
                    onError = {
                        loadingDialogHelper!!.dismiss()
                        val errCode = ErrHandler.getErrCode(it)
                        if(errCode == Constant.ErrCode.TIMEOUT || errCode == Constant.ErrCode.FAILED) {
                            MyToastUtil.toast(getString(R.string.network_error))
                        }
                    }
                ).toCancelable()
        }
    }

    @Receive(Constant.Event.EVENT_PICRETURN)
    fun getImgData(){
        endOrder(true)
    }
    @Receive(Constant.Event.NOTIFYPOWER)
    fun NOTIFYPOWER(){
        getOrderInfo(orderNo,false)
        MainDialogUtil.kfDig(loadingDialogHelper!!,lifecycleDialogHelper,true)
    }
    var CouponidList = ArrayList<String>()//选择的优惠券ID
    private var isUserEndBle = false //是否手动结束
    /**
     * 结束订单
     * @param isUserEnd 是否手动结束
     */
    fun endOrder(isUserEnd : Boolean){
        if (orderNo.isNullOrEmpty()) return
        isUserEndBle = isUserEnd
        loadingDialogHelper!!.show {  }
//        if(BleUtils.isBleOpened() && hasBluetoothPermission()){
//            bleColseCar(SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,false,isUserEnd)
//        }
        if (isRental_MyOrder){
            OrderModel.putPrecompleteByRental(orderNo,Glob.imgData)
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper!!.dismiss()
                        view?.findViewById<LinearLayout>(R.id.ly_ridingorder_hint)?.visibility = View.GONE
                        view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.visibility = View.GONE
                        MyLogUtil.Log("1111","===获取 结算订单 信息=="+it.toString())
                        onRidingListener(orderNo,isUserEnd,isRental_MyOrder)

                        if (isUserEnd){
                            EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                                EventModel.CAR_LOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_HC).subscribeBy(onNext = {}).toCancelable()
                        }else{
                            EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                                EventModel.CAR_LOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_HC_SYS).subscribeBy(onNext = {}).toCancelable()
                        }
                        endOrderState()
//                    BleModelNew.lock()
                    },
                    onError = {
                        val errCode = ErrHandler.getErrCode(it)
                        val errMsg = ErrHandler.getErrMsg(it)
                        returnCarNetErr(errCode,errMsg,isUserEnd)
                    }
                ).toCancelable()
        }else{
            OrderModel.putPrecomplete(orderNo,CouponidList)
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper!!.dismiss()
                        view?.findViewById<LinearLayout>(R.id.ly_ridingorder_hint)?.visibility = View.GONE
                        view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.visibility = View.GONE
                        MyLogUtil.Log("1111","===获取 结算订单 信息=="+it.toString())
                        onRidingListener(orderNo,isUserEnd,isRental_MyOrder)

                        if (isUserEnd){
                            EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                                EventModel.CAR_LOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_HC).subscribeBy(onNext = {}).toCancelable()
                        }else{
                            EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                                EventModel.CAR_LOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_HC_SYS).subscribeBy(onNext = {}).toCancelable()
                        }

                        endOrderState()
//                    BleModelNew.lock()
                    },
                    onError = {
                        val errCode = ErrHandler.getErrCode(it)
                        val errMsg = ErrHandler.getErrMsg(it)
                        returnCarNetErr(errCode,errMsg,isUserEnd)
                    }
                ).toCancelable()
        }
    }
    fun returnCarNetErr(errCode : Int,errMsg : String,isUserEnd : Boolean){
        if(errCode == Constant.ErrCode.ORDER_PAYMENT || errCode == Constant.ErrCode.ORDER_PAYCOMPANY){
            loadingDialogHelper!!.dismiss()
            view?.findViewById<LinearLayout>(R.id.ly_ridingorder_hint)?.visibility = View.GONE
            view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.visibility = View.GONE
            onRidingListener(orderNo,isUserEnd,isRental_MyOrder)

            if (isUserEnd){
                EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                    EventModel.CAR_LOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_HC).subscribeBy(onNext = {}).toCancelable()
            }else{
                EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                    EventModel.CAR_LOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_HC_SYS).subscribeBy(onNext = {}).toCancelable()
            }

            endOrderState()
//                        BleModelNew.lock()
        }else if(errCode == Constant.ErrCode.NET_CLOSECAR_FAIL || errCode == Constant.ErrCode.TIMEOUT || errCode == Constant.ErrCode.FAILED ||
            errCode == Constant.ErrCode.NET_CLOSECAR_FAIL_type1 || errCode == Constant.ErrCode.NET_CLOSECAR_FAIL_type2 ||
            errCode == Constant.ErrCode.NET_CLOSECAR_FAIL_type3){
            var remarkInfo = ""
            var type = 0
            if(errCode == Constant.ErrCode.NET_CLOSECAR_FAIL_type1 || errCode == Constant.ErrCode.NET_CLOSECAR_FAIL_type2 ||
                errCode == Constant.ErrCode.NET_CLOSECAR_FAIL_type3){
                type = errCode
                remarkInfo = "("+errMsg+")"
            }
            if (isUserEnd){
                EventModel.sumitCarEventByNet(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                    EventModel.CAR_LOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_HC_FAIL+remarkInfo,type).subscribeBy(onNext = {}).toCancelable()
            }else{
                EventModel.sumitCarEventByNet(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                    EventModel.CAR_LOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_HC_FAIL_SYS+remarkInfo,type).subscribeBy(onNext = {}).toCancelable()
            }

//            if(BleUtils.isBleOpened() && hasBluetoothPermission()){
//                isUseBleColse = true
//                bleColseCar(SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,true,isUserEnd)
//                EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
//                    EventModel.CAR_LOCK, EventModel.CAR_CHAN_NET,EventModel.CAR_REMARK_HC_BYBLE).subscribeBy(onNext = {}).toCancelable()
//            }else{
//                orderBleEndResq(OrderModel.Typt_EndOrder_foucs)
//            }
            orderBleEndResq(OrderModel.Typt_EndOrder_foucs)
        }else{
            loadingDialogHelper!!.dismiss()
            Apollo.emit(Constant.Event.EVENT_ORDERFISHERR)
        }
    }
    var resultDataBle : BleData? = null //蓝牙密钥数据
    var isUseBleColse = false//是否用蓝牙关锁
    fun hasBluetoothPermission(): Boolean {
        var isHasPer = true
        if (SpUtil.Companion.getInstance().find(Constant.SpKey.SP_BLE_PLE).isNullOrEmpty()){
            isHasPer = false
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 检查ACCESS_FINE_LOCATION权限
                (ContextCompat.checkSelfPermission(ContextUtil.getContext(), Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED // 或者检查ACCESS_COARSE_LOCATION权限
                        || ContextCompat.checkSelfPermission(ContextUtil.getContext(), Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED)
            } else {
                // 在API 23以下，直接检查蓝牙权限已经足够
                ContextCompat.checkSelfPermission(ContextUtil.getContext(), Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
            }
        }
        return isHasPer
    }
    //还车成功  重置状态
    fun endOrderState(){
        rentalTime = 0L
        park_id = 0
        orderNo = "";
        Glob.imgData.clear()
        allow_arrears_amount_now = 0f
        isEnd = true
        isArrearSwith = false
        isUseBleColse = false
        resultDataBle = null
        SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_VECHNO, "");
        CouponidList.clear()
        if (stopDiag != null) stopDiag?.dismissAllowingStateLoss()
        if (stopCenDiag != null) stopCenDiag?.dismissAllowingStateLoss()
        if (backCarDiog != null) backCarDiog?.dismissAllowingStateLoss()
        Apollo.emit(Constant.Event.EVENT_ORDERFISH)
        if (xiaoAnBleSdk != null){
            xiaoAnBleSdk?.disCon()
        }
        BleModelNew.disconnect()
    }

    /**
     * 蓝牙 关锁
     * @param isAutoSendMsg 连接成功后 是否直接发消息 ，否的话就是进行连接不发命令
     * @param isUserEnd 是否手动关锁
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun bleColseCar(vehicleNo : String,isAutoSendMsg : Boolean,isUserEnd : Boolean){
        if (resultDataBle != null){
            ConBle(resultDataBle,isAutoSendMsg)
        }else{
            if (!vehicleNo.isNullOrEmpty()){
                ComModel.getBleSecretKey(vehicleNo).subscribeBy(
                    onNext = {
                        MyLogUtil.Log("1111","===获取蓝牙密钥 信息=="+ it.toString())
                        val resultData = Gson().fromJson(it.toString(), BleData::class.java)
                        ConBle(resultData,isAutoSendMsg)
                        EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                            EventModel.CAR_LOCK, EventModel.CAR_CHAN_BLE,EventModel.CAR_REMARK_BLE_SUC).subscribeBy(onNext = {}).toCancelable()
                    }, onError = {
                        EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                            EventModel.CAR_LOCK, EventModel.CAR_CHAN_BLE,EventModel.CAR_REMARK_BLE_FAIL).subscribeBy(onNext = {}).toCancelable()

                        orderBleEndResq(OrderModel.Typt_EndOrder_foucs)

//                        Apollo.emit(Constant.Event.EVENT_ORDERFISHERR)
//                        if (isAutoSendMsg){
//                            loadingDialogHelper!!.dismiss()
//                            MyToastUtil.toast(getString(R.string.s_returnfail_ble)+"(01)")
//                        }
                    }
                ).toCancelable()
            }
        }
    }

    /**
     * 连接蓝牙
     * @param resultData 蓝牙连接数据
     * @param isAutoSendMsg 连接成功后是否自动发送消息
     */
    fun ConBle(resultData : BleData?,isAutoSendMsg : Boolean){
        var secretKey = ""
        var sn = ""
        if (resultData != null){
            if (resultData.mc.equals("xiaoan")){
                sn = AppUtil.getSicData(resultData.sn)!!
                secretKey = AppUtil.getSicData(resultData.key)!!.replace(" ","")
                MyLogUtil.Log("1111","===获取蓝牙密钥 信息=="+ sn+","+secretKey)
//                var command = Command(Constant.CtrlType.LOCK,ResUtil.getString(R.string.lock),EventModel.CAR_CHAN_BLE)
//                XiaoAnBleUtil.GoAction(sn,secretKey,command,true,isAutoSendMsg)
                if ((xiaoAnBleSdk != null && xiaoAnBleSdk?.coning!!) || (xiaoAnBleSdk != null &&xiaoAnBleSdk?.con!!)){
                    xiaoAnBleSdk?.setAutoSendMsg(isAutoSendMsg)
                }else{
                    CoroutinesUtil.launchMain {
                        xiaoAnBleSdk = XiaoAnBleSdk()
                        xiaoAnBleSdk?.setAutoSendMsg(isAutoSendMsg)
                        xiaoAnBleSdk?.Init(activity,sn,secretKey)
                    }
                }
            }else{
                if ((BleModelNew!= null && BleModelNew.isConing) || (BleModelNew!= null && BleModelNew.isCon)){
                    MyLogUtil.Log("1111","=========正在连接中=========")
                    BleModelNew.isLockAuto = isAutoSendMsg
                }else{
                    BleModelNew.connect(String.format("%09d", AppUtil.getSicData(resultData.sn)!!.toInt()),
                        AppUtil.getSicData(resultData.key)!!.replace(" ",""),isAutoSendMsg)
                }
            }
        }else{
            if (isAutoSendMsg){
                orderBleEndResq(OrderModel.Typt_EndOrder_foucs)
//                loadingDialogHelper!!.dismiss()
//                Apollo.emit(Constant.Event.EVENT_ORDERFISHERR)
//                MyToastUtil.toast(getString(R.string.s_returnfail_ble)+"(01)")
            }
        }
    }
    @Receive(Constant.Event.BLE_CLOSECARSUC)
    fun BleEndOrder(){
        MyLogUtil.Log("1111","======蓝牙关锁成功=====")
        EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
            EventModel.CAR_LOCK, EventModel.CAR_CHAN_BLE,EventModel.CAR_REMARK_RETURNSUC_BLE).subscribeBy(onNext = {}).toCancelable()

        orderBleEndResq(OrderModel.Typt_EndOrder_normal)
    }
    fun orderBleEndResq(reason : Int){
        OrderModel.putPrecompleteByBle(orderNo,Glob.imgData,reason,CouponidList)
            .subscribeBy(
                onNext = {
                    loadingDialogHelper!!.dismiss()
                    MyLogUtil.Log("1111","===获取 蓝牙 结束订单 信息=="+it.toString())
                    view?.findViewById<LinearLayout>(R.id.ly_ridingorder_hint)?.visibility = View.GONE
                    view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.visibility = View.GONE
                    EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                        EventModel.CAR_LOCK, EventModel.CAR_CHAN_BLE,EventModel.CAR_REMARK_RETURNSUC_BLE_SUMIT_SUC).subscribeBy(onNext = {}).toCancelable()
                    onRidingListener(orderNo,isUserEndBle,isRental_MyOrder)
                    endOrderState()
                },
                onError = {
                    loadingDialogHelper!!.dismiss()
                    val errCode = ErrHandler.getErrCode(it)
                    Apollo.emit(Constant.Event.EVENT_ORDERFISHERR)
                    EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                        EventModel.CAR_LOCK, EventModel.CAR_CHAN_BLE,EventModel.CAR_REMARK_RETURNSUC_BLE_SUMIT_FAIL).subscribeBy(onNext = {}).toCancelable()
                }
            ).toCancelable()
    }
    @Receive(Constant.Event.BLE_CLOSECARFAIL)
    fun BleEndOrderFail(){
        MyLogUtil.Log("1111","======蓝牙关锁失败=====")
        BleReBack(0)
    }

    @Receive(Constant.Event.BLE_CONFAIL_AUTO)
    fun BLE_CONFAIL_AUTO(){
        MyLogUtil.Log("1111","======蓝牙连断开=====")
        EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
            EventModel.CAR_LOCK, EventModel.CAR_CHAN_BLE,EventModel.CAR_REMARK_HC_BLECON_AUTO).subscribeBy(onNext = {}).toCancelable()
    }
    @Receive(Constant.Event.BLE_CONSUC)
    fun BleConSuc(){
        MyLogUtil.Log("1111","======蓝牙连接成功=====")
        EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
            EventModel.CAR_LOCK, EventModel.CAR_CHAN_BLE,EventModel.CAR_REMARK_HC_BLECON_SUC).subscribeBy(onNext = {}).toCancelable()
    }
    @Receive(Constant.Event.BLE_CONFAIL)
    fun BleConFail(){
        MyLogUtil.Log("1111","======蓝牙连接失败=====")
        BleReBack(2)
    }

    /**
     * 蓝牙还车失败
     * @param backState 0: 写入失败 ，1：无响应， 2：连接失败
     */
    fun BleReBack(backState : Int){
        if (!SpUtil.Companion.getInstance().find(Constant.SpKey.SP_VECHNO).isNullOrEmpty() && isUseBleColse){
//            loadingDialogHelper!!.dismiss()
            if (backState == 2){
//                MyToastUtil.toast(getString(R.string.s_returnfail_ble)+"(02)")
                EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                    EventModel.CAR_LOCK, EventModel.CAR_CHAN_BLE,EventModel.CAR_REMARK_HC_BLECON_FAIL).subscribeBy(onNext = {}).toCancelable()
            }else if (backState == 0){
//                MyToastUtil.toast(getString(R.string.s_returnfail_ble)+"(04)")
                EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                    EventModel.CAR_LOCK, EventModel.CAR_CHAN_BLE,EventModel.CAR_REMARK_RETURNFAIL_BLE+"("+Glob.xiaoanState+")").subscribeBy(onNext = {}).toCancelable()
            }else{
//                MyToastUtil.toast(getString(R.string.s_returnfail_ble)+"(04)")
                EventModel.sumitCarEvent(EventModel.EVENT_CAR, orderNo,SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO)!!,no,
                    EventModel.CAR_LOCK, EventModel.CAR_CHAN_BLE,EventModel.CAR_REMARK_RETURNFAIL_BLE+"("+Glob.xiaoanState+")").subscribeBy(onNext = {}).toCancelable()
            }
            orderBleEndResq(OrderModel.Typt_EndOrder_foucs)

//            MyToastUtil.toast(getString(R.string.op_failed_with_reason, ResUtil.getString(R.string.s_clockcar), ""))
//            Apollo.emit(Constant.Event.EVENT_ORDERFISHERR)
//            if (!isRental_MyOrder){
//                CommDialog.Builder(activity).setTitle(ResUtil.getString(R.string.dialog_tip)).setContent(getString(R.string.s_lineservice))
//                    .setLeftText(ResUtil.getString(R.string.cancel)).setRightText(getString(R.string.s_goline)).setCanceledOnOutside(true)
//                    .setClickListen(object : CommDialog.TwoSelDialog {
//                        override fun leftClick() {}
//                        override fun rightClick() {
//                            onRidingKFListener()
//                        } }).build().show()
//            }
        }
    }
    var isArrearSwith = false  //是否允许欠费
    var allow_arrears_amount = 0f //可欠费金额
    var allow_arrears_amount_now = 0f//用户当前欠费
    var stopTime = 0 //车辆临停 结束订单时间
    var isRental_MyOrder = Glob.isRental
    fun getConfigByRiding(isShowArrea : Boolean,isRental_Order : Boolean){
        isRental_MyOrder = isRental_Order
        if (isRental_MyOrder){
            view?.findViewById<LinearLayout>(R.id.ly_dz)?.visibility = View.GONE
            view?.findViewById<LinearLayout>(R.id.ly_cz)?.visibility = View.VISIBLE
            view?.findViewById<RoundTextView>(R.id.rv_riding_searchp)?.visibility = View.GONE
            view?.findViewById<ImageView>(R.id.img_car_s)?.visibility = View.VISIBLE
            view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.visibility = View.VISIBLE
            view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.getDelegate()?.backgroundColor = resources.getColor(R.color.c_grey)
            view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.setTextColor(resources.getColor(R.color.c_838588))
//            view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.text = getString(R.string.s_rentaling)

//            view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.text = getString(R.string.temporary_stop)
//            view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text = getString(R.string.s_car_back)

            view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.text = getString(R.string.s_riding_back)
//            view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text = getString(R.string.temporary_stop)
        }else{
            view?.findViewById<LinearLayout>(R.id.ly_dz)?.visibility = View.VISIBLE
            view?.findViewById<LinearLayout>(R.id.ly_cz)?.visibility = View.GONE
            view?.findViewById<RoundTextView>(R.id.rv_riding_searchp)?.visibility = View.VISIBLE
            view?.findViewById<ImageView>(R.id.img_car_s)?.visibility = View.GONE
            view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.visibility = View.GONE
            view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.getDelegate()?.backgroundColor = resources.getColor(R.color.blue_namal10)
            view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.setTextColor(resources.getColor(R.color.blue_namal))

            view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.text = getString(R.string.s_riding_back)
//            view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text = getString(R.string.temporary_stop)
        }

        isArrearSwith = false
        // insufficient_balance_reminder 余额不足提醒 //  end_order_countdown 结束订单倒计时 //  balance    触发提醒余额
//        vehicle_silence_time  车辆静止限制时长 //        sys_paymethod    支付方式
//        temp_lock   自动临时锁车 //        end_order   自动结束订单  //temp_lock_stop_order 临停自动锁车
        val listKey = arrayOf("remaining_time_warn","vehicle_silence_time",
            "sys_paymethod","temp_lock_stop_order","billing_rules")
        ComModel.getConfig(listKey).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取骑行配置信息 信息=="+it.toString())
                resultDataConfig = Gson().fromJson(it.toString(), getConfigByRidingData::class.java)
                if (resultDataConfig != null){
                    stopTime = resultDataConfig!!.temp_lock_stop_order
                }
//                MyLogUtil.Log("1111","==============时间转换==="+secondsToTimeFormat(181))
            }
        ).toCancelable()
        val listKeyArrears = arrayOf("arrears_switch","allow_arrears_amount")
        ComModel.getConfig(listKeyArrears).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取允许欠费开关(0开，1关) 金额 信息=="+it.toString())
                if (!it.toString().equals("[]")) {
                    var resultData = Gson().fromJson(it.toString(), ArrearsData::class.java)
                    if (resultData.arrears_switch == 0) isArrearSwith = true
                    allow_arrears_amount = resultData.allow_arrears_amount
                    if (isShowArrea && isArrearSwith){
                        var unit = AppUtil.getFloat2(allow_arrears_amount)+Glob.CurrencyUnit
                        var content = getString(R.string.s_arreas_tip,unit)
                        val spannableString = SpannableString(content)
                        if (content.contains(unit.toString())){
                            var data1 = spannableString.split(unit)[0].length
                            if (activity != null){
                                spannableString.setSpan(ForegroundColorSpan(activity!!.resources.getColor(R.color.blue_namal)), data1, data1+unit.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                            }
                        }
                        if (activity != null){
                            CommDialog.Builder(activity).setTitle(getString(R.string.s_ride_tip)).setSpanContent(spannableString)
                                .setLeftText("").setRightText(ResUtil.getString(R.string.i_know)).setType("2").setCanceledOnOutside(true)
                                .setClickListen(object : CommDialog.TwoSelDialog {
                                    override fun leftClick() {}
                                    override fun rightClick() {
//                                        val properties = JSONObject()
//                                        MDUtil.clickEvent("got_it_click",properties)
                                    }
                                }).build().show()
                        }
                    }
                }
            }
        ).toCancelable()
    }
    //震动提醒
    fun VibratorTip(times : Int){
        val vibrator = ContextUtil.getContext().getSystemService(AppCompatActivity.VIBRATOR_SERVICE) as Vibrator
        val pattern = longArrayOf(0, 200) // 第一个数字是开始前的延迟，‌第二个数字是振动的持续时间
        vibrator.vibrate(pattern, -1)// 0无限次重复振动模式
        val timesNow = times + 1
        if(timesNow < 3) {
            view?.findViewById<TextView>(R.id.tv_riding_no)?.postDelayed({
                if (view?.findViewById<TextView>(R.id.tv_riding_no) != null){
                    VibratorTip(timesNow)
                }
            },500)
        }
    }
    var eventName = "cycling"
    var eventPrice = ""
    fun onPageStar(){
//        MyLogUtil.Log("1111","=======onPageStar=============="+eventName+"-"+eventPrice)
        if (!eventName.isNullOrEmpty()) MDUtil.pageStar(eventName)

    }
    fun onPageEnd(){
//        MyLogUtil.Log("1111","=======onPageEnd=============="+eventName+"-"+eventPrice)
        if (!eventName.isNullOrEmpty()){
            val properties = JSONObject()
            properties.put("tag",eventName)
            properties.put("real_time_fees",eventPrice)
            if (Glob.isRental){
                properties.put("business_type",2)
            }else{
                properties.put("business_type",1)
            }
            MDUtil.pageEnd(eventName,properties)
        }
    }

    fun setStateView(){
        if (NetUtils.isConnected(ContextUtil.getContext())){
            if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
                if (!NetUtils.isConnected(ContextUtil.getContext())){
                    setErrorView(true)
                }else{
                    setErrorView(false)
                }
            }else{
                view?.findViewById<CapaLayout>(R.id.capaLayout)?.toContent()
            }
        }else{
            setErrorView(true)
        }

    }
    fun setErrorView(isNetError : Boolean){
//        MyLogUtil.Log("1111","======isNetError======"+isNetError)
        if (isNetError){
            view?.findViewById<CapaLayout>(R.id.capaLayout)?.findViewById<LinearLayout>(R.id.ly_err_net)?.visibility = View.VISIBLE
            view?.findViewById<CapaLayout>(R.id.capaLayout)?.findViewById<LinearLayout>(R.id.ly_err_local)?.visibility = View.GONE
        }else{
            view?.findViewById<CapaLayout>(R.id.capaLayout)?.findViewById<LinearLayout>(R.id.ly_err_net)?.visibility = View.GONE
            view?.findViewById<CapaLayout>(R.id.capaLayout)?.findViewById<LinearLayout>(R.id.ly_err_local)?.visibility = View.VISIBLE
        }
        view?.findViewById<CapaLayout>(R.id.capaLayout)?.toError()
        view?.findViewById<CapaLayout>(R.id.capaLayout)?.findViewById<RoundTextView>(R.id.tv_err_set_net)?.setOnClickListener{
            val intent = Intent(Settings.ACTION_DATA_ROAMING_SETTINGS)
            startActivity(intent)
        }
        view?.findViewById<CapaLayout>(R.id.capaLayout)?.findViewById<RoundTextView>(R.id.tv_err_set_local)?.setOnClickListener{
            val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
            startActivityForResult(intent, MainActivity.REQUEST_OPEN_GPS)
        }
    }

    var ParkName = ""
    var AutoTimes = 0
    var s_jz_time = 300 //倒计时时长
    var isEnd = false

    var isAutoResqOrderTime = 0
    fun AutoTask(){
        if(view?.findViewById<TextView>(R.id.tv_riding_no) != null){
            view?.findViewById<TextView>(R.id.tv_riding_no)?.postDelayed({
                try {
                    AutoTimes ++
                    AutoTask()
                    onRidingBatterListener(orderNo,batter)
                    if(AutoTimes >= 61) AutoTimes = 0
                    if(orderNo.equals("")) return@postDelayed
                    isAutoResqOrderTime++
                    if(isAutoResqOrderTime >= 5){
                        isAutoResqOrderTime = 0;
                        if(resultDataConfig == null){
                            getConfigByRiding(false,isRental_MyOrder)
                        }
                        getOrderInfo(orderNo,false)
                        setStateView()
                    }
                    MyLogUtil.Log("5555","=======倒计时====="+s_jz_time+","+orderState+","+carState+","+orderNo+","+isRental_MyOrder)
                    if (isRental_MyOrder){
                        //订单状态  0正常，1剩余免费时长，2超时
                        if (orderState_Rental == 0){

                        }else if (orderState_Rental == 1){
                            //            //剩余免费时间提醒
                            var timeDatas = s_jz_time
                            var sData = secondsToTimeFormatByDeail(timeDatas)
//                        var hintData = "剩余免费时间仅剩 "+sData+" ，前往【P点名称】还车"
                            var hintData = getString(R.string.s_ridingrental_reture_hint2,sData,ParkName)
                            val spannableString = SpannableString(hintData)
                            var data1 = spannableString.split(sData)[0].length
                            if (activity != null){
                                spannableString.setSpan(ForegroundColorSpan(activity!!.resources.getColor(R.color.c_ED0000)), data1, data1+sData.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                            }
                            view?.findViewById<TextView>(R.id.tv_riding_hint)?.setText(spannableString)
                            if (s_jz_time > 0){
                                s_jz_time = s_jz_time - 1
                            }else{
                                orderState_Rental = 2
                                getOrderInfo(orderNo,false)
                            }
                        }else if (orderState_Rental == 2){
                            //已超时提醒
                            var timeDatas = s_jz_time
                            var sData = getString(R.string.s_overtime)+secondsToTimeFormatByDeail(timeDatas)
                            var hintData = sData+" ，"+ getString(R.string.s_ridingrental_reture_hint3,ParkName)
                            val spannableString = SpannableString(hintData)
                            var data1 = spannableString.split(sData)[0].length
                            if (activity != null){
                                spannableString.setSpan(ForegroundColorSpan(activity!!.resources.getColor(R.color.c_ED0000)), data1, data1+sData.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                            }
                            view?.findViewById<TextView>(R.id.tv_riding_hint)?.setText(spannableString)
                            s_jz_time = s_jz_time + 1
                        }

                    }else{
                        if(s_jz_time >= 0){
                            if(resultDataConfig != null && view?.findViewById<TextView>(R.id.tv_riding_no) != null){
                                var sData = secondsToTimeFormat(s_jz_time)
                                var hintData = ""
                                if (orderState == 3){
                                    //欠费提醒
                                    sData = AppUtil.getFloat2(allow_arrears_amount_now)+Glob.CurrencyUnit
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.visibility = View.VISIBLE
                                    hintData = ResUtil.getString(R.string.s_arreashint,sData,AppUtil.getFloat2(allow_arrears_amount)+Glob.CurrencyUnit)
                                }else if(orderState == 1){
                                    //余额耗尽
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.visibility = View.VISIBLE
                                    hintData = ResUtil.getString(R.string.s_riding_nwallet_hint,sData)
                                    if(s_jz_time == 0) {
//                                endOrder()
//                                        view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.postDelayed({
//                                            getOrderInfo(orderNo,false)
//                                        },10000)
                                    }
                                }else if(orderState == 2){
                                    //余额不足
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.visibility = View.VISIBLE
                                    hintData = ResUtil.getString(R.string.s_riding_uwallet_hint,sData)
                                    if(s_jz_time == 0) {
                                        view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.postDelayed({
                                            getOrderInfo(orderNo,false)
                                        },1000)
                                    }
                                }else if(carState == 1){
                                    //结束订单
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.visibility = View.GONE
//                            hintData = ResUtil.getString(R.string.s_riding_end_hint,sData,resultDataConfig!!.vehicle_silence_time.end_order/60)
                                    hintData = ResUtil.getString(R.string.s_riding_end_hint,sData,resultDataConfig!!.vehicle_silence_time.end_order/60)
                                    MyLogUtil.Log("1111","===结束订单时间==="+resultDataConfig!!.vehicle_silence_time.end_order)
                                    if(s_jz_time == resultDataConfig!!.vehicle_silence_time.end_order) {
                                        endOrder(false)
//                                        view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.postDelayed({
//                                            getOrderInfo(orderNo,false)
//                                        },10000)
                                    }
                                }else if(carState == 2){
                                    //临时停车预警
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.visibility = View.GONE
//                            hintData = ResUtil.getString(R.string.s_riding_p_hint,sData,resultDataConfig!!.vehicle_silence_time.temp_lock/60)
                                    hintData = ResUtil.getString(R.string.s_riding_p_hint,sData,resultDataConfig!!.vehicle_silence_time.temp_lock/60)
                                    MyLogUtil.Log("1111","===临停时间==="+resultDataConfig!!.vehicle_silence_time.temp_lock+","+s_jz_time)
                                    if(s_jz_time == resultDataConfig!!.vehicle_silence_time.temp_lock){
                                        view?.findViewById<LinearLayout>(R.id.ly_ridingorder_hint)?.visibility = View.GONE
                                        carState = 0;
//                                stopState = "2"
                                    }
                                }else{
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.visibility = View.GONE
                                }
                                if(orderState == 0 && carState == 0){
                                    view?.findViewById<LinearLayout>(R.id.ly_ridingorder_hint)?.visibility = View.GONE
                                }else{
                                    view?.findViewById<LinearLayout>(R.id.ly_ridingorder_hint)?.visibility = View.VISIBLE
                                    if (orderState == 3){
                                        //3欠费提醒
                                        val spannableString = SpannableString(hintData)
                                        var data1 = spannableString.split(sData)[0].length
                                        if (activity != null){
                                            spannableString.setSpan(ForegroundColorSpan(activity!!.resources.getColor(R.color.c_FF5300)), data1, data1+sData.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                                        }
                                        view?.findViewById<TextView>(R.id.tv_riding_hint)?.setText(spannableString)
                                    }else{
                                        if (!orderNo.equals("") && orderState != 4){
                                            //0正常，1余额耗尽，2余额不足, 3欠费提醒，4 达到最大欠费金额
                                            val spannableString = SpannableString(hintData)
                                            var data1 = spannableString.split(sData)[0].length
                                            if (activity != null){
                                                spannableString.setSpan(ForegroundColorSpan(activity!!.resources.getColor(R.color.c_FF5300)), data1, data1+sData.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                                            }
                                            view?.findViewById<TextView>(R.id.tv_riding_hint)?.setText(spannableString)
                                        }
                                    }
                                    //orderState  0正常，1余额耗尽，2余额不足, 3欠费提醒，4 达到最大欠费金额
                                    //carState 车辆状态  0正常，1结束订单，2临时停车预警
                                    if(((carState == 1 || carState == 2) && orderState == 0) || orderState == 3){
                                        s_jz_time ++
                                    }else{
                                        if(s_jz_time > 0){
                                            s_jz_time --
                                        }else{
                                            s_jz_time = -1
                                        }
                                    }
                                }
                            }
                        }else{
                            if((orderState == 1) && !isEnd){
                                isEnd = true
                                endOrder(false)
                            }
                        }
                    }
                }catch (e : NullPointerException){}
            },1 * 1000)
        }
    }
    var orderState_Rental = 0 //订单状态  0正常，1剩余免费时长，2超时
    var orderState = 0 //订单状态  0正常，1余额耗尽，2余额不足, 3欠费提醒，4 达到最大欠费金额
    var carState = 0  // 车辆状态  0正常，1结束订单，2临时停车预警
    var no = ""
    /**
     * @param isStop 是否是 临时停车 继续用车操作
     */
    fun getOrderInfo(order_no: String,isStop : Boolean){
        orderNo = order_no
        if (!NetUtils.isConnected(ContextUtil.getContext())) return
        if (order_no.isNullOrEmpty()) return
        if (isRental_MyOrder){
            OrderModel.getRidingOrderInfoByRental(orderNo)
                .subscribeBy(
                    onNext = {
                        MyLogUtil.Log("1111","===获取 骑行订单长租信息=="+it.toString())
                        val resultData = Gson().fromJson(it.toString(), OrderInfoRentalData::class.java)
                        rentalResultData = resultData;
                        if (resultData != null){
                            if (resultData.status == 2 || resultData.status == 3 || resultData.status == 5){
                                view?.findViewById<LinearLayout>(R.id.ly_ridingorder_hint)?.visibility = View.GONE
                                view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.visibility = View.GONE
                                onRidingListener(orderNo,false,isRental_MyOrder)

                                endOrderState()
                            }else {
                                if (SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO).isNullOrEmpty()){
                                    SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_VECHNO, resultData.vehicle_no)
                                }
                                view?.findViewById<TextView>(R.id.tv_riding_no)?.text = "No: "+resultData.vehicle_no
                                if (resultData.is_lock){
//                                    view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.setTextColor(resources.getColor(R.color.white))
                                    view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text = getString(R.string.unlock)
                                }else{
//                                    view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.setTextColor(resources.getColor(R.color.white))
                                    view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text = getString(R.string.s_clockcar)
                                }
                                powerNum_U = resultData.use_battery_replace_times
                                powerNum = resultData.battery_replace_times - resultData.use_battery_replace_times
                                if (resultData.use_battery_replace_times < resultData.battery_replace_times){
                                    view?.findViewById<RoundTextView>(R.id.rt_riding_power)?.text = getString(R.string.s_changepower)+"("+
                                            resultData.use_battery_replace_times+"/"+resultData.battery_replace_times+")"
                                }else{
                                    view?.findViewById<RoundTextView>(R.id.rt_riding_power)?.text = getString(R.string.s_changepower)+"("+
                                            resultData.battery_replace_times+"/"+resultData.battery_replace_times+")"
                                }

                                view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.isEnabled = true
                                view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.isEnabled = true
                                //1 已开锁 2 已关锁 3 换电中
                                if (resultData.vehicle_status == 1){
                                    isPowering = false
                                    onPowerListener("")
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.text = getString(R.string.s_car_uclock)
                                    view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.visibility = View.VISIBLE
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.setTextColor(resources.getColor(R.color.white))
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.text = getString(R.string.s_riding_back)
                                }else if (resultData.vehicle_status == 2){
                                    isPowering = false
                                    onPowerListener("")
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.text = getString(R.string.s_car_lock)
                                    view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.visibility = View.VISIBLE
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.setTextColor(resources.getColor(R.color.white))
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.text = getString(R.string.s_riding_back)
                                }else if (resultData.vehicle_status == 3){
                                    isPowering = true
                                    onPowerListener(Constant.SUC)
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.text = getString(R.string.s_car_power)
                                    view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.visibility = View.GONE
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.setTextColor(resources.getColor(R.color.white50))
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.text = getString(R.string.s_power_ing)
                                }
//                            var lease_end_time = 1726647070L
                                var lease_end_time = resultData.lease_end_time
                                var hourTime = TimeFormatUtil.getTimeDistanceBysecond(System.currentTimeMillis(),TimeFormatUtil.transToStringBysqDataInfo_Long(lease_end_time))
                                if (resultData.park_area != null){
                                    ParkName = resultData.park_area.name
                                    ParkLatlng = GPSUtil.gps84_To_bd09(resultData.park_area.flag.lat,resultData.park_area.flag.lng)
                                }
//                                MyLogUtil.Log("1111","=====订单时间====="+hourTime+"===lease_end_time=="+TimeFormatUtil.transToStringBysqDataInfo_Long(lease_end_time))
                                rentalTime = hourTime!!
                                if (hourTime!! > 12 * 3600){
                                    orderState_Rental = 0
                                    //还车提醒
                                    var hintData = ""
                                    if (LanguageUtil.language_head.equals(LanguageUtil.LANG_ZH) || LanguageUtil.language_head.equals(LanguageUtil.LANG_ZH_TC)){
                                        hintData = getString(R.string.s_ridingrental_reture_hint1,
                                            TimeFormatUtil.transToStringBysqDataInfo(lease_end_time),ParkName)
                                    }else{
                                        hintData = getString(R.string.s_ridingrental_reture_hint1,ParkName,
                                            TimeFormatUtil.transToStringBysqDataInfo(lease_end_time))
                                    }
                                    view?.findViewById<TextView>(R.id.tv_riding_hint)?.setText(hintData)
                                    view?.findViewById<LinearLayout>(R.id.ly_return_hint)?.visibility = View.GONE
                                }else if (hourTime!! >= 0){
                                    //剩余免费时间提醒
                                    orderState_Rental = 1
                                    s_jz_time = TimeFormatUtil.getTimeDistanceBysecond(System.currentTimeMillis(),TimeFormatUtil.transToStringBysqDataInfo_Long(lease_end_time))!!.toInt()
                                    view?.findViewById<LinearLayout>(R.id.ly_return_hint)?.visibility = View.VISIBLE
                                    view?.findViewById<TextView>(R.id.tv_return_hint)?.setText(getspan(false))
//                                    MyLogUtil.Log("1111","=====订单时间 剩余免费时间提醒====="+s_jz_time)
                                }else{
                                    //已超时提醒
                                    orderState_Rental = 2
                                    s_jz_time = TimeFormatUtil.getTimeDistanceBysecond(System.currentTimeMillis(),TimeFormatUtil.transToStringBysqDataInfo_Long(lease_end_time))!!.toInt()
                                    if (s_jz_time < 0){
                                        s_jz_time = -s_jz_time
                                    }
                                    view?.findViewById<LinearLayout>(R.id.ly_return_hint)?.visibility = View.VISIBLE
                                    view?.findViewById<TextView>(R.id.tv_return_hint)?.setText(getspan(true))
//                                    MyLogUtil.Log("1111","=====订单时间 已超时提醒====="+s_jz_time)
                                }

                                view?.findViewById<LinearLayout>(R.id.ly_ridingorder_hint)?.visibility = View.VISIBLE
                                val latLng = OrderModel.getLocalData()
                                BikeModel.getBikesInfo(resultData.vehicle_no,latLng.lat,latLng.lng,BikeModel.isScan_N,isRental_MyOrder).subscribeBy(
                                    onNext = {
                                        MyLogUtil.Log("1111","===获取车辆信息=="+it.toString())
                                        val resultDataCar: CarInfoData = Gson().fromJson(it.toString(), CarInfoData::class.java)
                                        view?.findViewById<TextView>(R.id.tv_surplusMileage)?.text = getString(R.string.remaining_mileage_info,resultDataCar.remaining_mileage.toString())
                                        park_id = resultDataCar.park_id
                                        view?.findViewById<ImageView>(R.id.img_rental_go)?.visibility = View.VISIBLE
                                        if (resultDataCar.battery_energy != null){
                                            batter = resultDataCar.battery_energy
                                        }
                                        CarLatlng = GPSUtil.gps84_To_bd09(resultDataCar.vehicle_lat,resultDataCar.vehicle_lng)
                                    },
                                    onError = {
                                        view?.findViewById<TextView>(R.id.tv_surplusMileage)?.text = getString(R.string.remaining_mileage_info,"0")
                                    }
                                ).toCancelable()
                            }
                        }
                    }
                ).toCancelable()
        }else{
            OrderModel.getRidingOrderInfo(orderNo)
                .subscribeBy(
                    onNext = {
                        MyLogUtil.Log("1111","===获取 骑行订单信息=="+it.toString())
                        val resultData: RidingOrderInfoData = Gson().fromJson(it.toString(), RidingOrderInfoData::class.java)
//                    if (resultData != null) no = resultData.device_uuid
                        if (resultData.status == 2 || resultData.status == 3){
                            view?.findViewById<LinearLayout>(R.id.ly_ridingorder_hint)?.visibility = View.GONE
                            view?.findViewById<RoundTextView>(R.id.tv_riding_recharge)?.visibility = View.GONE
                            onRidingListener(orderNo,false,isRental_MyOrder)

                            endOrderState()
//                        BleModelNew.lock()
                        }else{
                            if (resultData.battery_energy != null){
                                batter = resultData.battery_energy
                            }
                            view?.findViewById<TextView>(R.id.tv_riding_no)?.text = "No: "+resultData.vehicle_no
                            view?.findViewById<TextView>(R.id.tv_riding_time)?.text = resultData.order_time.toString()
                            view?.findViewById<TextView>(R.id.tv_riding_length)?.text = resultData.mileage.toString()
                            view?.findViewById<TextView>(R.id.tv_riding_batterylife)?.text = resultData.remaining_mileage.toString()
                            view?.findViewById<TextView>(R.id.tv_riding_amount)?.text = AppUtil.getFloat2(resultData.order_amount).toString()+resultData.currency
                            eventPrice = AppUtil.getFloat2(resultData.order_amount).toString()
                            if(isStop){
                                if(stopState.equals("1")){
                                    resultData.temp_lock_time = 0
                                }else{
                                    resultData.temp_lock_time = 1
                                }
                            }
                            if(resultData.temp_lock_time > 0){
                                view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.visibility = View.GONE
                                view?.findViewById<LinearLayout>(R.id.tv_riding_stop)?.visibility = View.VISIBLE
                                view?.findViewById<TextView>(R.id.tv_riding_stoptime)?.text = ResUtil.getString(R.string.s_stop_time,(resultData.temp_lock_time/60+1))
                                view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text = ResUtil.getString(R.string.keep_going)
                            }else{
                                view?.findViewById<RoundTextView>(R.id.tv_riding_ing)?.visibility = View.VISIBLE
                                view?.findViewById<LinearLayout>(R.id.tv_riding_stop)?.visibility = View.GONE
                                view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.text = ResUtil.getString(R.string.temporary_stop)
                            }
                            if(!SpUtil.Companion.getInstance().find(Constant.SpKey.DEVICE_ID).isNullOrEmpty()){
                                if (SpUtil.Companion.getInstance().find(Constant.SpKey.DEVICE_ID)!!.equals(resultData.device_uuid)){
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.isEnabled = true
                                    view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.isEnabled = true
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.setTextColor(resources.getColor(R.color.white))
                                    view?.findViewById<LinearLayout>(R.id.ly_riding_bom)?.visibility = View.GONE
                                    isOpenUser = true
                                }else{
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.isEnabled = false
                                    view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.isEnabled = false
                                    view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.setTextColor(resources.getColor(R.color.white50))
                                    view?.findViewById<LinearLayout>(R.id.ly_riding_bom)?.visibility = View.GONE
                                    isOpenUser = false
                                }
                            }else{
                                view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.isEnabled = true
                                view?.findViewById<RoundTextView>(R.id.btn_riding_stop)?.isEnabled = true
                                view?.findViewById<RoundTextView>(R.id.tv_riding_repay)?.setTextColor(resources.getColor(R.color.white))
                            }

                            if (orderState == 0){
//                        s_jz_time = resultData.vehicle_silence_time
                                if(resultData.vehicle_silence_time >= resultDataConfig!!.vehicle_silence_time.end_order_warn){
                                    //静止 ==  结束订单
                                    if (carState != 1){
                                        VibratorTip(0)
                                    }
                                    carState = 1
//                            s_jz_time = resultDataConfig!!.vehicle_silence_time.end_order - resultData.vehicle_silence_time
                                    s_jz_time = resultData.vehicle_silence_time
                                }else{
                                    if(resultData.vehicle_silence_time >= resultDataConfig!!.vehicle_silence_time.temp_lock){
                                        carState = 0 //临停中
                                    }else if(resultData.vehicle_silence_time >= resultDataConfig!!.vehicle_silence_time.temp_lock_warn){
                                        //静止 ==  临时停车预警
                                        if (carState != 2){
                                            VibratorTip(0)
                                        }
                                        carState = 2
//                                s_jz_time = resultDataConfig!!.vehicle_silence_time.temp_lock - resultData.vehicle_silence_time
                                        s_jz_time = resultData.vehicle_silence_time
                                    }else{
                                        carState = 0
                                    }
                                }
                            }
                        }
                    }
                ).toCancelable()

            if (isArrearSwith){
                OrderModel.getArreasState(orderNo)
                    .subscribeBy(
                        onNext = {
                            MyLogUtil.Log("1111","===获取 订单欠款情况 信息=="+it.toString())
                            val resultData = Gson().fromJson(it.toString(), ArrearsAmountData::class.java)
                            if (resultData.arrears_amount >= allow_arrears_amount){
                                if (orderState != 4) {
                                    orderState = 4
                                    endOrder(false)
                                }
                            }else if(resultData.arrears_amount > 0){
                                orderState = 3
                                s_jz_time = 1
                                allow_arrears_amount_now = resultData.arrears_amount
                            }else{
                                orderState = 0
                            }
                        }
                    ).toCancelable()
            }else{
                OrderModel.getRemainingtime(orderNo)
                    .subscribeBy(
                        onNext = {
                            MyLogUtil.Log("1111","===获取 订单剩余可骑行时长 信息=="+it.toString())
                            val resultData: RindingRemainTimeData = Gson().fromJson(it.toString(), RindingRemainTimeData::class.java)

                            if(resultData.remaining_time == 0){
                                // 余额耗尽
                                if(orderState == 1)return@subscribeBy
                                VibratorTip(0)
                                s_jz_time = resultData.end_order_countdown
                                orderState = 1
                            }else{
                                if(resultData.remaining_time <= resultDataConfig!!.remaining_time_warn){
                                    //余额不足
                                    if(orderState == 2)return@subscribeBy
                                    VibratorTip(0)
                                    s_jz_time = resultDataConfig!!.remaining_time_warn
                                    orderState = 2
                                }else{
                                    orderState = 0
                                }
                            }
                        }
                    ).toCancelable()
            }
        }
    }
    fun secondsToTimeFormat(seconds: Int): String {
        val minutes = (seconds / 60).toString().padStart(2, '0') // 计算分钟部分并补全前导零
        val second = (seconds % 60).toString().padStart(2, '0') // 计算秒钟部分并补全前导零
        return "$minutes:$second" // 返回结果字符串
    }
    fun secondsToTimeFormatByDeail(secondsData : Int): String {
        var seconds = Math.abs(secondsData)
        val hours = (seconds / 3600).toString().padStart(2, '0') // 计算小时部分并补全前导零
        val minutes = (((seconds % 3600) / 60)).toString().padStart(2, '0') // 计算分钟部分并补全前导零
        val second = (seconds % 60).toString().padStart(2, '0') // 计算秒钟部分并补全前导零
//        return "$hours$minutes:$second" // 返回结果字符串
        return ""+hours+getString(R.string.s_unit_hour)+minutes+getString(R.string.min)+
                second+getString(R.string.s_unit_s)
    }
    fun getspan(isDown : Boolean) : SpannableString{
        var sData = ""+Glob.CurrencyUnit+"/"+getString(R.string.min)
        if(resultDataConfig != null){
            sData = AppUtil.getFloat2(resultDataConfig!!.billing_rules.ride_card.ride_price)+Glob.CurrencyUnit+"/"+getString(R.string.min)
        }
        var hintData = ""
        if (isDown){
            hintData = getString(R.string.s_cz_hint1,sData)
        }else{
            hintData = getString(R.string.s_cz_hint,sData)
        }
        val spannableString = SpannableString(hintData)
        var data1 = spannableString.split(sData)[0].length
        if (activity != null){
            spannableString.setSpan(ForegroundColorSpan(activity!!.resources.getColor(R.color.c_FF9500)), data1, data1+sData.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        return spannableString
    }
    var timeOut = 0
    override fun onResume() {
        super.onResume()
        if(orderState == 1 || orderState == 2){
            var newtimeOut = (System.currentTimeMillis()/1000).toInt() - timeOut
//            MyLogUtil.Log("1111","=========时间差 ===" + newtimeOut)
            if(s_jz_time > newtimeOut){
                s_jz_time = s_jz_time - newtimeOut
            }else{
                s_jz_time = 0
            }
        }
        getOrderInfo(orderNo,false)
    }
    override fun onPause() {
        super.onPause()
        if(orderState == 1 || orderState == 2){
            timeOut = (System.currentTimeMillis()/1000).toInt()
        }
    }
    override fun onDestroy() {
        super.onDestroy()
        try {
            if (xiaoAnBleSdk != null){
                xiaoAnBleSdk?.disCon()
            }
        }catch (e : NullPointerException){}
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (xiaoAnBleSdk != null){
            xiaoAnBleSdk?.ActivityResult(requestCode,resultCode,data)
        }
    }

    /**
     * 判断当前用户是否拥有非P免罚会员权益
     * 回调参数：Boolean（true=有权益且未锁定，false=无权益或已锁定）
     */
    fun isNonPImpunityActive(callback: (Boolean) -> Unit) {
        UserRClient.client.getMemberInfo()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeBy(
                onNext = { memberInfoList ->
                    // 取当前等级权益（默认第一个）
                    val info = memberInfoList.firstOrNull()
                    val active = info?.equity?.otherPark?.isLock == false && (info.equity?.otherPark?.parkImpunity ?: 0) > 0
                    callback(active)
                },
                onError = { callback(false) }
            )
    }

    /**
     * 判断当前位置是否在P点范围内
     * @param lat 当前纬度
     * @param lng 当前经度
     * @param range 距离阈值（米），默认50米
     */
    fun isInPPoint(lat: Double, lng: Double, range: Double = 50.0): Boolean {
        val pLat = ParkLatlng?.lat ?: return false
        val pLng = ParkLatlng?.lng ?: return false
        val distance = GPSUtil.getDistance(pLng, pLat, lng, lat)
        return distance <= range
    }
}