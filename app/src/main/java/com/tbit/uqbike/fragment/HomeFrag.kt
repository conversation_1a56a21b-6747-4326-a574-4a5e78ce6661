package com.tbit.uqbike.fragment

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.google.gson.Gson
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.AuthActivity
import com.tbit.uqbike.activity.FaultRecordActivity
import com.tbit.uqbike.activity.HomeActivity
import com.tbit.uqbike.activity.MainActivity
import com.tbit.uqbike.activity.RecordDetailActivity
import com.tbit.uqbike.activity.ScanForBorrowActivity
import com.tbit.uqbike.activity.UserCarActivity
import com.tbit.uqbike.activity.WalletActivity
import com.tbit.uqbike.activity.WebActionActivity
import com.tbit.uqbike.activity.WebActivity
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.base.BaseFragment
import com.tbit.uqbike.bean.AdDeposit
import com.tbit.uqbike.bean.PhoneInfo
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.dialog.InviteDialog
import com.tbit.uqbike.dialog.UserHelpBottomSheep
import com.tbit.uqbike.dialog.kfBottomSheep
import com.tbit.uqbike.dialog.util.MainDialogUtil
import com.tbit.uqbike.dialog.util.RidingDialogUtil
import com.tbit.uqbike.entity.CarInfoData
import com.tbit.uqbike.entity.Coupon
import com.tbit.uqbike.entity.MyOrderStateData
import com.tbit.uqbike.entity.ParkData
import com.tbit.uqbike.entity.RIG_TYPE_NEWUSER
import com.tbit.uqbike.entity.RigisterData
import com.tbit.uqbike.entity.SysMinAmount
import com.tbit.uqbike.entity.VehicleData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.entity.getAdData
import com.tbit.uqbike.entity.getAreaData
import com.tbit.uqbike.entity.isFirstOrderData
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.entity.type_coupon_invite
import com.tbit.uqbike.entity.type_coupon_money
import com.tbit.uqbike.entity.type_coupon_ridecard
import com.tbit.uqbike.mvp.constract.MainContract
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.model.UserModel
import com.tbit.uqbike.mvp.presenter.MainPresenter
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.OrderModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.NetUtils
import com.tbit.uqbike.utils.UrlDecodeUtil
import com.tbit.uqbike.widget.CustomNestedScrollView
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.subjects.PublishSubject
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.support.v4.startActivity
import org.json.JSONObject
import java.util.concurrent.TimeUnit
import wendu.dsbridge.DWebView

class HomeFrag : BaseFragment(), MainContract.View {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.frag_home, container, false)
    }

    private val mainMapFragment by lazy { childFragmentManager.findFragmentById(R.id.main_map_fragment) as MainMapFragment }
    private val mainBusinessFragment by lazy { childFragmentManager.findFragmentById(R.id.main_upda_fragment) as MainBusinessFragment }
    val mainAdFragment by lazy { childFragmentManager.findFragmentById(R.id.main_ad_fragment) as HomeAdFragment }
    private val mainAdFullFragment by lazy { childFragmentManager.findFragmentById(R.id.main_adfull_fragment) as? MainAdFullFragment }
    private var MyView : View? = null
    private var pendingAreaId: Long? = null

    private val geoMapDelegate by lazy { mainMapFragment.geoMapDelegate }
    private val bikeMapDelegate by lazy { mainMapFragment.bikeMapDelegate }
    private val parkPointMapDelegate by lazy { mainMapFragment.parkPointMapDelegate }
    private val prohibitAreaMapDelegate by lazy { mainMapFragment.prohibitAreaMapDelegate }
    private val routeLineMapDelegate by lazy { mainMapFragment.routeLineMapDelegate }
    private val presenter = MainPresenter(this)

    private val publish = PublishSubject.create<Int>()
    private val loadDataDisposable = publish.sample(1, TimeUnit.SECONDS)
        .observeOn(AndroidSchedulers.mainThread())
        .subscribeBy(onNext = { loadDataImpl() })
    @RequiresApi(Build.VERSION_CODES.M)
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        this.MyView = view
        super.onViewCreated(view, savedInstanceState)
        EventBus.getDefault().register(this);

        lifecycle.addObserver(presenter)

        mainAdFragment.getMyView()?.findViewById<RelativeLayout>(R.id.rl_ad_banner_over)?.setOnTouchListener(View.OnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                (activity as HomeActivity).setViewPageMove(false)
                MyView?.findViewById<CustomNestedScrollView>(R.id.nscl)?.setEnableScroll(false)
            }
            false // 返回true表示已经处理了触摸事件，不再传递
        })

        MyView?.findViewById<RelativeLayout>(R.id.rl_map)?.setOnTouchListener(View.OnTouchListener { v, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                (activity as HomeActivity).setViewPageMove(false)
                MyView?.findViewById<CustomNestedScrollView>(R.id.nscl)?.setEnableScroll(false)
            }
            false // 返回true表示已经处理了触摸事件，不再传递
        })
//        MyView?.findViewById<LinearLayout>(R.id.frag_home)?.setOnTouchListener(View.OnTouchListener { v, event ->
//            false // 返回true表示已经处理了触摸事件，不再传递
//        })

        mainMapFragment.onAreaIdSucListener = {
            getbottomAdData()
            mainAdFragment.getData()
            onAreaIdSucListener("HomeFrag_MapListener")
            if (!Glob.isLogin){
                if (Glob.isRental){
                    MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(R.string.scan_lease)
                }else{
                    MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(com.tbit.uqbike.R.string.scan_unlock)
                }
            }else{
                getRideState()
                mainBusinessFragment.getInviteData()
            }
            (activity as HomeActivity).getMapData()
        }
        mainMapFragment.onAreaIdFailListener = {
            getbottomAdData()
            mainAdFragment.getData()
            onAreaIdFailListener("HomeFrag_MapListener")
            (activity as HomeActivity).getMapData()
        }
        mainMapFragment.onSearchCenterChangeListener = { onSearchCenterChange() }
        mainMapFragment.onMsgUresdListener = {
//            mainAdFragment.updaMsg(it)
        }
        mainAdFragment.setTopState()

        mainMapFragment.sumitLocal(false)

        MyView?.findViewById<LinearLayout>(R.id.ly_menu_balance)?.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("wallet_icon_click",properties)
            if(!(activity as BaseActivity).hasLocationPermission()){
                (activity as BaseActivity).openLocal()
                return@clickDelay
            }
            if ((activity as BaseActivity).tryLoginIfNot()) activity?.startActivity<WalletActivity>()
        }
        MyView?.findViewById<LinearLayout>(R.id.ly_menu_cost)?.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("billing_rules_icon_click",properties)
            if ((activity as BaseActivity).tryLoginIfNot()) {
                loadingDialogHelper!!.show {  }
                PageModel.getPageUrl(PageModel.billing_rules).subscribeBy(
                    onNext = {
                        loadingDialogHelper!!.dismiss()
                        MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                        var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                        activity?.startActivity<WebActivity>(
                            WebActivity.TITLE to getString(R.string.billing_rules),
                            WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
                    },
                    onError = {loadingDialogHelper!!.dismiss()}
                ).toCancelable()
            }
        }
//        binding.layoutMainBottomMenu
        MyView?.findViewById<LinearLayout>(R.id.ly_menu_more)?.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("more_icon_click",properties)
            if(!IsAuth()) return@clickDelay
            if(!(activity as BaseActivity).hasLocationPermission()){
                (activity as BaseActivity).openLocal()
                return@clickDelay
            }
            if ((activity as BaseActivity).tryLoginIfNot()) {
                lifecycleDialogHelper.show(UserHelpBottomSheep((activity as BaseActivity)))
            }
        }
//        binding.layoutMainBottomMenu
        MyView?.findViewById<LinearLayout>(R.id.ly_menu_hotline)?.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("contact_icon_click",properties)
            if(!IsAuth()) return@clickDelay
            if(!(activity as BaseActivity).hasLocationPermission()){
                (activity as BaseActivity).openLocal()
                return@clickDelay
            }
//            MainDialogUtil.kfDig(loadingDialogHelper,lifecycleDialogHelper)
            if ((activity as BaseActivity).tryLoginIfNot()) {
                lifecycleDialogHelper.show(kfBottomSheep((activity as BaseActivity)))
            }
        }

        MyView?.findViewById<RelativeLayout>(R.id.rl_scanner)?.clickDelay {
            if (Glob.isRental){
                val properties = JSONObject()
                MDUtil.clickEvent("scan_rent_car_click",properties)
            }else{
                val properties = JSONObject()
                MDUtil.clickEvent("scan_ride_bike_click",properties)
            }
            if(!IsAuth()) return@clickDelay
            if ((activity as BaseActivity).tryLoginIfNot()) {
                if (!(activity as BaseActivity).hasCamPermission()){
                    if (SpUtil.getInstance().find(Constant.SpKey.SP_PERM_CAM).isNullOrEmpty()){
                        SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_PERM_CAM,"1")
                    }else{
                        CommDialog.Builder(activity).setTitle(getString(R.string.s_per_carm_title)).setContent(getString(R.string.s_per_carm_cont))
                            .setLeftText(ResUtil.getString(R.string.cancel)).setRightText(getString(R.string.s_set)).setCanceledOnOutside(true)
                            .setClickListen(object : CommDialog.TwoSelDialog {
                                override fun leftClick() {}
                                override fun rightClick() {
                                    LocationUtil.gotoLocalPermiss(activity!!)
                                }
                            }).build().show()
                        return@clickDelay
                    }
                }

                (childFragmentManager.findFragmentById(R.id.fragment_camera_permission) as CameraPermissionFragment).requestPermission {
                    if(!(activity as BaseActivity).hasLocationPermission()){
                        (activity as BaseActivity).openLocal()
                    }else{
                        if (!LocationUtil.isGpsEnabled()) {
                            MainDialogUtil.GpsDig(activity as HomeActivity)
                            return@requestPermission
                        }
                        if(MainState == 2){
                            var isRental = false
                            if (MainOrderState == Constant.OrderType.RENTAL_Y) isRental = true
//                            MainDialogUtil.RecordDetailDig(this@MainActivity,orderNo,isRental)
                            activity?.startActivity(RecordDetailActivity.createIntent(activity!!,orderNo,false,isRental))
                        }else if(MainState == 1){
//                            binding.layoutMainBottomMenu.imgGohome.performClick()
                        }else{
                            // Disable the loading dialog here to avoid flicker before scan activity
                            /*
                            loadingDialogHelper?.show {  }
                            */
                            UserModel.getCreatRideOrder()
                                .subscribeBy(
                                    onNext = {
                                        loadingDialogHelper?.dismiss()
                                        MyLogUtil.Log("1111","用户是否可以创建订单==="+it.toString())
                                        if (!it.toString().equals("[]")){
                                            val resultData = Gson().fromJson(it.toString(), isFirstOrderData::class.java)
                                            if (resultData != null) {
                                                isFirstOrder = resultData.is_first
                                                if (resultData.is_first_temp){
                                                    SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_NEWUSERCAR,"1")
                                                }else{
                                                    SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_NEWUSERCAR,"")
                                                }
                                            }
                                        }else{
                                            isFirstOrder = false
                                        }
                                        if (Glob.isRental){
                                            goToScanBorrow()
                                        }else{
                                            RidingDialogUtil.showRideCardDialog(activity as BaseActivity, onNotify = {goToScanBorrow()})
                                        }
                                    },
                                    onError = {
                                        val errCode = ErrHandler.getErrCode(it)
                                        if (errCode == Constant.ErrCode.RIDE_LOW){
                                            //sys_minimum_amount 最低骑行额度
                                            val listKey = arrayOf("sys_minimum_amount")
                                            ComModel.getConfig(listKey).subscribeBy(
                                                onNext = {
                                                    loadingDialogHelper?.dismiss()
                                                    MyLogUtil.Log("1111","===获取最低骑行额度 信息=="+it.toString())
                                                    var resultData = Gson().fromJson(it.toString(), SysMinAmount::class.java)
                                                    MainDialogUtil.MinAmountDig(activity as BaseActivity,resultData.sys_minimum_amount)
                                                },
                                                onError = {loadingDialogHelper?.dismiss()}
                                            ).toCancelable()
                                        }else{
                                            MyToastUtil.toast(ErrHandler.getErrMsg(it))
                                            loadingDialogHelper?.dismiss()
                                        }
                                    }
                                ).toCancelable()
                        }
                    }
                }

            }

        }

        MyView?.findViewById<ImageView>(R.id.image_big)?.setOnClickListener {
            if (!Glob.isLogin) {
                if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
                    MyToastUtil.toast(ResUtil.getString(R.string.s_local_err))
                    return@setOnClickListener
                }

                MyToastUtil.toast(ResUtil.getString(R.string.login_first))
                FlavorConfig.appRoute.login()
                return@setOnClickListener
            }
            startActivity<MainActivity>()
        }
        MyView?.findViewById<RoundTextView>(R.id.rv_seeOrder)?.setOnClickListener {
            if (!Glob.isLogin) {
                if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
                    MyToastUtil.toast(ResUtil.getString(R.string.s_local_err))
                    return@setOnClickListener
                }

                MyToastUtil.toast(ResUtil.getString(R.string.login_first))
                FlavorConfig.appRoute.login()
                return@setOnClickListener
            }
            startActivity<MainActivity>()
        }

        checkGoshopH5Enable(view)

        // 临时调试：强制显示H5区域进行测试
        // 注释掉这行代码在生产环境中使用
        // forceShowH5AreaForDebug(view)
    }

    // 临时调试方法：强制显示H5区域
    private fun forceShowH5AreaForDebug(rootView: View) {
        MyLogUtil.Log("GOSHOP_H5", "=== 强制显示H5区域进行调试 ===")
        val h5Area = rootView.findViewById<ViewGroup>(R.id.layout_h5_area)
        if (h5Area != null && h5Area.childCount == 0) {
            // 直接创建WebView进行测试
            val metrics = rootView.context.resources.displayMetrics
            val height = metrics.heightPixels

            val webView = DWebView(rootView.context)
            val jsApi = com.tbit.uqbike.web.js.JsApi()

            // 设置JsApi的点击监听，处理setWebpageHeight
            jsApi.setOnItemClickListener(object : com.tbit.uqbike.web.js.JsApi.OnClickListener {
                override fun onClickItem(msg: String?, handler: wendu.dsbridge.CompletionHandler<String>) {
                    MyLogUtil.Log("GOSHOP_H5", "强制显示：JS调用: $msg")
                    try {
                        val jsonObject = org.json.JSONObject(msg)
                        val type = jsonObject.optString("type")

                        if (type == "setWebpageHeight") {
                            val data = jsonObject.optJSONObject("data")
                            val height = data?.optString("height") ?: "0"
                            MyLogUtil.Log("GOSHOP_H5", "强制显示：收到高度设置请求: ${height}")

                            // 暂时忽略JS的高度设置，让WebView自然展开
                            MyLogUtil.Log("GOSHOP_H5", "强制显示：忽略JS高度设置，让WebView自然展开显示完整内容")
                            handler.complete("{\"code\":0,\"data\":\"ignored for full content display\"}")

                            // 可选：记录JS尝试设置的高度，用于调试
                            try {
                                val heightPx = height.toFloat()
                                MyLogUtil.Log("GOSHOP_H5", "强制显示：JS尝试设置高度: ${heightPx.toInt()}px（已忽略）")
                            } catch (e: Exception) {
                                MyLogUtil.Log("GOSHOP_H5", "强制显示：JS高度解析异常: ${e.message}")
                            }
                        } else {
                            // 其他类型的JS调用，使用默认处理
                            if (activity is com.tbit.uqbike.base.BaseActivity) {
                                com.tbit.uqbike.web.js.util.webUtil.goJsMethod(activity as com.tbit.uqbike.base.BaseActivity, handler, msg!!)
                            } else {
                                MyLogUtil.Log("GOSHOP_H5", "强制显示：activity不是BaseActivity类型，无法处理JS调用")
                                handler.complete("{\"code\":-1,\"data\":\"activity type error\"}")
                            }
                        }
                    } catch (e: Exception) {
                        MyLogUtil.Log("GOSHOP_H5", "强制显示：JS调用解析异常: ${e.message}")
                        handler.complete("{\"code\":-1,\"data\":\"${e.message}\"}")
                    }
                }
            })

            webView.addJavascriptObject(jsApi, null)
            val url = FlavorConfig.NET.GOSHOP_WEB_HOST + "/app/wealth/tao_recommend?locale=" + FlavorConfig.Local.language
            MyLogUtil.Log("GOSHOP_H5", "强制显示：H5推荐区域URL: $url")
            MyLogUtil.Log("GOSHOP_H5", "强制显示：GOSHOP_WEB_HOST: ${FlavorConfig.NET.GOSHOP_WEB_HOST}")
            MyLogUtil.Log("GOSHOP_H5", "强制显示：语言参数: ${FlavorConfig.Local.language}")

            webView.settings.domStorageEnabled = true
            webView.settings.javaScriptEnabled = true
            webView.settings.allowUniversalAccessFromFileURLs = true
            webView.settings.offscreenPreRaster = true
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP) {
                webView.settings.mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            }
            webView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            // 禁用WebView内部滚动，让整个屏幕统一滚动
            webView.overScrollMode = View.OVER_SCROLL_NEVER
            webView.isVerticalScrollBarEnabled = false
            webView.isHorizontalScrollBarEnabled = false

            // 禁用WebView的自动聚焦和滚动行为
            webView.isFocusable = false
            webView.isFocusableInTouchMode = false
            webView.clearFocus()

            // 禁用WebView的自动滚动到输入框功能
            webView.settings.setSupportZoom(false)
            webView.settings.builtInZoomControls = false
            webView.settings.displayZoomControls = false

            MyLogUtil.Log("GOSHOP_H5", "强制显示：WebView配置完成，禁用内部滚动和自动聚焦，支持整屏滚动")

            // 设置WebView客户端监听页面加载
            webView.webViewClient = object : android.webkit.WebViewClient() {
                override fun onPageFinished(view: android.webkit.WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    MyLogUtil.Log("GOSHOP_H5", "强制显示：H5页面加载完成，内容将自然展开: $url")

                    // 页面加载完成后，确保WebView不会获取焦点并自动滚动到顶部
                    view?.clearFocus()
                    view?.isFocusable = false
                    view?.isFocusableInTouchMode = false
                    MyLogUtil.Log("GOSHOP_H5", "强制显示：页面加载完成，已禁用WebView自动聚焦")
                }

                override fun onPageStarted(view: android.webkit.WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                    super.onPageStarted(view, url, favicon)
                    MyLogUtil.Log("GOSHOP_H5", "强制显示：H5页面开始加载: $url")
                }

                override fun onReceivedError(view: android.webkit.WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                    super.onReceivedError(view, errorCode, description, failingUrl)
                    MyLogUtil.Log("GOSHOP_H5", "强制显示：WebView加载错误: $errorCode, $description, $failingUrl")
                }
            }

            // 设置触摸事件处理，让WebView支持整屏滚动
            webView.setOnTouchListener { v, event ->
                // 不拦截触摸事件，让父视图处理滚动
                false // 返回false，让触摸事件传递给父视图处理整屏滚动
            }

            webView.loadUrl(url)

            // 创建容器，使用WRAP_CONTENT让内容完全展开
            val constrainedWebView = FrameLayout(rootView.context)
            MyLogUtil.Log("GOSHOP_H5", "强制显示：创建WebView容器，使用WRAP_CONTENT完全展开")

            constrainedWebView.addView(webView, FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            ))

            // 设置H5区域的布局参数，使用WRAP_CONTENT完全展开
            val h5LayoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            h5Area.addView(constrainedWebView, h5LayoutParams)

            MyLogUtil.Log("GOSHOP_H5", "强制显示：H5区域已添加，使用WRAP_CONTENT完全展开，支持整屏滚动")
            h5Area.visibility = View.VISIBLE
            MyLogUtil.Log("GOSHOP_H5", "强制显示：H5区域设置为可见")

            // 确保WebView添加到布局后不会自动获取焦点
            webView.post {
                webView.clearFocus()
                MyLogUtil.Log("GOSHOP_H5", "强制显示：WebView添加到布局后已清除焦点")
            }
            // 临时添加背景色便于调试
            h5Area.setBackgroundColor(android.graphics.Color.parseColor("#FFFF00")) // 黄色背景
            MyLogUtil.Log("GOSHOP_H5", "强制显示：设置H5区域黄色背景便于调试")

            // 确保父容器也是可见的
            val adArea = MyView?.findViewById<LinearLayout>(R.id.layout_ad_area)
            if (adArea?.visibility != View.VISIBLE) {
                adArea?.visibility = View.VISIBLE
                MyLogUtil.Log("GOSHOP_H5", "强制显示：设置广告区域父容器为可见")
            }

            MyLogUtil.Log("GOSHOP_H5", "强制显示H5区域完成: ${url}")

            // 延迟检查布局信息，等待布局完成
            h5Area.post {
                MyLogUtil.Log("GOSHOP_H5", "=== 延迟布局调试信息 ===")
                MyLogUtil.Log("GOSHOP_H5", "h5Area.visibility: ${h5Area.visibility}")
                MyLogUtil.Log("GOSHOP_H5", "h5Area.childCount: ${h5Area.childCount}")
                MyLogUtil.Log("GOSHOP_H5", "h5Area.width: ${h5Area.width}, h5Area.height: ${h5Area.height}")
                MyLogUtil.Log("GOSHOP_H5", "adArea.visibility: ${adArea?.visibility}")
                MyLogUtil.Log("GOSHOP_H5", "adArea.width: ${adArea?.width}, adArea.height: ${adArea?.height}")

                // 检查WebView的状态
                if (h5Area.childCount > 0) {
                    val firstChild = h5Area.getChildAt(0)
                    MyLogUtil.Log("GOSHOP_H5", "firstChild类型: ${firstChild::class.java.simpleName}")
                    MyLogUtil.Log("GOSHOP_H5", "firstChild.visibility: ${firstChild.visibility}")
                    MyLogUtil.Log("GOSHOP_H5", "firstChild.width: ${firstChild.width}, firstChild.height: ${firstChild.height}")

                    if (firstChild is FrameLayout && firstChild.childCount > 0) {
                        val webView = firstChild.getChildAt(0)
                        MyLogUtil.Log("GOSHOP_H5", "webView类型: ${webView::class.java.simpleName}")
                        MyLogUtil.Log("GOSHOP_H5", "webView.visibility: ${webView.visibility}")
                        MyLogUtil.Log("GOSHOP_H5", "webView.width: ${webView.width}, webView.height: ${webView.height}")
                    }
                }

                // H5区域高度将由JS动态设置，无需强制设置
                if (h5Area.height == 0) {
                    MyLogUtil.Log("GOSHOP_H5", "H5区域高度为0，等待JS设置高度")
                } else {
                    MyLogUtil.Log("GOSHOP_H5", "H5区域当前高度: ${h5Area.height}px")
                }
            }
        }
    }

    private var isCheckingH5Enable = false // 防止重复请求的标志

    private fun checkGoshopH5Enable(rootView: View) {
        MyLogUtil.Log("GOSHOP_H5", "=== 开始检查H5区域 ===")

        // 检查当前环境状态
        val isTestEnv = !SpUtil.getInstance().find(Constant.SpKey.SP_TESTNET).isNullOrEmpty()
        MyLogUtil.Log("GOSHOP_H5", "当前环境状态: ${if (isTestEnv) "测试环境" else "生产环境"}")
        MyLogUtil.Log("GOSHOP_H5", "当前GOSHOP_WEB_HOST: ${FlavorConfig.NET.GOSHOP_WEB_HOST}")

        // 防止重复请求
        if (isCheckingH5Enable) {
            MyLogUtil.Log("GOSHOP_H5", "正在检查中，跳过重复请求")
            return
        }

        val h5Area = rootView.findViewById<ViewGroup>(R.id.layout_h5_area)
        MyLogUtil.Log("GOSHOP_H5", "H5区域当前状态: visibility=${h5Area?.visibility}, childCount=${h5Area?.childCount}")

        // 如果H5区域已经有内容且可见，不需要重复检查
        if (h5Area?.visibility == View.VISIBLE && h5Area.childCount > 0) {
            MyLogUtil.Log("GOSHOP_H5", "H5区域已存在内容，跳过检查")
            return
        }

        isCheckingH5Enable = true
        val apiUrl = FlavorConfig.NET.COM_URL + "1.0/common/config/self/taokeisopen"
        MyLogUtil.Log("GOSHOP_H5", "开始API请求: $apiUrl")
        MyLogUtil.Log("GOSHOP_H5", "当前网络状态: ${NetUtils.isConnected(rootView.context)}")
        MyLogUtil.Log("GOSHOP_H5", "当前登录状态: ${Glob.isLogin}")
        MyLogUtil.Log("GOSHOP_H5", "当前区域ID: ${Glob.area_id}")
        ComModel.getTaoKeIsOpen().subscribeBy(
            onNext = {
                isCheckingH5Enable = false
                val json = it.toString()
                MyLogUtil.Log("GOSHOP_H5", "API返回: $json")
                val isOpen = try {
                    val value = org.json.JSONObject(json).opt("is_open")
                    val result = when (value) {
                        is Number -> value.toDouble() == 1.0
                        is String -> value == "1"
                        else -> false
                    }
                    MyLogUtil.Log("GOSHOP_H5", "解析结果: is_open=$value, isOpen=$result")
                    result
                } catch (e: Exception) {
                    MyLogUtil.Log("GOSHOP_H5", "解析异常: ${e.message}")
                    false
                }
                val h5Area = rootView.findViewById<ViewGroup>(R.id.layout_h5_area)
                if (isOpen) {
                    h5Area.visibility = View.VISIBLE
                    MyLogUtil.Log("GOSHOP_H5", "API创建：H5区域设置为可见")
                    if (h5Area.childCount == 0) {
                        val displayMetrics = rootView.context.resources.displayMetrics
                        MyLogUtil.Log("GOSHOP_H5", "开始创建WebView，无高度限制")

                        val webView = DWebView(rootView.context)
                        val jsApi = com.tbit.uqbike.web.js.JsApi()

                        // 设置JsApi的点击监听，处理setWebpageHeight
                        jsApi.setOnItemClickListener(object : com.tbit.uqbike.web.js.JsApi.OnClickListener {
                            override fun onClickItem(msg: String?, handler: wendu.dsbridge.CompletionHandler<String>) {
                                MyLogUtil.Log("GOSHOP_H5", "API创建：JS调用: $msg")
                                try {
                                    val jsonObject = org.json.JSONObject(msg)
                                    val type = jsonObject.optString("type")

                                    if (type == "setWebpageHeight") {
                                        val data = jsonObject.optJSONObject("data")
                                        val height = data?.optString("height") ?: "0"
                                        MyLogUtil.Log("GOSHOP_H5", "API创建：收到高度设置请求: ${height}")

                                        // 暂时忽略JS的高度设置，让WebView自然展开
                                        MyLogUtil.Log("GOSHOP_H5", "API创建：忽略JS高度设置，让WebView自然展开显示完整内容")
                                        handler.complete("{\"code\":0,\"data\":\"ignored for full content display\"}")

                                        // 可选：记录JS尝试设置的高度，用于调试
                                        try {
                                            val heightPx = height.toFloat()
                                            MyLogUtil.Log("GOSHOP_H5", "API创建：JS尝试设置高度: ${heightPx.toInt()}px（已忽略）")
                                        } catch (e: Exception) {
                                            MyLogUtil.Log("GOSHOP_H5", "API创建：JS高度解析异常: ${e.message}")
                                        }
                                    } else {
                                        // 其他类型的JS调用，使用默认处理
                                        if (activity is com.tbit.uqbike.base.BaseActivity) {
                                            com.tbit.uqbike.web.js.util.webUtil.goJsMethod(activity as com.tbit.uqbike.base.BaseActivity, handler, msg!!)
                                        } else {
                                            MyLogUtil.Log("GOSHOP_H5", "API创建：activity不是BaseActivity类型，无法处理JS调用")
                                            handler.complete("{\"code\":-1,\"data\":\"activity type error\"}")
                                        }
                                    }
                                } catch (e: Exception) {
                                    MyLogUtil.Log("GOSHOP_H5", "API创建：JS调用解析异常: ${e.message}")
                                    handler.complete("{\"code\":-1,\"data\":\"${e.message}\"}")
                                }
                            }
                        })

                        webView.addJavascriptObject(jsApi, null)
                        val url = FlavorConfig.NET.GOSHOP_WEB_HOST + "/app/wealth/tao_recommend?locale=" + FlavorConfig.Local.language
                        MyLogUtil.Log("GOSHOP_H5", "API创建：H5推荐区域URL: $url")
                        MyLogUtil.Log("GOSHOP_H5", "API创建：GOSHOP_WEB_HOST: ${FlavorConfig.NET.GOSHOP_WEB_HOST}")
                        MyLogUtil.Log("GOSHOP_H5", "API创建：语言参数: ${FlavorConfig.Local.language}")

                        // WebView配置
                        webView.settings.domStorageEnabled = true
                        webView.settings.javaScriptEnabled = true
                        webView.settings.allowUniversalAccessFromFileURLs = true
                        webView.settings.offscreenPreRaster = true
                        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP) {
                            webView.settings.mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                        }
                        webView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                        // 禁用WebView内部滚动，让整个屏幕统一滚动
                        webView.overScrollMode = View.OVER_SCROLL_NEVER
                        webView.isVerticalScrollBarEnabled = false
                        webView.isHorizontalScrollBarEnabled = false

                        // 禁用WebView的自动聚焦和滚动行为
                        webView.isFocusable = false
                        webView.isFocusableInTouchMode = false
                        webView.clearFocus()

                        // 禁用WebView的自动滚动到输入框功能
                        webView.settings.setSupportZoom(false)
                        webView.settings.builtInZoomControls = false
                        webView.settings.displayZoomControls = false

                        MyLogUtil.Log("GOSHOP_H5", "API创建：WebView配置完成，禁用内部滚动和自动聚焦，支持整屏滚动")

                        // 设置WebView客户端监听页面加载
                        webView.webViewClient = object : android.webkit.WebViewClient() {
                            override fun onPageFinished(view: android.webkit.WebView?, url: String?) {
                                super.onPageFinished(view, url)
                                MyLogUtil.Log("GOSHOP_H5", "H5页面加载完成，内容将自然展开: $url")

                                // 页面加载完成后，确保WebView不会获取焦点并自动滚动到顶部
                                view?.clearFocus()
                                view?.isFocusable = false
                                view?.isFocusableInTouchMode = false
                                MyLogUtil.Log("GOSHOP_H5", "API创建：页面加载完成，已禁用WebView自动聚焦")

                                // 页面加载完成后，记录内容高度信息
                                view?.post {
                                    // 获取WebView内容高度
                                    val contentHeight = view.contentHeight * view.scale
                                    MyLogUtil.Log("GOSHOP_H5", "WebView内容高度: ${contentHeight}px，无高度限制，内容完全展开")
                                }
                            }

                            override fun onPageStarted(view: android.webkit.WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                                super.onPageStarted(view, url, favicon)
                                MyLogUtil.Log("GOSHOP_H5", "H5页面开始加载: $url")
                            }
                        }

                        webView.loadUrl(url)

                        // 创建容器，使用WRAP_CONTENT让内容完全展开
                        val constrainedWebView = FrameLayout(rootView.context)
                        MyLogUtil.Log("GOSHOP_H5", "API创建：创建WebView容器，使用WRAP_CONTENT完全展开")

                        // WebView滚动配置已在上面设置，这里只处理触摸事件

                        // 设置触摸事件处理，让WebView支持整屏滚动
                        webView.setOnTouchListener { v, event ->
                            // 不拦截触摸事件，让父视图处理滚动
                            false // 返回false，让触摸事件传递给父视图处理整屏滚动
                        }

                        constrainedWebView.addView(webView, FrameLayout.LayoutParams(
                            FrameLayout.LayoutParams.MATCH_PARENT,
                            FrameLayout.LayoutParams.WRAP_CONTENT
                        ))

                        // 设置H5区域的布局参数，使用WRAP_CONTENT完全展开
                        val h5LayoutParams = ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                        h5Area.addView(constrainedWebView, h5LayoutParams)

                        MyLogUtil.Log("GOSHOP_H5", "API创建：H5区域已添加，使用WRAP_CONTENT完全展开，支持整屏滚动")

                        MyLogUtil.Log("GOSHOP_H5", "WebView已添加，无高度限制，内容将完全展开")

                        // 确保WebView添加到布局后不会自动获取焦点
                        webView.post {
                            webView.clearFocus()
                            MyLogUtil.Log("GOSHOP_H5", "API创建：WebView添加到布局后已清除焦点")
                        }

                        // 确保父容器也是可见的
                        val adArea = MyView?.findViewById<LinearLayout>(R.id.layout_ad_area)
                        if (adArea?.visibility != View.VISIBLE) {
                            adArea?.visibility = View.VISIBLE
                            MyLogUtil.Log("GOSHOP_H5", "API创建：强制设置广告区域父容器为可见")
                        }

                        // 添加详细的布局调试信息
                        MyLogUtil.Log("GOSHOP_H5", "=== API创建后布局调试信息 ===")
                        MyLogUtil.Log("GOSHOP_H5", "h5Area.visibility: ${h5Area.visibility}")
                        MyLogUtil.Log("GOSHOP_H5", "h5Area.childCount: ${h5Area.childCount}")
                        MyLogUtil.Log("GOSHOP_H5", "adArea.visibility: ${adArea?.visibility}")

                        // 延迟检查布局信息，等待布局完成
                        h5Area.post {
                            MyLogUtil.Log("GOSHOP_H5", "=== 延迟布局检查 ===")
                            MyLogUtil.Log("GOSHOP_H5", "h5Area.width: ${h5Area.width}, h5Area.height: ${h5Area.height}")
                            MyLogUtil.Log("GOSHOP_H5", "adArea.width: ${adArea?.width}, adArea.height: ${adArea?.height}")

                            if (h5Area.childCount > 0) {
                                val firstChild = h5Area.getChildAt(0)
                                MyLogUtil.Log("GOSHOP_H5", "firstChild.width: ${firstChild.width}, firstChild.height: ${firstChild.height}")
                            }
                        }
                    }
                } else {
                    h5Area.visibility = View.GONE
                    MyLogUtil.Log("GOSHOP_H5", "API返回is_open=false，隐藏H5区域")
                }
            },
            onError = {
                isCheckingH5Enable = false // 重置标志
                // 增强异常日志输出
                MyLogUtil.Log("GOSHOP_H5", "API请求异常: ${it.message}")
                MyLogUtil.Log("GOSHOP_H5", "API请求异常类型: ${it::class.java.name}")
                MyLogUtil.Log("GOSHOP_H5", "API请求异常堆栈: ${android.util.Log.getStackTraceString(it)}")
                it.printStackTrace()
                rootView.findViewById<ViewGroup>(R.id.layout_h5_area).visibility = View.GONE
            }
        )
    }

    fun getbottomAdData(){
        ComModel.getAd(Glob.area_id, ComModel.TYPE_AD_4).subscribeBy(
            onNext = {
                MyLogUtil.Log("AD_DEBUG", "广告接口返回: $it")
                val resultData = Gson().fromJson(it.toString(), getAdData::class.java)
                if (resultData.size > 0){
                    val listKey = ArrayList<String>()
                    val listLink_url = ArrayList<String>()
                    val listTitle = ArrayList<String>()
                    val listIs_show_app_header = ArrayList<Int>()
                    resultData.forEach {
                        if (it.images.size > 0){
                            listKey.add(it.images[0])
                        }
                        listLink_url.add(it.link_url)
                        listTitle.add(it.title)
                        listIs_show_app_header.add(it.is_show_app_header!!)
                    }

                    if (listKey.size > 0){
                        MyView?.findViewById<ImageView>(R.id.im_image0)?.visibility = View.VISIBLE
                        MyView?.findViewById<ImageView>(R.id.im_image0)?.let { it1 ->
                            ImageLoad.loadimgWithCorner(listKey[0],
                                MyView?.findViewById<ImageView>(R.id.im_image0)!!,10
                            )
                        }
                        MyView?.findViewById<ImageView>(R.id.im_image0)?.setOnClickListener {
                            val link = UrlDecodeUtil().getParm(listLink_url[0])
                            AppUtil.goAdAciton(activity!!,listTitle[0],
                                AppUtil.parseLink(link)!!,listIs_show_app_header[0],1)
                        }
                    }else{
                        MyView?.findViewById<ImageView>(R.id.im_image0)?.visibility = View.GONE
                    }

                    if (listKey.size > 1){
                        MyView?.findViewById<ImageView>(R.id.im_image1)?.visibility = View.VISIBLE
                        MyView?.findViewById<ImageView>(R.id.im_image1)?.let { it1 ->
                            ImageLoad.loadimgWithCorner(listKey[1],
                                MyView?.findViewById<ImageView>(R.id.im_image1)!!,10
                            )
                        }
                        MyView?.findViewById<ImageView>(R.id.im_image1)?.setOnClickListener {
                            val link = UrlDecodeUtil().getParm(listLink_url[1])
                            AppUtil.goAdAciton(activity!!,listTitle[1],
                                AppUtil.parseLink(link)!!,listIs_show_app_header[1],1)
                        }
                    }else{
                        MyView?.findViewById<ImageView>(R.id.im_image1)?.visibility = View.GONE
                    }

                    if (listKey.size > 2){
                        MyView?.findViewById<ImageView>(R.id.im_image2)?.visibility = View.VISIBLE
                        MyView?.findViewById<ImageView>(R.id.im_image2)?.let { it1 ->
                            ImageLoad.loadimgWithCorner(listKey[2],
                                MyView?.findViewById<ImageView>(R.id.im_image2)!!,10
                            )
                        }
                        MyView?.findViewById<ImageView>(R.id.im_image2)?.setOnClickListener {
                            val link = UrlDecodeUtil().getParm(listLink_url[2])
                            AppUtil.goAdAciton(activity!!,listTitle[2],
                                AppUtil.parseLink(link)!!,listIs_show_app_header[2],1)
                        }
                    }else{
                        MyView?.findViewById<ImageView>(R.id.im_image2)?.visibility = View.GONE
                    }

                    if (listKey.size > 3){
                        MyView?.findViewById<ImageView>(R.id.im_image3)?.visibility = View.VISIBLE
                        MyView?.findViewById<ImageView>(R.id.im_image3)?.let { it1 ->
                            ImageLoad.loadimgWithCorner(listKey[3],
                                MyView?.findViewById<ImageView>(R.id.im_image3)!!,10
                            )
                        }
                        MyView?.findViewById<ImageView>(R.id.im_image3)?.setOnClickListener {
                            val link = UrlDecodeUtil().getParm(listLink_url[3])
                            AppUtil.goAdAciton(activity!!,listTitle[3],
                                AppUtil.parseLink(link)!!,listIs_show_app_header[3],1)
                        }
                    }else{
                        MyView?.findViewById<ImageView>(R.id.im_image3)?.visibility = View.GONE
                    }

                    if (listKey.size > 4){
                        MyView?.findViewById<ImageView>(R.id.im_image4)?.visibility = View.VISIBLE
                        MyView?.findViewById<ImageView>(R.id.im_image4)?.let { it1 ->
                            ImageLoad.loadimgWithCorner(listKey[4],
                                MyView?.findViewById<ImageView>(R.id.im_image4)!!,10
                            )
                        }
                        MyView?.findViewById<ImageView>(R.id.im_image4)?.setOnClickListener {
                            val link = UrlDecodeUtil().getParm(listLink_url[4])
                            AppUtil.goAdAciton(activity!!,listTitle[4],
                                AppUtil.parseLink(link)!!,listIs_show_app_header[4],1)
                        }
                    }else{
                        MyView?.findViewById<ImageView>(R.id.im_image4)?.visibility = View.GONE
                    }
                } else {
                    // 没有广告时，全部隐藏
                    MyView?.findViewById<ImageView>(R.id.im_image0)?.visibility = View.GONE
                    MyView?.findViewById<ImageView>(R.id.im_image1)?.visibility = View.GONE
                    MyView?.findViewById<ImageView>(R.id.im_image2)?.visibility = View.GONE
                    MyView?.findViewById<ImageView>(R.id.im_image3)?.visibility = View.GONE
                    MyView?.findViewById<ImageView>(R.id.im_image4)?.visibility = View.GONE
                }

                // 广告区整体隐藏逻辑
                val adArea = MyView?.findViewById<LinearLayout>(R.id.layout_ad_area)
                val adImgs = listOf(
                    MyView?.findViewById<ImageView>(R.id.im_image0),
                    MyView?.findViewById<ImageView>(R.id.im_image1),
                    MyView?.findViewById<ImageView>(R.id.im_image2),
                    MyView?.findViewById<ImageView>(R.id.im_image3),
                    MyView?.findViewById<ImageView>(R.id.im_image4)
                )
                val h5Area = MyView?.findViewById<View>(R.id.layout_h5_area)
                adImgs.forEachIndexed { idx, img ->
                    MyLogUtil.Log("AD_DEBUG", "im_image$idx visibility: ${img?.visibility}")
                }
                MyLogUtil.Log("AD_DEBUG", "layout_h5_area visibility: ${h5Area?.visibility}")
                MyLogUtil.Log("GOSHOP_H5", "广告加载完成后H5区域状态检查: visibility=${h5Area?.visibility}")

                val allAdGone = adImgs.all { it?.visibility == View.GONE }
                val h5AreaGone = h5Area?.visibility == View.GONE
                val allGone = allAdGone && h5AreaGone

                MyLogUtil.Log("GOSHOP_H5", "广告区域联动控制详细: allAdGone=$allAdGone, h5AreaGone=$h5AreaGone, allGone=$allGone")

                adArea?.visibility = if (allGone) View.GONE else View.VISIBLE
                MyLogUtil.Log("AD_DEBUG", "layout_ad_area visibility: ${adArea?.visibility}")
                MyLogUtil.Log("GOSHOP_H5", "广告区域联动控制结果: allGone=$allGone, adArea.visibility=${adArea?.visibility}")
            }
        ).toCancelable()
    }

    fun goScan(){
        MyView?.findViewById<RelativeLayout>(R.id.rl_scanner)?.performClick()
    }
    private fun goToScanBorrow() {
//        startActivity<ScanForBorrowActivity>()
        activity?.startActivity(ScanForBorrowActivity.createIntent(activity!!, ScanForBorrowActivity.TYPE_CAR))
    }
    fun IsAuth() : Boolean{
        if(SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
            activity?.startActivity<AuthActivity>()
            activity?.finish()
            return false
        }
        return true
    }
    @RequiresApi(Build.VERSION_CODES.M)
    fun AutoTask(){
        mainAdFragment.setTopState()
        getRideState()

        // 防止定时任务导致H5区域重新检查时出现布局异常
        // 如果H5区域已经显示，不要重复检查，避免重新创建WebView
        val h5Area = MyView?.findViewById<ViewGroup>(R.id.layout_h5_area)
        if (h5Area?.visibility != View.VISIBLE || h5Area.childCount == 0) {
            // 只有在H5区域未显示或没有内容时才重新检查
            MyLogUtil.Log("GOSHOP_H5", "定时任务触发H5区域检查")
            checkGoshopH5Enable(MyView!!)
        } else {
            // H5区域已显示，跳过重复检查（减少日志输出）
        }
    }


    private var isFirstOrder = false
    var isFirstGetOrderState = true
    @RequiresApi(Build.VERSION_CODES.M)
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: BaseEventData) {
        MyLogUtil.Log("1111", "==== event message ==="+event.code+","+event.msg)
        if(event.code == EventUtil.EVENT_NOTITY){
            val resultData: CarInfoData = Gson().fromJson(event.msg, CarInfoData::class.java)
//            MyView?.postDelayed({
//                if (Glob.isRental){
////                    var carRentalDialog = CarRentalDialog(this@MainActivity,resultData)
////                    lifecycleDialogHelper.show(carRentalDialog)
//                    activity?.startActivity<WebActionActivity>(
//                        WebActionActivity.TITLE to getString(R.string.s_sureorder),
//                        WebActionActivity.URL to UrlDecodeUtil().getParm(
//                            FlavorConfig.NET.H5_URL+"checkOrder?vehicle_no="+resultData.vehicle_no+
//                                    "&remaining_mileage="+resultData.remaining_mileage+ "&park_name="+resultData.park_name+"&park_area_id="+resultData.park_id+"&locale="+
//                                    FlavorConfig.Local.language+"&currency="+ Glob.CurrencyUnit))
//                }
//            },800)
            MyView?.postDelayed({
                if (isFirstOrder && !Glob.isRental){
                    activity?.startActivity<UserCarActivity>(
                        UserCarActivity.TYPE to UserCarActivity.TYPE_NEW,
                        UserCarActivity.UNIT to "")
                }
            },1000)
        }else if(event.code == EventUtil.EVENT_LOGINOUT){
            if (Glob.isRental){
//                binding.textScanner.text = getString(R.string.scan_lease)
                MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(R.string.scan_lease)
            }else{
//                binding.textScanner.text = getString(com.tbit.uqbike.R.string.scan_unlock)
                MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(com.tbit.uqbike.R.string.scan_unlock)
            }
            setBomRidingView(false,false)
        }else if(event.code == EventUtil.EVENT_RIDINGINFO){
            MyView?.postDelayed({
//                bikeRidingFragment.getConfigByRiding(false, MainStateTypeIsRental)
//                bikeRidingFragment.getOrderInfo(orderNo,false)
//                bikeRidingBehavior.state = BottomSheetBehavior.STATE_EXPANDED
//                setViewRiding()
//                binding.rlMainRiding.visibility = View.GONE
//                setStopingVis()
            },800)
        }else if(event.code == EventUtil.EVENT_LOCAL){
            MyLogUtil.Log("4444","====定位授权 main========")
            LocationModel.onRequestPermissionSuccess()
            (activity as HomeActivity).getMapData()
        } else if(event.code == EventUtil.EVENT_INFOVIEW){
            MyLogUtil.Log("1111","====infoview click========")
//            AppUtil.startNav(this@MainActivity,latLngMy!!,latLngTag!!,isWalk)
        } else if(event.code == EventUtil.EVENT_GORIDE){
//            binding.layoutMainBottomMenu.imgGohome.performClick()
        }else if (event.code == EventUtil.EVENT_LEASESUC){
            isFirstGetOrderState = true
//            if (isHomeState){
//                binding.lyOver.postDelayed({
//                    MainState = 1
//                    binding.layoutMainBottomMenu.imgGohome.performClick()
//                },500)
//            }
        }
        if(event.code == EventUtil.EVENT_EXIT) activity!!.finish()
    }
    @RequiresApi(Build.VERSION_CODES.M)
    fun onRestar(){
        try {
            (activity as HomeActivity).getMapData()
            mainAdFragment.onRestart()
        }catch (e : NullPointerException){}
    }

    override fun onResume() {
        super.onResume()
        mainAdFragment.setHeadImg()

        // Call the check function when fragment resumes
        tryTriggerAdLoad("onResume")
    }
    private fun onSearchCenterChange() {
        loadData()
    }
    private fun loadData() {
        publish.onNext(1)
    }

    var oldLatlng = LatLng()
    var isMoveMapFirst = true
    private fun loadDataImpl() {
        val latLng = mainMapFragment.searchCenter
        MyLogUtil.Log("4444","地图移动=："+latLng.lat+","+latLng.lng)
        if(!SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
            if(GPSUtil.getDistance(oldLatlng.lng,oldLatlng.lat,latLng.lng,latLng.lat) > 10){
                mainMapFragment.IsOutArea()
            }
            MyLogUtil.Log("1111","地图移动====================："+isMoveMapFirst)
            MyView?.findViewById<RelativeLayout>(R.id.rl_map)?.postDelayed({isMoveMapFirst = false},2000)
            if(!isMoveMapFirst){
                if(GPSUtil.getDistance(oldLatlng.lng,oldLatlng.lat,latLng.lng,latLng.lat) > 50){
                    oldLatlng = latLng
//                    routeLineMapDelegate.clean()
                    bikeMapDelegate.clear()
                    bikeMapDelegate.select(null)
                    parkPointMapDelegate.clear()
                    parkPointMapDelegate.select(null)
                    prohibitAreaMapDelegate.clear()
                    prohibitAreaMapDelegate.select(null)
                    getNearGeo()
                    getNearParkPointsAndProhibits()
                    getBikes()
                }
            }
        }
    }
    private fun getNearGeo() {
        val latLng = mainMapFragment.searchCenter
//        MyLogUtil.Log("1111","地理位置："+latLng.lat+","+latLng.lng)
        presenter.getNearGeo(latLng.lat, latLng.lng, 5000)
    }

    // 获取主页数据 及设置
    @RequiresApi(Build.VERSION_CODES.M)
    fun getMainData(){
        mainMapFragment.sumitLocal(false)
        getNearGeo()
        getNearParkPointsAndProhibits()
        getBikes()
        mainBusinessFragment.sumitEvent(mainMapFragment.getLocation())
        mainMapFragment.IsOutArea()

        if (Glob.isLogin) {
            getRideState()

            ComModel.getNewRegister().subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取新手奖励 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), RigisterData::class.java)
                    if (resultData != null){
                        if (resultData.is_display){
                            var hintData = ""
                            var listData = ArrayList<Coupon>()
                            if (resultData.type == RIG_TYPE_NEWUSER){
                                if (resultData.present_amount != 0){
                                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_NEWUSER,resultData.present_amount.toString())
                                    hintData = getString(R.string.s_get) + AppUtil.formatAmount(resultData.present_amount.toLong())+Glob.CurrencyUnit+ getString(R.string.s_ridemoney)
                                    listData.add(
                                        Coupon(0,0f,0,0f,
                                        AppUtil.formatAmount(resultData.present_amount.toLong())+Glob.CurrencyUnit+ getString(R.string.s_ridemoney),0,
                                        0,0,false,
                                            type_coupon_money,"")
                                    )
                                }
                                if (resultData!!.ride_card != null){
                                    if (resultData.ride_card!!.number > 0){
                                        if (hintData.isNullOrEmpty()){
                                            hintData = getString(R.string.s_get) + getString(R.string.s_get_ridecard,resultData.ride_card.number.toString())
                                        }else{
                                            hintData = hintData + "、"+ getString(R.string.s_get_ridecard,resultData.ride_card.number.toString())
                                        }
                                        listData.add(
                                            Coupon(resultData.ride_card.ride_card_id,0f,0,0f,
                                            resultData.ride_card.name,0, 0,0,false,
                                                type_coupon_ridecard,"")
                                        )
                                    }
                                }
                            }
                            if(resultData.coupon.size > 0){
                                if (hintData.isNullOrEmpty()){
                                    hintData = getString(R.string.s_get) + getString(R.string.s_get_invite,resultData.coupon.size.toString())
                                }else{
                                    hintData = hintData + "、"+ getString(R.string.s_get_invite,resultData.coupon.size.toString())
                                }
                                resultData.coupon.forEach {
                                    listData.add(
                                        Coupon(it.user_coupon_id,it.amount,it.is_min_spend,it.min_spend_amount, it.name,it.end_time,
                                        0,it.type,false,
                                            type_coupon_invite,"")
                                    )
                                }
                            }

                            var inviteDialog = InviteDialog.newInstance(hintData)
                            if (resultData.type == RIG_TYPE_NEWUSER){
                                inviteDialog.type = InviteDialog.TYPE_NEWUSER
                            }else{
                                inviteDialog.type = InviteDialog.TYPE_INVITE
                            }
                            inviteDialog.listData = listData
                            lifecycleDialogHelper.show(inviteDialog)
                        }
                    }
                },
                onError = {}
            ).toCancelable()
        }

//        bikeRidingFragment.getConfigByRiding(false,true)
//        bikeRidingBehavior.state = BottomSheetBehavior.STATE_EXPANDED
    }

    fun moveUp(){
//        MyLogUtil.Log("1111","========setOnTouchListener===============")
        (activity as HomeActivity).setViewPageMove(true)
        MyView?.findViewById<CustomNestedScrollView>(R.id.nscl)?.setEnableScroll(true)
    }


    var MainState = 0 //0正常 ， 1存在进行中订单，  2存在未支付订单
    var MainStateTypeIsRental = false //是否是 长租订单
    var MainOrderState = 1 //1 短租，4长租
    var orderNo = "" //未结束订单的  订单号
    @RequiresApi(Build.VERSION_CODES.M)
    fun getRideState(){
        if (Glob.isLogin){
            if (!NetUtils.isConnected(ContextUtil.getContext())) return
            OrderModel.getMyOrderStatus().subscribeBy(
                onNext = {
                    MyLogUtil.Log("7777","===是否存在未结束的骑行订单 信息=="+it.toString())
                    val resultData: MyOrderStateData = Gson().fromJson(it.toString(), MyOrderStateData::class.java)
                    if (resultData != null){
                        //骑行订单状态：1进行中，2待支付
                        MainState = resultData.status
                        orderNo = resultData.order_no
                        MainOrderState = resultData.type
                        if (MainOrderState == Constant.OrderType.RENTAL_Y){
                            MainStateTypeIsRental = true
                        }else{
                            MainStateTypeIsRental = false
                        }
                        if (resultData.status == 1 || resultData.status == 2){
                            ridingTime = resultData.order_time
                            ridingPrice = resultData.amount
                        }
                        if(resultData.status == 1){
                            if (isFirstGetOrderState){
                                MyLogUtil.Log("7777","===是否存在未结束的骑行订单 信息=="+isFirstGetOrderState)
                                isFirstGetOrderState = false
                                //跳转骑行页面
                                startActivity<MainActivity>()
                            }
                            setBomRidingView(true,false)
                        }else{
                            SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_VECHNO, "");

                            if (resultData.status == 2){
//                                binding.imageScanner.visibility = View.GONE
                                MyView?.findViewById<ImageView>(R.id.image_scanner)?.visibility = View.GONE
                                MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(R.string.s_order_npay)+"("+AppUtil.getFloat2(resultData.amount)+Glob.CurrencyUnit+")"
                                setBomRidingView(true,true)
                            }else{
//                                binding.imageScanner.visibility = View.VISIBLE
                                MyView?.findViewById<ImageView>(R.id.image_scanner)?.visibility = View.VISIBLE
                                if (Glob.isRental){
                                    MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(R.string.scan_lease)
                                }else{
                                    MyView?.findViewById<TextView>(R.id.text_scanner)?.text = getString(com.tbit.uqbike.R.string.scan_unlock)
                                }
                                setBomRidingView(false,false)
                            }
                        }
                    }
                }
            ).toCancelable()
        }
    }

    /**
     * 首页 展示骑行中
     * @param isRiding 是否骑行中
     * @param isPaying 是否支付中
     */
    var ridingTime = 0L //骑行时间 长租待支付不需要该参数
    var ridingPrice = 0f //骑行金额
    fun setBomRidingView(isRiding : Boolean,isPaying : Boolean){
        if (isRiding){
//            binding.lyRidingBtn.visibility = View.VISIBLE
//            binding.lyUriding.visibility = View.GONE
            MyView?.findViewById<LinearLayout>(R.id.ly_riding_btn)?.visibility = View.VISIBLE
            MyView?.findViewById<LinearLayout>(R.id.ly_uriding)?.visibility = View.GONE

//            ImageLoad.loadimg(resources.getDrawable(com.tbit.uqbike.R.drawable.ic_riding_gif),binding.imageScanner1)
            if (isPaying){
//                binding.rvSeeOrder.text = getString(R.string.s_gopay)
                MyView?.findViewById<RoundTextView>(R.id.rv_seeOrder)?.text = getString(R.string.s_gopay)
                if (MainStateTypeIsRental){
//                    binding.tvRidingTitle.text = getString(R.string.s_rental_end)
                    MyView?.findViewById<TextView>(R.id.tv_riding_title)?.text = getString(R.string.s_rental_end)
                    var unit = AppUtil.getFloat2(ridingPrice)+Glob.CurrencyUnit
                    var content = getString(R.string.s_cost_rental_upay)+unit
                    val spannableString = AppUtil.getSpanStr(unit,content,R.color.c_F86125)
//                    binding.tvRidingCont.text = spannableString
                    MyView?.findViewById<TextView>(R.id.tv_riding_cont)?.text = spannableString
                }else{
                    MyView?.findViewById<TextView>(R.id.tv_riding_title)?.text = getString(R.string.s_riding_end)
//                    binding.tvRidingCont.text = "已骑行"+"2"+"分钟"+"，"+"骑行费用"+"9THB"
                    var unit2 = AppUtil.getFloat2(ridingPrice)+Glob.CurrencyUnit
                    var content2 = getString(R.string.s_cost_upay)+unit2
                    val spannableString = AppUtil.getSpanStr(unit2,content2,R.color.c_F86125)
                    MyView?.findViewById<TextView>(R.id.tv_riding_cont)?.text = spannableString
                }
            }else{
                MyView?.findViewById<RoundTextView>(R.id.rv_seeOrder)?.text = getString(R.string.s_see)
                if (MainStateTypeIsRental){
                    MyView?.findViewById<TextView>(R.id.tv_riding_title)?.text = getString(R.string.s_rentaling)
//                    binding.tvRidingCont.text = "已租赁"+"9"+"分钟"
                    var unit = ridingTime.toString()+getString(R.string.min)
                    var content = getString(R.string.s_rental_has)+unit
                    val spannableString = AppUtil.getSpanStr(unit,content,R.color.blue_namal)
                    MyView?.findViewById<TextView>(R.id.tv_riding_cont)?.text = spannableString
                }else{
                    MyView?.findViewById<TextView>(R.id.tv_riding_title)?.text = getString(R.string.riding)
//                    binding.tvRidingCont.text = "已骑行"+"2"+"分钟"+"，"+"骑行费用"+"9THB"
                    var unit = ridingTime.toString()+getString(R.string.min)
                    var content = getString(R.string.s_riding_has)+unit
                    var unit2 = AppUtil.getFloat2(ridingPrice)+Glob.CurrencyUnit
                    var content2 = getString(R.string.travel_consumption)+unit2
                    content = content +", "+ content2
                    val spannableString = AppUtil.getSpanStr2(content,unit,unit2,R.color.blue_namal,R.color.blue_namal)
                    MyView?.findViewById<TextView>(R.id.tv_riding_cont)?.text = spannableString
                }
            }
        }else{
            MyView?.findViewById<LinearLayout>(R.id.ly_riding_btn)?.visibility = View.GONE
            MyView?.findViewById<LinearLayout>(R.id.ly_uriding)?.visibility = View.VISIBLE
        }
    }

    override fun onDestroy() {
        loadDataDisposable.dispose()
        super.onDestroy()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }

    override fun onGetNearGeoSuccess(areaData: getAreaData) {
        if (areaData.isNotEmpty()) {
            if (areaData.size > 0){
                geoMapDelegate.setGeoNew(areaData)
            }
        }
    }
    private fun getNearParkPointsAndProhibits() {
        getNearParkPoints()
        getNearProhibitsImpl(false)
    }
    private fun getNearParkPoints() {
        val latLng = mainMapFragment.searchCenter
        presenter.getNearParkPoints(latLng.lat, latLng.lng, 500)
    }
    override fun onGetNearParkPointsSuccess(areaData: getAreaData) {
        if (areaData.isNotEmpty()) {
            if (areaData.size > 0){
                parkPointMapDelegate.setPartAreaData(areaData)
            }
        }
    }
    private fun getNearProhibitsImpl(isProhibitMode: Boolean) {
        val latLng = mainMapFragment.searchCenter
        presenter.getNearProhibits(latLng.lat, latLng.lng)
    }
    override fun onGetNearProhibitsSuccess(areaData: getAreaData) {
        if (areaData.isNotEmpty()) {
            if (areaData.size > 0){
                prohibitAreaMapDelegate.setProhibitAreaData(areaData)
            }
        }
    }
    private fun getBikes() {
        getNearBikes()
    }
    private fun getNearBikes() {
        val latLng = mainMapFragment.searchCenter
        presenter.getNearBikes(latLng.lat, latLng.lng, 500)
    }
    override fun onGetNearBikesSuccess(resultData: VehicleData) {
        bikeMapDelegate.setBickData(resultData)
    }
    override fun onGetPhoneInfoSuccess(phoneInfoList: List<PhoneInfo>?) {}
    override fun onGetParkPointInfoSuccess(resultData: ParkData) {}
    override fun onGetDepositSuccess(adDeposit: AdDeposit?) {}
    override fun onGetUnreadMessageCount(unreadCount: Int) {}
    override fun onGoogleNetNotAvailable() {}
    override fun showErrMsg(message: String) {}

    fun onAreaIdSucListener(callSource: String){
        if (Glob.area_id == 0L) return

        // Store the area_id
        pendingAreaId = Glob.area_id

        // Call the check function now that area_id is available
        tryTriggerAdLoad("onAreaIdSucListener_$callSource")
    }

    fun onAreaIdFailListener(callSource: String){
        // Clear any pending ID on failure
        pendingAreaId = null

        // Optionally, still trigger getData in child fragment to potentially clear its state if it's ready
        // Note: This call doesn't depend on pendingAreaId
        if (mainAdFullFragment?.isAdded == true) {
            mainAdFullFragment?.getData("${callSource}_fail")
        }
    }

    // Helper function to check conditions and trigger ad load
    private fun tryTriggerAdLoad(callSource: String) {
        MyLogUtil.Log("GOSHOP_H5", "tryTriggerAdLoad调用: $callSource, pendingAreaId: $pendingAreaId")

        if (pendingAreaId != null && mainAdFullFragment?.isAdded == true) {
            mainAdFullFragment?.getData("HomeFrag_$callSource") // Pass specific source
            pendingAreaId = null // Clear the pending ID after use
        }

        // 同时检查H5区域（不依赖pendingAreaId）
        MyView?.let { view ->
            MyLogUtil.Log("GOSHOP_H5", "tryTriggerAdLoad触发H5区域检查: $callSource")
            checkGoshopH5Enable(view)
        }
    }

}