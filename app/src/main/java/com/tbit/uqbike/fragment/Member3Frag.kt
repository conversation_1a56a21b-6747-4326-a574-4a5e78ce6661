package com.tbit.uqbike.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseFragment

class Member3Frag : BaseFragment() {
    // private lateinit var tvTitle: TextView
    private lateinit var tvTime: TextView
    private lateinit var tvDescription: TextView
    private lateinit var tvUnlockStatusTitle: TextView
    private lateinit var tvRideCardFormattedDesc: TextView
    private lateinit var tvEquityExplanationButton: TextView
    // ImageView for icon can be omitted if it's static from XML

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.frag_member3, container, false)
    }

    private var MyView : View? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        this.MyView = view
        super.onViewCreated(view, savedInstanceState)

        // tvTitle = view.findViewById(R.id.tv_ride_card_title)
        tvTime = view.findViewById(R.id.tv_ride_card_time)
        tvDescription = view.findViewById(R.id.tv_ride_card_description)
        tvUnlockStatusTitle = view.findViewById(R.id.tv_member3_unlock_status_title)
        tvRideCardFormattedDesc = view.findViewById(R.id.tv_ride_card_formatted_desc)
        tvEquityExplanationButton = view.findViewById(R.id.tv_equity_explanation_button)

        // title and time are likely set directly or via a more specific method if they vary beyond desc/lock
    }
    override fun onDestroy() {
        super.onDestroy()
    }

    fun updateRideCardExtensionInfo(title: String, timeText: String, description: String) {
        // if (!::tvTitle.isInitialized) { 
        //     return
        // }
        // tvTitle.text = title
        tvTime.text = timeText 
        tvDescription.text = description
    }

    /**
     * 更新骑行卡延时的时间值，使用"分钟/次"格式
     * @param minutes 延时分钟数
     */
    fun updateRideCardTime(minutes: Int) {
        if (!::tvTime.isInitialized) {
            return
        }
        // 使用"+分钟数 分钟/次"格式
        tvTime.text = "+$minutes ${getString(R.string.s_vip_minute_times)}"
        
        // 更新格式化描述中的分钟数
        if (::tvRideCardFormattedDesc.isInitialized) {
            tvRideCardFormattedDesc.text = getString(R.string.s_vip_ridecard_desc, minutes)
            tvRideCardFormattedDesc.visibility = View.VISIBLE
        }
    }

    fun updateContent(description: String, isLocked: Boolean) {
        if (!::tvDescription.isInitialized) { 
            return
        }
        if (isLocked) {
            // Example: tvDescription.text = getString(R.string.s_vip_locked_equity)
        } else {
            tvDescription.text = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                android.text.Html.fromHtml(description, android.text.Html.FROM_HTML_MODE_LEGACY)
            } else {
                android.text.Html.fromHtml(description)
            }
        }
    }

    fun updateUnlockStatusTitle(textResId: Int, isVisible: Boolean) {
        if (!::tvUnlockStatusTitle.isInitialized) return
        if (isVisible && textResId != 0) {
            tvUnlockStatusTitle.setText(textResId)
            tvUnlockStatusTitle.visibility = View.VISIBLE
        } else {
            tvUnlockStatusTitle.visibility = View.GONE
        }
    }
}