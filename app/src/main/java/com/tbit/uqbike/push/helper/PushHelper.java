package com.tbit.uqbike.push.helper;

import android.app.Notification;
import android.content.Context;
import android.content.Intent;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RectShape;
import android.os.Build;
import android.util.TypedValue;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.google.gson.Gson;
import com.tbit.uqbike.R;
import com.tbit.uqbike.activity.ChargeNewActivity;
import com.tbit.uqbike.entity.pushResultData;
import com.tbit.uqbike.utils.MyLogUtil;
import com.umeng.message.MsgConstant;
import com.umeng.message.PushAgent;
import com.umeng.message.UmengMessageHandler;
import com.umeng.message.UmengNotificationClickHandler;
import com.umeng.message.api.UPushInAppMessageCallback;
import com.umeng.message.api.UPushInAppMessageHandler;
import com.umeng.message.api.UPushThirdTokenCallback;
import com.umeng.message.entity.UMessage;

import org.android.agoo.fcm.FCMRegister;
import org.android.agoo.honor.HonorRegister;
import org.android.agoo.oppo.OppoRegister;

/**
 * PushSDK集成帮助类
 */
public class PushHelper {

    public static final String TAG = "umeng";

    /**
     * 推送设置
     */
    public static void setting(PushAgent api) {
        //修改为您app/src/main/AndroidManifest.xml中package值
        api.setResourcePackageName("com.tbit.uqbike");
        //设置通知栏显示通知的最大个数（0～10），0：不限制个数
        api.setDisplayNotificationNumber(0);
        api.setNotificationPlaySound(MsgConstant.NOTIFICATION_PLAY_SERVER);
        api.setNotificationPlayVibrate(MsgConstant.NOTIFICATION_PLAY_SDK_DISABLE);

        //推送消息处理
        UmengMessageHandler msgHandler = new UmengMessageHandler() {
            //处理通知栏消息
            @RequiresApi(api = Build.VERSION_CODES.M)
            @Override
            public void dealWithNotificationMessage(Context context, UMessage msg) {
                super.dealWithNotificationMessage(context, msg);
                MyLogUtil.Log(TAG, "notification receiver:\n" + msg.getRaw().toString());
//                PushNotifyUtil.INSTANCE.setHeadNotify(msg.getContent());
            }

            //自定义通知样式，此方法可以修改通知样式等
            @Override
            public Notification getNotification(Context context, UMessage msg) {
                return super.getNotification(context, msg);
            }

            //处理透传消息
            @Override
            public void dealWithCustomMessage(Context context, UMessage msg) {
                super.dealWithCustomMessage(context, msg);
                MyLogUtil.Log(TAG, "custom receiver:\n" + msg.getRaw().toString());
            }
        };
        api.setMessageHandler(msgHandler);

        //推送消息点击处理
        UmengNotificationClickHandler notificationClickHandler = new UmengNotificationClickHandler() {
            @Override
            public void openActivity(Context context, UMessage msg) {
                super.openActivity(context, msg);
//                MyLogUtil.Log(TAG, "click open activity:\n" + msg.getRaw().toString());
                MyLogUtil.Log(TAG, "click open activity:\n" + msg.getExtra().toString());
//                MyLogUtil.Log(TAG, "click open activity=========getExtra=====:\n" + msg.getExtra().get("routeData"));
                pushActUtil.INSTANCE.goAct(context,msg.getExtra().get("routeData"),msg.title);

//                pushActUtil.INSTANCE.test(context);
            }

            @Override
            public void launchApp(Context context, UMessage msg) {
                super.launchApp(context, msg);
                MyLogUtil.Log(TAG, "click launch app:\n");
            }

            @Override
            public void openUrl(Context context, UMessage msg) {
                super.openUrl(context, msg);
                MyLogUtil.Log(TAG, "click open deeplink:\n");
            }

            @Override
            public void dismissNotification(Context context, UMessage msg) {
                super.dismissNotification(context, msg);
                MyLogUtil.Log(TAG, "dismissNotification:\n");
            }
        };
        api.setNotificationClickHandler(notificationClickHandler);

        //通过Service自定义接收并处理消息
//        api.setPushIntentServiceClass(MyCustomMessageService.class);

        //设置厂商Token回调
        api.setThirdTokenCallback(new UPushThirdTokenCallback() {
            @Override
            public void onToken(String type, String token) {
                MyLogUtil.Log(TAG, "push type:" + type + " token:" + token);
            }
        });

        //设置通知消息转应用内浮窗展示的回调
        api.setInAppMessageCallback(new UPushInAppMessageCallback() {
            @Override
            public void onShow(Context context, UMessage message) {
                MyLogUtil.Log(TAG, "inapp message show:" + message.getRaw().toString());
            }

            @Override
            public void onClick(Context context, UMessage message) {
                MyLogUtil.Log(TAG, "inapp message click:" + message.getRaw().toString());
            }

            @Override
            public void onDismiss(Context context, UMessage message) {
                MyLogUtil.Log(TAG, "inapp message dismiss:" + message.getRaw().toString());
            }
        });

        api.setInAppMessageHandler(new UPushInAppMessageHandler() {
            @Override
            public View getView(Context context, UMessage msg, FrameLayout.LayoutParams params) {
                View view = View.inflate(context, R.layout.notification_floating_layout, null);

                //设置浮窗背景
                final float radius = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 12, context.getResources().getDisplayMetrics());
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    view.setElevation(radius);
                    view.setTranslationZ(radius);
                }
                RectShape shape = new RectShape() {
                    @Override
                    public void draw(Canvas canvas, Paint paint) {
                        paint.setColor(0xFADCE2F1);
                        canvas.drawRoundRect(rect(), radius, radius, paint);
                    }
                };
                view.setBackgroundDrawable(new ShapeDrawable(shape));

                //设置标题
                TextView title = view.findViewById(R.id.notification_title);
                title.setText(msg.getTitle());

                //设置内容
                TextView content = view.findViewById(R.id.notification_content);
                content.setText(msg.getContent());

                //可修改浮窗的高度（可选）
                params.height = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 64, context.getResources().getDisplayMetrics());
                return view;
            }
        });
    }

    /**
     * 注册设备推送通道（小米、华为等设备的推送）
     */
    public static void registerDevicePush(Context context) {
        //小米推送：填写您在小米后台APP对应的xiaomi id和key
//        MiPushRegistar.register(context, PushConstants.MI_ID, PushConstants.MI_KEY);
        //华为推送：注意华为推送的初始化参数在AndroidManifest.xml中配置
//        HuaWeiRegister.register(context.getApplicationContext());
        //魅族推送：填写您在魅族后台APP对应的app id和key
        // MeizuRegister.register(context, PushConstants.MEI_ZU_ID, PushConstants.MEI_ZU_KEY); // 已移除魅族推送注册
        //OPPO推送：填写您在OPPO后台APP对应的app key和secret
//        OppoRegister.register(context, PushConstants.OPPO_KEY, PushConstants.OPPO_SECRET);
        //vivo推送：注意vivo推送的初始化参数在AndroidManifest.xml中配置
//        VivoRegister.register(context);
        //荣耀推送：注意荣耀推送的初始化参数在AndroidManifest.xml中配置
        HonorRegister.register(context);
        //谷歌fcm推送
        FCMRegister.register(context);
    }
}
