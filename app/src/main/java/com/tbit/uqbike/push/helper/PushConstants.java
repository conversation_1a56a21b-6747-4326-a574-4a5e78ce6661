package com.tbit.uqbike.push.helper;

/**
 * 常量类
 */
public class PushConstants {
    /**
     * 应用申请的Appkey
     */
//    public static final String APP_KEY = "应用申请的Appkey";
    public static final String APP_KEY = "669f4ce0940d5a4c4996c4bd";
    /**
     * 应用申请的UmengMessageSecret
     */
//    public static final String MESSAGE_SECRET = "应用申请的UmengMessageSecret";
    public static final String MESSAGE_SECRET = "7bd7e9fe9578ea3c2941f1ee95b83da3";

    /**
     * 后台加密消息的密码（仅Demo用，请勿将此密码泄漏）
     */
    public static final String APP_MASTER_SECRET = "6vnajkupxywhpgf60ndh73pbotyd8mfn";

    /**
     * 渠道名称，修改为您App的发布渠道名称
     */
    public static final String CHANNEL = "Umeng";

    /**
     * 小米后台APP对应的xiaomi id
     */
//    public static final String MI_ID = "填写您在小米后台APP对应的xiaomi id";
    public static final String MI_ID = "2882303761517875511";

    /**
     * 小米后台APP对应的xiaomi key
     */
//    public static final String MI_KEY = "填写您在小米后台APP对应的xiaomi key";
    public static final String MI_KEY = "5961787531511";

    /**
     * OPPO后台APP对应的app key
     */
//    public static final String OPPO_KEY = "填写您在OPPO后台APP对应的app key";
    public static final String OPPO_KEY = "d8fdff31550c444da545b572fe3c470f";

    /**
     * OPPO后台APP对应的app secret
     */
//    public static final String OPPO_SECRET = "填写您在OPPO后台APP对应的app secret";
    public static final String OPPO_SECRET = "628c3eef0dfa4a7696cf3181ea9315ed";
}
