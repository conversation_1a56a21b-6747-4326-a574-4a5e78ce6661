package com.tbit.uqbike.dialog

import android.content.ClipboardManager
import android.content.Context.CLIPBOARD_SERVICE
import android.content.DialogInterface
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.CheckedTextView
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.gson.Gson
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.LoadingDialogHelper
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.CouponUseRecd
import com.tbit.uqbike.entity.EnoughData
import com.tbit.uqbike.entity.OrderInfoRentalData
import com.tbit.uqbike.entity.RidingOrderInfoData
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.OrderModel
import com.tbit.uqbike.roundview.RoundLinearLayout
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.json.JSONObject

class RecordDetailDialog(var loadingDialogHelper: LoadingDialogHelper) : BottomSheetDialogFragment() {
    var onBtnListener = {}
    var onDissListener = {}
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(BottomSheetDialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme);
    }
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.dialog_recorddetail, container, false)
    }

    var Myview : View? = null
    @RequiresApi(Build.VERSION_CODES.O)
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Myview = view

        Myview?.findViewById<RoundTextView>(R.id.rv_ridrecord_time)?.text = TimeFormatUtil.transToStringBysq(ridingOrderInfoData.finish_time.toLong())
        Myview?.findViewById<TextView>(R.id.rv_ridrecord_no)?.text = ridingOrderInfoData.order_no

        Myview?.findViewById<TextView>(R.id.tv_ridrecord_coupon_explain)?.text = getString(R.string.s_coupon_amount_dis)+"("+Glob.CurrencyUnit+")"
        if (ridingOrderInfoData.coupon_deduction == 0f){
            Myview?.findViewById<TextView>(R.id.tv_ridrecord_coupon)?.text = "0.00"
        }else{
            Myview?.findViewById<TextView>(R.id.tv_ridrecord_coupon)?.text = AppUtil.getFloat2(ridingOrderInfoData.coupon_deduction)
        }
        Myview?.findViewById<TextView>(R.id.tv_ridrecord_coupon)?.setOnClickListener {
            Myview?.findViewById<ImageView>(R.id.img_recorddetail_coupon)?.performClick()
        }
        var isGetCoupon = false
        var isShowCoupon = false
        Myview?.findViewById<ImageView>(R.id.img_recorddetail_coupon)?.setOnClickListener {
            if (ridingOrderInfoData.coupon_deduction != 0f){
                isShowCoupon = !isShowCoupon
                if (isShowCoupon){
                    Myview?.findViewById<RoundLinearLayout>(R.id.ry_recorddetail_coupon)?.visibility = View.VISIBLE
                    ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_down),Myview?.findViewById<ImageView>(R.id.img_recorddetail_coupon)!!)
                }else{
                    Myview?.findViewById<RoundLinearLayout>(R.id.ry_recorddetail_coupon)?.visibility = View.GONE
                    ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_up),Myview?.findViewById<ImageView>(R.id.img_recorddetail_coupon)!!)
                }
                if (!isGetCoupon){
                    loadingDialogHelper.show {  }
                    ComModel.getOrderCoupon(ridingOrderInfoData.order_no).subscribeBy(
                        onNext = {
                            loadingDialogHelper.dismiss()
                            MyLogUtil.Log("1111","===获取当前订单 使用的优惠券=="+it.toString())
                            val resultData = Gson().fromJson(it.toString(), CouponUseRecd::class.java)
                            if (resultData != null && resultData.size > 0){
                                val mInflater = LayoutInflater.from(view.context)
                                resultData.forEach {
                                    val couponView = mInflater.inflate(R.layout.layout_recorddetail_coupon,
                                        Myview?.findViewById<RoundLinearLayout>(R.id.ry_recorddetail_coupon), false) as LinearLayout
                                    couponView.findViewById<TextView>(R.id.tv_lay_ridrecord_coupon_explain).text = it.name+"("+it.currency+")"
                                    couponView.findViewById<TextView>(R.id.tv_lay_ridrecord_coupon).text = AppUtil.getFloat2(it.coupon_deduction)
                                    Myview?.findViewById<RoundLinearLayout>(R.id.ry_recorddetail_coupon)?.addView(couponView)
                                }
                                isGetCoupon = true
                            }
                        },
                        onError = {loadingDialogHelper.dismiss()}
                    ).toCancelable()
                }
            }
        }

        // 支付金额
        var payNomey = ridingOrderInfoData.order_amount + ridingOrderInfoData.dispatch_amount + ridingOrderInfoData.ride_card_deduction+
                ridingOrderInfoData.balance_deduction + ridingOrderInfoData.present_deduction + ridingOrderInfoData.coupon_deduction
        //订单状态：1进行中,2待支付，3已支付
        if (ridingOrderInfoData.status == 2){
            Myview?.findViewById<TextView>(R.id.rv_ridrecord_state)?.setTextColor(resources.getColor(R.color.blue_namal))
            Myview?.findViewById<TextView>(R.id.rv_ridrecord_state)?.text = getString(R.string.s_order_npay)
            if (payNomey != 0f){
                Myview?.findViewById<LinearLayout>(R.id.ly_paybom)?.visibility = View.VISIBLE
                Myview?.findViewById<TextView>(R.id.tv_ridrecord_allcost1)?.text = getString(R.string.s_tobepay)+"("+Glob.CurrencyUnit+")"
                Myview?.findViewById<TextView>(R.id.tv_ridrecord_costnum1)?.text = AppUtil.getFloat2(payNomey)
            }
            OrderModel.getMyWallet(ridingOrderInfoData.order_no)
                .subscribeBy(
                    onNext = {
                        MyLogUtil.Log("1111","=== 余额是否足够 信息=="+it.toString())
                        var resultData = Gson().fromJson(it.toString(), EnoughData::class.java)
                        if(resultData.is_enough == 1){
                            Myview?.findViewById<RoundTextView>(R.id.btn_riding_ok)?.text = getString(R.string.pay)
                        }else{
                            Myview?.findViewById<RoundTextView>(R.id.btn_riding_ok)?.text = getString(R.string.s_pay)
                        }
                        Myview?.findViewById<RoundTextView>(R.id.btn_riding_ok)?.visibility = View.VISIBLE
                    }
                    , onError = {}
                ).toCancelable()
        }else if (ridingOrderInfoData.status == 3){
            Myview?.findViewById<TextView>(R.id.rv_ridrecord_state)?.setTextColor(resources.getColor(R.color.c_838588))
            Myview?.findViewById<TextView>(R.id.rv_ridrecord_state)?.text = getString(R.string.s_paycomp)
            Myview?.findViewById<RoundTextView>(R.id.btn_riding_ok)?.visibility = View.GONE

            if (payNomey != 0f){
                Myview?.findViewById<LinearLayout>(R.id.ly_paybom)?.visibility = View.VISIBLE
                Myview?.findViewById<TextView>(R.id.tv_ridrecord_allcost1)?.text = getString(R.string.s_exchange_paymoney)+"("+Glob.CurrencyUnit+")"
                Myview?.findViewById<TextView>(R.id.tv_ridrecord_costnum1)?.text = AppUtil.getFloat2(payNomey)
            }
        }
        Myview?.findViewById<RoundTextView>(R.id.btn_riding_ok)?.setOnClickListener {
            dismiss()
            onBtnListener()
        }
        Myview?.findViewById<ImageView>(R.id.img_ridrecord_copy)?.setOnClickListener{
            val cm: ClipboardManager = activity!!.getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
            cm.setText(Myview?.findViewById<TextView>(R.id.rv_ridrecord_no)?.text.toString())
            MyToastUtil.toast(getString(R.string.s_copy))
        }
        Myview?.findViewById<TextView>(R.id.tv_ridrecord_length)?.text = ridingOrderInfoData.mileage.toString()
        Myview?.findViewById<TextView>(R.id.tv_ridrecord_time)?.text = ridingOrderInfoData.order_time.toString()
        if (ridingOrderInfoData.ride_card_time != 0){
            Myview?.findViewById<LinearLayout>(R.id.ly_ridcard)?.visibility = View.VISIBLE
            Myview?.findViewById<TextView>(R.id.tv_ridrecord_card)?.text = "-"+ridingOrderInfoData.ride_card_time.toString()
            // 会员权益文案拼接
            val memberBonus = ridingOrderInfoData.ride_card_delay
            val memberTag = getString(R.string.s_member_tag)
            val memberTextView = Myview?.findViewById<TextView>(R.id.tv_ridrecord_card_member)
            if (memberBonus != null && memberBonus > 0) {
                memberTextView?.visibility = View.VISIBLE
                memberTextView?.text = "（$memberTag+$memberBonus）"
            } else {
                memberTextView?.visibility = View.GONE
            }
        }
        Myview?.findViewById<TextView>(R.id.tv_ridrecord_cost_explain)?.text = getString(R.string.s_riding_time_cost)+"("+Glob.CurrencyUnit+")"
        Myview?.findViewById<TextView>(R.id.tv_ridrecord_cost)?.text = AppUtil.getFloat2(ridingOrderInfoData.order_amount).toString()
        // 调度费展示逻辑
        if(ridingOrderInfoData.dispatch_amount != 0f || ridingOrderInfoData.is_park_impunity == 1){
            Myview?.findViewById<LinearLayout>(R.id.ly_recode_dispatch)?.visibility = View.VISIBLE
            Myview?.findViewById<TextView>(R.id.tv_ridrecord_dispatch_explain)?.text = getString(R.string.s_riding_dispatch_cost)+"("+Glob.CurrencyUnit+")"
            Myview?.findViewById<TextView>(R.id.tv_ridrecord_dispatch)?.text = AppUtil.getFloat2(ridingOrderInfoData.dispatch_amount).toString()
        }
        // 会员免罚逻辑
        val dispatchMemberTextView = Myview?.findViewById<TextView>(R.id.tv_ridrecord_dispatch_member)
        if (ridingOrderInfoData.is_park_impunity == 1) {
            dispatchMemberTextView?.visibility = View.VISIBLE
            dispatchMemberTextView?.text = getString(R.string.s_rideorder_payFree)
        } else {
            dispatchMemberTextView?.visibility = View.GONE
        }
        Myview?.findViewById<TextView>(R.id.tv_ridrecord_allcost)?.text = ResUtil.getString(R.string.s_riding_allcost)+"("+Glob.CurrencyUnit+")"
        Myview?.findViewById<TextView>(R.id.tv_ridrecord_costnum)?.text = AppUtil.getFloat2(ridingOrderInfoData.order_amount +
                ridingOrderInfoData.dispatch_amount + ridingOrderInfoData.ride_card_deduction).toString()

        if (ridingOrderInfoData.balance_deduction != 0f){
            Myview?.findViewById<LinearLayout>(R.id.ly_ridrecord_balance)?.visibility = View.VISIBLE
            Myview?.findViewById<TextView>(R.id.tv_ridrecord_balancecost)?.text = getString(R.string.s_balance_cost)+"("+Glob.CurrencyUnit+")"
            Myview?.findViewById<TextView>(R.id.tv_ridrecord_balancenum)?.text = AppUtil.getFloat2(ridingOrderInfoData.balance_deduction).toString()
        }
        if (ridingOrderInfoData.present_deduction != 0f){
            Myview?.findViewById<LinearLayout>(R.id.ly_ridrecord_present)?.visibility = View.VISIBLE
            Myview?.findViewById<TextView>(R.id.tv_ridrecord_presentcost)?.text = getString(R.string.s_present_cost)+"("+Glob.CurrencyUnit+")"
            Myview?.findViewById<TextView>(R.id.tv_ridrecord_presentnum)?.text = AppUtil.getFloat2(ridingOrderInfoData.present_deduction).toString()
        }

//        if(ridingOrderInfoData.status == 3){
//            ly_ridrecord_balance.visibility = View.VISIBLE
//            tv_ridrecord_balancecost.text = ResUtil.getString(R.string.s_record_balancecast)+"("+ridingOrderInfoData.currency+")"
//            tv_ridrecord_balancenum.text = AppUtil.getFloat2(ridingOrderInfoData.balance_deduction+ridingOrderInfoData.present_deduction).toString()
//        }
    }
    lateinit var ridingOrderInfoData: OrderInfoRentalData
    fun setData(ridingOrderInfoData: OrderInfoRentalData){
        this.ridingOrderInfoData = ridingOrderInfoData
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDissListener()
    }


    override fun onStart() {
        super.onStart()
        MDUtil.pageStar(eventName)
    }

    var eventName = "trip_information_details"
    override fun onDestroy() {
        super.onDestroy()
        val properties = JSONObject()
        properties.put("tag",eventName)
        properties.put("business_type","1")
        properties.put("deposit","")
        MDUtil.pageEnd(eventName,properties)
    }
}