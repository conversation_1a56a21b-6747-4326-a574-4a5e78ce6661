package com.tbit.uqbike.dialog;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.tbit.maintenance.utils.AppUtil;
import com.tbit.maintenance.utils.ResUtil;
import com.tbit.uqbike.App;
import com.tbit.uqbike.R;
import com.tbit.uqbike.activity.model.RidingModel;
import com.tbit.uqbike.entity.RidingOrderPrecompleteData;
import com.tbit.uqbike.qrcode.CommonUtils;
import com.tbit.uqbike.resqmodel.AutoResq;
import com.tbit.uqbike.roundview.RoundLinearLayout;
import com.tbit.uqbike.utils.ImageLoad;
import com.tbit.uqbike.utils.MDUtil;
import com.tbit.uqbike.utils.MyLogUtil;

import org.json.JSONException;
import org.json.JSONObject;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

import android.graphics.Typeface;

public class backCarAbnormalDialog extends Dialog implements View.OnClickListener {

    private Builder builder;

    public Context context;
    private TextView mTvLeft, mTvRight, mTvTitle, mTvSpecial,go_p;
    private RoundLinearLayout rl_left;
    private View viewLine;
    private LinearLayout ly_gop;
    private ImageView img,img_diag_ts;
    private RoundLinearLayout rl_right;

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.mTvLeft) {
            if (builder.mTwoSelDialog != null)
                builder.mTwoSelDialog.leftClick();
            this.dismiss();
        } else if (view.getId() == R.id.mTvRight) {
            if (builder.mOneSelDialog != null)
                builder.mOneSelDialog.sureClick();
            if (builder.mTwoSelDialog != null)
                builder.mTwoSelDialog.rightClick(builder.precompleteData.getPre_complete_status());
            this.dismiss();
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        if (builder.onOutClick != null) {
            builder.onOutClick.onOutClick();
        }
    }

    @Override
    public boolean onTouchEvent(@NonNull MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                //点击弹窗外部区域
                if (!isOutOfBounds(getContext(), event)) {
                    return super.onTouchEvent(event);
                }
                break;
            case MotionEvent.ACTION_UP:
                //点击弹窗外部区域
                if (isOutOfBounds(getContext(), event)) {
                    if (builder.onOutClick != null) {
                        builder.onOutClick.onOutClick();
                    }
                }
                break;
        }
        return super.onTouchEvent(event);
    }

    private boolean isOutOfBounds(Context context, MotionEvent event) {
        final int x = (int) event.getX();//相对弹窗左上角的x坐标
        final int y = (int) event.getY();//相对弹窗左上角的y坐标
        final int slop = ViewConfiguration.get(context).getScaledWindowTouchSlop();//最小识别距离
        final View decorView = getWindow().getDecorView();//弹窗的根View
        return (x < -slop) || (y < -slop) || (x > (decorView.getWidth() + slop))
                || (y > (decorView.getHeight() + slop));
    }

    public interface TwoSelDialog {
        void leftClick();

        void rightClick(int orderstate);
    }

    public interface OneSelDialog {
        void sureClick();
    }

    public interface OnOutClick {
        void onOutClick();
    }

    public interface OnBomClick {
        void onBomClick();
    }
    String eventName = "second_confirmation_car_return";
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.returncar_dialog);
        init();
//        dialog=this;
        MDUtil.INSTANCE.pageStar(eventName);
        this.setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                try {
                    JSONObject properties = new JSONObject();
                    properties.put("tag",eventName);
                    properties.put("business_type",2);
                    properties.put("pop_up_type",builder.precompleteData.getPre_complete_status());
                    MDUtil.INSTANCE.pageEnd(eventName,properties);
                } catch (JSONException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }

    private backCarAbnormalDialog(Builder builder) {
        super(builder.mContext, R.style.PXDialog);
        this.context = builder.mContext;
        this.builder = builder;
    }

    @SuppressLint("UseCompatLoadingForColorStateLists")
    private void init() {
        mTvTitle = (TextView) findViewById(R.id.mTvTitle);
        mTvLeft = (TextView) findViewById(R.id.mTvLeft);
        rl_left = (RoundLinearLayout) findViewById(R.id.rl_left);
        mTvSpecial = (TextView) findViewById(R.id.mTvSpecial);
        mTvRight = (TextView) findViewById(R.id.mTvRight);
        viewLine = (View) findViewById(R.id.vD);
        ly_gop = (LinearLayout) findViewById(R.id.ly_gop);
        go_p = (TextView) findViewById(R.id.go_p);
        img = (ImageView) findViewById(R.id.dig_img);
        img_diag_ts = (ImageView) findViewById(R.id.img_diag_ts);
        rl_right = (RoundLinearLayout) findViewById(R.id.rl_right);

        mTvTitle.setVisibility(builder.isShowTitle ? View.VISIBLE : View.GONE);
        if (builder.isShowGoP){
            if (!(builder.contentp == null || builder.contentp.equals(""))){
                go_p.setText(builder.contentp);
                ly_gop.setVisibility(View.VISIBLE);
            }else {
                ly_gop.setVisibility(View.GONE);
            }

            ly_gop.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (builder.onBomClick != null){
                        builder.onBomClick.onBomClick();
                    }
                }
            });
        }

        if (!TextUtils.isEmpty(builder.title)) {
            mTvTitle.setText(builder.title);
        }
        if (!TextUtils.isEmpty(builder.left)) {
            mTvLeft.setText(builder.left);
        }else{
            rl_left.setVisibility(View.GONE);
            viewLine.setVisibility(View.GONE);
        }
        if (!TextUtils.isEmpty(builder.right)) {
            mTvRight.setText(builder.right+"("+countDownTime+"s)");
        }
        if(builder.leftColor!=0){
            mTvLeft.setTextColor(App.context.getResources().getColor(builder.leftColor));
        }
        if(builder.rightColor!=0){
            mTvRight.setTextColor(App.context.getResources().getColor(builder.rightColor));
        }
        if (!TextUtils.isEmpty(builder.content)) {
            mTvSpecial.setText(builder.content);
        }else {
            mTvSpecial.setVisibility(View.GONE);
        }

        if (!TextUtils.isEmpty(builder.spanncontent)) {
            // 判断是否为免罚权益文案，若是则整体加粗
            String impunityTip = ResUtil.INSTANCE.getString(R.string.s_impunity_tip);
            if (builder.spanncontent.toString().equals(impunityTip)) {
                SpannableString boldSpan = new SpannableString(builder.spanncontent);
                boldSpan.setSpan(new StyleSpan(Typeface.BOLD), 0, boldSpan.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                mTvSpecial.setVisibility(View.VISIBLE);
                mTvSpecial.setText(boldSpan);
            } else {
                mTvSpecial.setVisibility(View.VISIBLE);
                mTvSpecial.setText(builder.spanncontent);
            }
        }else {
//            mTvSpecial.setVisibility(View.GONE);
        }

        if(builder.isConLeft){
            mTvSpecial.setGravity(Gravity.START);
        }

        if (builder.isShowImg != 0){
            img.setVisibility(View.VISIBLE);
            ImageLoad.INSTANCE.loadimg(getContext().getDrawable(builder.isShowImg),img);
        }

        if (builder.mOneSelDialog != null) {
            findViewById(R.id.vD).setVisibility(View.GONE);
            mTvLeft.setVisibility(View.GONE);
        }
        if(builder.isShowClose){
            findViewById(R.id.iv_close).setVisibility(View.VISIBLE);
        }
        Window dialogWindow = this.getWindow();
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = CommonUtils.dp2px(context, 270); // 宽度
//        lp.height = HnDimenUtil.dp2px(context,  170); // 高度
        dialogWindow.setAttributes(lp);
        this.setCanceledOnTouchOutside(builder.isCancel);
        this.setCancelable(builder.isCancel);

        dialogWindow.getAttributes().windowAnimations = R.style.AnimBottom;

        mTvLeft.setOnClickListener(this);
        mTvRight.setOnClickListener(this);
        mTvRight.setEnabled(false);
        AutoTask();
    }
    private int countDownTime = 5;
    private void AutoTask(){
        mTvRight.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (mTvRight != null){
                    if (countDownTime > 1){
                        countDownTime --;
                        if (!TextUtils.isEmpty(builder.right)) {
                            mTvRight.setText(builder.right+"("+countDownTime+"s)");
                        }
                        AutoTask();
                    }else {
                        mTvRight.setEnabled(true);
                        if (!TextUtils.isEmpty(builder.right)) {
                            mTvRight.setText(builder.right);
                        }
                        getOrderPreComp();
                    }
                }
            }
        },1000);
    }

    private void getOrderPreComp(){
        if (builder.precompleteData != null){
            AutoResq.INSTANCE.getRentalPrecomp(builder.precompleteData.getOrderNo());
            AutoResq.INSTANCE.setOnRentalSuc(new Function1<RidingOrderPrecompleteData, Unit>() {
                @Override
                public Unit invoke(RidingOrderPrecompleteData resultData) {
                    resultData.setOrderNo(builder.precompleteData.getOrderNo());
                    builder.precompleteData.setPre_complete_status(resultData.getPre_complete_status());
//                    MyLogUtil.Log("1111","===========setOnRentalSuc===="+resultData.getOrderNo());
                    AutoResqTask();
                    if (mTvRight != null){
                        String content = ResUtil.INSTANCE.getString(R.string.s_riding_repay);
                        String unit = AppUtil.INSTANCE.getFloat2(resultData.getDispatch_amount()).toString()+resultData.getCurrency();
                        //预还车状态：1正常还车，2 短租P点外还车，3禁停区还车，4运营区外还车,5 长租异P
                        content = RidingModel.INSTANCE.getRentalPre(resultData.getPre_complete_status(),unit);
                        SpannableString spannableString = new SpannableString(content);
                        if(resultData.getPre_complete_status() != 1 && resultData.getPre_complete_status() != 11){
                            if (content.contains(unit.toString())){
//                                int data1 = spannableString.split(unit)[0].length;
                                int data1 = spannableString.toString().split(unit)[0].length();
                                spannableString.setSpan(new ForegroundColorSpan(context.getResources().getColor(R.color.c_y)), data1, data1+unit.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                            }
                        }
                        if (resultData.getPre_complete_status() == 1 || resultData.getPre_complete_status() == 11 || resultData.getPre_complete_status() == 5){
                            mTvSpecial.setText(spannableString);
//                            mTvTitle.setTextColor(context.getResources().getColor(R.color.black_namal));
//                            mTvTitle.setPadding(0,CommonUtils.dip2px(context,20f),0,0);
//                            mTvSpecial.setTextColor(context.getResources().getColor(R.color.black_hint));
//                            rl_right.getDelegate().setBackgroundColor(context.getResources().getColor(R.color.blue_namal));
//                            img_diag_ts.setVisibility(View.GONE);
                            ly_gop.setVisibility(View.GONE);
                        }else {
//                            MyLogUtil.Log("1111","=====spannableString======"+spannableString);
                            mTvSpecial.setText(spannableString);
//                            mTvTitle.setTextColor(context.getResources().getColor(R.color.c_8A2019));
//                            mTvTitle.setPadding(0,CommonUtils.dip2px(context,40f),0,0);
//                            mTvSpecial.setTextColor(context.getResources().getColor(R.color.c_8A2019));
//                            rl_right.getDelegate().setBackgroundColor(context.getResources().getColor(R.color.c_E7544C));
//                            img_diag_ts.setVisibility(View.VISIBLE);
                            ly_gop.setVisibility(View.VISIBLE);

                        }
                    }
                    return null;
                }
            });

            AutoResq.INSTANCE.setOnRentalFail(new Function1<String, Unit>() {
                @Override
                public Unit invoke(String s) {
                    MyLogUtil.Log("1111","===========setOnRentalFail====");
                    AutoResqTask();
                    return null;
                }
            });
        }
    }
    private void AutoResqTask(){
        if (mTvRight != null){
            mTvRight.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (mTvRight != null){
                        getOrderPreComp();
                    }
                }
            },5000L);
        }
    }
    @Override
    public void show() {
        if (context == null) {
            dismiss();
            builder = null;
            return;
        }
        super.show();
    }

    public static class Builder {
        private String title;
        private String left;
        private String right;
        private String content;
        private String contentp;

        private SpannableString spanncontent;
        private boolean isConLeft = false;
        private int isShowImg = 0;
        private int leftColor;
        private int rightColor;
        private TwoSelDialog mTwoSelDialog;
        private OneSelDialog mOneSelDialog;
        private OnOutClick onOutClick;
        private OnBomClick onBomClick;

        private boolean isCancel = true;
        private boolean isShowTitle = true;
        private boolean isShowClose = false;

        private boolean isShowGoP = false;
        private RidingOrderPrecompleteData precompleteData = null;
        private Context mContext;

        public Builder(Context mContext) {
            this.mContext = mContext;
        }



        public Builder setCanceledOnOutside(boolean isCancel) {
            this.isCancel = isCancel;
            return this;
        }
        public Builder setLeftText(String left) {
            this.left = left;
            return this;
        }

        public Builder setOrderData(RidingOrderPrecompleteData orderData) {
            this.precompleteData = orderData;
            return this;
        }

        public Builder setRightText(String right) {
            this.right = right;
            return this;
        }

        public Builder setLeftColor(int leftColor) {
            this.leftColor = leftColor;
            return this;
        }

        public Builder setRightColor(int rightColor) {
            this.rightColor = rightColor;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setTitleVisible(boolean isShowTitle) {
            this.isShowTitle = isShowTitle;
            return this;
        }
        public Builder setCloseVisible(boolean isShowClose) {
            this.isShowClose = isShowClose;
            return this;
        }

        public Builder setShowGoP(boolean isShowGoP) {
            this.isShowGoP = isShowGoP;
            return this;
        }

        public Builder setConLeft(boolean isConLeft) {
            this.isConLeft = isConLeft;
            return this;
        }

        public Builder setShowImg(int isShowImg) {
            this.isShowImg = isShowImg;
            return this;
        }

        public Builder setContent(String content) {
            this.content = content;
            return this;
        }

        public Builder setSpanContent(SpannableString spancontent) {
            this.spanncontent = spancontent;
            return this;
        }

        public Builder setContentp(String contentp) {
            this.contentp = contentp;
            return this;
        }
        public Builder setClickListen(TwoSelDialog mTwoSelDialog) {
            this.mTwoSelDialog = mTwoSelDialog;
            return this;
        }

        public Builder setClickListen(OneSelDialog mOneSelDialog) {
            this.mOneSelDialog = mOneSelDialog;
            return this;
        }

        public Builder setOnOUtCllickListener(OnOutClick outClick) {
            this.onOutClick = outClick;
            return this;
        }

        public Builder setOnBomCllickListener(OnBomClick BomClick) {
            this.onBomClick = BomClick;
            return this;
        }

        public backCarAbnormalDialog build() {
            return new backCarAbnormalDialog(this);
        }
    }
}
