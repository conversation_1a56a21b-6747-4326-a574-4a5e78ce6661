package com.tbit.uqbike.dialog

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.ShareData
import com.tbit.uqbike.entity.SharePlatform
import com.tbit.uqbike.utils.MyToastUtil

/**
 * 分享返利弹窗
 */
class ShareRebateDialog(private val shareData: ShareData) : DialogFragment() {

    private var onShareResultListener: ((success: <PERSON><PERSON><PERSON>, platform: String) -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.BottomDialogStyle)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_share_rebate, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupViews(view)
        setupRebateLevels(view)
        setupClickListeners(view)
    }

    private fun setupViews(view: View) {
        // 设置总收益
        val tvTotalEarnings = view.findViewById<TextView>(R.id.tv_total_earnings)
        tvTotalEarnings.text = getString(R.string.s_share_rebate_total_earnings, shareData.all_rebate)
    }

    private fun setupRebateLevels(view: View) {
        val llRebateLevels = view.findViewById<LinearLayout>(R.id.ll_rebate_levels)
        
        // 佣金层级描述
        val levelDescriptions = arrayOf(
            getString(R.string.s_share_rebate_level_you),
            getString(R.string.s_share_rebate_level_a),
            getString(R.string.s_share_rebate_level_b),
            getString(R.string.s_share_rebate_level_c),
            getString(R.string.s_share_rebate_level_d)
        )

        // 动态添加佣金层级项目
        shareData.rebates.forEachIndexed { index, rebate ->
            if (index < levelDescriptions.size) {
                val itemView = LayoutInflater.from(context)
                    .inflate(R.layout.item_rebate_level, llRebateLevels, false)
                
                val tvDescription = itemView.findViewById<TextView>(R.id.tv_level_description)
                val tvRebate = itemView.findViewById<TextView>(R.id.tv_rebate_percentage)
                
                tvDescription.text = levelDescriptions[index]
                tvRebate.text = "+$rebate%"
                
                llRebateLevels.addView(itemView)
            }
        }
    }

    private fun setupClickListeners(view: View) {
        // 关闭按钮
        view.findViewById<ImageView>(R.id.iv_close).setOnClickListener {
            dismiss()
        }

        // WhatsApp分享
        view.findViewById<LinearLayout>(R.id.ll_whatsapp).setOnClickListener {
            shareToApp(SharePlatform.WHATSAPP)
        }

        // Line分享
        view.findViewById<LinearLayout>(R.id.ll_line).setOnClickListener {
            shareToApp(SharePlatform.LINE)
        }

        // Facebook分享
        view.findViewById<LinearLayout>(R.id.ll_facebook).setOnClickListener {
            shareToApp(SharePlatform.FACEBOOK)
        }

        // 复制链接
        view.findViewById<LinearLayout>(R.id.ll_copy_link).setOnClickListener {
            copyLinkToClipboard()
        }
    }

    private fun shareToApp(platform: SharePlatform) {
        val context = requireContext()
        
        // 检查应用是否已安装
        if (!isAppInstalled(context, platform.packageName)) {
            MyToastUtil.toast(getString(R.string.s_share_rebate_share_failed))
            onShareResultListener?.invoke(false, platform.platformName)
            return
        }

        try {
            val shareText = "${shareData.text}\n${shareData.url}"
            
            val intent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, shareText)
                setPackage(platform.packageName)
            }
            
            startActivity(intent)
            MyToastUtil.toast(getString(R.string.s_share_rebate_sharing))
            onShareResultListener?.invoke(true, platform.platformName)
            dismiss()
            
        } catch (e: Exception) {
            MyToastUtil.toast(getString(R.string.s_share_rebate_share_failed))
            onShareResultListener?.invoke(false, platform.platformName)
        }
    }

    private fun copyLinkToClipboard() {
        val context = requireContext()
        val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("share_link", shareData.url)
        clipboard.setPrimaryClip(clip)
        
        MyToastUtil.toast(getString(R.string.s_share_rebate_copy_success))
        onShareResultListener?.invoke(true, "Copy")
        dismiss()
    }

    private fun isAppInstalled(context: Context, packageName: String): Boolean {
        return try {
            context.packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    fun setOnShareResultListener(listener: (success: Boolean, platform: String) -> Unit) {
        this.onShareResultListener = listener
    }
}
