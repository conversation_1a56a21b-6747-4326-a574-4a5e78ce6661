package com.tbit.uqbike.dialog

import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.DialogFragment
import com.google.gson.Gson
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.LoadingDialogHelper
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.SelInviteActivity
import com.tbit.uqbike.activity.model.RidingModel
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.entity.RidingOrderPrecompleteData
import com.tbit.uqbike.entity.event.CouponData
import com.tbit.uqbike.entity.getAreaDataOrder
import com.tbit.uqbike.entity.preCompCouponData
import com.tbit.uqbike.map.bean.Location
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.model.ParkPointModel
import com.tbit.uqbike.qrcode.CommonUtils
import com.tbit.uqbike.resqmodel.OrderModel
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.support.v4.act
import org.json.JSONObject

class BackCarDialog(var loadingDialogHelper: LoadingDialogHelper, var orderNo : String, var pre_complete_status : Int, var isShowGop : Boolean, val hasImpunity: Boolean = false, val isInPPoint: Boolean = false) : DialogFragment() {
    var lat = 0.0
    var lng = 0.0
    var onGoPListener = {_: Double,_: Double ->}
    var onSureListener = {_: ArrayList<String> ->}
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog?.setCanceledOnTouchOutside(true)
        return inflater.inflate(R.layout.dialog_backcar, container, false)
    }

    var Myview : View? = null
    var payPrice = 0f//原价
    var final_Price = 0f//优惠价
    var CouponidList = ArrayList<String>()//选择的优惠券ID
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Myview = view
        EventBus.getDefault().register(this);

//        Myview?.findViewById<TextView>(R.id.mTvSpecial)?.setText(spancontent)

        Myview?.findViewById<TextView>(R.id.tv_diog_backcar_payamount_hint)?.text = getString(R.string.s_payamount)+"("+Glob.CurrencyUnit+")"
        Myview?.findViewById<TextView>(R.id.tv_diog_backcar_rideamount_hint)?.text = getString(R.string.travel_consumption)+"("+Glob.CurrencyUnit+")"
        Myview?.findViewById<TextView>(R.id.tv_diog_backcar_couponamount_hint)?.text = getString(R.string.s_coupon_amount_dis)+"("+Glob.CurrencyUnit+")"
        Myview?.findViewById<TextView>(R.id.tv_diog_backcar_dispamount_hint)?.text = getString(R.string.s_dispatch_cost)+"("+Glob.CurrencyUnit+")"

        Myview?.findViewById<LinearLayout>(R.id.ly_diog_backcar_couponamount)?.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("short_rent_select_coupon_click",properties)

            startActivity(
                SelInviteActivity.createIntent(act, SelInviteActivity.EXTRA_TYPE_RIDEORDER,orderNo,
                    final_Price,payPrice,CouponidList))
        }
        setSytle()
        getPriceData()

        if (isShowGop){
            Myview?.findViewById<LinearLayout>(R.id.ly_gop)?.visibility = View.VISIBLE
        }
        Myview?.findViewById<LinearLayout>(R.id.ly_gop)?.setOnClickListener {
            onGoPListener(lat,lng)
            dismissAllowingStateLoss()
        }
        Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.setOnClickListener {
            onSureListener(CouponidList)

            val properties = JSONObject()
            MDUtil.clickEvent("confirm_return_click",properties)

            dismissAllowingStateLoss()
        }
        Myview?.findViewById<RoundTextView>(R.id.tv_close)?.setOnClickListener { dismissAllowingStateLoss() }

        if(pre_complete_status != 1 && pre_complete_status != 11){
            Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.setEnabled(false)
            Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.setText(getString(R.string.s_backcar) + "(" + countDownTime + "s)")
        }else{
            //正常还车
            Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.setEnabled(true)
            Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.setText(getString(R.string.s_backcar))
        }

        AutoTask()

    }
    private var countDownTime = 5
    private fun AutoTask() {
        try {
            if (Myview != null && Myview?.findViewById<RoundTextView>(R.id.tv_sure) != null) {
                Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.postDelayed(Runnable {
                    if (Myview != null && Myview?.findViewById<RoundTextView>(R.id.tv_sure) != null) {
                        if (countDownTime > 1) {
                            countDownTime--
                            if(pre_complete_status != 1 && pre_complete_status != 11){
                                Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.setEnabled(false)
                                Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.setText(getString(R.string.s_backcar) + "(" + countDownTime + "s)")
                            }else{
                                //正常还车
                                try {
                                    Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.setEnabled(true)
                                    Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.setText(getString(R.string.s_backcar))
                                }catch (e: IllegalStateException){}
                            }
                            AutoTask()
                        } else {
                            Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.setEnabled(true)
                            Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.setText(getString(R.string.s_backcar))
                            getOrderPreComp()
                        }
                    }
                }, 1000)
            }
        }catch (e : IllegalStateException){}
    }
    fun getOrderPreComp(){
        OrderModel.getPrecomplete(orderNo)
            .subscribeBy(
                onNext = {
                    AutoResqTask()
                    MyLogUtil.Log("1111","===获取 预结算订单信息=="+it.toString())
//                        MyToastUtil.toast(it.toString())
                    val resultData: RidingOrderPrecompleteData = Gson().fromJson(it.toString(), RidingOrderPrecompleteData::class.java)
                    if (resultData != null && Myview?.findViewById<RoundTextView>(R.id.tv_sure) != null){
                        pre_complete_status = resultData.pre_complete_status
                        if(pre_complete_status != 1 && pre_complete_status != 11){
                            var location = LocationModel.lastLocation
                            var latLng = LatLng()
                            try {
                                latLng.lat = location!!.latitude
                                latLng.lng = location!!.longitude
                            }catch (e : NullPointerException){
                                location = Location(0f, 0f, 0.0, 0.0, -1,"","")
                                latLng.lat = 0.0
                                latLng.lng = 0.0
                            }
                            var MylatLng = latLng
                            if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
                                latLng = GPSUtil.bd09_To_gps84(location!!.latitude,location!!.longitude)
                            }
                            latLng.lat = GPSUtil.retain6(latLng.lat)
                            latLng.lng = GPSUtil.retain6(latLng.lng)

                            ParkPointModel.getNearParkPointsOrder(orderNo,latLng.lat, latLng.lng)
                                .subscribeBy(
                                    onNext = {
                                        MyLogUtil.Log("1111","====获取附近停车点=="+it.toString())
                                        if (!it.toString().equals("[]")){
                                            val resultData = Gson().fromJson(it.toString(), getAreaDataOrder::class.java)
                                            if (resultData != null){
                                                var latlngFlag = resultData.flag
                                                if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
                                                    var dataFlag = GPSUtil.gps84_To_bd09(latlngFlag.lat,latlngFlag.lng)
                                                    latlngFlag.lat = dataFlag.lat
                                                    latlngFlag.lng = dataFlag.lng
                                                }else{
                                                    var dataFlag = GPSUtil.gps84_To_Gcj02_new(latlngFlag.lat,latlngFlag.lng)
                                                    latlngFlag.lat = dataFlag.lat
                                                    latlngFlag.lng = dataFlag.lng
                                                }
                                                lat = latlngFlag.lat
                                                lng = latlngFlag.lng
                                                Myview?.findViewById<LinearLayout>(R.id.ly_gop)?.visibility = View.VISIBLE
                                            }else{
                                                Myview?.findViewById<LinearLayout>(R.id.ly_gop)?.visibility = View.GONE
                                            }
                                        }else{
                                            Myview?.findViewById<LinearLayout>(R.id.ly_gop)?.visibility = View.GONE
                                        }
                                    },
                                    onError = {}
                                ).toCancelable()
                        }
                    }
                    getPriceData()
                },
                onError = {
                    AutoResqTask()
                    val errCode = ErrHandler.getErrCode(it)
                    if(errCode == Constant.ErrCode.TIMEOUT || errCode == Constant.ErrCode.FAILED){
                        MyToastUtil.toast(getString(R.string.network_error))
                    }
                }
            ).toCancelable()
    }
    private fun AutoResqTask() {
        if (Myview != null && Myview?.findViewById<RoundTextView>(R.id.tv_sure) != null) {
            Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.postDelayed(Runnable {
                if (Myview != null && Myview?.findViewById<RoundTextView>(R.id.tv_sure) != null) {
                    getOrderPreComp()
                }
            }, 5000L)
        }
    }


    var isSelResq = false
    fun getPriceData(){
        OrderModel.getCouponPrecomplete(orderNo,CouponidList,isSelResq)
            .subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取 预结算优惠券明细 信息=="+it.toString())
                    loadingDialogHelper.dismiss()
                    val resultData = Gson().fromJson(it.toString(), preCompCouponData::class.java).copy(
                        hasImpunity = hasImpunity,
                        isInPPoint = isInPPoint
                    )
                    if (resultData != null && Myview?.findViewById<RoundTextView>(R.id.tv_sure) != null){
                        payPrice = resultData.order_amount
                        final_Price = payPrice + resultData.coupon_deduction
                        Myview?.findViewById<TextView>(R.id.tv_diog_backcar_payamount)?.text = AppUtil.getFloat2(resultData.amount)
                        Myview?.findViewById<TextView>(R.id.tv_diog_backcar_rideamount)?.text = AppUtil.getFloat2(resultData.order_amount)
                        if(resultData.coupon_deduction == 0f && !isSelResq){
                            Myview?.findViewById<LinearLayout>(R.id.ly_diog_backcar_couponamount)?.visibility = View.GONE
                        }else{
                            Myview?.findViewById<LinearLayout>(R.id.ly_diog_backcar_couponamount)?.visibility = View.VISIBLE
                            Myview?.findViewById<TextView>(R.id.tv_diog_backcar_couponamount)?.text = AppUtil.getFloat2(resultData.coupon_deduction)
                            CouponidList.clear()
                            resultData.used_coupon_list.forEach {
                                CouponidList.add(it.user_coupon_id.toString())
                            }
                        }
                        Myview?.findViewById<TextView>(R.id.tv_diog_backcar_dispamount)?.text = AppUtil.getFloat2(resultData.dispatch_amount)

                        // 新增：免罚权益优先判断
                        val dispatchAmountShow = if (resultData.hasImpunity && !resultData.isInPPoint) 0f else resultData.dispatch_amount
                        if (resultData.hasImpunity && !resultData.isInPPoint) {
                            val context = Myview?.context
                            val spannableString = SpannableString(context?.getString(R.string.s_impunity_tip) ?: "会员专属权益：当前车辆不在p点内，免收任何调度费，您可直接归还")
                            spannableString.setSpan(StyleSpan(Typeface.BOLD), 0, spannableString.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                            spannableString.setSpan(ForegroundColorSpan(ContextCompat.getColor(context!!, R.color.c_8A2019)), 0, spannableString.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                            Myview?.findViewById<TextView>(R.id.tv_diog_backcar_dispamount)?.text = "0"
                            Myview?.findViewById<TextView>(R.id.mTvSpecial)?.setText(spannableString)
                        } else {
                            var unit = AppUtil.getFloat2(dispatchAmountShow).toString()+resultData.currency
                            var content = RidingModel.getPre(pre_complete_status,unit)
                            val spannableString = SpannableString(content)
                            if(pre_complete_status != 1 && pre_complete_status != 11){
                                if (content.contains(unit.toString())){
                                    var data1 = spannableString.split(unit)[0].length
                                    spannableString.setSpan(ForegroundColorSpan(ContextUtil.getContext().resources.getColor(R.color.c_y)), data1, data1+unit.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                                }
                            }
                            Myview?.findViewById<TextView>(R.id.mTvSpecial)?.setText(spannableString)
                        }
                        setSytle()
                        val dispAmountTag = Myview?.findViewById<TextView>(R.id.tv_diog_backcar_dispamount_tag)
                        if (resultData.hasImpunity && !resultData.isInPPoint && dispatchAmountShow == 0f) {
                            dispAmountTag?.visibility = View.VISIBLE
                            dispAmountTag?.text = Myview?.context?.getString(R.string.s_rideorder_payFree) ?: "已免除"
                        } else {
                            dispAmountTag?.visibility = View.GONE
                        }
                    }
                },
                onError = {
                    loadingDialogHelper.dismiss()
                }
            ).toCancelable()
    }
    fun setSytle(){
        if(pre_complete_status != 1 && pre_complete_status != 11){
            Myview?.findViewById<TextView>(R.id.tv_diag_title)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.c_8A2019))
            Myview?.findViewById<TextView>(R.id.tv_diag_title)?.setPadding(0, CommonUtils.dip2px(activity, 15f),0,0)
            Myview?.findViewById<TextView>(R.id.mTvSpecial)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.c_8A2019))
            Myview?.findViewById<ImageView>(R.id.img_diag_ts)?.visibility = View.VISIBLE
            Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.getDelegate()?.backgroundColor = ContextUtil.getContext().getResources().getColor(R.color.c_E7544C)
        }else{
            Myview?.findViewById<TextView>(R.id.tv_diag_title)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.black_namal))
            Myview?.findViewById<TextView>(R.id.tv_diag_title)?.setPadding(0, CommonUtils.dip2px(activity, 1f),0,0)
            Myview?.findViewById<TextView>(R.id.mTvSpecial)?.setTextColor(ContextUtil.getContext().resources.getColor(R.color.black_namal))
            Myview?.findViewById<ImageView>(R.id.img_diag_ts)?.visibility = View.GONE
            Myview?.findViewById<RoundTextView>(R.id.tv_sure)?.getDelegate()?.backgroundColor = ContextUtil.getContext().getResources().getColor(R.color.blue_namal)
        }
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: CouponData) {
        if (event != null){
            CouponidList.clear()
            CouponidList.addAll(event.couponids)
            isSelResq = true
            loadingDialogHelper.show {  }
            getPriceData()
        }
    }
    private fun startAdIntent(intent: Intent, needLogin: Boolean) {
        if (needLogin && !Glob.isLogin) {
            FlavorConfig.appRoute.login(intent)
        } else {
            startActivity(intent)
        }
    }
    var eventName = "second_confirmation_car_return"
    override fun onStart() {
        super.onStart()
        MDUtil.pageStar(eventName)
    }
    override fun onDestroy() {
        super.onDestroy()
        val properties = JSONObject()
        properties.put("tag",eventName)
        properties.put("business_type", 1)
        properties.put("pop_up_type", pre_complete_status)
        MDUtil.pageEnd(eventName,properties)

        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }
}