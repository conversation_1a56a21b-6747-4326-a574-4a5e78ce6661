package com.tbit.uqbike.dialog;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.view.Gravity;

import androidx.annotation.NonNull;

import com.tbit.maintenance.utils.ResUtil;
import com.tbit.uqbike.App;
import com.tbit.uqbike.R;
import com.tbit.uqbike.activity.WebActivity;
import com.tbit.uqbike.qrcode.CommonUtils;
import com.tbit.uqbike.roundview.RoundLinearLayout;
import com.tbit.uqbike.utils.ClickableTextUtil;
import com.tbit.uqbike.utils.MyLogUtil;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

public class PllicyDialog extends Dialog implements View.OnClickListener {

    private Builder builder;
    public Context context;
    private TextView mTvLeft, mTvRight, mTvTitle, mTvSpecial;
    private RoundLinearLayout rl_left;
    private View viewLine;

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.mTvLeft) {
            if (builder.mTwoSelDialog != null)
                builder.mTwoSelDialog.leftClick();
            this.dismiss();
        } else if (view.getId() == R.id.mTvRight) {
            if (builder.mOneSelDialog != null)
                builder.mOneSelDialog.sureClick();
            if (builder.mTwoSelDialog != null)
                builder.mTwoSelDialog.rightClick();
            this.dismiss();
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        if (builder.onOutClick != null) {
            builder.onOutClick.onOutClick();
        }
    }

    @Override
    public boolean onTouchEvent(@NonNull MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                //点击弹窗外部区域
                if (!isOutOfBounds(getContext(), event)) {
                    return super.onTouchEvent(event);
                }
                break;
            case MotionEvent.ACTION_UP:
                //点击弹窗外部区域
                if (isOutOfBounds(getContext(), event)) {
                    if (builder.onOutClick != null) {
                        builder.onOutClick.onOutClick();
                    }
                }
                break;
        }
        return super.onTouchEvent(event);
    }

    private boolean isOutOfBounds(Context context, MotionEvent event) {
        final int x = (int) event.getX();//相对弹窗左上角的x坐标
        final int y = (int) event.getY();//相对弹窗左上角的y坐标
        final int slop = ViewConfiguration.get(context).getScaledWindowTouchSlop();//最小识别距离
        final View decorView = getWindow().getDecorView();//弹窗的根View
        return (x < -slop) || (y < -slop) || (x > (decorView.getWidth() + slop))
                || (y > (decorView.getHeight() + slop));
    }

    public interface TwoSelDialog {
        void leftClick();

        void rightClick();

        void privacyPolicyClick();

        void serviceTerms();

    }

    public interface OneSelDialog {
        void sureClick();
    }

    public interface OnOutClick {
        void onOutClick();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.comm_dialog);
        init();
//        dialog=this;
    }

    private PllicyDialog(Builder builder) {
        super(builder.mContext, R.style.PXDialog);
        this.context = builder.mContext;
        this.builder = builder;
    }

    @SuppressLint("UseCompatLoadingForColorStateLists")
    private void init() {
        mTvTitle = (TextView) findViewById(R.id.mTvTitle);
        mTvLeft = (TextView) findViewById(R.id.mTvLeft);
        rl_left = (RoundLinearLayout) findViewById(R.id.rl_left);
        RoundLinearLayout rl_right = (RoundLinearLayout) findViewById(R.id.rl_right);
        LinearLayout lay_middle = (LinearLayout) findViewById(R.id.lay_middle);
        mTvSpecial = (TextView) findViewById(R.id.mTvSpecial);
        mTvRight = (TextView) findViewById(R.id.mTvRight);
        viewLine = (View) findViewById(R.id.vD);
        mTvTitle.setVisibility(builder.isShowTitle ? View.VISIBLE : View.GONE);
        if (!TextUtils.isEmpty(builder.title)) {
            mTvTitle.setText(builder.title);
        }
        if (!TextUtils.isEmpty(builder.left)) {
            mTvLeft.setText(builder.left);
            LinearLayout.LayoutParams params_left = (LinearLayout.LayoutParams) rl_left.getLayoutParams();
            params_left.width = 0;
            params_left.weight = 1f;
            rl_left.setLayoutParams(params_left);
            rl_left.setVisibility(View.VISIBLE);

            LinearLayout.LayoutParams params_right = (LinearLayout.LayoutParams) rl_right.getLayoutParams();
            params_right.width = 0;
            params_right.weight = 1f;
            rl_right.setLayoutParams(params_right);
            
            viewLine.setVisibility(View.VISIBLE);
            lay_middle.setGravity(Gravity.NO_GRAVITY);
        } else {
            rl_left.setVisibility(View.GONE);
            viewLine.setVisibility(View.GONE);

            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) rl_right.getLayoutParams();
            params.width = LinearLayout.LayoutParams.WRAP_CONTENT;
            params.weight = 0f;
            rl_right.setLayoutParams(params);

            int horizontalPadding = (int) (30 * getContext().getResources().getDisplayMetrics().density);
            mTvRight.setPadding(horizontalPadding, mTvRight.getPaddingTop(), horizontalPadding, mTvRight.getPaddingBottom());

            lay_middle.setGravity(Gravity.CENTER_HORIZONTAL);
        }
        if (!TextUtils.isEmpty(builder.right)) {
            mTvRight.setText(builder.right);
        }
        if(builder.leftColor!=0){
            mTvLeft.setTextColor(App.context.getResources().getColor(builder.leftColor));
        }
        if(builder.rightColor!=0){
            mTvRight.setTextColor(App.context.getResources().getColor(builder.rightColor));
        }
        if (!TextUtils.isEmpty(builder.content)) {
            mTvSpecial.setText(builder.content);
            ClickableTextUtil.INSTANCE.makeClickable(mTvSpecial, ResUtil.INSTANCE.getString(R.string.str_privacy_policy), new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    builder.mTwoSelDialog.privacyPolicyClick();
                    return null;
                }
            });
            ClickableTextUtil.INSTANCE.makeClickable(mTvSpecial, ResUtil.INSTANCE.getString(R.string.str_service_terms), new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    builder.mTwoSelDialog.serviceTerms();
                    return null;
                }
            });
        }else {
            mTvSpecial.setVisibility(View.GONE);
        }

        if (!TextUtils.isEmpty(builder.spanncontent)) {
            mTvSpecial.setVisibility(View.VISIBLE);
            mTvSpecial.setText(builder.spanncontent);
        }else {
//            mTvSpecial.setVisibility(View.GONE);
        }
        if (builder.mOneSelDialog != null) {
            findViewById(R.id.vD).setVisibility(View.GONE);
            mTvLeft.setVisibility(View.GONE);
        }
        if(builder.isShowClose){
            findViewById(R.id.iv_close).setVisibility(View.VISIBLE);
        }
        Window dialogWindow = this.getWindow();
        WindowManager.LayoutParams lp = dialogWindow.getAttributes();
        lp.width = CommonUtils.dp2px(context, 270); // 宽度
//        lp.height = HnDimenUtil.dp2px(context,  170); // 高度
        dialogWindow.setAttributes(lp);
        this.setCanceledOnTouchOutside(builder.isCancel);
        this.setCancelable(builder.isCancel);

        mTvLeft.setOnClickListener(this);
        mTvRight.setOnClickListener(this);

    }
    @Override
    public void show() {
        if (context == null) {
            dismiss();
            builder = null;
            return;
        }
        super.show();
    }

    public static class Builder {
        private String title;
        private String left;
        private String right;
        private String content;

        private SpannableString spanncontent;
        private int leftColor;
        private int rightColor;
        private TwoSelDialog mTwoSelDialog;
        private OneSelDialog mOneSelDialog;
        private OnOutClick onOutClick;

        private boolean isCancel = true;
        private boolean isShowTitle = true;
        private boolean isShowClose = false;
        private Context mContext;

        public Builder(Context mContext) {
            this.mContext = mContext;
        }



        public Builder setCanceledOnOutside(boolean isCancel) {
            this.isCancel = isCancel;
            return this;
        }
        public Builder setLeftText(String left) {
            this.left = left;
            return this;
        }

        public Builder setRightText(String right) {
            this.right = right;
            return this;
        }

        public Builder setLeftColor(int leftColor) {
            this.leftColor = leftColor;
            return this;
        }

        public Builder setRightColor(int rightColor) {
            this.rightColor = rightColor;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setTitleVisible(boolean isShowTitle) {
            this.isShowTitle = isShowTitle;
            return this;
        }
        public Builder setCloseVisible(boolean isShowClose) {
            this.isShowClose = isShowClose;
            return this;
        }

        public Builder setContent(String content) {
            this.content = content;
            return this;
        }

        public Builder setSpanContent(SpannableString spancontent) {
            this.spanncontent = spancontent;
            return this;
        }

        public Builder setClickListen(TwoSelDialog mTwoSelDialog) {
            this.mTwoSelDialog = mTwoSelDialog;
            return this;
        }

        public Builder setClickListen(OneSelDialog mOneSelDialog) {
            this.mOneSelDialog = mOneSelDialog;
            return this;
        }

        public Builder setOnOUtCllickListener(OnOutClick outClick) {
            this.onOutClick = outClick;
            return this;
        }

        public PllicyDialog build() {
            return new PllicyDialog(this);
        }
    }
}
