package com.tbit.uqbike.web.js.util

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import androidx.core.content.ContextCompat.startActivity
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.doule.database.CoroutinesUtil
import com.google.gson.Gson
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.preview.GlideEngine
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.MainActivity
import com.tbit.uqbike.activity.MineActivity
import com.tbit.uqbike.activity.RideCardNewActivity
import com.tbit.uqbike.activity.SelInviteActivity
import com.tbit.uqbike.activity.WebActionActivity
import com.tbit.uqbike.activity.WebActivity
import com.tbit.uqbike.adapter.SelectPhotoAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.dialog.PicSumitDialog
import com.tbit.uqbike.entity.JsData
import com.tbit.uqbike.entity.JsGoNativeData
import com.tbit.uqbike.entity.RentalRechData
import com.tbit.uqbike.entity.TextData
import com.tbit.uqbike.entity.WebBrowserData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.entity.getHeadData
import com.tbit.uqbike.entity.goWebData
import com.tbit.uqbike.entity.jsChoseCoupon
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.entity.protocolData
import com.tbit.uqbike.entity.saveImgData
import com.tbit.uqbike.entity.shareUrlData
import com.tbit.uqbike.file.FileUtil
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.resqmodel.OrderModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.utils.ClipboardUtil
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.UrlDecodeUtil
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.delay
import org.greenrobot.eventbus.EventBus
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.startActivityForResult
import org.jetbrains.anko.support.v4.startActivity
import org.json.JSONObject
import wendu.dsbridge.CompletionHandler
import android.widget.FrameLayout
import wendu.dsbridge.DWebView

object webUtil {
//    获取headers  // '{"type":"headers"}'
//    打开《GOGOBike长租协议》 // '{"type":"protocol","data":{"key":"long_lease_instructions"}}'
//    关闭 webview // '{"type":"goback"}'
//    确认长租订单 // '{"type":"submitLease","data":{"money":"50"}}'
    val Type_Head = "headers"  //头部信息
    val Type_goback = "goback"  //关闭
    val Type_protocol = "protocol"  //协议
    val Type_submitLease = "submitLease"  //确认长租订单
    val Type_leaseRecharge = "leaseRecharge"  //长租充值
    val Type_leaseOrderSucceed = "leaseOrderSucceed"  //确认订单成功
    val Type_latlng = "latlng"  //获取位置信息
    var Type_copyText = "copyText" //复制订单编号
    var Type_uploadCertification = "uploadCertification" //上传图片
    var Type_getUserAgent = "getUserAgent" //ua 数据
    var Type_shareUrl = "shareUrl" // 分享链接
    var Type_shareRebate = "shareRebate" // 分享返利
    var Type_saveImage = "saveImage" // 保存图片
    var Type_chooseCoupon = "chooseCoupon" // 选择优惠券
    var Type_goNative = "goAppPage" // 调原生界面   page： home 首页，rideCard 购买骑行卡
    var Type_goWebview = "goWebview" //跳转web界面
    var Type_goWebBrowser = "goWebBrowser"//跳转外部浏览器
    var Type_setWebpageHeight = "setWebpageHeight" //设置网页高度
    fun getHeads() : String{
        var resultData = ""
        resultData = Gson().toJson(getHeadData(AppUtil.getUserAgent(), Glob.area_id.toString(),
            SpUtil.Companion.getInstance().find(Constant.SpKey.DEVICE_ID)!!,
            if (Glob.CurrencyLld.isNullOrEmpty()) AppUtil.getLLd() else Glob.CurrencyLld,
            FlavorConfig.Local.language,Glob.region_code.toString(),FlavorConfig.Local.timeZone,
            Glob.token.toString(),Glob.uacsta.toString()))
        return resultData
    }

    fun getLatlng(): String{
        var resultData = ""
        var myPos = OrderModel.getLocalData()
        resultData = Gson().toJson(LatLng(myPos.lat,myPos.lng))
        return resultData
    }

    var onGoPayListener = {_: Float ->}
    fun goJsMethod(act : BaseActivity, handler : CompletionHandler<String>, msg : String){
        try {
            val resultData = Gson().fromJson(msg, JsData::class.java)
            if (resultData != null){
                when(resultData.type){
                    webUtil.Type_Head -> {
                        if (!Glob.isLogin) {
                            MyToastUtil.toast(ResUtil.getString(R.string.login_first))
                            FlavorConfig.appRoute.login()
                            act.finish()
                        }else{
                            var sumitData = webUtil.getHeads()
//                                MyLogUtil.Log("1111","====="+sumitData)
                            handler.complete(sumitData)
                        }
                    }
                    webUtil.Type_protocol -> {
                        var datas = Gson().fromJson(msg, protocolData::class.java)
                        CoroutinesUtil.launchMain {act.loadingDialogHelper.show {  }}
                        PageModel.getPageUrl(datas.data.key).subscribeBy(
                            onNext = {
                                CoroutinesUtil.launchMain {act.loadingDialogHelper.dismiss()}
                                MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                                var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                                act.startActivity<WebActivity>(
                                    WebActivity.TITLE to resultData.title,
                                    WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
                            },
                            onError = {
                                CoroutinesUtil.launchMain {act.loadingDialogHelper.dismiss()}
                            }
                        ).toCancelable()
                    }
                    webUtil.Type_submitLease -> {
//                                var datas = Gson().fromJson(msg, LeasePayMoneyData::class.java)
                    }
                    webUtil.Type_leaseOrderSucceed -> {
//                                var datas = Gson().fromJson(msg, LeasePayMoneyData::class.java)
                        CoroutinesUtil.launchMain {
                            EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_LEASESUC,""))
                            delay(200)
                            act.finish()
                        }
                    }
                    webUtil.Type_latlng -> {
                        var sumitData = webUtil.getLatlng()
//                                MyLogUtil.Log("1111","====="+sumitData)
                        handler.complete(sumitData)
                    }
                    webUtil.Type_leaseRecharge -> {
                        var datas = Gson().fromJson(msg, RentalRechData::class.java)
//                        goPay(datas.data.money.toFloat())
                        onGoPayListener(datas.data.money.toFloat())
                    }
                    webUtil.Type_goback -> {
                        act.finish()
                    }
                    webUtil.Type_copyText -> {
                        var datas = Gson().fromJson(msg, TextData::class.java)
                        ClipboardUtil.copy(datas.data.text)
//                                com.tbit.uqbike.utils.MyToastUtil.toast(getString(R.string.str_copy_success))
                        handler.complete(getjsResult(true))
                    }
                    webUtil.Type_uploadCertification -> {
                        showPicSelDig(act)
                    }
                    webUtil.Type_goWebBrowser -> {
                        try {
                            var datas = Gson().fromJson(msg, WebBrowserData::class.java)
                            var url = datas.data.url
                            val intent: Intent
                            if (url.startsWith("intent://") && url.contains("scheme")) {
                                intent = Intent.parseUri(url, 0)
                            } else {
                                intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                            }
                            act.startActivity(intent)
                        } catch (e: Exception) {
//                            MyToastUtil.toast(ResUtil.getString(R.string.s_noapp))
                            e.printStackTrace()
                        }
                    }
                    webUtil.Type_getUserAgent -> {
//                                MyLogUtil.Log("1111","====="+AppUtil.getUserAgent())
                        handler.complete(AppUtil.getUserAgent())
                    }
                    webUtil.Type_shareUrl -> {
                        var datas = Gson().fromJson(msg, shareUrlData::class.java)

                        // 检测是否为分享返利链接（包含商品信息和用户信息）
                        if (isShareRebateUrl(datas.data.url)) {
                            // 调用分享返利弹窗
                            showShareRebateDialog(act, datas.data.text, datas.data.url, handler)
                        } else {
                            // 使用原有的系统分享
                            shareText(act,"",datas.data.text+"\n"+datas.data.url)
                        }
                    }
                    webUtil.Type_shareRebate -> {
                        MyLogUtil.Log("1111", "收到分享返利请求: $msg")
                        try {
                            val shareRebateData = Gson().fromJson(msg, com.tbit.uqbike.entity.ShareRebateData::class.java)
                            if (shareRebateData?.data != null) {
                                // 显示分享弹窗
                                val shareDialog = com.tbit.uqbike.dialog.ShareRebateDialog(shareRebateData.data)
                                shareDialog.setOnShareResultListener { success, platform ->
                                    MyLogUtil.Log("1111", "Share result: success=$success, platform=$platform")
                                }
                                act.lifecycleDialogHelper.show(shareDialog)
                                handler.complete(getjsResult(true))
                            } else {
                                MyLogUtil.Log("1111", "分享返利数据为空")
                                handler.complete(getjsResult(false))
                            }
                        } catch (e: Exception) {
                            MyLogUtil.Log("1111", "处理分享返利出错: ${e.message}")
                            handler.complete(getjsResult(false))
                        }
                    }
                    webUtil.Type_goWebview -> {
                        var datas = Gson().fromJson(msg, goWebData::class.java)
                        if (datas.data.needLogin == 1){
                            if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
                                MyToastUtil.toast(ResUtil.getString(R.string.s_local_err))
                                return
                            }
                            if (!Glob.isLogin) {
                                MyToastUtil.toast(ResUtil.getString(R.string.login_first))
                                FlavorConfig.appRoute.login()
                                return
                            }
                        }
                        if (datas.data.title.isNullOrEmpty()){
                            act.startActivity<WebActionActivity>(WebActionActivity.TITLE to "",
                                WebActionActivity.URL to datas.data.url, WebActionActivity.IsHideHead to true)
                        }else{
                            act.startActivity<WebActionActivity>(WebActionActivity.TITLE to datas.data.title,
                                WebActionActivity.URL to datas.data.url, WebActionActivity.IsHideHead to false)
                        }
                    }
                    webUtil.Type_setWebpageHeight -> {
                        // 判断当前WebView是否属于首页H5区
                        val webView = act.findViewById<DWebView>(R.id.webView)
                        if (webView != null && webView.parent is FrameLayout && (webView.parent as FrameLayout).id == R.id.layout_h5_area) {
                            // 首页H5区，忽略高度调整请求
                            handler.complete("{\"code\":0,\"data\":null}")
                            return
                        }
                        // 非首页H5区，正常处理
                        handler.complete("{\"code\":0,\"data\":null}")
                    }
                    webUtil.Type_goNative -> {
                        if (!Glob.isLogin) {
                            if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
                                MyToastUtil.toast(ResUtil.getString(R.string.s_local_err))
                                return
                            }
                            MyToastUtil.toast(ResUtil.getString(R.string.login_first))
                            FlavorConfig.appRoute.login()
                        }else{
                            var datas = Gson().fromJson(msg, JsGoNativeData::class.java)
                            CoroutinesUtil.launchMain {
                                when(datas.data.page){
                                    "home"->{
                                        EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_HOME,""))
                                        act.finish()
                                    }
                                    "rideCard"->{
                                        EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_RIDECARD,""))
                                        act.finish()
                                    }
                                    "buyRideCard" ->{
                                        act.startActivityForResult<RideCardNewActivity>(MineActivity.REQUEST_RIDE_CARD)
                                    }
                                    "ride" ->{
//                                        EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_GORIDE,""))
                                        MyLogUtil.Log("1111","点击首页骑行的按钮")
                                        act.startActivity<MainActivity>()
                                    }
                                    "goRecharge" ->{
                                        EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_GORECHARGE,""))
                                        act.finish()
                                    }
                                    "rideMainPage" ->{
                                        act.startActivity<MainActivity>()
                                    }
                                }
                            }
                        }

                    }
                    webUtil.Type_chooseCoupon -> {
                        var datas = Gson().fromJson(msg, jsChoseCoupon::class.java)
                        var CouponidList = ArrayList<String>()//选择的优惠券ID
                        datas.data.couponIds.forEach {
                            CouponidList.add(it.toString())
                        }
                        act.startActivity(
                            SelInviteActivity.createIntent(act, SelInviteActivity.EXTRA_TYPE_RIDECARD,
                            datas.data.rideCardId.toString(), datas.data.finalPrice,datas.data.price,CouponidList,true))
                    }
                    webUtil.Type_saveImage -> {
                        var datas = Gson().fromJson(msg, saveImgData::class.java)
                        if (!datas.data.base64.isNullOrEmpty()){
                            if (!FileUtil.saveImageToGallery(ContextUtil.getContext(), FileUtil.convert(datas.data.base64)).isNullOrEmpty()){
                                handler.complete(getjsResult(true))
                            }else{
                                handler.complete(getjsResult(false))
                            }
                        }else if (!datas.data.url.isNullOrEmpty()){
                            act.loadingDialogHelper.show {  }
                            Glide.with(ContextUtil.getContext()).asBitmap().load(datas.data.url).into(object : SimpleTarget<Bitmap>() {
                                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                                    act.loadingDialogHelper.dismiss()
                                    //resource即为下载取得的bitmap
                                    if (!FileUtil.saveImageToGallery(ContextUtil.getContext(),resource).isNullOrEmpty()){
                                        handler.complete(getjsResult(true))
                                    }
                                }
                                override fun onLoadFailed(errorDrawable: Drawable?) {
                                    super.onLoadFailed(errorDrawable)
                                    act.loadingDialogHelper.dismiss()
                                    handler.complete(getjsResult(false))
                                }
                            })
                        }
                    }
                }
            }
        }catch (e: Exception){
            MyLogUtil.Log("1111",e.message)
        }
    }

    fun getjsResult(isSuc: Boolean): String{
        var result = ""
        if (isSuc){
            result = "{\"result\":\"1\"}"
        }else{
            result = "{\"result\":\"0\"}"
        }
        return result
    }

    fun shareText(act : BaseActivity,title: String, text: String) {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, text)
            putExtra(Intent.EXTRA_TITLE, title)
            type = "text/plain"
        }
//        val shareIntent = Intent.createChooser(sendIntent, "分享到")
        val shareIntent = Intent.createChooser(sendIntent, "")
        act.startActivity(shareIntent)
    }
    fun showPicSelDig(act : BaseActivity){
        var picDialog = PicSumitDialog(false)
        picDialog.onBtnListener = {
            when (it) {
                0 -> selectCamera(act)
                1 -> selectImage(act)
            }
        }
        act.lifecycleDialogHelper.show(picDialog)
    }
    val REQUEST_CODE_CHOOSE = 111000
    fun selectImage(act : BaseActivity) {
        val properties = JSONObject()
        MDUtil.clickEvent("album_upload_click",properties)
        PictureSelector.create(act)
            .openGallery(SelectMimeType.ofImage())
            .setMaxSelectNum(1)// 最大图片选择数量
            .setMinSelectNum(1)// 最小选择数量
            .setImageSpanCount(3)// 每行显示个数
            .setSelectionMode(SelectModeConfig.MULTIPLE)// 多选 or 单选
            .setImageEngine(GlideEngine.createGlideEngine())
            .isPreviewImage(false)// 是否可预览图片
            .isSelectZoomAnim(true)// 图片列表点击 缩放效果 默认true
            .isDisplayCamera(false)//是否显示相机入口
//            .setCompressEngine(ImageFileCompressEngine())// 是否压缩
            .forResult(REQUEST_CODE_CHOOSE)//结果回调onActivityResult code
    }
    fun selectCamera(act : BaseActivity) {
        val properties = JSONObject()
        MDUtil.clickEvent("take_photos_upload_click",properties)
        PictureSelector.create(act)
            .openCamera(SelectMimeType.ofImage())// 全部.PictureMimeType.ofAll()、图片.ofImage()、视频.ofVideo()、音频.ofAudio()
            .forResultActivity(REQUEST_CODE_CHOOSE)//结果回调onActivityResult code
    }

    /**
     * 检测是否为分享返利链接
     * 判断URL是否包含商品相关参数，如product_id、id、username等
     */
    private fun isShareRebateUrl(url: String): Boolean {
        return url.contains("gogo-shop") &&
               (url.contains("product") || url.contains("item")) &&
               (url.contains("id=") || url.contains("username="))
    }

    /**
     * 显示分享返利弹窗
     */
    private fun showShareRebateDialog(
        act: BaseActivity,
        text: String,
        url: String,
        handler: CompletionHandler<String>
    ) {
        try {
            // 从URL中提取商品ID
            val productId = extractProductIdFromUrl(url)

            // 创建分享返利数据
            val shareData = com.tbit.uqbike.entity.ShareData(
                url = url,
                text = text,
                Product_id = productId,
                rebates = listOf("10", "5", "3", "2", "1"), // 默认佣金层级
                all_rebate = 21 // 总计21%
            )

            // 显示分享弹窗
            val shareDialog = com.tbit.uqbike.dialog.ShareRebateDialog(shareData)
            shareDialog.setOnShareResultListener { success, platform ->
                MyLogUtil.Log("1111", "Share result: success=$success, platform=$platform")
            }

            act.lifecycleDialogHelper.show(shareDialog)
            handler.complete(getjsResult(true))

        } catch (e: Exception) {
            MyLogUtil.Log("1111", "showShareRebateDialog error: ${e.message}")
            // 如果出错，回退到系统分享
            shareText(act, "", text + "\n" + url)
            handler.complete(getjsResult(false))
        }
    }

    /**
     * 从URL中提取商品ID
     */
    private fun extractProductIdFromUrl(url: String): Int {
        return try {
            // 尝试从URL中提取product_id或item_id
            val regex = Regex("(?:product|item)[_/]?(\\d+)")
            val matchResult = regex.find(url)
            matchResult?.groupValues?.get(1)?.toInt() ?: 0
        } catch (e: Exception) {
            0
        }
    }
}