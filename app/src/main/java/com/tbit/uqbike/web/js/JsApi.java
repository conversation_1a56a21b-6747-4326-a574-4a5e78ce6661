package com.tbit.uqbike.web.js;

import android.os.CountDownTimer;
import android.webkit.JavascriptInterface;

import org.json.JSONException;
import org.json.JSONObject;

import wendu.dsbridge.CompletionHandler;

public class JsApi {
    @JavascriptInterface
    public String testSyn(Object msg)  {
        return msg + "［syn call］";
    }

    @JavascriptInterface
    public void testAsyn(Object msg, CompletionHandler<String> handler){
        android.util.Log.d("JsApi", "testAsyn 被调用，参数: " + msg);
        if(onItemClickListener != null){
            onItemClickListener.onClickItem(msg+"",handler);
        }
//        handler.complete(msg+" [ asyn call]");
    }

    @JavascriptInterface
    public String testNoArgSyn(Object arg) throws JSONException {
        return  "testNoArgSyn called [ syn call]";
    }

    @JavascriptInterface
    public void testNoArgAsyn(Object arg,CompletionHandler<String> handler) {
        handler.complete( "testNo<PERSON>rg<PERSON>yn   called [ asyn call]");
    }


    //@JavascriptInterface
    //without @JavascriptInterface annotation can't be called
    public String testNever(Object arg) throws JSONException {
        JSONObject jsonObject= (JSONObject) arg;
        return jsonObject.getString("msg") + "[ never call]";
    }

    @JavascriptInterface
    public void callProgress(Object args, final CompletionHandler<Integer> handler) {

        new CountDownTimer(11000, 1000) {
            int i=10;
            @Override
            public void onTick(long millisUntilFinished) {
                //setProgressData can be called many times util complete be called.
                handler.setProgressData((i--));

            }
            @Override
            public void onFinish() {
                //complete the js invocation with data; handler will be invalid when complete is called
                handler.complete(0);

            }
        }.start();
    }

    @JavascriptInterface
    public void shareRebate(Object args, final CompletionHandler<String> handler) {
        android.util.Log.d("JsApi", "shareRebate 被调用，参数: " + args);
        if(onShareRebateListener != null){
            onShareRebateListener.onShareRebate(args.toString(), handler);
        } else {
            android.util.Log.d("JsApi", "shareRebate 监听器为空");
        }
    }
    private OnClickListener onItemClickListener;
    public interface OnClickListener {
        void onClickItem(String msg,CompletionHandler<String> handler);
    }

    public void setOnItemClickListener(OnClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    private OnShareRebateListener onShareRebateListener;
    public interface OnShareRebateListener {
        void onShareRebate(String jsonData, CompletionHandler<String> handler);
    }

    public void setOnShareRebateListener(OnShareRebateListener onShareRebateListener) {
        this.onShareRebateListener = onShareRebateListener;
    }
}