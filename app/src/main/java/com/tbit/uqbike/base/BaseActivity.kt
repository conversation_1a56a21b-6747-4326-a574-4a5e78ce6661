package com.tbit.uqbike.base

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.view.Window
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import com.sensorsdata.analytics.android.sdk.ScreenAutoTracker
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.ApolloLifecycleObserver
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.LifecycleDialogHelper
import com.tbit.maintenance.utils.LoadingDialogHelper
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.SaveState
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.fullScreen
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.App
import com.tbit.uqbike.Glob
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.ChargeNewActivity
import com.tbit.uqbike.activity.ExchangeActivity
import com.tbit.uqbike.activity.LanguageActivity
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.entity.UserEntity
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.model.UserModel
import com.tbit.uqbike.push.tester.UPushAlias
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.StatusBarUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.toast
import org.json.JSONException
import org.json.JSONObject


/**
 * Created by yankaibang on 2018/10/8.
 */
abstract class BaseActivity: AppCompatActivity(),ScreenAutoTracker{

    private val apolloLifecycleObserver = ApolloLifecycleObserver(this)

    val lifecycleDialogHelper by lazy { LifecycleDialogHelper(supportFragmentManager) }

    protected val saveState = SaveState("EXTRA_SAVE_STATE")

    val loadingDialogHelper by lazy {
        LoadingDialogHelper(this, getString(com.tbit.uqbike.R.string.please_wait),false)
    }
    val payingDialogHelper by lazy {
        LoadingDialogHelper(this, getString(com.tbit.uqbike.R.string.s_paying),true)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        fullScreen()
//        setBarState()
        lifecycle.addObserver(apolloLifecycleObserver)
        lifecycle.addObserver(lifecycleDialogHelper)
        addLifecycleObserver()
        saveState.onRestoreInstanceState(savedInstanceState)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().setNavigationBarColor(ContextCompat.getColor(this, R.color.white));
        }
        MyLogUtil.Log("0000","=====界面====="+this::class.simpleName)
//        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//            //修改为深色，因为我们把状态栏的背景色修改为主题色白色，默认的文字及图标颜色为白色，导致看不到了。
//            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
//        }

    }

    fun setStatusBarTextColor(window: Window, light: Boolean) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            var systemUiVisibility = window.decorView.systemUiVisibility
            systemUiVisibility = if (light) { //白色文字
                systemUiVisibility and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
            } else { //黑色文字
                systemUiVisibility or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
            }
            window.decorView.systemUiVisibility = systemUiVisibility
        }
    }


    open fun setBarState(){
        showBarTextColorIsBlack(false)
    }

    protected open fun addLifecycleObserver() {
        lifecycle.addObserver(LocationModel)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        saveState.onSaveInstanceState(outState)
    }

    public open fun tryLoginIfNot(): Boolean {
        if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
            toast(getString(R.string.s_local_err))
            return false
        }
        val isLoggedIn: Boolean = Glob.isLogin
        if (!isLoggedIn) {
            toast(getString(com.tbit.uqbike.R.string.login_first))
            FlavorConfig.appRoute.login()
        }
        return isLoggedIn
    }

    open fun showBarTextColorIsBlack(isBlack: Boolean) {
        if (isBlack) {
            StatusBarUtil.setBackgroundStatusBar(this)
        } else {
            StatusBarUtil.setWhiteStatusBar(this)
        }
    }

    // 检查是否已经获得定位权限的函数
    fun hasLocationPermission(): Boolean {
        val permission = Manifest.permission.ACCESS_FINE_LOCATION // 或者使用 ACCESS_COARSE_LOCATION
        return ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
    }
    fun hasCamPermission(): Boolean {
        val permission = Manifest.permission.CAMERA
        return ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
    }
    fun openLocal(){
        CommDialog.Builder(this).setTitle(ResUtil.getString(com.tbit.uqbike.R.string.dialog_tip)).setContent(getString(
            com.tbit.uqbike.R.string.s_opengps_hint))
            .setLeftText(ResUtil.getString(com.tbit.uqbike.R.string.cancel)).setRightText(getString(
                com.tbit.uqbike.R.string.s_opengo)).setCanceledOnOutside(true)
            .setClickListen(object : CommDialog.TwoSelDialog {
                override fun leftClick() {}
                override fun rightClick() {
                    val intent = Intent()
                    intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                    val uri = Uri.fromParts("package", App.context.getPackageName(), null)
                    intent.data = uri
                    startActivity(intent)
                }
            }).build().show()
    }

    override fun attachBaseContext(newBase: Context?) {
//        super.attachBaseContext(newBase)
        LanguageUtil().settingLanguage(ContextUtil.getContext())
        super.attachBaseContext(ContextUtil.getContext())
    }

    object ViewClickDelay {
        var hash: Int = 0
        var lastClickTime: Long = 0
        var SPACE_TIME: Long = 800
    }
    infix fun View.clickDelay(clickAction: () -> Unit) {
        this.setOnClickListener {
            if (this.hashCode() != ViewClickDelay.hash) {
                ViewClickDelay.hash = this.hashCode()
                ViewClickDelay.lastClickTime = System.currentTimeMillis()
                clickAction()
            } else {
                val currentTime = System.currentTimeMillis()
                if (currentTime - ViewClickDelay.lastClickTime > ViewClickDelay.SPACE_TIME) {
                    ViewClickDelay.lastClickTime = System.currentTimeMillis()
                    clickAction()
                }
            }
        }
    }
    override fun onResume() {
        super.onResume()
        if (this::class.simpleName.toString().contains("HomeActivity")){
            Glob.isHome = true
        }else{
            Glob.isHome = false
        }
        if (!(this::class.simpleName.toString().contains("SplashActivity"))){
            if (Glob.isLogin){
                if (SpUtil.Companion.getInstance().find(Constant.SpKey.SP_UUID).isNullOrEmpty()){
                    UserModel.getUser()
                        .subscribeBy(
                            onNext = {
                                val resultData: UserEntity = Gson().fromJson(it.toString(), UserEntity::class.java)
                                if (resultData != null){
                                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_UUID,resultData.uuid)
                                    if (FlavorConfig.NET.COM_URL.contains("api.gogoep.io") || FlavorConfig.NET.COM_URL.contains("api.gogoep.dev")){
                                        UPushAlias.add(ContextUtil.getContext(), resultData.uuid, "test-uuid")
                                    }else{
                                        UPushAlias.add(ContextUtil.getContext(), resultData.uuid, "uuid")
                                    }
                                }
                            },
                            onError = {
                            }
                        ).toCancelable()
                }
            }
        }
    }

    protected var SCPageType: String? = null
    protected var SCJsonObject: JSONObject? = null

    /**
     * 返回自定义属性集合
     * @params: null
     * @return: JSONObject
     */
    @Throws(JSONException::class)
    override fun getTrackProperties(): JSONObject? {
        if (SCJsonObject == null) SCJsonObject = JSONObject()
        SCJsonObject!!.put("tag", MDUtil.getComMD_Name((this::class.simpleName).toString(),webUrl,exchangeType))
        //上一层url
//        MyLogUtil.Log("1111","===getTrackPropertiesAct=="+this::class.simpleName+"--"+SensorsDataAPI.sharedInstance().lastScreenUrl+
//                "-weburl="+webUrl+",exchType="+exchangeType+"----"+MDUtil.getComMD_Name((this::class.simpleName).toString(),webUrl,exchangeType))
        if (this::class.simpleName.toString().contains("LoginActivity")){
            SCJsonObject!!.put("login_method", LoginType_Event)
        }
        if (this::class.simpleName.toString().contains("ScanForBorrowActivity")){
            SCJsonObject!!.put("vehicle_number", vehicle_number)
            if (Glob.isRental){
                SCJsonObject!!.put("business_type", 1)
            }else{
                SCJsonObject!!.put("business_type", 2)
            }
        }

        if (this::class.simpleName.toString().contains("RecordDetailActivity")){
            SCJsonObject!!.put("vehicle_number", vehicle_number)
            if (Glob.isRental){
                SCJsonObject!!.put("business_type", 1)
            }else{
                SCJsonObject!!.put("business_type", 2)
            }
        }
        if (this::class.simpleName.toString().contains("LanguageActivity")){
            SCJsonObject!!.put("choose_language", language_event)
        }
        if (this::class.simpleName.toString().contains("ExchangeActivity")){
            if (exchangeType.contains(ExchangeActivity.TYPE_RIDECARD.toString())){
                SCJsonObject!!.put("card_id", inputValue_event)
            }
            if (exchangeType.contains(ExchangeActivity.TYPE_EXTEND.toString())){
                SCJsonObject!!.put("promotional_code_id", inputValue_event)
            }
            if (exchangeType.contains(ExchangeActivity.TYPE_GIFTCARD.toString())){
                SCJsonObject!!.put("gift_card_redemption_id", inputValue_event)
            }
        }
        if (this::class.simpleName.toString().contains("ChargeNewActivity")){
            SCJsonObject!!.put("recharge_amount", inputValue_event)
        }

        if (this::class.simpleName.toString().contains("WalletActivity")){
            SCJsonObject!!.put("wallet_balance", wallbalance_event)
            SCJsonObject!!.put("gift_balance", giftbalance_event)
        }

        if (this::class.simpleName.toString().contains("WalletBalanceActivity")){
            SCJsonObject!!.put("wallet_balance", wallbalance_event)
        }

        if (this::class.simpleName.toString().contains("RefundActivity")){
            SCJsonObject!!.put("refund_amount", refund_amount_event)
        }

        if (this::class.simpleName.toString().contains("RecordDetailActivity")){
            SCJsonObject!!.put("cost", cost_event)
            SCJsonObject!!.put("time", time_event)
            SCJsonObject!!.put("order_no", order_no_event)
            SCJsonObject!!.put("business_type", business_type_event)//1=短租 2=长租
        }

        if (this::class.simpleName.toString().contains("CountrySelActivity")){
            SCJsonObject!!.put("country_id", country_id_event)
        }

        if (this::class.simpleName.toString().contains("InputForBorrowActivity")){
            SCJsonObject!!.put("vehicle_number", vehicle_number_event)
            if (Glob.isRental){
                SCJsonObject!!.put("business_type", 1)
            }else{
                SCJsonObject!!.put("business_type", 2)
            }
        }

        return SCJsonObject
    }
    //订单行程页
    var cost_event = ""
    var time_event = ""
    var order_no_event = ""
    var business_type_event = ""
    //其他
    var vehicle_number_event = ""
    var country_id_event = ""
    var refund_amount_event = ""
    var LoginType_Event = ""
    var webUrl = ""
    var exchangeType = ""
    var vehicle_number = ""
    var language_event = ""
    var inputValue_event = ""
    var wallbalance_event = ""
    var giftbalance_event = ""
    /**
     * 返回当前页面的Url,用作下个页面的referrer
     * @params: null
     * @return: String
     */
    override fun getScreenUrl(): String? {
        //当子类未设置SCPageType时，采用子类默认类名字段
//        if (SCPageType == null) SCPageType = javaClass.canonicalName
        if (javaClass.canonicalName != null){
            if (SCPageType == null) SCPageType = MDUtil.getComMD_Name(javaClass.canonicalName.toString(),webUrl,exchangeType)
        }else{
            SCPageType = ""
        }
        return SCPageType
    }

}