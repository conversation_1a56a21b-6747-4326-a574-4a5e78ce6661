package com.tbit.uqbike.base

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import com.sensorsdata.analytics.android.sdk.ScreenAutoTracker
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI
import com.tbit.maintenance.utils.ApolloLifecycleObserver
import com.tbit.maintenance.utils.LifecycleDialogHelper
import com.tbit.maintenance.utils.LoadingDialogHelper
import com.tbit.uqbike.R
import com.tbit.uqbike.utils.MyLogUtil
import org.jetbrains.anko.longToast
import org.jetbrains.anko.toast
import org.json.JSONException
import org.json.JSONObject

/**
 * Created by yankaibang on 2018/10/8.
 */
abstract class BaseFragment : Fragment()  {

    private val apolloLifecycleObserver = ApolloLifecycleObserver(this)

    protected val lifecycleDialogHelper by lazy { LifecycleDialogHelper(childFragmentManager) }

    protected val loadingDialogHelper by lazy {
        activity?.let { LoadingDialogHelper(it, getString(R.string.please_wait),false) }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycle.addObserver(apolloLifecycleObserver)
        lifecycle.addObserver(lifecycleDialogHelper)
    }

    inline fun Fragment.toast(textResource: Int) = activity?.toast(textResource)

    inline fun Fragment.toast(text: CharSequence) = activity?.toast(text)

    inline fun Fragment.longToast(textResource: Int) = activity?.longToast(textResource)

    inline fun Fragment.longToast(text: CharSequence) = activity?.longToast(text)

    object ViewClickDelay {
        var hash: Int = 0
        var lastClickTime: Long = 0
        var SPACE_TIME: Long = 800
    }
    infix fun View.clickDelay(clickAction: () -> Unit) {
        this.setOnClickListener {
            if (this.hashCode() != ViewClickDelay.hash) {
                ViewClickDelay.hash = this.hashCode()
                ViewClickDelay.lastClickTime = System.currentTimeMillis()
                clickAction()
            } else {
                val currentTime = System.currentTimeMillis()
                if (currentTime - ViewClickDelay.lastClickTime > ViewClickDelay.SPACE_TIME) {
                    ViewClickDelay.lastClickTime = System.currentTimeMillis()
                    clickAction()
                }
            }
        }
    }

//    protected var SCPageType: String? = null
//    protected var SCJsonObject: JSONObject? = null
    /**
     * 返回自定义属性集合
     * @params: null
     * @return: JSONObject
     */
//    @Throws(JSONException::class)
//    override fun getTrackProperties(): JSONObject? {
//        if (SCJsonObject == null) SCJsonObject = JSONObject()
//        SCJsonObject!!.put("tag", "home")
//        //上一层url
//        MyLogUtil.Log("1111","===getTrackPropertiesFragment=="+this::class.simpleName+"--"+ SensorsDataAPI.sharedInstance().lastScreenUrl)
////        SCJsonObject!!.put("referrer_page_type", SensorsDataAPI.sharedInstance().lastScreenUrl)
//        return SCJsonObject
//    }

    /**
     * 返回当前页面的Url,用作下个页面的referrer
     * @params: null
     * @return: String
     */
//    override fun getScreenUrl(): String? {
//        //当子类未设置SCPageType时，采用子类默认类名字段
//        if (SCPageType == null) SCPageType = javaClass.canonicalName
//        return SCPageType
//    }
}