package com.tbit.uqbike.base

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent

/**
 * Created by WG on 08/04/2018.
 * Email: <EMAIL>
 * Github: https://github.com/WGwangguan
 * Desc:
 */
interface BasePresenter<T>: LifecycleObserver {
    val view: T
    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE) fun onCreate()
    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY) fun onDestroy()
}