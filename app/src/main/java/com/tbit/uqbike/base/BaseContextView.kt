package com.tbit.uqbike.base

import android.content.Context
import android.content.Intent
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import io.reactivex.Observable

/**
 * Created by yankaibang on 2018/8/2.
 */
interface BaseContextView : BaseView {

    fun getContext(): Context?

    fun showDialog(dialogFragment: DialogFragment)

    fun findFragmentById(id: Int): Fragment?

    fun startActivityForObservable(requestCode: Int, intent: Intent?): Observable<Pair<Int, Intent?>>
}