package com.tbit.uqbike

import android.app.ActivityManager
import android.content.Context
import android.os.Looper
import android.os.Process
import android.text.TextUtils
import android.webkit.WebView
import android.widget.Toast
import androidx.annotation.Keep
import androidx.multidex.MultiDexApplication
import com.doule.database.CoroutinesUtil
import com.facebook.stetho.Stetho
import com.lsxiao.apollo.core.Apollo
import com.sensorsdata.analytics.android.sdk.SAConfigOptions
import com.sensorsdata.analytics.android.sdk.SensorsAnalyticsAutoTrackEventType
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI
import com.sensorsdata.analytics.android.sdk.core.business.exposure.SAExposureConfig
import com.sensorsdata.analytics.android.sdk.util.SensorsDataUtils
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.preview.GlideImageLoader
import com.tbit.preview.ImageConfig
import com.tbit.tbitblesdk.Bike.TbitBle
import com.tbit.uqbike.ble.SecretProtocolAdapter
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.mvp.model.ExtConfigModel
import com.tbit.uqbike.mvp.model.OnePassLoginModel
import com.tbit.uqbike.mvp.model.TrackEventModel
import com.tbit.uqbike.push.helper.PushHelper
import com.tbit.uqbike.push.helper.PushHelper.registerDevicePush
import com.tbit.uqbike.utils.AdUtil
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.ThirdAdInit
import com.umeng.analytics.MobclickAgent
import com.umeng.commonsdk.UMConfigure
import com.umeng.message.PushAgent
import com.umeng.message.api.UPushRegisterCallback
import com.umeng.message.common.UPushNotificationChannel
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.plugins.RxJavaPlugins
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import me.jessyan.autosize.AutoSizeConfig
import java.util.TimeZone


/**
 * Created by yankaibang on 2018/10/8.
 */
@Keep
class App : MultiDexApplication() {
    companion object {
        lateinit var appOpenAdManager: AdUtil.AppOpenAdManager
        lateinit var context: Context
        var SA_SERVER_URL = "https://tracking.gogoep.io/receiver/api/rv?project=gogo-app&token=5388ed7459ba4c4cad0c8693fb85630b"
        fun initThird(){
            if (!(FlavorConfig.NET.COM_URL.contains("api.gogoep.io") || FlavorConfig.NET.COM_URL.contains("api.gogoep.dev"))){
                SA_SERVER_URL = "https://tracking.gogoep.com/receiver/api/rv?project=gogo-app&token=5388ed7459ba4c4cad0c8693fb85630b"
            }
            TbitBle.initialize(ContextUtil.getContext(), SecretProtocolAdapter())
            Stetho.initializeWithDefaults(context)
            OnePassLoginModel.init()
//            checkGoogleNetAvailable()
            UMConfigure.setLogEnabled(true)
            UMConfigure.init(ContextUtil.getContext(),ContextUtil.getContext().getString(R.string.umeng_appkey),"google",
                UMConfigure.DEVICE_TYPE_PHONE,ContextUtil.getContext().getString(R.string.umeng_push_secret))
            MobclickAgent.setPageCollectionMode(MobclickAgent.PageMode.AUTO);

            CoroutinesUtil.launchIO {
                UPushNotificationChannel.getDefaultMode(ContextUtil.getContext())
//                UPushNotificationChannel.getSilenceMode(ContextUtil.getContext())

                var api = PushAgent.getInstance(ContextUtil.getContext())
                // 推送设置
                PushHelper.setting(api)
                // 推送注册
                api.register(object : UPushRegisterCallback {
                    override fun onSuccess(deviceToken: String) {
                        MyLogUtil.Log("umeng", "deviceToken: $deviceToken")
                        //注册厂商通道
                        registerDevicePush(ContextUtil.getContext())
                    }

                    override fun onFailure(errCode: String, errDesc: String) {
                        MyLogUtil.Log("umeng", "register failed! code:$errCode,desc:$errDesc")
                    }
                })
//                AppUtil.determineAdvertisingInfo()
                ThirdAdInit.PangleInit(ContextUtil.getContext())
                ThirdAdInit.MetaInit(ContextUtil.getContext())
//
                val backgroundScope = CoroutineScope(Dispatchers.IO)
                backgroundScope.launch {
                    // Initialize the Google Mobile Ads SDK on a background thread.
                    ThirdAdInit.AdMobInit(ContextUtil.getContext())
                    appOpenAdManager = AdUtil.AppOpenAdManager()
                }
            }
            //初始化 Sensors Analytics SDK
            val configOptions = SAConfigOptions(SA_SERVER_URL)
            // 打开自动采集, 并指定追踪哪些 AutoTrack 事件 //or SensorsAnalyticsAutoTrackEventType.APP_CLICK
            configOptions.setAutoTrackEventType(
                SensorsAnalyticsAutoTrackEventType.APP_START or SensorsAnalyticsAutoTrackEventType.APP_END or
                        SensorsAnalyticsAutoTrackEventType.APP_VIEW_SCREEN)
            // 打开 crash 信息采集
            configOptions.enableTrackAppCrash()
            configOptions.enableTrackPageLeave(true,false)
            configOptions.enableJavaScriptBridge(true)
            configOptions.enableSession(true)
            configOptions.eventSessionTimeout = 1800

            SensorsDataUtils.enableAndroidId(true)
            SensorsDataUtils.enableOAID(true)

            val exposureConfig = SAExposureConfig(1.0f, 1.0, true)
            configOptions.setExposureConfig(exposureConfig)
            //传入 SAConfigOptions 对象，初始化神策 SDK
            SensorsDataAPI.startWithConfigOptions(ContextUtil.getContext(), configOptions)
//            if (Glob.isLogin && SpUtil.Companion.getInstance().find(Constant.SpKey.SP_LOGIN_UUID).isNullOrEmpty()){
//                if (!SpUtil.Companion.getInstance().find(Constant.SpKey.SP_UUID).isNullOrEmpty()){
////                    MyToastUtil.toast("login"+SpUtil.Companion.getInstance().find(Constant.SpKey.SP_UUID))
//                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_LOGIN_UUID,"1")
//                    SensorsDataAPI.sharedInstance().login(SpUtil.Companion.getInstance().find(Constant.SpKey.SP_UUID))
//                }
//            }
            if (SensorsDataAPI.sharedInstance().loginId.isNullOrEmpty()){
                if (!SpUtil.Companion.getInstance().find(Constant.SpKey.SP_UUID).isNullOrEmpty()){
                    SensorsDataAPI.sharedInstance().login(SpUtil.Companion.getInstance().find(Constant.SpKey.SP_UUID))
                }
            }
            MDUtil.setComParm()
        }
        private fun checkGoogleNetAvailable_bynative() {
            if (Glob.isGoogleServiceAvailable) {
                MyLogUtil.Log("1111","=====google 可用===")
                ExtConfigModel.checkGoogleNetAvailable()
                    .subscribeBy(
                        onComplete = {
                            if (Glob.isGoogleNetAvailable == null){
                                Glob.isGoogleNetAvailable = true
                            }
                            if (TimeZone.getDefault().toString().contains("Asia/Shanghai")){
                                Glob.isGoogleNetAvailable = false
                            }
                            MyLogUtil.Log("1111","=========Glob.isGoogleNetAvailable = true")
                            Apollo.emit(Constant.Event.CHECK_GOOGLE_NET_RESULT)
                        },
                        onError = {
//                            Glob.isGoogleNetAvailable = false
                            Glob.isGoogleNetAvailable = true
//                            if (TimeZone.getDefault().toString().contains("Asia/Shanghai")){
//                                Glob.isGoogleNetAvailable = false
//                            }
                            MyLogUtil.Log("1111","=========Glob.isGoogleNetAvailable = false")
                            Apollo.emit(Constant.Event.CHECK_GOOGLE_NET_RESULT)
                        }
                    )
            }else{
                MyLogUtil.Log("1111","=====google 不可用===")
            }
        }


    }
    override fun onCreate() {
        super.onCreate()
        if(!isMainProcess()) return
//        LanguageUtil().settingLanguage(this)
        context = applicationContext
        val webView = WebView(context)
        webView.settings.javaScriptEnabled = true
        ContextUtil.init(context)
        LanguageUtil().settingLanguage(ContextUtil.getContext())
        // 环境切换逻辑：根据SP_TESTNET标志动态设置环境
        val isTestEnv = !SpUtil.Companion.getInstance().find(Constant.SpKey.SP_TESTNET).isNullOrEmpty()
        MyLogUtil.Log("ENV_SWITCH", "当前环境切换标志 SP_TESTNET: ${if (isTestEnv) "测试环境" else "生产环境"}")

        if (isTestEnv) {
            // 测试环境配置
            if (!FlavorConfig.NET.COM_URL.contains("api.gogoep.dev")){
                FlavorConfig.NET.USER_URL = "https://my.gogoep.io/"
                FlavorConfig.NET.COM_URL = "https://api.gogoep.io/"
                FlavorConfig.NET.EVENT_URL = "https://ci.gogoep.io/"
                FlavorConfig.NET.H5_URL = "https://h5.gogoep.io/"
                MyLogUtil.Log("ENV_SWITCH", "设置API环境为测试环境: ${FlavorConfig.NET.COM_URL}")
            }
            // 设置H5推荐区域为测试环境
            FlavorConfig.NET.GOSHOP_WEB_HOST = "https://www.gogo-shop.tech"
            MyLogUtil.Log("ENV_SWITCH", "设置H5推荐区域为测试环境: ${FlavorConfig.NET.GOSHOP_WEB_HOST}")
        } else {
            // 生产环境配置
            FlavorConfig.NET.GOSHOP_WEB_HOST = "https://www.gogo-shop.com"
            MyLogUtil.Log("ENV_SWITCH", "设置H5推荐区域为生产环境: ${FlavorConfig.NET.GOSHOP_WEB_HOST}")
        }
        //屏幕适配AutoSize框架初始化
        AutoSizeConfig.getInstance().setCustomFragment(true)
        ImageConfig.setImageLoader(GlideImageLoader())
        RxJavaPlugins.setErrorHandler { it.printStackTrace() }
        Apollo.init(AndroidSchedulers.mainThread(), this)
        Apollo.bind(TrackEventModel)
        checkGoogleNetAvailable_bynative()
        SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_AUTH,"1")
        if(!SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
            initThird()
        }

        Thread.setDefaultUncaughtExceptionHandler { t, e ->
            if (t === Looper.getMainLooper().thread) {
//                Toast.makeText(this@App, "主线程出现错误：" + e.message, Toast.LENGTH_SHORT).show()
                while (true) {
                    try {
                        Looper.loop()
                    } catch (e1: Throwable) {
//                        Toast.makeText(this@App, "主线程出现错误：" + e.message, Toast.LENGTH_SHORT).show()
                    }
                }
            } else {
                CoroutinesUtil.launchMain {
//                    Toast.makeText(this@App, "系统异常：" + e.message, Toast.LENGTH_SHORT).show()
                    Toast.makeText(this@App, getString(R.string.s_err_sys), Toast.LENGTH_SHORT).show()
                }
            }
        }
//        MyLogUtil.Log("1111","=======getQuDao()======="+getQuDao())
    }

    private fun isMainProcess(): Boolean {
        val pid = Process.myPid()
        var processNameString = ""
        val m = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        for (appProcess in m.runningAppProcesses) {
            if (appProcess.pid == pid) {
                processNameString = appProcess.processName
            }
        }
        return TextUtils.equals(BuildConfig.APPLICATION_ID, processNameString)
    }

}
