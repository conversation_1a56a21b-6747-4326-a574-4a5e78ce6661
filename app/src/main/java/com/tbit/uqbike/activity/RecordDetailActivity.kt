package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.widget.CheckedTextView
import android.widget.LinearLayout
import androidx.annotation.RequiresApi
import com.baidu.ar.it
import com.google.gson.Gson
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.listener.OnMapLoadedCallback
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.Track
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.databinding.ActivityRecordDetailBinding
import com.tbit.uqbike.delegate.PolylineMapDelegate
import com.tbit.uqbike.dialog.RecordDetailDialog
import com.tbit.uqbike.dialog.RecordDetailNewDialog
import com.tbit.uqbike.entity.EnoughData
import com.tbit.uqbike.entity.ExchangePayStateData
import com.tbit.uqbike.entity.FormoptionData
import com.tbit.uqbike.entity.FormoptionDataItem
import com.tbit.uqbike.entity.OrderInfoRentalData
import com.tbit.uqbike.entity.ReportNumData
import com.tbit.uqbike.entity.RidingOrderInfoData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.map.bean.Location
import com.tbit.uqbike.mvp.constract.RecordDetailContract
import com.tbit.uqbike.mvp.model.FeedbackModel
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.presenter.RecordDetailPresenter
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.OrderModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.resqmodel.PayModel
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.NetUtils
import com.tbit.uqbike.utils.PointUtil
import com.tbit.uqbike.utils.UrlDecodeUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.toast
import org.json.JSONObject
import java.util.*

class RecordDetailActivity: BaseActivity(), RecordDetailContract.View, OnMapLoadedCallback{

    companion object {
        const val REQUEST_TYPE = 4
        private const val EXTRA_RECORD = "EXTRA_RECORD"
        private const val EXTRA_USEREND = "EXTRA_USEREND"
        private const val EXTRA_ISRENTAL = "EXTRA_ISRENTAL"
        fun createIntent(context: Context, orderNo : String,isUserEnd : Boolean,isRental : Boolean): Intent {
            return context.intentFor<RecordDetailActivity>(
                EXTRA_RECORD to orderNo,EXTRA_USEREND to isUserEnd,EXTRA_ISRENTAL to isRental
            )
        }
    }

    private val presenter = RecordDetailPresenter(this)
    private val orderNo: String by bindExtra(EXTRA_RECORD)
    private val isUserEnd: Boolean by bindExtra(EXTRA_USEREND)//是否用户手动结束
    private val isRental: Boolean by bindExtra(EXTRA_ISRENTAL)//是否长租

    private var map: IBaseMap? = null
    private var polylineMapDelegate =  PolylineMapDelegate()
    var ridingOrderInfoData: OrderInfoRentalData ?= null
    //    private val recordDetailDialog = RecordDetailDialog()
    private var backType = 1 //0 直接返回，1 钱包支付，2 充值支付
    private var PayMoney = 0f//需要支付的钱
    private val FeedList = mutableListOf<FormoptionDataItem>()
    private val FeedListSel = mutableListOf<FormoptionDataItem>()
    private lateinit var binding: ActivityRecordDetailBinding
    var payingState = 0 //0 未支付，1 已支付，2 支付中超过2秒时间
    var orderState = 0//订单状态

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRecordDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)

//        setContentView(R.layout.activity_record_detail)
        EventBus.getDefault().register(this);
        setSupportActionBar(binding.toolbars.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        binding.toolbars.toolbarTitle.text = getString(R.string.record_detail_title)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { onBackPressed() }

        lifecycle.addObserver(presenter)

        val map = FlavorConfig.mapFactory.createMap(binding.flMap, savedInstanceState)
        this.map = map
        map.setOnMapLoadedCallback(this@RecordDetailActivity)
        polylineMapDelegate.setMap(map)
        loadingDialogHelper.show { false }
        getOrderInfo()
        binding.layoutRecordinfo.lyRecordinfoMoney.clickDelay {
            if(ridingOrderInfoData == null){
                return@clickDelay
            }
            if (isRental){
//                            var recordDetailDialogNew = RecordDetailNewDialog("https://www.baidu.com/")
                var recordDetailDialogNew = RecordDetailNewDialog(FlavorConfig.NET.H5_URL+"order?order_no="+orderNo+
                        "&locale="+FlavorConfig.Local.language+"&currency="+Glob.CurrencyUnit)
                lifecycleDialogHelper.show(recordDetailDialogNew)
                recordDetailDialogNew.onDissListener = {
                    getOrderInfo()
                }
            }else{
                val recordDetailDialog = RecordDetailDialog(loadingDialogHelper)
                recordDetailDialog.setData(ridingOrderInfoData!!)
                lifecycleDialogHelper.show(recordDetailDialog)
                recordDetailDialog.onBtnListener = {
                    binding.layoutRecordinfo.btnRidingOk.postDelayed({binding.layoutRecordinfo.btnRidingOk.performClick()},200)
                }
                recordDetailDialog.onDissListener={
                    getOrderInfo()
                }
            }
        }

        binding.layoutRecordinfo.btnRidingOk.clickDelay {
            //0 直接返回，1 钱包支付，2 充值支付
            when (backType) {
                0 -> {
                    val properties = JSONObject()
                    MDUtil.clickEvent("return_click",properties)
                    finish()
                }
                1 -> {
                    payingDialogHelper.show {  }
                    payingState = 0 //0 未支付，1 已支付，2 支付中超过2秒时间

                    if (isRental){
                        PayModel.payRidingByRental(orderNo).subscribeBy(
                            onNext = {
                                MyLogUtil.Log("1111","===骑行支付 信息=="+it.toString())
                                payDown()
                            },
                            onError = { try {
                                getOrderInfo()
                                payingDialogHelper.dismiss()
                            }catch (e : IllegalArgumentException){}}
                        ).toCancelable()
                    }else{
                        PayModel.payRiding(orderNo).subscribeBy(
                            onNext = {
                                MyLogUtil.Log("1111","===骑行支付 信息=="+it.toString())
//                                                var chargeData = Gson().fromJson(it.toString(), ChargeData::class.java)
                                OrderModel.getRidingOrderInfo(orderNo).subscribeBy(
                                    onNext = {
                                        MyLogUtil.Log("1111","===获取 骑行订单信息=="+it.toString())
                                        ridingOrderInfoData = Gson().fromJson(it.toString(), OrderInfoRentalData::class.java)
                                        if(ridingOrderInfoData?.is_short_order == 1){
                                            if (payingState != null && payingState == 2){
                                                showShortOrder()
                                            }else{
                                                binding.layoutRecordinfo.tvRidingNo.postDelayed({
                                                    showShortOrder()
                                                },1500)
                                            }
                                        }
                                    }
                                ).toCancelable()
                                payDown()
                            },
                            onError = { try {
                                getOrderInfo()
                                payingDialogHelper.dismiss()
                            }catch (e : IllegalArgumentException){}}
                        ).toCancelable()
                    }

                    binding.layoutRecordinfo.tvRidingNo.postDelayed({
                        if (payingState != null && payingState == 1) {
                            payingDialogHelper.dismiss()
                            MyToastUtil.toast(getString(R.string.errcode_success))
                        }
                        payingState = 2
                    },2000)
                }
                2 -> {
                    startActivity(ChargeNewActivity.createIntent(this, PayMoney))
                    val properties = JSONObject()
                    MDUtil.clickEvent("recharge_payment_click",properties)
                }
            }
        }
        binding.layoutRecordinfo.rvOrderrecordFeedback.clickDelay {
            loadingDialogHelper.show {  }
            ComModel.getReportNum(ComModel.Report_Fault,orderNo,ridingOrderInfoData!!.vehicle_no).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===用户反馈统计 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), ReportNumData::class.java)
                    if (resultData != null && resultData.num == 0){
                        startActivity(FeedbackFaultActivity.createIntent(this, ridingOrderInfoData!!.vehicle_no,orderNo))
                    }else{
                        MyToastUtil.toast(getString(R.string.s_sumit_suc))
                    }
                },
                onError = {loadingDialogHelper.dismiss()}
            ).toCancelable()
        }
        binding.layoutRecordinfo.rvOrderrecordCostappeal.clickDelay {
            loadingDialogHelper.show {  }

            val properties = JSONObject()
            MDUtil.clickEvent("expense_appeal_click",properties)

            ComModel.getReportNum(ComModel.Report_Cost,orderNo,ridingOrderInfoData!!.vehicle_no).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===用户反馈统计 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), ReportNumData::class.java)
                    if (resultData != null && resultData.num == 0){
                        startActivity(CostAppealActivity.createIntent(this, ridingOrderInfoData!!.vehicle_no,orderNo))
                    }else{
                        MyToastUtil.toast(getString(R.string.s_sumit_suc))
                    }
                },
                onError = {loadingDialogHelper.dismiss()}
            ).toCancelable()
        }

        binding.layoutShortrecord.btnShortrecordN.clickDelay {
            binding.layoutRecordinfo.lyLayoutRecordinfo.visibility = View.VISIBLE
            binding.layoutShortrecord.lyLayoutShortrecord.visibility = View.GONE
        }
        binding.layoutShortrecord.btnShortrecordSumit.clickDelay {
            if (FeedListSel.size > 0){
                val listType = ArrayList<Int>()
                FeedListSel.forEach {
                    listType.add(it.id.toInt())
                }
                val listImg = ArrayList<String>()
                loadingDialogHelper.show {  }
                FeedbackModel.costAppeal(REQUEST_TYPE,listType,ridingOrderInfoData!!.vehicle_no, "", orderNo, listImg)
                    .subscribeBy(
                        onComplete = {
                            loadingDialogHelper.dismiss()
                            MyToastUtil.toast(getString(R.string.thanks_for_feedback))
                            binding.layoutRecordinfo.lyLayoutRecordinfo.visibility = View.VISIBLE
                            binding.layoutShortrecord.lyLayoutShortrecord.visibility = View.GONE
                        },
                        onError = {loadingDialogHelper.dismiss()}
                    ).toCancelable()
            }else{
                MyToastUtil.toast(getString(R.string.s_ques_sel))
            }
        }

        binding.layoutRecordinfo.lyBillrule.clickDelay {
            loadingDialogHelper.show {  }

            val properties = JSONObject()
            MDUtil.clickEvent("travel_billing_rules_click",properties)

            PageModel.getPageUrl(PageModel.billing_rules).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                    startActivity<WebActivity>(WebActivity.TITLE to getString(R.string.billing_rules),
                        WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
                },
                onError = {loadingDialogHelper.dismiss()}
            ).toCancelable()
        }

//        view_travel_consumption_group.setOnClickListener { onRemarkClick() }
//        text_helper.setOnClickListener { lifecycleDialogHelper.show(helperDialog) }
        AutoTask()
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun AutoTask(){
        if (binding.layoutRecordinfo.btnRidingOk != null){
            binding.layoutRecordinfo.btnRidingOk.postDelayed({
                if (binding.layoutRecordinfo.btnRidingOk != null){
                    //订单状态：1进行中,2待支付，3已支付
                    if (orderState != 3){
                        AutoTask()
                        getOrderInfo(true)
                    }
                }
            },5000L)
        }
    }
    override fun onMapLoaded() {
    }
    @RequiresApi(Build.VERSION_CODES.O)
    fun payDown(){
        if (payingState != null && payingState == 2) {
            payingDialogHelper.dismiss()
            MyToastUtil.toast(getString(R.string.errcode_success))
        }
        binding.layoutRecordinfo.tvRecordinfoCast.text = getString(R.string.s_cast)+"("+Glob.CurrencyUnit+")"
        payingState = 1
        EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_RIDINGRECORD,""));
        backType = 0
        binding.layoutRecordinfo.btnRidingOk.text = getString(R.string.back)

        ridingOrderInfoData?.status = 3
        if (isRental){
            binding.layoutRecordinfo.rvOrderrecordState.text = getString(R.string.s_paycomp)
        }else{
            binding.layoutRecordinfo.rvOrderrecordState.text = getString(R.string.s_order_comp)
        }
        binding.layoutRecordinfo.rvOrderrecordState.delegate.backgroundColor= resources.getColor(R.color.c_grey)
        binding.layoutRecordinfo.rvOrderrecordState.setTextColor(resources.getColor(R.color.c_838588))

        binding.layoutRecordinfo.lyOrderfinsh1.visibility = View.VISIBLE
        binding.layoutRecordinfo.lyOrderfinsh2.visibility = View.VISIBLE
        binding.layoutRecordinfo.lyOrderfinshline1.visibility = View.VISIBLE
        binding.layoutRecordinfo.lyOrderfinshline2.visibility = View.VISIBLE
        getOrderInfo()
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onRestart() {
        super.onRestart()
        getOrderInfo()
    }
    @RequiresApi(Build.VERSION_CODES.O)
    private fun getOrderInfo(isAutoTask : Boolean = false){
        if (binding.layoutRecordinfo.btnRidingOk != null){
            if (orderNo.isNullOrEmpty()) return
            if (isRental){
                OrderModel.getRidingOrderInfoByRental(orderNo)
                    .subscribeBy(
                        onNext = {
                            loadingDialogHelper.dismiss()
                            MyLogUtil.Log("1111","===获取 骑行订单信息=="+isAutoTask+"-"+ it.toString())
                            ridingOrderInfoData = Gson().fromJson(it.toString(), OrderInfoRentalData::class.java)
                            setData(isAutoTask)
                            binding.capaLayout.toContent()
                        }, onError = {
                            loadingDialogHelper.dismiss()
                            setErrorView(true)
                        }
                    ).toCancelable()
            }else{
                OrderModel.getRidingOrderInfo(orderNo)
                    .subscribeBy(
                        onNext = {
                            loadingDialogHelper.dismiss()
                            MyLogUtil.Log("1111","===获取 骑行订单信息=="+isAutoTask+"-"+it.toString())
                            ridingOrderInfoData = Gson().fromJson(it.toString(), OrderInfoRentalData::class.java)
                            setData(isAutoTask)
                            binding.capaLayout.toContent()
                        }, onError = {
                            loadingDialogHelper.dismiss()
                            setErrorView(true)
                        }
                    ).toCancelable()
            }
        }
    }

    fun setErrorView(isNetError : Boolean){
//        MyLogUtil.Log("1111","======isNetError======"+isNetError)
        if(isNetError){
            if (NetUtils.isConnected(ContextUtil.getContext())){
//                binding.capaLayout.toEmpty()
                return
            }
        }
        if (isNetError){
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_net).visibility = View.VISIBLE
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_local).visibility = View.GONE
        }else{
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_net).visibility = View.GONE
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_local).visibility = View.VISIBLE
        }
        binding.capaLayout.toError()
        binding.capaLayout.findViewById<RoundTextView>(R.id.tv_err_set_net).setOnClickListener{
            val intent = Intent(Settings.ACTION_DATA_ROAMING_SETTINGS)
            startActivity(intent)
        }
        binding.capaLayout.findViewById<RoundTextView>(R.id.tv_err_set_local).setOnClickListener{
            val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
            startActivityForResult(intent, MainActivity.REQUEST_OPEN_GPS)
        }
    }
    @RequiresApi(Build.VERSION_CODES.O)
    fun setData(isAutoTask : Boolean){
        order_no_event = ridingOrderInfoData!!.order_no

        vehicle_number = ridingOrderInfoData!!.vehicle_no
        binding.layoutRecordinfo.tvRidingNo.text = "No: "+ridingOrderInfoData!!.vehicle_no
        orderState = ridingOrderInfoData!!.status
        if (isRental){
            business_type_event = "2"
            binding.layoutRecordinfo.tvRidingTime.text = "-"
            time_event = ""
            binding.layoutRecordinfo.tvRidingLength.text = "-"
            binding.layoutRecordinfo.tvRidingBatterylife.text = AppUtil.getFloat2(ridingOrderInfoData!!.extra_pay_amount)
            cost_event = AppUtil.getFloat2(ridingOrderInfoData!!.extra_pay_amount)
            if (ridingOrderInfoData!!.status == 2){
                binding.layoutRecordinfo.tvRidingDate.visibility = View.VISIBLE
                binding.layoutRecordinfo.tvRidingDate.text = TimeFormatUtil.transToStringBysq(ridingOrderInfoData!!.finish_time.toLong())
                binding.layoutRecordinfo.tvRidingBatterylife.text = AppUtil.getFloat2(ridingOrderInfoData!!.extra_pay_amount)
                cost_event = AppUtil.getFloat2(ridingOrderInfoData!!.extra_pay_amount)
                binding.layoutRecordinfo.tvRecordinfoCast.text = getString(R.string.s_upay_state)+"("+Glob.CurrencyUnit+")"
            }else{
                binding.layoutRecordinfo.tvRecordinfoCast.text = getString(R.string.s_cast)+"("+Glob.CurrencyUnit+")"
                if (ridingOrderInfoData!!.status == 1){
                    binding.layoutRecordinfo.tvRidingDate.visibility = View.GONE
                    binding.layoutRecordinfo.tvRidingBatterylife.text = "-"
                    cost_event = ""
                }else{
                    binding.layoutRecordinfo.tvRidingDate.visibility = View.VISIBLE
                    binding.layoutRecordinfo.tvRidingDate.text = TimeFormatUtil.transToStringBysq(ridingOrderInfoData!!.finish_time.toLong())
                    binding.layoutRecordinfo.tvRidingBatterylife.text = AppUtil.getFloat2(ridingOrderInfoData!!.amount).toString()
                    cost_event = AppUtil.getFloat2(ridingOrderInfoData!!.amount).toString()
                }
            }
        }else{
            business_type_event = "1"
            binding.layoutRecordinfo.tvRidingDate.text = TimeFormatUtil.transToStringBysq(ridingOrderInfoData!!.finish_time.toLong())
            binding.layoutRecordinfo.tvRidingLength.text = ridingOrderInfoData!!.mileage.toString()
            binding.layoutRecordinfo.tvRidingTime.text = ridingOrderInfoData!!.order_time.toString()
            time_event = ridingOrderInfoData!!.order_time.toString()
            if (ridingOrderInfoData!!.status == 2){
                cost_event = AppUtil.getFloat2(ridingOrderInfoData!!.order_amount + ridingOrderInfoData!!.dispatch_amount+
                        ridingOrderInfoData!!.balance_deduction+ridingOrderInfoData!!.present_deduction+ridingOrderInfoData!!.ride_card_deduction+ridingOrderInfoData!!.coupon_deduction).toString()
                binding.layoutRecordinfo.tvRidingBatterylife.text = AppUtil.getFloat2(ridingOrderInfoData!!.order_amount + ridingOrderInfoData!!.dispatch_amount+
                        ridingOrderInfoData!!.balance_deduction+ridingOrderInfoData!!.present_deduction+ridingOrderInfoData!!.ride_card_deduction+ridingOrderInfoData!!.coupon_deduction).toString()
                binding.layoutRecordinfo.tvRecordinfoCast.text = getString(R.string.s_upay_state)+"("+Glob.CurrencyUnit+")"
            }else{
                cost_event =AppUtil.getFloat2(ridingOrderInfoData!!.order_amount +
                        ridingOrderInfoData!!.dispatch_amount+ridingOrderInfoData!!.ride_card_deduction).toString()
                binding.layoutRecordinfo.tvRidingBatterylife.text = AppUtil.getFloat2(ridingOrderInfoData!!.order_amount +
                        ridingOrderInfoData!!.dispatch_amount+ridingOrderInfoData!!.ride_card_deduction).toString()
                binding.layoutRecordinfo.tvRecordinfoCast.text = getString(R.string.s_cast)+"("+Glob.CurrencyUnit+")"
            }
        }

        //订单状态：1进行中,2待支付，3已支付
        when (ridingOrderInfoData!!.status) {
            1 -> {
                if (isRental){
                    binding.layoutRecordinfo.rvOrderrecordState.text = getString(R.string.s_rentaling)
                    binding.layoutRecordinfo.rvOrderrecordState.delegate.backgroundColor= resources.getColor(R.color.blue_namal10)
                    binding.layoutRecordinfo.rvOrderrecordState.setTextColor(resources.getColor(R.color.blue_namal))
                }else{
                    binding.layoutRecordinfo.rvOrderrecordState.text = getString(R.string.riding)
                }
                binding.layoutRecordinfo.btnRidingOk.text = getString(R.string.back)
                backType = 0
            }
            2 -> {
                binding.layoutRecordinfo.rvOrderrecordState.text = getString(R.string.s_order_npay)
                binding.layoutRecordinfo.rvOrderrecordState.delegate.backgroundColor= resources.getColor(R.color.blue_namal10)
                binding.layoutRecordinfo.rvOrderrecordState.setTextColor(resources.getColor(R.color.blue_namal))
                OrderModel.getMyWallet(orderNo)
                    .subscribeBy(
                        onNext = {
                            MyLogUtil.Log("1111","=== 余额是否足够 信息=="+it.toString())
                            var resultData = Gson().fromJson(it.toString(), EnoughData::class.java)
                            if(resultData.is_enough == 1){
                                backType = 1
                                binding.layoutRecordinfo.btnRidingOk.text = getString(R.string.pay)
                                if (isUserEnd && !isAutoTask){
                                    loadingDialogHelper.show {  }
                                    binding.layoutRecordinfo.btnRidingOk.postDelayed({
                                        loadingDialogHelper.dismiss()
                                        binding.layoutRecordinfo.btnRidingOk.performClick()
                                    },800)
                                }
                            }else{
                                backType = 2
                                binding.layoutRecordinfo.btnRidingOk.text = getString(R.string.s_pay)
                                PayMoney = resultData.amount - resultData.balance
                            }
                        }
                        , onError = {
                            binding.layoutRecordinfo.btnRidingOk.text = getString(R.string.back)
                        }
                    ).toCancelable()
            }
            3 -> {
                if (isRental){
                    binding.layoutRecordinfo.rvOrderrecordState.text = getString(R.string.s_paycomp)
                }else{
                    binding.layoutRecordinfo.rvOrderrecordState.text = getString(R.string.s_order_comp)
                }
                binding.layoutRecordinfo.rvOrderrecordState.delegate.backgroundColor= resources.getColor(R.color.c_grey)
                binding.layoutRecordinfo.rvOrderrecordState.setTextColor(resources.getColor(R.color.c_838588))
                binding.layoutRecordinfo.btnRidingOk.text = getString(R.string.back)
                backType = 0
                binding.layoutRecordinfo.lyOrderfinsh1.visibility = View.VISIBLE
                binding.layoutRecordinfo.lyOrderfinsh2.visibility = View.VISIBLE
                binding.layoutRecordinfo.lyOrderfinshline1.visibility = View.VISIBLE
                binding.layoutRecordinfo.lyOrderfinshline2.visibility = View.VISIBLE
            }
            5 -> {
                binding.layoutRecordinfo.rvOrderrecordState.text = getString(R.string.s_order_comp)
                binding.layoutRecordinfo.rvOrderrecordState.delegate.backgroundColor= resources.getColor(R.color.c_grey)
                binding.layoutRecordinfo.rvOrderrecordState.setTextColor(resources.getColor(R.color.c_838588))
                binding.layoutRecordinfo.btnRidingOk.text = getString(R.string.back)
                backType = 0
                binding.layoutRecordinfo.lyOrderfinsh1.visibility = View.VISIBLE
                binding.layoutRecordinfo.lyOrderfinsh2.visibility = View.VISIBLE
                binding.layoutRecordinfo.lyOrderfinshline1.visibility = View.VISIBLE
                binding.layoutRecordinfo.lyOrderfinshline2.visibility = View.VISIBLE
            }
        }

        if (isRental){
            if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
                var pointDataNew = PointUtil.latLngTranfrom(ridingOrderInfoData!!.mileage_point)
                polylineMapDelegate.setPoints(pointDataNew,isRental)
            }else{
                polylineMapDelegate.setPoints(ridingOrderInfoData!!.mileage_point,isRental)
            }
            binding.layoutRecordinfo.tvRidingNo.postDelayed({
                polylineMapDelegate.fitMapView()
            },500)
        }else{
            if (ridingOrderInfoData!!.mileage_point.size < 2) {
                toast(R.string.no_locating_points)
                showCurrentLocate()
            } else {
                if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
                    var pointDataNew = PointUtil.latLngTranfrom(ridingOrderInfoData!!.mileage_point)
                    polylineMapDelegate.setPoints(pointDataNew,isRental)
                }else{
                    polylineMapDelegate.setPoints(ridingOrderInfoData!!.mileage_point,isRental)
                }
                binding.layoutRecordinfo.tvRidingNo.postDelayed({
                    polylineMapDelegate.fitMapView()
                },500)
            }
        }
    }
    override fun onGetHistoryTrackSuccess(tracks: List<Track>) {}
    private fun showCurrentLocate() {
        val location = getLocation() ?: return
        map?.showCurrentLocate(location)
    }

    private fun getLocation(): Location? {
        return map?.getCurrentLocate() ?: LocationModel.lastLocation
    }

    override fun showErrMsg(message: String) {
        toast(message)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: ExchangePayStateData) {
        backType = 1
        binding.layoutRecordinfo.btnRidingOk.text = getString(R.string.pay)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBaseMsgEvent(event: BaseEventData) {
        if (event.code == EventUtil.EVENT_HOME) finish()
    }
    fun showShortOrder(){
        loadingDialogHelper.show {  }
        // val recordShortDialog = ShortRecordDetailDialog()
        ComModel.getFormType(REQUEST_TYPE).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===获取短时骑行反馈类型 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), FormoptionData::class.java)
                resultData.forEach {
                    FeedList.add(it)
                }
                binding.layoutRecordinfo.lyLayoutRecordinfo.visibility = View.GONE
                binding.layoutShortrecord.lyLayoutShortrecord.visibility = View.VISIBLE
                setFlowLayout()
            },
            onError = {loadingDialogHelper.dismiss()}
        ).toCancelable()
    }
    override fun onDestroy() {
        super.onDestroy()
//        polylineMapDelegate = null
        map = null
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }

    private fun setFlowLayout() {
        binding.layoutShortrecord.flowLayout.removeAllViews()
        for (i in 0 until FeedList.size) {
            val mInflater = LayoutInflater.from(ContextUtil.getContext())
            val tv = mInflater.inflate(R.layout.flow_layout, binding.layoutShortrecord.flowLayout, false) as CheckedTextView
            tv.setText(FeedList.get(i).title)
            //点击事件
            tv.clickDelay {
                if(FeedList.get(i).ischeck){
                    for (s in 0 until FeedListSel.size){
                        if(FeedListSel.get(s).id == FeedList.get(i).id) {
                            FeedListSel.removeAt(s)
                            break
                        }
                    }
                }else{
                    if(FeedListSel.size < 3){
                        FeedListSel.add(FeedList.get(i))
                    }else{
                        FeedListSel.removeAt(0)
                        FeedListSel.add(FeedList.get(i))
                    }
                }
                for (s in 0 until FeedList.size){
                    FeedList.get(s).ischeck = false
                    (binding.layoutShortrecord.flowLayout.getChildAt(s) as CheckedTextView).isChecked = false
                    for (s_sel in 0 until FeedListSel.size){
                        if(FeedListSel.get(s_sel).id == FeedList.get(s).id) {
                            (binding.layoutShortrecord.flowLayout.getChildAt(s) as CheckedTextView).isChecked = true
                            FeedList.get(s).ischeck = true
                        }
                    }
                }
            }
            binding.layoutShortrecord.flowLayout.addView(tv) //添加到父View
        }
    }
}