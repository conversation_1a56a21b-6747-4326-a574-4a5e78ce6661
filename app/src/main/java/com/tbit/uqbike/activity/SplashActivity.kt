package com.tbit.uqbike.activity

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.RequestListener
import com.google.gson.Gson
import com.lsxiao.apollo.core.annotations.Receive
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.fullScreen
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.App
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivitySplashBinding
import com.tbit.uqbike.entity.UidData
import com.tbit.uqbike.entity.getAdData
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.qrcode.CommonUtils
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.utils.DeviceIdUtil
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.UrlDecodeUtil
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.startActivity
import org.json.JSONObject
import java.io.File
import java.util.concurrent.TimeUnit
import android.graphics.drawable.Drawable
import android.widget.ImageView
import com.bumptech.glide.request.target.Target
import com.tbit.uqbike.entity.ThirdAdData
import com.tbit.uqbike.utils.AdUtil
import com.zj.easyfloat.EasyFloat.listener

class SplashActivity : BaseActivity() {

    companion object {
        private const val REQUEST_AD = 1
    }

//    private val TIME_DURATION: Long = 1
    private var countdownDisposable: Disposable? = null
    private lateinit var binding: ActivitySplashBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_AUTH,"")
        AppUtil.screenWidth = CommonUtils.getScreenWidth(this@SplashActivity)
        Glob.IsVirtual = isEmulator()

        if(!SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
            ComModel.getUserUid(DeviceIdUtil.createDeviceId(this@SplashActivity)).subscribeBy(
                onNext = {
//                    MyLogUtil.Log("1111","===uuid 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), UidData::class.java)
                    if (resultData.uuid != null){
                        MyLogUtil.Log("1111","==============uuid===========11=="+ resultData.uuid)
                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.DEVICE_ID, resultData.uuid);
                    }
                }
            ).toCancelable()

            ComModel.sumitLang().subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===sumitLang 信息=="+it.toString())
                },
                onError = {}
            ).toCancelable()

//            CoroutinesUtil.launchIO {
//                delay(800)
//                val listKey = arrayOf("open_screen")
//                ComModel.getConfig(listKey).subscribeBy(
//                    onNext = {
//                        MyLogUtil.Log("1111","===获取开屏广告 信息=="+it.toString())
//                        var resultData = Gson().fromJson(it.toString(), AdScreenData::class.java)
//                        if (resultData != null){
//                            Glob.open_screen = resultData.open_screen.countdown
//                        }
//                    }
//                ).toCancelable()
//            }
        }


        /**判断是不是app已经启动,若已经启动，不启动本界面 */
        if (!isTaskRoot) {
            if (intent.hasCategory(Intent.CATEGORY_LAUNCHER) && Intent.ACTION_MAIN == intent.action) {
                finish()
                return
            }
        }

        fullScreen()
        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_splash)

        toMainActivity()


//        text_version.text = BuildConfig.VERSION_NAME
//        handleSplashAd()
    }

    override fun onDestroy() {
        countdownDisposable?.dispose()
        isActivityRun = ""
        AdUtil.isShowSpal = false
        super.onDestroy()
    }

    /**
     * 开始倒计时
     * @param time 倒计时时间(秒)
     */
    private fun startCountdown(time: Long) {
        countdownDisposable?.takeIf { !it.isDisposed }?.dispose()
        countdownDisposable = Observable.interval(0, 1, TimeUnit.SECONDS)
            .map { time - it }
            .takeUntil { it <= 0 }
            .observeOn(AndroidSchedulers.mainThread())
            .subscribeBy {
                onCountDown(it)
            }
    }

    private fun onCountDown(time: Long) {
        binding.textSkip.text = getString(R.string.skip, time)
        if(time == 0L) {
            if(SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
                startActivity<AuthActivity>()
                finish()
            }else{
                getAdDeposit()
                gotoMain()
            }
        }
    }
    @Receive(Constant.Event.EVENT_ADSPAL_SUC)
    fun EVENT_ADSPAL_SUC(){
//        gotoMain()
    }
    @Receive(Constant.Event.EVENT_ADSPAL_FAIL)
    fun EVENT_ADSPAL_FAIL(){
//        gotoMain()
    }
    private var isActivityRun = "1"
    private fun toMainActivity() {
        var splAd_str_self = SpUtil.getInstance().find(Constant.SpKey.SP_SPLAD)
        var splAd_str_third = SpUtil.getInstance().find(Constant.SpKey.SP_SPLAD_THIRD)

        if (splAd_str_self.isNullOrEmpty() && splAd_str_third.isNullOrEmpty()){
            gotoMain()
        }else{
            if(!splAd_str_self.isNullOrEmpty()){
                val resultData = try { Gson().fromJson(splAd_str_self, getAdData::class.java) } catch (e: Exception) { null }
                if (resultData != null && resultData.size > 0){
                    loadGif(binding.imgSplashGif)
                    binding.imageSplash.visibility = View.VISIBLE
                    binding.lySkip.visibility = View.VISIBLE
                    ImageLoad.loadimg(resultData.get(0).images[0],binding.imageSplash)

                    var TIME_DURATION = Glob.open_screen.toLong()
                    startCountdown(TIME_DURATION)
                    binding.lySkip.setOnClickListener {
                        countdownDisposable?.dispose()

                        val properties = JSONObject()
                        properties.put("startup_page_id", resultData.get(0).id) // 设置商品 ID
                        properties.put("startup_page_name", resultData.get(0).title) // 设置商品 名称
                        MDUtil.clickEvent("startup_page_over",properties)
                        gotoMain()
                    }
                    binding.imageSplash.setOnClickListener {
                        countdownDisposable?.dispose()
                        gotoMain()

                        var linkUrl = UrlDecodeUtil().getParm(resultData.get(0).link_url)
                        AppUtil.goAdAciton(this@SplashActivity,resultData.get(0).title, AppUtil.parseLink(linkUrl)!!,resultData.get(0).is_show_app_header,3)

                        val properties = JSONObject()
                        properties.put("startup_page_id", resultData.get(0).id) // 设置商品 ID
                        properties.put("startup_page_name", resultData.get(0).title) // 设置商品 名称
                        MDUtil.clickEvent("startup_page_click",properties)
                    }
                } else {
                     gotoMain()
                }
            }else{
                binding.textSkip.postDelayed({
                    if(!splAd_str_third.isNullOrEmpty()){
                        loadGif(binding.imgSplashGif)
                        App.appOpenAdManager.loadAd(ContextUtil.getContext())
                        val resultData = try { Gson().fromJson(splAd_str_third, ThirdAdData::class.java) } catch (e: Exception) { null }
                        if (resultData != null){
                            binding.textSkip.postDelayed({
                                AdUtil.showSpal(this@SplashActivity,resultData.open_screen_ad_sdk.type,resultData.open_screen_ad_sdk.android_id,
                                    onNotify = {
                                        if (it.equals("0")){
                                            gotoMain()
                                        }else{
                                            isActivityRun = ""
                                        }
                                    })
                            },500)
                        } else {
                            gotoMain()
                        }
                        binding.textSkip.postDelayed({
                            if (binding.textSkip != null && !isActivityRun.isNullOrEmpty()){
                                gotoMain()
                            }
                        },5000)
                    }else{
                        gotoMain()
                    }
                },800)
            }
        }

    }
    fun loadGif(imageView : ImageView){
//        var width = CommonUtils.getScreenWidth(this@SplashActivity)/4*3
//        var hight = CommonUtils.getScreenHeight(this@SplashActivity)/3*2
        var width = CommonUtils.getScreenWidth(this@SplashActivity)/5*4
        var hight = CommonUtils.getScreenHeight(this@SplashActivity)/4*3
        Glide.with(ContextUtil.getContext()).asGif()
            .load(R.drawable.img_spal) // 替换为你的 GIF 资源或 URL
            .override(width,hight)
            .listener(object : RequestListener<GifDrawable> {
                override fun onResourceReady(resource: GifDrawable?, model: Any?, target: com.bumptech.glide.request.target.Target<GifDrawable>?,
                    dataSource: DataSource?, isFirstResource: Boolean): Boolean {
                    resource?.setLoopCount(1)
                    resource?.start()
                    return  false
                }

                override fun onLoadFailed(e: GlideException?, model: Any?, target: Target<GifDrawable>?, isFirstResource: Boolean): Boolean {
                    return false
                }
            }).into(imageView) // 目标 ImageView
    }
    private fun gotoMain(){
//        if (Glob.isGoogleServiceAvailable && Glob.isGoogleNetAvailable == null) {
//            startActivity<SplashLoadingActivity>()
//        } else {
//            startActivity(MainActivity.createIntent(this@SplashActivity))
//        }


        startActivity<HomeActivity>()
//        startActivity(MainActivity.createIntent(this@SplashActivity))
        finish()
    }
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        toMainActivity()
    }
    fun checkFileExists(imageId: String):Boolean {
        var fileExists = getSplashImageFile(imageId).exists()
//        if (fileExists) MyLogUtil.Log("5555","==="+getSplashImageFile(imageId).absolutePath)
        return fileExists
    }
    /**
     * 获取Splash页面广告图片本地保存路径
     * @param imageId 广告图片Id
     */
    fun getSplashImageFile(imageId: String?): File {
//        return File(Constant.DIR.IMAGE, "SplashImage-$imageId.png")
        return File(Constant.DIR.IMAGE, "SplashImage.png")
    }
    private fun getAdDeposit() {
        LocationModel.requestLocation()
            .subscribeBy(
                onNext = {
//                            DepositModel.getDeposit(it.latitude, it.longitude).subscribeBy(onError = {})
                         },
                onError = {}
            )
    }

    fun isEmulator(): Boolean {
        return (Build.FINGERPRINT.startsWith("generic")
                || Build.FINGERPRINT.startsWith("unknown")
                || Build.MODEL.contains("google_sdk")
                || Build.MODEL.contains("Emulator")
                || Build.MODEL.contains("Android SDK built for x86")
                || Build.MANUFACTURER.contains("Genymotion")
                || (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"))
                || "google_sdk" == Build.PRODUCT)
    }
}