package com.tbit.uqbike.activity

import android.Manifest
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.recyclerview.widget.GridLayoutManager
import com.google.gson.Gson
import com.lsxiao.apollo.core.Apollo
import com.lsxiao.apollo.core.annotations.Receive
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.luck.picture.lib.permissions.PermissionChecker
import com.luck.picture.lib.permissions.PermissionConfig
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.preview.ImageInfo
import com.tbit.preview.ImagePreviewActivity
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.App
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.SelectPhotoAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityPicReturnBinding
import com.tbit.uqbike.entity.AliData
import com.tbit.uqbike.entity.getAdressData
import com.tbit.uqbike.fragment.PermissionReadFragment
import com.tbit.uqbike.fragment.ReadMediaPermissionFragment
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip
import org.jetbrains.anko.intentFor
import org.json.JSONObject
import java.io.Serializable

class PicReturnActivity : BaseActivity() {
    companion object {
        private const val EXTRA_DATA1 = "EXTRA_DATA1"
        private const val EXTRA_DATA2 = "EXTRA_DATA2"
        fun createIntent(context: Context,vehicle_no: String, order_no: String): Intent {
            return context.intentFor<PicReturnActivity>(EXTRA_DATA1 to vehicle_no,EXTRA_DATA2 to order_no)
        }
    }
    private val vehicle_no: String by bindExtra(EXTRA_DATA1)
    private val order_no: String by bindExtra(EXTRA_DATA2)
    private lateinit var binding: ActivityPicReturnBinding

    private var adapt1 : SelectPhotoAdapter? = null
    private var adapt2 : SelectPhotoAdapter? = null
    private val photos1 = mutableListOf<ImageInfo>()
    private val photos2 = mutableListOf<ImageInfo>()
    private var MyAdress = ""
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPicReturnBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_pic_return)

        binding.appbarLayout.toolbarTitle.text = getString(R.string.s_picrereturn)
        binding.appbarLayout.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.appbarLayout.toolbar.setNavigationOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("confirm_photo_return_click",properties)
            finish()
        }

        ComModel.getAlConfig().subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取阿里云 配置 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), AliData::class.java)
                Glob.ACCESS_ID = resultData.accesskey_id
                Glob.ACCESS_KEY = resultData.accesskey_secret
                Glob.ACCESS_TOKEN = resultData.security_token
                Glob.ACCESS_BUCKET_NAME = resultData.bucket
                Glob.ACCESS_ENDPOINT = resultData.endpoint
                Glob.ACCESS_DOMAINNAME = resultData.host_cdn
                Glob.IMG_PREFIX = resultData.prefix
            }
        ).toCancelable()

        val spacing = dip(1)
        val spanCount = 1
        val maxCount = 1
        adapt1 = SelectPhotoAdapter(this, spanCount, maxCount, photos1, SelectPhotoAdapter.TYPE_APPEAL)
        adapt1?.onDelListener = { setBtnState() }
        adapt1?.onclickListener = {
            val properties = JSONObject()
            MDUtil.clickEvent("left_side_photo_click",properties)
        }
        adapt1?.isWate = true
        adapt1?.carnum = vehicle_no
        adapt1?.adress = MyAdress
        adapt1?.showTypeBig = true
        adapt1?.isCam = true
        binding.rcvImage1.adapter = adapt1
        binding.rcvImage1.layoutManager = GridLayoutManager(this, spanCount)
        binding.rcvImage1.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        adapt1?.notifyDataSetChanged()

        adapt2 = SelectPhotoAdapter(this, spanCount, maxCount, photos2, SelectPhotoAdapter.TYPE_APPEAL)
        adapt2?.onDelListener = { setBtnState() }
        adapt2?.onclickListener = {
            val properties = JSONObject()
            MDUtil.clickEvent("right_side_photo_click",properties)
        }
        adapt2?.isWate = true
        adapt2?.carnum = vehicle_no
        adapt2?.adress = MyAdress
        adapt2?.isCam = true
        binding.rcvImage2.adapter = adapt2
        binding.rcvImage2.layoutManager = GridLayoutManager(this, spanCount)
        binding.rcvImage2.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        adapt2?.notifyDataSetChanged()


        var location = LocationModel.lastLocation
        var latLng = LatLng()
        latLng.lat = location!!.latitude
        latLng.lng = location!!.longitude
        if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
            latLng = GPSUtil.bd09_To_gps84(location.latitude,location.longitude)
        }
        latLng.lat = GPSUtil.retain6(latLng.lat)
        latLng.lng = GPSUtil.retain6(latLng.lng)
        ComModel.getAdress(latLng.lat,latLng.lng).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===当前位置地址 信息=="+it.toString())
                val resultData = Gson().fromJson(it.toString(), getAdressData::class.java)
                if (resultData != null){
                    MyAdress = resultData.address
                    adapt1?.adress = MyAdress
                    adapt2?.adress = MyAdress
                }
            }
        ).toCancelable()
        binding.imgPicreturn1.setOnClickListener {
            var source = ArrayList<ImageInfo>()
            var imgData = ImageInfo()
            imgData.imageDrew = R.drawable.ic_picreturn_l
            source.add(imgData)
            startImagePreviewActivity(0,source)
        }

        binding.imgPicreturn2.setOnClickListener {
            var source = ArrayList<ImageInfo>()
            var imgData = ImageInfo()
            imgData.imageDrew = R.drawable.ic_picreturn_r
            source.add(imgData)
            startImagePreviewActivity(0,source)
        }

        binding.rlLeft.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("take_photo_cancel_click",properties)
            finish()
        }
        binding.rlRight.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("take_photo_confirm_click",properties)

            val listImg1 = ArrayList<String>()
            if(photos1.size > 0){
                photos1.forEach {
//                    if(!it.bigImageUrl.contains("http")){
//                        Toast.makeText(App.context, getString(R.string.s_sumitimg_fail), Toast.LENGTH_SHORT).show()
//                        return@clickDelay
//                    }
                    listImg1.add(it.bigImageUrl)
                }
            }
            val listImg2 = ArrayList<String>()
            if(photos2.size > 0){
                photos2.forEach {
//                    if(!it.bigImageUrl.contains("http")){
//                        Toast.makeText(App.context, getString(R.string.s_sumitimg_fail), Toast.LENGTH_SHORT).show()
//                        return@clickDelay
//                    }
                    listImg2.add(it.bigImageUrl)
                }
            }
            Glob.imgData.clear()
            if (listImg1.size > 0 || listImg2.size > 0){
                loadingDialogHelper.show {  }
                if (listImg1.size > 0){
                    Glob.imgData.add(listImg1.get(0))
                }
                if (listImg2.size > 0){
                    Glob.imgData.add(listImg2.get(0))
                }

                Apollo.emit(Constant.Event.EVENT_PICRETURN)
            }else{
                loadingDialogHelper.show {  }
                Apollo.emit(Constant.Event.EVENT_PICRETURN)
            }
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            (supportFragmentManager.findFragmentById(R.id.read_media_permission_fragment) as ReadMediaPermissionFragment).requestPermission {
                if (!it) { }
            }
        }else{
            (supportFragmentManager.findFragmentById(R.id.read_permission_fragment) as PermissionReadFragment).requestPermission {
            }
        }

    }

    @Receive(Constant.Event.EVENT_ORDERFISH)
    fun EVENT_ORDERFISH(){
        loadingDialogHelper.dismiss()
        finish()
    }
    @Receive(Constant.Event.EVENT_ORDERFISHERR)
    fun EVENT_ORDERFISHERR(){
        loadingDialogHelper.dismiss()
    }
    override fun onResume() {
        super.onResume()
        setBtnState()
    }
    fun setBtnState(){
//        if (photos1.size > 0 && photos2.size > 0){
//            binding.tvConfirmR.setTextColor(resources.getColor(R.color.white))
//        }else{
//            binding.tvConfirmR.setTextColor(resources.getColor(R.color.white50))
//        }
    }

    private fun startImagePreviewActivity(position: Int,source: MutableList<ImageInfo>) {
        val intent = Intent(this@PicReturnActivity, ImagePreviewActivity::class.java)
        val bundle = Bundle()
        bundle.putSerializable(ImagePreviewActivity.IMAGE_INFO, source as Serializable)
        bundle.putInt(ImagePreviewActivity.CURRENT_ITEM, position)
        bundle.putBoolean(ImagePreviewActivity.USER_CACHE, true)
        intent.putExtras(bundle)
        startActivity(intent)
        overridePendingTransition(0, 0)
    }
    override fun onDestroy() {
        super.onDestroy()
    }
}