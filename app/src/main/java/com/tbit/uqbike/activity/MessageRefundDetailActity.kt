package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import androidx.annotation.RequiresApi
import com.google.gson.Gson
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.preview.ImageInfo
import com.tbit.preview.ImagePreviewActivity
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityMessageRefundDetailActityBinding
import com.tbit.uqbike.entity.MsgRefundInfoData
import com.tbit.uqbike.mvp.model.MessageModel
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.intentFor
import java.io.Serializable

class MessageRefundDetailActity : BaseActivity() {

    companion object {

        private const val EXTRA_MESSAGE = "EXTRA_MESSAGE"

        fun createIntent(context: Context, msgid : Int): Intent {
            return context.intentFor<MessageRefundDetailActity>(
                EXTRA_MESSAGE to msgid
            )
        }
    }

    private val msgid: Int by bindExtra(EXTRA_MESSAGE)
    private lateinit var binding: ActivityMessageRefundDetailActityBinding

    var imgUrl = ""
    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMessageRefundDetailActityBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_message_refund_detail_actity)

        binding.layoutToolbar.toolbarTitle.text = getString(R.string.s_msginfo)
        binding.layoutToolbar.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.layoutToolbar.toolbar.setNavigationOnClickListener { finish() }
        binding.imgMsginfo.setOnClickListener {
            if (imgUrl.isNullOrEmpty()) return@setOnClickListener
            var source = ArrayList<ImageInfo>()
            var imgData = ImageInfo()
            imgData.bigImageUrl = imgUrl
            source.add(imgData)
            startImagePreviewActivity(0,source)
        }

        MessageModel.getMessageInfo(msgid)
            .subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","=== 获取消息详情 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), MsgRefundInfoData::class.java)
                    if (resultData.type == 11){
                        binding.tvMsginfoState.text = getString(R.string.s_win)
                    }else if (resultData.type == 10){
                        binding.tvMsginfoState.text = getString(R.string.s_invite_suc)
                    }else{
                        if (resultData.info.status != null && !resultData.info.status.value.isNullOrEmpty()){
                            binding.tvMsginfoState.text = resultData.info.status.value
                        }
                    }
                    if (!resultData.img_url.isNullOrEmpty()) {
                        binding.lyMsginfoDispose.visibility = View.VISIBLE
                        binding.tvMsginfoDisposeTime.text = TimeFormatUtil.transToStringBysq(resultData.create_time.toLong())
                        imgUrl = resultData.img_url
                        binding.imgMsginfo.visibility = View.VISIBLE
                        ImageLoad.loadimg(imgUrl,binding.imgMsginfo)
                    }

                    if (!resultData.response.isNullOrEmpty() && resultData.type != 11){
                        binding.lyMsginfoDispose.visibility = View.VISIBLE
                        binding.tvMsginfoDisposeTime.text = TimeFormatUtil.transToStringBysq(resultData.create_time.toLong())
                        binding.tvMsginfoDisposeCont.visibility = View.VISIBLE
                        binding.tvMsginfoDisposeCont.text = resultData.response
                    }
                    //消息类型:1故障上报，2意见反馈，3骑行费用申诉，4短时长骑行订单反馈，5余额退款，6骑行卡退款 ,9押金退还，10 邀请成功
                    if(resultData.type == 5){
                        //5余额退款
                        binding.tvMsginfoTimeL.text = getString(R.string.s_refundmsg_applemoney)
                        binding.tvMsginfoTime.text = AppUtil.getFloat2(resultData.info.amount.value)+Glob.CurrencyUnit

                        binding.lvMsginfo2.visibility = View.GONE
                        binding.lvMsginfo3.visibility = View.GONE
                        binding.lvMsginfo4.visibility = View.GONE

                    }else if(resultData.type == 9){
                        //9押金退还
                        binding.tvMsginfoTimeL.text = getString(R.string.s_refund_yj)
                        binding.tvMsginfoTime.text = AppUtil.getFloat2(resultData.info.amount.value)+Glob.CurrencyUnit

                        binding.lvMsginfo2.visibility = View.GONE
                        binding.lvMsginfo3.visibility = View.GONE
                        binding.lvMsginfo4.visibility = View.GONE
                        binding.lvMsginfo5.visibility = View.GONE

                    }else if(resultData.type == 10){
                        //10 邀请成功
                        binding.tvMsginfoTimeL.text = getString(R.string.s_invite_amount)

                        //1赠送金  ，2优惠券
                        if (resultData.info.reward_mode.value == 1){
                            var amoutInvite = AppUtil.getFloat2(resultData.info.present_amount.value)+Glob.CurrencyUnit
                            binding.tvMsginfoTime.text = amoutInvite
                        }else{
                            var couponNameData = ""
                            if (resultData.info.coupon.value !=null && resultData.info.coupon.value.size > 0){
                                resultData.info.coupon.value.forEach {
                                    couponNameData = couponNameData + it + "、"
                                }
                                couponNameData = couponNameData.substring(0,couponNameData.length-1)
                            }
                            binding.tvMsginfoTime.text = couponNameData
                        }


                        binding.tvMsginfoR6.visibility = View.VISIBLE
//                        binding.tvMsginfoR6.text = getString(R.string.s_msginfo_invite,resultData.info.invited_uid_username.value,amoutInvite)
                        binding.tvMsginfoR6.text = resultData.content

                        binding.lvMsginfo2.visibility = View.GONE
                        binding.lvMsginfo3.visibility = View.GONE
                        binding.lvMsginfo4.visibility = View.GONE
                        binding.lvMsginfo5.visibility = View.GONE

                    }else if(resultData.type == 11){
                        //11 中奖成功
                        binding.tvMsginfoTimeL.text = getString(R.string.s_award)
                        binding.tvMsginfoTime.text = resultData.info.name.value

                        binding.tvMsginfoR6.visibility = View.VISIBLE
//                        binding.tvMsginfoR6.text = getString(R.string.s_msginfo_invite,resultData.info.invited_uid_username.value,amoutInvite)
                        binding.tvMsginfoR6.text = resultData.info.remark.value

                        binding.lvMsginfo2.visibility = View.GONE
                        binding.lvMsginfo3.visibility = View.GONE
                        binding.lvMsginfo4.visibility = View.GONE
                        binding.lvMsginfo5.visibility = View.GONE

                    }else if(resultData.type == 6){
                        //6骑行卡退款
                        binding.tvMsginfoTimeL.text = getString(R.string.s_refundmsg_applecard)
                        binding.tvMsginfoTime.text = resultData.info.ride_card_item_id.value.toString()

                        binding.lvMsginfo2.visibility = View.VISIBLE

                        binding.tvMsginfoL2.text = getString(R.string.s_refundmsg_cardmoney)
                        binding.tvMsginfoR2.text = AppUtil.getFloat2(resultData.info.amount.value)+Glob.CurrencyUnit

                        binding.lvMsginfo3.visibility = View.VISIBLE
                        binding.tvMsginfoL3.text = getString(R.string.s_refundmsg_pay)
                        binding.tvMsginfoR3.text = resultData.info.refund_method.value

                        if (resultData.info.refund_payment_code.value.isNullOrEmpty()){
                            binding.lvMsginfo4.visibility = View.GONE
                        }else{
                            binding.lvMsginfo4.visibility = View.VISIBLE
                            binding.tvMsginfoL4.text = getString(R.string.s_refundmsg_back)
                            binding.tvMsginfoR4.text = resultData.info.refund_payment_code.value
                        }
                    }
                    if (resultData.type == 10 || resultData.type == 11){
                        binding.tvMsginfoL1.text = getString(R.string.str_time)
                    }else{
                        binding.tvMsginfoL1.text = getString(R.string.s_refundmsg_appletime)
                    }
                    binding.tvMsginfoR1.text = TimeFormatUtil.transToStringBysqDataInfo(resultData.info.create_time.value)

                    binding.tvMsginfoL5.text = getString(R.string.s_refundmsg_reason)
                    if(resultData.info.form_options.value.size > 1){
                        var info = ""
                        for (i in 0 until resultData.info.form_options.value.size) {
                            info = info + resultData.info.form_options.value.get(i) +"、"
                        }
                        info = info.substring(0,info.length - 1)
                        binding.tvMsginfoR5.text = info
                    }else{
                        if (resultData.info.form_options.value.size > 0){
                            binding.tvMsginfoR5.text = resultData.info.form_options.value[0]
                        }
                    }

                    if (resultData.info.remark != null && !resultData.info.remark.value.isNullOrEmpty()){
                        binding.lvMsginfoExplain.visibility = View.VISIBLE
                        binding.tvMsginfoExplain.text = resultData.info.remark.value
                    }
                }
            ).toCancelable()

    }

    private fun startImagePreviewActivity(position: Int,source: MutableList<ImageInfo>) {
        val intent = Intent(this@MessageRefundDetailActity, ImagePreviewActivity::class.java)
        val bundle = Bundle()
        bundle.putSerializable(ImagePreviewActivity.IMAGE_INFO, source as Serializable)
        bundle.putInt(ImagePreviewActivity.CURRENT_ITEM, position)
        bundle.putBoolean(ImagePreviewActivity.USER_CACHE, true)
        intent.putExtras(bundle)
        startActivity(intent)
        overridePendingTransition(0, 0)
    }
}