package com.tbit.uqbike.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.RelativeSizeSpan
import android.view.View
import android.widget.TextView
import com.google.gson.Gson
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.BuildConfig
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.databinding.ActivityMineBinding
import com.tbit.uqbike.entity.AssetsData
import com.tbit.uqbike.entity.MyWallet
import com.tbit.uqbike.entity.UserEntity
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.fragment.UpdateFragment
import com.tbit.uqbike.mvp.constract.MineContract
import com.tbit.uqbike.mvp.presenter.MinePresenter
import com.tbit.uqbike.resqmodel.WalletModel
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.UrlDecodeUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.startActivityForResult
import org.jetbrains.anko.toast
import org.json.JSONObject
import android.util.Log

class MineActivity: BaseActivity(), MineContract.View {

    companion object {
        const val REQUEST_RIDE_CARD = 1
        private const val REQUEST_VIP_CARD = 3
        private const val REQUEST_MEMBER_FEE= 2
        val REQUEST_ID_VERIFICATION= 4
    }

    private val presenter = MinePresenter(this)

    private lateinit var binding: ActivityMineBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMineBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_mine)
        EventBus.getDefault().register(this);
        lifecycle.addObserver(presenter)

        if (!resources.getBoolean(R.bool.support_name_auth)) {
            binding.textIdVerificationState.visibility = View.GONE
        }

        if (SpUtil.Companion.getInstance().find(Constant.SpKey.USERBALANCE).isNullOrEmpty()){
//            binding.tvMineBalance.text = "0.00"
            binding.tvMineBalance.setText(AppUtil.getPriceSmall("0.00"), TextView.BufferType.SPANNABLE)
        }else{
//            MyLogUtil.Log("1111","========USERBALANCE======"+SpUtil.Companion.getInstance().find(Constant.SpKey.USERBALANCE))
//            binding.tvMineBalance.text = SpUtil.Companion.getInstance().find(Constant.SpKey.USERBALANCE)
            binding.tvMineBalance.setText(AppUtil.getPriceSmall(SpUtil.Companion.getInstance().find(Constant.SpKey.USERBALANCE).toString()), TextView.BufferType.SPANNABLE)
        }
        if (SpUtil.Companion.getInstance().find(Constant.SpKey.SP_PERSONINFO).isNullOrEmpty()){
            binding.rtvPerson.visibility = View.VISIBLE
            SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_PERSONINFO,"1")
        }
        binding.rtvPerson.setOnClickListener { binding.imageProfilePhoto.performClick() }
        binding.llUserInfo.setOnClickListener { binding.imageProfilePhoto.performClick() }
        binding.imageProfilePhoto.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("personal_click",properties)
            startActivity<PersonActivity>()
        }

        binding.textVersion.text = BuildConfig.VERSION_NAME
        binding.lyBalance.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("wallet_click",properties)
            if(!hasLocationPermission()){
                openLocal()
                return@setOnClickListener
            }
            if (tryLoginIfNot()) startActivity<WalletActivity>()
        }
//        layout_ride_card.setOnClickListener { if (tryLoginIfNot()) startActivityForResult<RideCardActivity>(REQUEST_RIDE_CARD) }
        binding.layoutRideCard.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("ride_card_click",properties)
            if(!hasLocationPermission()){
                openLocal()
                return@setOnClickListener
            }
            if (tryLoginIfNot()) startActivityForResult<RideCardNewActivity>(REQUEST_RIDE_CARD)
        }
        binding.lyRidecard.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("ride_card_click",properties)
            if(!hasLocationPermission()){
                openLocal()
                return@setOnClickListener
            }
            if (tryLoginIfNot()) startActivityForResult<RideCardNewActivity>(REQUEST_RIDE_CARD)
        }
        binding.lyCoupon.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("coupon_click",properties)
            startActivity<WebActionActivity>(
                WebActionActivity.TITLE to "",
                WebActionActivity.URL to UrlDecodeUtil().getParm(FlavorConfig.NET.H5_URL+"myCoupons"+"?locale="+
                        FlavorConfig.Local.language),WebActionActivity.IsHideHead to true)
        }
//        layout_member_fee.setOnClickListener { if (tryLoginIfNot()) startActivityForResult<MemberFeeActivity>(REQUEST_MEMBER_FEE) }
        binding.layoutTransactionDetail.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("order_record",properties)
            if (tryLoginIfNot()) startActivity<TransactionDetailActivity>()
        }
        binding.layoutRideRecord.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("cycling_record",properties)
            if (tryLoginIfNot()) startActivity<RidingRecordActivity>()
        }
        binding.layoutInvis.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("promotion_code",properties)
            startActivity(ExchangeActivity.createIntent(this, ExchangeActivity.TYPE_EXTEND))
        }
        binding.layoutGift.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("gift_card_redemption",properties)
            startActivity(ExchangeActivity.createIntent(this, ExchangeActivity.TYPE_GIFTCARD))
        }

//        cl_head.setOnClickListener { if(!Glob.isLogin) FlavorConfig.appRoute.login() }
        binding.btnLogout.setOnClickListener {
            FlavorConfig.appRoute.login()
        }

        binding.textIdVerificationState.setOnClickListener {
            if(Glob.user?.isNameAuth == false) {
                startActivityForResult<NameAuthActivity>(REQUEST_ID_VERIFICATION)
            }
        }

        binding.layoutCheckUpdate.setOnClickListener {
            (supportFragmentManager.findFragmentById(R.id.fragment_update) as UpdateFragment).checkVersion(
                onIsLatestVersion = { toast(R.string.about_version_new) }
            )
        }
        binding.ivClose.setOnClickListener { finish() }
        binding.ivSet.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("set_click",properties)
            startActivity<SetActivity>()
        }
        binding.layoutLanguage.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("switch_language",properties)
            startActivity<LanguageActivity>()
        }
        binding.layoutCountry.setOnClickListener { startActivity<CountrySelActivity>() }
        binding.layoutFxs.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("distributors_settle_in",properties)
            startActivity<WebActionActivity>(WebActionActivity.TITLE to getString(R.string.s_fxs),
            WebActionActivity.URL to UrlDecodeUtil().getParm(FlavorConfig.NET.H5_URL+"distributorOnboarding"+"?locale="+
                    FlavorConfig.Local.language)) }

        binding.layoutInvite.setOnClickListener {
            startActivity<WebActionActivity>(WebActionActivity.TITLE to getString(R.string.s_invite),
                WebActionActivity.URL to UrlDecodeUtil().getParm(Glob.UrlInvite),WebActionActivity.IsHideHead to true,
                WebActionActivity.se_type to "4")
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            getUser()
        }
    }
    var isLocalper = false
    override fun onRestart() {
        super.onRestart()
//        MyLogUtil.Log("4444","====定位授权 mine========"+hasLocationPermission())
        if(!isLocalper && hasLocationPermission()) {
            isLocalper = true
            EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_LOCAL,""))
        }
    }

    override fun onResume() {
        super.onResume()
        doIfLogin()
        if (Glob.isLogin) {
//            ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_head), binding.imageProfilePhoto)
//            if (Glob.IsInvite){
//                binding.layoutInvite.visibility = View.VISIBLE
//            }else{
//                binding.layoutInvite.visibility = View.GONE
//            }
            binding.tvMineBalancehint.text = getString(R.string.s_balance)+"("+Glob.CurrencyUnit+")"
        }else{
            ImageLoad.loadimg(resources.getDrawable(R.drawable.icon_profile_photo),binding.imageProfilePhoto)
            binding.layoutInvite.visibility = View.GONE
        }
    }
    private fun doIfLogin() {
        if (!Glob.isLogin) return
        getUser()
        binding.llUserInfo.visibility = View.VISIBLE
        Glob.userEntity?.let { onUserUpdate(it) }
        WalletModel.getMywallet()
            .subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","======= 获取我的余额信息 ==="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), MyWallet::class.java)

                    binding.tvMineBalance.setText(AppUtil.getPriceSmall(AppUtil.getFloat2Double(resultData.balance + resultData.present)), TextView.BufferType.SPANNABLE)
//                    binding.tvMineBalance.text = AppUtil.getFloat2(resultData.balance + resultData.present).toString()
                    binding.tvMineBalancehint.text = getString(R.string.s_balance)+"("+resultData.currency+")"
                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.USERBALANCE, AppUtil.getFloat2Double(resultData.balance + resultData.present).toString()+resultData.currency);
                }
            ).toCancelable()

        WalletModel.getMyAssets()
            .subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","======= 获取卡券信息 ==="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), AssetsData::class.java)
                    if (resultData != null){
                        binding.tvMineRidecardnum.text = resultData.ride_card_num.toString()
                        binding.rvMineCouponnum.text = resultData.user_coupon_num.toString()
                        if (resultData.user_coupon_expired_num > 0){
                            binding.lyMineCouponnumExpired.visibility = View.VISIBLE
                            binding.rvMineCouponnumExpired.text = getString(R.string.s_expired_num,resultData.user_coupon_expired_num.toString())
                        }else{
                            binding.lyMineCouponnumExpired.visibility = View.GONE
                        }
                    }
                }
            ).toCancelable()
    }

    private fun getUser() {
        presenter.getUser()
    }

    override fun onGetUserSuccess(user: UserEntity) {
        loadingDialogHelper.dismiss()
        onUserUpdate(user)
    }

    private fun onUserUpdate(user: UserEntity) {
        binding.textPhone.text = if (!user.phone.toString().isNullOrEmpty() && user.phone != 0L) user.phone.toString() else user.user_name
//        binding.textPhone.text = if (!user.user_name.isNullOrEmpty()) user.user_name else user.phone
        if (user.avatar.isNullOrEmpty()){
            ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_head), binding.imageProfilePhoto)
        }else{
            ImageLoad.loadimg(user.avatar, binding.imageProfilePhoto)
        }
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }
    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onMsgEvent(event: BaseEventData) {
        if(event.code == EventUtil.EVENT_FINSH) finish()
        if(event.code == EventUtil.EVENT_EXIT) finish()
        if(event.code == EventUtil.EVENT_HOME) finish()
        if(event.code == EventUtil.EVENT_RIDECARD) {
            binding.lyRidecard.postDelayed({
                binding.lyRidecard.performClick()
            },500)
        }
    }
    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
        // presenter.onDestroy() // If presenter is created here, call onDestroy
    }

    // --- Add Placeholder Implementation for showUserAssets --- 
    override fun showUserAssets(assetsData: AssetsData?) {
        Log.d("MineActivity", "showUserAssets called (placeholder): ${assetsData?.member_level_name}")
        // TODO: Implement actual UI update later
    }
}