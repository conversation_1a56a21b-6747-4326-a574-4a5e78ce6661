package com.tbit.uqbike.activity.model

import com.tbit.maintenance.utils.ResUtil
import com.tbit.uqbike.R

object RidingModel {
    fun getRentalPre(pre_complete_status : Int,unit : String) : String{
        var content = ""
        when (pre_complete_status) {
            1 -> content = ResUtil.getString(R.string.s_riding_repay_rental)
            2 -> content = ResUtil.getString(R.string.s_riding_repay_p_rental,unit)
            3 -> content = ResUtil.getString(R.string.s_riding_repay_jt_rental,unit)
            4 -> content = ResUtil.getString(R.string.s_riding_repay_yj_rental,unit)
            5 -> content = ResUtil.getString(R.string.s_riding_repay_p_rental,unit)
            11 -> content = ResUtil.getString(R.string.s_riding_repay_rental)
            12 -> content = ResUtil.getString(R.string.s_riding_repay_p_rental,unit)
            13 -> content = ResUtil.getString(R.string.s_riding_repay_jt_rental,unit)
            14 -> content = ResUtil.getString(R.string.s_riding_repay_yj_rental,unit)
            15 -> content = ResUtil.getString(R.string.s_riding_repay_p_rental,unit)
        }
        return content
    }
    fun getPre(pre_complete_status : Int,unit : String) : String{
        var content = ""
        when (pre_complete_status) {
//            1 -> content = ResUtil.getString(R.string.s_riding_repay)
            1 -> content = ResUtil.getString(R.string.s_endorder_now)
            2 -> content = ResUtil.getString(R.string.s_riding_repay_p,unit)
            3 -> content = ResUtil.getString(R.string.s_riding_repay_jt,unit)
            4 -> content = ResUtil.getString(R.string.s_riding_repay_yj,unit)
//            11 -> content = ResUtil.getString(R.string.s_riding_repay)
            11 -> content = ResUtil.getString(R.string.s_endorder_now)
            12 -> content = ResUtil.getString(R.string.s_riding_repay_p,unit)
            13 -> content = ResUtil.getString(R.string.s_riding_repay_jt,unit)
            14 -> content = ResUtil.getString(R.string.s_riding_repay_yj,unit)
        }
        return content
    }
}