package com.tbit.uqbike.activity

import android.os.Bundle
import android.text.Html
import com.google.gson.Gson
import com.lsxiao.apollo.core.annotations.Receive
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityRefundBinding
import com.tbit.uqbike.dialog.RefundDialog
import com.tbit.uqbike.entity.RefundBalanceState
import com.tbit.uqbike.entity.pageData
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.resqmodel.RefundModel
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.startActivity
import org.json.JSONObject

class RefundActivity : BaseActivity() {
    var resultDataBalanceState : RefundBalanceState? = null
    private lateinit var binding: ActivityRefundBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRefundBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_refund)
        binding.ivClose.setOnClickListener { finish() }
        binding.tvRefundUnit.text = Glob.CurrencyUnit

        PageModel.getPage(PageModel.refund_rules, PageModel.isH5).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取退款说明 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), pageData::class.java)
//                tv_charge_info.text = resultData.content
                var htmlFormattedText = Html.fromHtml(resultData.content)
                binding.tvChargeInfo.text = htmlFormattedText
            }
        ).toCancelable()
        loadingDialogHelper.show { false }
        RefundModel.getBalanceCheck().subscribeBy(
            onNext = {
                loadingDialogHelper!!.dismiss()
                MyLogUtil.Log("1111","===获取余额退款获取余额/是否需提交银行卡 信息=="+it.toString())
                resultDataBalanceState = Gson().fromJson(it.toString(), RefundBalanceState::class.java)
                if (resultDataBalanceState?.balance != null){
                    binding.tvRefundBalance.text = AppUtil.getFloat2(resultDataBalanceState!!.balance)
                    refund_amount_event = AppUtil.getFloat2(resultDataBalanceState!!.balance)
                    if (resultDataBalanceState?.balance == 0f || resultDataBalanceState?.is_riding!!){
                        binding.btnPay.setTextColor(resources.getColor(R.color.white50))
                    }
                }

            },
            onError = {
                binding.btnPay.setTextColor(resources.getColor(R.color.white50))
                loadingDialogHelper!!.dismiss()
            }
        ).toCancelable()

        binding.tvRefundRecord.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("refund_record",properties)

            startActivity<RefunRecordActivity>()
        }
        binding.btnPay.setOnClickListener {
            if (resultDataBalanceState != null){
                val properties = JSONObject()
                MDUtil.clickEvent("refund_click",properties)

                if (resultDataBalanceState?.balance == 0f){
                    MyToastUtil.toast(getString(R.string.s_refund_n))
                }else if(resultDataBalanceState?.is_riding!!){
                    MyToastUtil.toast(getString(R.string.s_refund_n_riding))
                }else{
                    RefundDialog.Builder(this@RefundActivity).setTitle("").
                    setContent(getString(R.string.s_sure_refund))
                        .setLeftText(getString(R.string.s_balance_ridecard)).setRightText(
                            getString(R.string.s_sure_refund_balance)).setCanceledOnOutside(true)
                        .setClickListen(object : RefundDialog.TwoSelDialog {
                            override fun leftClick() {
                                startActivity<RideCardNewActivity>()
                            }
                            override fun rightClick() {
                                if (resultDataBalanceState?.need_bank_info!!){
                                    startActivity(RefundPersonInfoActivity.createIntent(this@RefundActivity,ComModel.FormType_Refund,""))
                                }else{
                                    startActivity(RefunReasonActivity.createIntent(this@RefundActivity,ComModel.FormType_Refund,
                                        "","","",""))
                                }
                            }
                        }).build().show()
                }
            }else{
                MyToastUtil.toast(getString(R.string.s_refund_n))
            }
        }
    }
    @Receive(Constant.Event.REFUNDSUC)
    fun onRefundSuc() {
        finish()
    }
}