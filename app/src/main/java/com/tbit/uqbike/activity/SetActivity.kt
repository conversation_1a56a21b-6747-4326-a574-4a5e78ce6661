package com.tbit.uqbike.activity

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.os.SystemClock
import com.google.gson.Gson
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.BuildConfig
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivitySetBinding
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.entity.DownPageData
import com.tbit.uqbike.entity.VersionData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.fragment.UpdateFragment
import com.tbit.uqbike.mvp.model.UserModel
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.EventModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.UrlDecodeUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity
import org.json.JSONObject

class SetActivity : BaseActivity(){
    var isShowCompVersion = false
    var versionData = BuildConfig.VERSION_NAME.split(".")
    private val updateFragment by lazy { supportFragmentManager.findFragmentById(R.id.fragment_update) as UpdateFragment }
    private lateinit var binding: ActivitySetBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySetBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_set)
        EventBus.getDefault().register(this);
        setSupportActionBar(binding.appbarLayout.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        binding.appbarLayout.toolbarTitle.text = getString(R.string.s_set)
        binding.appbarLayout.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.appbarLayout.toolbar.setNavigationOnClickListener { onBackPressed() }

        binding.tvSetPrivacy.text = getString(R.string.str_privacy_policy).replace("《","").replace("》","")
        binding.tvSetService.text = getString(R.string.str_service_terms).replace("《","").replace("》","")

        binding.tvSetVersion.text = getString(R.string.version_code_title)+"  "+versionData[0]+"."+versionData[1]+"."+versionData[2]
        binding.tvSetVersion.setOnClickListener { ClickButton() }
//        cl_head.setOnClickListener { if(!Glob.isLogin) FlavorConfig.appRoute.login() }
        binding.btnLogout.setOnClickListener {
//            FlavorConfig.appRoute.login()

            val properties = JSONObject()
            MDUtil.clickEvent("exit_click",properties)

            CommDialog.Builder(this@SetActivity).setTitle(ResUtil.getString(R.string.dialog_tip)).setContent(getString(R.string.s_quit_sure))
                .setLeftText(ResUtil.getString(R.string.cancel)).setRightText(getString(R.string.quit)).setCanceledOnOutside(true)
                .setClickListen(object : CommDialog.TwoSelDialog {
                    override fun leftClick() {}
                    override fun rightClick() {
                        loadingDialogHelper.show {  }
                        UserModel.UserLoginOut()
                            .subscribeBy(
                                onNext = {
                                    //退出登录
                                    loadingDialogHelper.dismiss()
                                    UserModel.logout()
//                                    EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_LOGINOUT,""));
//                                    EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_FINSH,""));
                                    finish()
                                    tryLoginIfNot()
                                },
                                onError = {loadingDialogHelper.dismiss()}
                            ).toCancelable()
                    }
                }).build().show()
        }

        binding.layoutPrivacy.setOnClickListener {
            loadingDialogHelper.show {  }
            PageModel.getPageUrl(PageModel.privacy_policy).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                    startActivity<WebActivity>(
                        WebActivity.TITLE to getString(R.string.str_privacy_policy).replace("《","").replace("》",""),
                        WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
                },
                onError = {loadingDialogHelper.dismiss()}
            ).toCancelable()
        }

        binding.layoutServiceTerms.setOnClickListener {
            loadingDialogHelper.show {  }
            PageModel.getPageUrl(PageModel.user_policy).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                    startActivity<WebActivity>(
                        WebActivity.TITLE to getString(R.string.str_service_terms).replace("《","").replace("》",""),
                        WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
                },
                onError = {loadingDialogHelper.dismiss()}
            ).toCancelable()
//            startActivity<WebActivity>(
//                WebActivity.TITLE to getString(R.string.str_service_terms).replace("《","").replace("》",""),
//                WebActivity.URL to UrlModel.getServiceTermsUrl(this))
        }

        binding.layoutCheckUpdate.setOnClickListener {
            loadingDialogHelper.show {  }
            ComModel.getVersion().subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===版本更新 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), VersionData::class.java)
//                var localVersion = BuildConfig.VERSION_NAME.replace("v","").split(".")
//                var newVersion = resultData.version.replace("v","").split(".")
                    if(!resultData.version.isNullOrEmpty()){
                        var isOutCan = true
                        if(resultData.is_forced == 1){
                            isOutCan = false
                        }
                        CommDialog.Builder(this@SetActivity).setTitle(ResUtil.getString(R.string.dialog_tip)).setContent(resultData.info)
                            .setLeftText(ResUtil.getString(R.string.cancel)).setRightText(getString(R.string.s_upda)).setCanceledOnOutside(isOutCan)
                            .setClickListen(object : CommDialog.TwoSelDialog {
                                override fun leftClick() {}
                                override fun rightClick() {
                                    try {
                                        val intent = Intent(Intent.ACTION_VIEW).apply {
                                            data = Uri.parse("https://play.google.com/store/apps/details?id=${BuildConfig.APPLICATION_ID}")
                                            setPackage("com.android.vending")
                                            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                        }
                                        startActivity(intent)
                                    } catch (e: Exception) {
                                        EventModel.getDownUrl().subscribeBy(onNext = {
                                            MyLogUtil.Log("1111","===获取下载页 信息=="+it.toString())
                                            var resultData = Gson().fromJson(it.toString(), DownPageData::class.java)
                                            if (resultData != null){
                                                var url = resultData.dowmload_url
                                                var intent = Intent(Intent.ACTION_VIEW, Uri.parse(url));
                                                startActivity(intent);
                                            }
                                        }).toCancelable()
                                    }
                                }
                            }).build().show()
                    }else{
                        MyToastUtil.toast(getString(R.string.is_last_version))
                    }
                },
                onError = {loadingDialogHelper.dismiss()}
            ).toCancelable()
        }

        binding.layoutCountry.setOnClickListener { startActivity<CountrySelActivity>() }

        binding.layoutDel.setOnClickListener {
            startActivity<SignOutActivity>()
        }

        binding.layoutFixpwd.setOnClickListener {
            startActivity<FixPwdActivity>()
        }
    }
    fun isPackageInstalled(packageName: String): Boolean {
        try {
            ContextUtil.getContext().getPackageManager().getPackageInfo(packageName, 0)
        } catch (e: PackageManager.NameNotFoundException) {
            return false
        }
        return true
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: BaseEventData) {
        if(event.code == EventUtil.EVENT_FINSH) finish()
        if(event.code == EventUtil.EVENT_EXIT) finish()
    }
    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }

    //点击次数
    var count = 5
    //规定的有效时间
    var time: Long = 1000
    var mHits = LongArray(count)
    //连续点击按钮三次
    fun ClickButton() {
        //每次点击时，数组向前移动一位
        System.arraycopy(mHits, 1, mHits, 0, mHits.size - 1);
        //为数组最后一位赋值
        mHits[mHits.size - 1] = SystemClock.uptimeMillis();
        if (mHits[0] >= (SystemClock.uptimeMillis() - time)) {
            //数组重新初始化
            mHits = LongArray(count)
            binding.tvSetVersion.text = getString(R.string.version_code_title)+"  "+
                    versionData[0]+"."+versionData[1]+"."+versionData[2]+"."+versionData[3]
        }
    }
}