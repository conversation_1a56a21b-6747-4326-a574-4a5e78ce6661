package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Patterns
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import com.google.android.material.tabs.TabLayoutMediator
import com.google.gson.Gson
import com.tbit.maintanenceplus.utils.InputMethodUtils
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.adapter.NonePageAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.AdDeposit
import com.tbit.uqbike.bean.PhoneInfo
import com.tbit.uqbike.bean.User
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.custom.CountryCodeWindow
import com.tbit.uqbike.databinding.ActivityRegisterBinding
import com.tbit.uqbike.dialog.CustomPhoneCallDialog
import com.tbit.uqbike.entity.CountryDataItem
import com.tbit.uqbike.entity.HotLineData
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.mvp.constract.LoginContract
import com.tbit.uqbike.mvp.model.AuthCodeModel
import com.tbit.uqbike.mvp.model.CountryCodeModel
import com.tbit.uqbike.mvp.model.UserModel
import com.tbit.uqbike.mvp.presenter.LoginPresenter
import com.tbit.uqbike.qrcode.CommonUtils
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.utils.ClickableTextUtil
import com.tbit.uqbike.utils.LoginViewUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.UrlDecodeUtil
import com.tbit.uqbike.widget.SimpleTabLay
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.toast


class RegisterActivity : BaseActivity(), LoginContract.View, TextWatcher {

    companion object {
        var oauth_token = ""
        var oauth_code = ""
        var oauth_unique_email = ""
        val TYPE_REG = 0 // 注册
        val TYPE_PWD = 1 // 忘记密码
        val TYPE_SEL_PHONE = 2// 手机
        val TYPE_SEL_EMAIL = 3// 邮箱

        val EXTRA_REG_LOGIN = "EXTRA_REG_LOGIN"
        val EXTRA_REG_SEL = "EXTRA_REG_SEL"
        val EXTRA_REG_GOOGLE = "EXTRA_REG_GOOGLE"
        private const val LOGIN_TYPE_PHONE = 0 //手机登录
        val LOGIN_TYPE_EMAIL = 3 //邮箱登录
        fun createIntent(context: Context, TypeAct: Int, TypeSel: Int,isGoogle : Boolean): Intent {
            return context.intentFor<RegisterActivity>(EXTRA_REG_LOGIN to TypeAct,EXTRA_REG_SEL to TypeSel,
                EXTRA_REG_GOOGLE to isGoogle)
        }
    }
    private val TypeAct: Int? by bindExtra(EXTRA_REG_LOGIN)//类型 注册，忘记密码
    private val TypeSel: Int? by bindExtra(EXTRA_REG_SEL)//类型 手机，邮箱
    private val isGoogle: Boolean? by bindExtra(EXTRA_REG_GOOGLE)//是否 谷歌登录的注册
    private var LoginType = LOGIN_TYPE_PHONE

    private val presenter = LoginPresenter(this)
    private val countryCodeWindow by lazy {
        CountryCodeWindow(this, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }
    private lateinit var binding: ActivityRegisterBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRegisterBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(com.tbit.uqbike.R.layout.activity_register)
//        setSupportActionBar(toolbar)
        lifecycle.addObserver(presenter)

        if (TypeAct != null) exchangeType = TypeAct.toString()

        when(TypeAct){
            TYPE_REG-> {
                binding.tvRegTitle.text = getString(com.tbit.uqbike.R.string.s_reg_account)
                binding.lyRegAgreement.visibility = View.VISIBLE
                binding.lyGologin.visibility = View.VISIBLE
                binding.lyForgetpwd.visibility = View.GONE
            }
            TYPE_PWD-> binding.tvRegTitle.text = getString(com.tbit.uqbike.R.string.s_forgetpwd).replace("？","").replace("?","")
        }

        var tabLayoutMediator: TabLayoutMediator? = null
        var adapters: NonePageAdapter? = null
        var type:Array<String>? = null
        type = arrayOf(getString(com.tbit.uqbike.R.string.s_phone), getString(com.tbit.uqbike.R.string.s_email))
        adapters = NonePageAdapter(this@RegisterActivity, type!!)
        binding.vpProps.adapter = adapters
        tabLayoutMediator = TabLayoutMediator(binding.tabLayout!!, binding.vpProps, true,true) { tab, position ->
            binding.tabLayout.initTab(tab, type!![position], SimpleTabLay.TYPE_LOGIN) }
        tabLayoutMediator!!.attach()
        binding.tabLayout.setSelectListener(object : SimpleTabLay.SelectListener{
            override fun onTabClick(pos: Int) {
                super.onTabClick(pos)
                when(pos){
                    0 -> {
                        LoginType = LOGIN_TYPE_PHONE
                        binding.inclLoginPhone.lyInclLoginPhone.visibility = View.VISIBLE
                        binding.inclLoginEmail.lyInclLoginEmail.visibility = View.GONE
                        checkPhoneState()
                    }
                    1 -> {
                        LoginType = LOGIN_TYPE_EMAIL
                        binding.inclLoginPhone.lyInclLoginPhone.visibility = View.GONE
                        binding.inclLoginEmail.lyInclLoginEmail.visibility = View.VISIBLE
                        checkEmailState()
                    }
                }
            }
        })
        binding.ivKf.setOnClickListener {
            loadingDialogHelper.show {  }
            val listKey = arrayOf("customer_service_number","line_id","facebook_url")
            ComModel.getConfig(listKey).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===获取客服电话 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), HotLineData::class.java)
                    var phoneInfoList = ArrayList<PhoneInfo>()
                    if (!resultData.customer_service_number.isNullOrEmpty()){
                        phoneInfoList.add(PhoneInfo(resultData.customer_service_number, Constant.CustomerServiceType.PHONE))
                    }
                    if (!resultData.line_id.isNullOrEmpty()){
                        phoneInfoList.add(PhoneInfo(resultData.line_id, Constant.CustomerServiceType.LINE))
                    }
                    if (!resultData.facebook_url.isNullOrEmpty()){
                        phoneInfoList.add(PhoneInfo(resultData.facebook_url, Constant.CustomerServiceType.FACEBOOK))
                    }
                    lifecycleDialogHelper.show(CustomPhoneCallDialog.newInstance(phoneInfoList))
                },
                onError = {loadingDialogHelper.dismiss()}
            ).toCancelable()
        }

        binding.inclLoginPhone.buttonAuthCode.setOnClickListener { sendAutoCode() }
        binding.btnLogin.setOnClickListener {
            when(LoginType){
                LOGIN_TYPE_PHONE->{
                    regPhone()
                }
                LOGIN_TYPE_EMAIL->{
                    regEmail()
                }
            }
        }
        binding.inclLoginPhone.editAuthCode.addTextChangedListener(this)
        binding.inclLoginPhone.editPhoneNumber.addTextChangedListener(this)
        binding.inclLoginPhone.editPhonePwd1.addTextChangedListener(this)
        binding.inclLoginPhone.editPhonePwd2.addTextChangedListener(this)

        binding.inclLoginPhone.editPhonePwd1.setOnFocusChangeListener{ v, hasFocus ->
            if (!hasFocus) {
                if (AppUtil.checkPwdLength(binding.inclLoginPhone.editPhonePwd1.text.toString())){
                    binding.inclLoginPhone.tvRegPhonePwdhint1.visibility = View.GONE
                }else{
                    binding.inclLoginPhone.tvRegPhonePwdhint1.visibility = View.VISIBLE
                }
            }
        }

        binding.inclLoginPhone.editPhoneNumber.setOnFocusChangeListener { v, hasFocus ->
            val phone = binding.inclLoginPhone.editPhoneNumber.text.toString()
            if (!hasFocus && phone.isNotEmpty()) {
                checkPhone(phone)
            }
        }
        initUserAgreementUI()
        countryCodeWindow.setOnItemClickListener { onSelectCountryCode(it) }
        binding.inclLoginPhone.ivCountryCode.setOnClickListener { binding.inclLoginPhone.textCountryCode.performClick() }
        binding.inclLoginPhone.textCountryCode.setOnClickListener { showCountryCodeWindow() }
        binding.inclLoginPhone.imageClearPhone.setOnClickListener { binding.inclLoginPhone.editPhoneNumber.text = null }


        var isHintPhonePwd1 = true
        binding.inclLoginPhone.imagePhonePwd1.setOnClickListener {
            isHintPhonePwd1 = !isHintPhonePwd1
            if (isHintPhonePwd1){
                LoginViewUtil.hidePwd(binding.inclLoginPhone.editPhonePwd1,binding.inclLoginPhone.imagePhonePwd1)
            }else{
                LoginViewUtil.showPwd(binding.inclLoginPhone.editPhonePwd1,binding.inclLoginPhone.imagePhonePwd1)
            }
        }

        var isHintPhonePwd2 = true
        binding.inclLoginPhone.imagePhonePwd2.setOnClickListener {
            isHintPhonePwd2 = !isHintPhonePwd2
            if (isHintPhonePwd2){
                LoginViewUtil.hidePwd(binding.inclLoginPhone.editPhonePwd2,binding.inclLoginPhone.imagePhonePwd2)
            }else{
                LoginViewUtil.showPwd(binding.inclLoginPhone.editPhonePwd2,binding.inclLoginPhone.imagePhonePwd2)
            }
        }

//========================================================================================

        binding.inclLoginEmail.editEmailNumber.addTextChangedListener(this)
        binding.inclLoginEmail.editEmailPwd.addTextChangedListener(this)
        binding.inclLoginEmail.editEmailPwd1.addTextChangedListener(this)
        binding.inclLoginEmail.editEmailPwd2.addTextChangedListener(this)

        binding.inclLoginEmail.editEmailPwd1.setOnFocusChangeListener{ v, hasFocus ->
            if (!hasFocus) {
                if (AppUtil.checkPwdLength(binding.inclLoginEmail.editEmailPwd1.text.toString())){
                    binding.inclLoginEmail.tvRegEmailPwdhint1.visibility = View.GONE
                }else{
                    binding.inclLoginEmail.tvRegEmailPwdhint1.visibility = View.VISIBLE
                }
            }
        }

        binding.inclLoginEmail.imageClearEmail.setOnClickListener { binding.inclLoginEmail.editEmailNumber.text = null }
        var isHintEmailPwd1 = true
        binding.inclLoginEmail.imageEmailPwd1.setOnClickListener {
            isHintEmailPwd1 = !isHintEmailPwd1
            if (isHintEmailPwd1){
                LoginViewUtil.hidePwd(binding.inclLoginEmail.editEmailPwd1,binding.inclLoginEmail.imageEmailPwd1)
            }else{
                LoginViewUtil.showPwd(binding.inclLoginEmail.editEmailPwd1,binding.inclLoginEmail.imageEmailPwd1)
            }
        }
        var isHintEmailPwd2 = true
        binding.inclLoginEmail.imageEmailPwd2.setOnClickListener {
            isHintEmailPwd2 = !isHintEmailPwd2
            if (isHintEmailPwd2){
                LoginViewUtil.hidePwd(binding.inclLoginEmail.editEmailPwd2,binding.inclLoginEmail.imageEmailPwd2)
            }else{
                LoginViewUtil.showPwd(binding.inclLoginEmail.editEmailPwd2,binding.inclLoginEmail.imageEmailPwd2)
            }
        }
        binding.inclLoginEmail.buttonEmailCode.clickDelay {
            if (binding.inclLoginEmail.editEmailNumber.text.toString().isNullOrEmpty()){
                MyToastUtil.toast(getString(com.tbit.uqbike.R.string.s_input_email))
                return@clickDelay
            }
            if (!isValidEmail(binding.inclLoginEmail.editEmailNumber.text.toString())){
                MyToastUtil.toast(getString(com.tbit.uqbike.R.string.s_email_error))
                return@clickDelay
            }
            loadingDialogHelper?.show{}
            AuthCodeModel.sendEmailAuthCode(binding.inclLoginEmail.editEmailNumber.text.toString())
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper.dismiss()
                        presenter.startCountdownEmail(60)
                    },
                    onError = {
                        loadingDialogHelper.dismiss()
                        val errMsg = ErrHandler.getErrMsg(it)
                    }
                ).toCancelable()
        }

        binding.ivClose.setOnClickListener { finish() }
        binding.tvRegLogin.setOnClickListener { finish() }
        updateCountryCodeInfo()
        when(TypeSel){
            TYPE_SEL_PHONE-> { }
            TYPE_SEL_EMAIL-> {binding.tabLayout.postDelayed({binding.tabLayout.getTabAt(1)!!.view.performClick()},50)}
        }
    }
    fun isValidEmail(target: CharSequence?): Boolean {
        return !TextUtils.isEmpty(target) && Patterns.EMAIL_ADDRESS.matcher(target).matches()
    }
    private inline fun initUserAgreementUI() {
        ClickableTextUtil.makeClickable(binding.tvUserAgreement, getString(com.tbit.uqbike.R.string.str_privacy_policy), ::showPrivacyPolicy)
        ClickableTextUtil.makeClickable(binding.tvUserAgreement, getString(com.tbit.uqbike.R.string.str_service_terms), ::showServiceTerms)
    }

    private inline fun showCountryCodeWindow() {
        InputMethodUtils.hideInput(ContextUtil.getContext(),binding.inclLoginPhone.textCountryCode)
        countryCodeWindow.width = CommonUtils.getScreenWidth(this@RegisterActivity)
        countryCodeWindow.height = CommonUtils.getScreenHeight(this@RegisterActivity)
//        countryCodeWindow.showAsDropDown(ll_country_code)
        countryCodeWindow.showAtLocation(binding.inclLoginPhone.llCountryCode, Gravity.BOTTOM,0,0)
    }

    private inline fun onSelectCountryCode(countryCodeInfo: CountryDataItem) {
        CountryCodeModel.countryCodeInfo = countryCodeInfo
        binding.inclLoginPhone.textCountryCode.text = countryCodePlus(countryCodeInfo.country_code)
        SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_COUNTRY_SEL,countryCodeInfo.country_code.toString())
        countryCodeWindow.dismiss()
    }

    private fun countryCodePlus(code: String?): String {
        if (code.isNullOrEmpty()) {
            return ""
        }
        return "+"+code.toInt()
    }

    private fun countryCodeNoPlus(code: String?): String {
        return code?.substring(1) ?: ""
    }

    private fun showPrivacyPolicy() {
        loadingDialogHelper.show {  }
        PageModel.getPageUrl(PageModel.privacy_policy).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                startActivity<WebActivity>(
                    WebActivity.TITLE to getString(com.tbit.uqbike.R.string.str_privacy_policy).replace("《","").replace("》",""),
                    WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
            },
            onError = {loadingDialogHelper.dismiss()}
        ).toCancelable()
    }

    private fun showServiceTerms() {
        loadingDialogHelper.show {  }
        PageModel.getPageUrl(PageModel.user_policy).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                startActivity<WebActivity>(
                    WebActivity.TITLE to getString(com.tbit.uqbike.R.string.str_service_terms).replace("《","").replace("》",""),
                    WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
            },
            onError = {loadingDialogHelper.dismiss()}
        ).toCancelable()
    }

    override fun afterTextChanged(s: Editable?) {
        when(LoginType){
            LOGIN_TYPE_PHONE->{
                checkPhoneState()
            }
            LOGIN_TYPE_EMAIL->{
                checkEmailState()
            }
        }

//        presenter.getAuthCodeResendDelay(edit_phone_number.text.toString())
    }
    private fun checkPhoneState(){
        binding.inclLoginPhone.imageClearPhone.visibility = if(binding.inclLoginPhone.editPhoneNumber.text.isEmpty()) View.INVISIBLE else View.VISIBLE
        if (binding.inclLoginPhone.editPhoneNumber.text.toString().length > 0){
            binding.inclLoginPhone.editPhoneNumber.setTextSize(TypedValue.COMPLEX_UNIT_SP, 19f)
        }else{
            binding.inclLoginPhone.editPhoneNumber.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15f)
        }
        if (binding.inclLoginPhone.editAuthCode.text.toString().length > 0 && binding.inclLoginPhone.editPhoneNumber.text.toString().length > 0 &&
            binding.inclLoginPhone.editPhonePwd1.text.toString().length > 0 && binding.inclLoginPhone.editPhonePwd2.text.toString().length > 0){
            binding.btnLogin.isEnabled = true
            binding.btnLogin.setTextColor(resources.getColor(com.tbit.uqbike.R.color.white))
        }else{
            binding.btnLogin.isEnabled = false
            binding.btnLogin.setTextColor(resources.getColor(com.tbit.uqbike.R.color.white50))
        }
    }
    private fun checkEmailState(){
        binding.inclLoginEmail.imageClearEmail.visibility = if(binding.inclLoginEmail.editEmailNumber.text.isEmpty()) View.INVISIBLE else View.VISIBLE
        if (binding.inclLoginEmail.editEmailNumber.text.toString().length > 0){
            binding.inclLoginEmail.editEmailNumber.setTextSize(TypedValue.COMPLEX_UNIT_SP, 19f)
        }else{
            binding.inclLoginEmail.editEmailNumber.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15f)
        }
        if (binding.inclLoginEmail.editEmailPwd.text.toString().length > 0 && binding.inclLoginEmail.editEmailNumber.text.toString().length > 0 &&
            binding.inclLoginEmail.editEmailPwd1.text.toString().length > 0 && binding.inclLoginEmail.editEmailPwd2.text.toString().length > 0){
            binding.btnLogin.isEnabled = true
            binding.btnLogin.setTextColor(resources.getColor(com.tbit.uqbike.R.color.white))
        }else{
            binding.btnLogin.isEnabled = false
            binding.btnLogin.setTextColor(resources.getColor(com.tbit.uqbike.R.color.white50))
        }
    }
    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
    private fun updateCountryCodeInfo() {
        presenter.updateCountryCodeInfo()
    }
    override fun onGetCountryCodeInfoSuccess(data: List<CountryDataItem>) {
        if(SpUtil.getInstance().find(Constant.SpKey.SP_COUNTRY_SEL).isNullOrEmpty()){
            binding.inclLoginPhone.textCountryCode.text = AppUtil.getPhoneCode()
//            when(FlavorConfig.Local.language) {
//                LanguageUtil.LANG_US -> {binding.inclLoginPhone.textCountryCode.text = countryCodePlus("+66")}
//                LanguageUtil.LANG_ZH -> {binding.inclLoginPhone.textCountryCode.text = countryCodePlus("+86")}
//                LanguageUtil.LANG_TH -> {binding.inclLoginPhone.textCountryCode.text = countryCodePlus("+66")}
//            }
        }else{
            val oldCode = CountryCodeModel.countryCodeInfo.country_code
            val target = data.firstOrNull {
//            MyLogUtil.Log("1111","国家码数据"+oldCode+","+it.country_code)
                it.country_code == oldCode }
            if (target != null) {
                var code = oldCode.toInt()
                binding.inclLoginPhone.textCountryCode.text = countryCodePlus(code.toString())
            }
        }
    }

    override fun onGetAuthCodeResendDelay(time: Long) {}

    override fun onCountDown(time: Long) {
        if(time > 0) {
            binding.inclLoginPhone.buttonAuthCode.setTextColor(resources.getColor(com.tbit.uqbike.R.color.blue_namal))
            binding.inclLoginPhone.buttonAuthCode.text = getString(com.tbit.uqbike.R.string.second_with_unit, time)
            binding.inclLoginPhone.buttonAuthCode.isEnabled = false
        } else {
            binding.inclLoginPhone.buttonAuthCode.setTextColor(resources.getColor(com.tbit.uqbike.R.color.blue_namal))
            binding.inclLoginPhone.buttonAuthCode.text = getString(com.tbit.uqbike.R.string.get_auth_code)
            binding.inclLoginPhone.buttonAuthCode.isEnabled = true
            val code = binding.inclLoginPhone.editAuthCode.text.toString()
            if (code.isEmpty()) {
//                longToast(getString(R.string.str_auth_code_receive_hint))
            }
        }
    }
    override fun onCountDownEmail(time: Long) {
        if(time > 0) {
            binding.inclLoginEmail.buttonEmailCode.setTextColor(resources.getColor(com.tbit.uqbike.R.color.blue_namal))
            binding.inclLoginEmail.buttonEmailCode.text = getString(com.tbit.uqbike.R.string.second_with_unit, time)
            binding.inclLoginEmail.buttonEmailCode.isEnabled = false
        } else {
            binding.inclLoginEmail.buttonEmailCode.setTextColor(resources.getColor(com.tbit.uqbike.R.color.blue_namal))
            binding.inclLoginEmail.buttonEmailCode.text = getString(com.tbit.uqbike.R.string.get_auth_code)
            binding.inclLoginEmail.buttonEmailCode.isEnabled = true
            val code = binding.inclLoginEmail.editEmailPwd.text.toString()
            if (code.isEmpty()) {
//                longToast(getString(R.string.str_auth_code_receive_hint))
            }
        }
    }

    private fun sendAutoCode() {
        val code = binding.inclLoginPhone.textCountryCode.text.toString()
        if (!checkCountryCode(code)) {
            return
        }
        val countryCode = countryCodeNoPlus(code)
        val phone = binding.inclLoginPhone.editPhoneNumber.text.toString()
        if(checkPhone(phone)) {
            val cancellable = presenter.sendAuthCode(countryCode, phone)
            loadingDialogHelper?.show { cancellable.cancel() }
        }
    }

    override fun onSendAutoCodeSuccess() {
        toast(com.tbit.uqbike.R.string.auth_code_sent)
        loadingDialogHelper?.dismiss()
        presenter.startCountdown(60)
    }

    private fun regPhone() {
        val code = binding.inclLoginPhone.textCountryCode.text.toString()
        if (!checkCountryCode(code)) {
            return
        }
        val countryCode = countryCodeNoPlus(code)
        val phone = binding.inclLoginPhone.editPhoneNumber.text.toString()
        val authCode = binding.inclLoginPhone.editAuthCode.text.toString()
        val pwd1 = binding.inclLoginPhone.editPhonePwd1.text.toString()
        val pwd2 = binding.inclLoginPhone.editPhonePwd2.text.toString()
        if(!AppUtil.checkPwd(pwd1,pwd2)) return
        if(checkPhone(phone) && checkAuthCode(authCode)) {
            when(TypeAct){
                TYPE_REG-> {
                    if (checkAgreementTick()){
                        if (isGoogle == null || !isGoogle!!){
                            loadingDialogHelper.show { false }
                            MyLogUtil.Log("1111","===手机验证码注册==="+countryCode+","+phone+","+authCode+","+pwd1+","+pwd2)
                            UserModel.registerPhone(phone,countryCode,authCode.toInt(),pwd1,pwd2)
                                .subscribeBy(
                                    onNext = {
                                        loadingDialogHelper.dismiss()
                                        startActivity(MainActivity.createIntent(this,true))
                                    },
                                    onError = {
                                        loadingDialogHelper.dismiss()
                                        MyToastUtil.toast(ErrHandler.getErrMsg(it))
                                    }
                                ).toCancelable()
                        }else{
                            MyLogUtil.Log("1111","===手机验证码注册=google=="+countryCode+","+phone+","+authCode+","+pwd1+","+pwd2)
//                            MyLogUtil.Log("1111","==手机验证码注册=google="+ oauth_token+","+ oauth_code+","+ oauth_unique_email)
                            loadingDialogHelper.show { false }
                            UserModel.registerGooglePhone(oauth_token, oauth_code, oauth_unique_email,
                                phone,countryCode,authCode.toInt(),pwd1,pwd2)
                                .subscribeBy(
                                    onNext = {
                                        loadingDialogHelper.dismiss()
                                        startActivity(MainActivity.createIntent(this,true))
                                    },
                                    onError = {
                                        loadingDialogHelper.dismiss()
                                        MyToastUtil.toast(ErrHandler.getErrMsg(it))
                                    }
                                ).toCancelable()
                        }
                    }
                }
                TYPE_PWD-> {
                    MyLogUtil.Log("1111","===手机忘记密码==="+countryCode+","+phone+","+authCode+","+pwd1+","+pwd2)
                    loadingDialogHelper.show { false }
                    UserModel.forgetPwdPhone(phone,countryCode,authCode.toInt(),pwd1,pwd2)
                        .subscribeBy(
                            onNext = {
                                loadingDialogHelper.dismiss()
                                MyToastUtil.toast(getString(com.tbit.uqbike.R.string.s_fix_suc))
                                finish()
                            },
                            onError = {
                                loadingDialogHelper.dismiss()
                                MyToastUtil.toast(ErrHandler.getErrMsg(it))
                            }
                        ).toCancelable()
                }
            }
        }
    }
    private fun regEmail() {
        val emailNum = binding.inclLoginEmail.editEmailNumber.text.toString()
        val emailCode = binding.inclLoginEmail.editEmailPwd.text.toString()
        val pwd1 = binding.inclLoginEmail.editEmailPwd1.text.toString()
        val pwd2 = binding.inclLoginEmail.editEmailPwd2.text.toString()
        if(!AppUtil.checkPwd(pwd1,pwd2)) return
        when(TypeAct){
            TYPE_REG-> {
                if(checkAgreementTick()) {
                    if (isGoogle == null || !isGoogle!!){
                        MyLogUtil.Log("1111","==邮箱验证码注册=="+emailNum+","+emailCode+","+pwd1+","+pwd2)
                        loadingDialogHelper.show { false }
                        UserModel.registerEmail(emailNum,emailCode.toInt(),pwd1,pwd2)
                            .subscribeBy(
                                onNext = {
                                    loadingDialogHelper.dismiss()
                                    startActivity(MainActivity.createIntent(this,true))
                                },
                                onError = {
                                    loadingDialogHelper.dismiss()
                                    MyToastUtil.toast(ErrHandler.getErrMsg(it))
                                }
                            ).toCancelable()
                    }else{
                        MyLogUtil.Log("1111","==邮箱验证码注册=google="+emailNum+","+emailCode+","+pwd1+","+pwd2)
//                        MyLogUtil.Log("1111","==邮箱验证码注册=google="+ oauth_token+","+ oauth_code+","+ oauth_unique_email)
                        loadingDialogHelper.show { false }
                        UserModel.registerGoogleEmail(oauth_token, oauth_code, oauth_unique_email,
                            emailNum,emailCode.toInt(),pwd1,pwd2)
                            .subscribeBy(
                                onNext = {
                                    loadingDialogHelper.dismiss()
                                    startActivity(MainActivity.createIntent(this,true))
                                },
                                onError = {
                                    loadingDialogHelper.dismiss()
                                    MyToastUtil.toast(ErrHandler.getErrMsg(it))
                                }
                            ).toCancelable()
                    }
                }
            }
            TYPE_PWD-> {
                MyLogUtil.Log("1111","==邮箱忘记密码=="+emailNum+","+emailCode+","+pwd1+","+pwd2)
                loadingDialogHelper.show { false }
                UserModel.forgetPwdEmail(emailNum,emailCode.toInt(),pwd1,pwd2)
                    .subscribeBy(
                        onNext = {
                            loadingDialogHelper.dismiss()
                            MyToastUtil.toast(getString(com.tbit.uqbike.R.string.s_fix_suc))
                            finish()
                        },
                        onError = {
                            loadingDialogHelper.dismiss()
                            MyToastUtil.toast(ErrHandler.getErrMsg(it))
                        }
                    ).toCancelable()
            }
        }
    }
    private fun checkPhone(phone: String): Boolean {
        if (phone.isEmpty()) {
            toast(com.tbit.uqbike.R.string.phone_number_hint)
            return false
        }
        var result = true
        return result
    }

    private fun checkAuthCode(authCode: String): Boolean {
        if(authCode.isEmpty()) {
            toast(com.tbit.uqbike.R.string.auth_code_can_not_be_empty)
            return false
        }
        return true
    }

    private fun checkCountryCode(countryCode: String): Boolean {
        if(countryCode.isEmpty()) {
            toast(com.tbit.uqbike.R.string.choose_country_code_hint)
            return false
        }
        return true
    }

    private fun checkAgreementTick(): Boolean {
        if (binding.textUserAgreement.visibility == View.VISIBLE && !binding.textUserAgreement.isChecked) {
            toast(getString(com.tbit.uqbike.R.string.str_tick_privacy_and_agreement_tips))
            return false
        }
        return true
    }
    override fun onLoginSuccess() {}
    override fun onLoginError(code: Int) {}
    override fun onGoogleLoginSuccess(isFirst: Int,email: String,IdToken: String,ServerAuthCode: String) {}
    override fun onGetInviteRegisterSwitchSuccess(isOpen: Boolean, user: User, adDeposit: AdDeposit?) {}
    override fun onGetInviteRegisterSwitchFail(user: User, adDeposit: AdDeposit?) { }
    override fun onGetProtocolPassMethodBeforeLoginSuccess(type: Int, forceReadDuration: Int) {}
    override fun onGetProtocolPassMethodSuccess(type: Int, forceReadDuration: Int, user: User, adDeposit: AdDeposit?) {}
    override fun onGetProtocolPassMethodFailed(user: User, adDeposit: AdDeposit?) {}
    override fun onProtocolAgreeOrSign(user: User, adDeposit: AdDeposit?) {}
    override fun onGetProtocolAgreeRecord(isRecord: Boolean, type: Int, user: User, adDeposit: AdDeposit?) {}
    override fun showErrMsg(message: String) {
        loadingDialogHelper?.dismiss()
        toast(message)
    }
}