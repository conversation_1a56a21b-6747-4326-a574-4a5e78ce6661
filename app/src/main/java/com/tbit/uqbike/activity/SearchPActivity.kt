package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.MenuItem
import android.view.View
import androidx.annotation.NonNull
import com.doule.database.CoroutinesUtil
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.maintenance.config.Constant
import com.tbit.tbituser.map.base.MarkerWrapper
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.AdDeposit
import com.tbit.uqbike.bean.ParkPoint
import com.tbit.uqbike.bean.PhoneInfo
import com.tbit.uqbike.databinding.ActivitySearchPactivityBinding
import com.tbit.uqbike.entity.ParkData
import com.tbit.uqbike.entity.VehicleData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.getAreaData
import com.tbit.uqbike.fragment.MainMapFragment
import com.tbit.uqbike.fragment.ParkPointInfoFragment
import com.tbit.uqbike.mvp.constract.MainContract
import com.tbit.uqbike.mvp.presenter.MainPresenter
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.subjects.PublishSubject
import kotlinx.coroutines.delay
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.alert
import org.jetbrains.anko.clearTop
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.singleTop
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.toast
import java.util.concurrent.TimeUnit

class SearchPActivity : BaseActivity(), MainContract.View {

    companion object {
        private const val EXTRA_FROM_LOGIN = "EXTRA_FROM_LOGIN"
        private const val REQUEST_TO_RIDING = 1
        private const val REQUEST_OPEN_GPS = 2

        fun createIntent(context: Context, fromLogin: Boolean? = null): Intent {
            return context.intentFor<MainActivity>(EXTRA_FROM_LOGIN to fromLogin).clearTop().singleTop()
        }
    }
    private val mainMapFragment by lazy { supportFragmentManager.findFragmentById(R.id.main_map_fragment) as MainMapFragment }
    private val geoMapDelegate by lazy { mainMapFragment.geoMapDelegate }
    private val parkPointMapDelegate by lazy { mainMapFragment.parkPointMapDelegate }
    private val prohibitAreaMapDelegate by lazy { mainMapFragment.prohibitAreaMapDelegate }
    private val presenter = MainPresenter(this)
    //    private var loadDataAction :KFunction0<Unit> = ::getBikes
    private val parkPointInfoBehavior by lazy { BottomSheetBehavior.from(supportFragmentManager.findFragmentById(R.id.park_point_info_fragment)!!.requireView()) }
    private var searchMenu: MenuItem? = null
    private var adDeposit = Glob.adDeposit
    private val publish = PublishSubject.create<Int>()
    private val loadDataDisposable = publish.sample(1, TimeUnit.SECONDS)
        .observeOn(AndroidSchedulers.mainThread())
        .subscribeBy(onNext = { loadDataImpl() })
    private var isPassProtocol = false

    private lateinit var binding: ActivitySearchPactivityBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySearchPactivityBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_search_pactivity)
        EventBus.getDefault().register(this);
        lifecycle.addObserver(presenter)

        parkPointInfoBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)
        parkPointMapDelegate.onMarkerClickListener = ::onParkPointClick
        mainMapFragment.onSearchCenterChangeListener = { onSearchCenterChange() }
        mainMapFragment.onMapClickListener = { onMapClick(it) }
        mainMapFragment.onMapLoadListener = { onGetDepositSuccess(Glob.adDeposit) }

        checkGpsEnabled()

        parkPointInfoBehavior.setBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
            override fun onStateChanged(@NonNull bottomSheet: View, newState: Int) {
                //拖动
                if (newState == BottomSheetBehavior.STATE_DRAGGING) {//判断为向下拖动行为时，则强制设定状态为展开
                    parkPointInfoBehavior.setState(BottomSheetBehavior.STATE_EXPANDED );
                }
                if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                    onTopStatesHide()
                }
            }
            override fun onSlide(@NonNull bottomSheet: View, slideOffset: Float) { }
        })
        binding.imageBack.setOnClickListener { finish() }

    }

    private fun checkGpsEnabled() {
        if (!LocationUtil.isGpsEnabled()) {
            alert {
                title = getString(R.string.dialog_tip)
                message = getString(R.string.str_gps_open_hint)
                positiveButton(getString(R.string.open)) {
                    val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                    startActivityForResult(intent, REQUEST_OPEN_GPS)
                }
                isCancelable = false
            }.show()
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        Log.d("dd","==========mainActivity onNewIntent()")
        val fromLogin = intent?.getBooleanExtra(EXTRA_FROM_LOGIN, false)
        if (fromLogin == true) {
            isPassProtocol = true
        }
    }

    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onMsgEvent(event: BaseEventData) {
        MyLogUtil.Log("1111", "==== event message ==="+event.code+","+event.msg)
    }
    override fun onDestroy() {
        loadDataDisposable.dispose()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
        super.onDestroy()
    }

    override fun onGetDepositSuccess(adDeposit: AdDeposit?) {
        this.adDeposit = adDeposit
        MyLogUtil.Log("1111","====地图加载 ========")
        searchMenu?.isVisible = adDeposit?.modelType != Constant.ModelType.PROHIBIT_AREA
        CoroutinesUtil.launchMain {
            delay(600)
            getNearGeo()
            getNearParkPoints()
            getNearParkPointsAndProhibits()
        }
//        loadDataAction()
    }
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.action_search -> startActivity<SearchParkPointActivity>()
            android.R.id.home -> startActivity<MineActivity>()
        }
        return super.onOptionsItemSelected(item)
    }

    private fun onSearchCenterChange() {
        loadData()
    }

    private fun loadData() {
        publish.onNext(1)
    }

    var oldLatlng = LatLng()
    private fun loadDataImpl() {
//        getDeposit()
//        getBookInfo()
        val latLng = mainMapFragment.searchCenter
//        MyLogUtil.Log("1111","地图移动=："+latLng.lat+","+latLng.lng)
        if(GPSUtil.getDistance(oldLatlng.lng,oldLatlng.lat,latLng.lng,latLng.lat) > 10){
            mainMapFragment.IsOutArea()
        }
        if(GPSUtil.getDistance(oldLatlng.lng,oldLatlng.lat,latLng.lng,latLng.lat) > 50){
            oldLatlng = latLng
            parkPointMapDelegate.clear()
            parkPointMapDelegate.select(null)
            getNearGeo()
            getNearParkPoints()
            getNearParkPointsAndProhibits()
        }
    }
    private fun getNearParkPointsAndProhibits() {
        getNearProhibitsImpl(false)
    }
    private fun getNearProhibitsImpl(isProhibitMode: Boolean) {
        val latLng = mainMapFragment.searchCenter
        presenter.getNearProhibits(latLng.lat, latLng.lng)
    }
    private fun onMapClick(latLng: LatLng) {
        if(parkPointInfoBehavior.state == BottomSheetBehavior.STATE_EXPANDED){
            parkPointInfoBehavior.state = BottomSheetBehavior.STATE_HIDDEN
        }
    }

    private fun onTopStatesHide() {
        mainMapFragment.autoSearchCenter()
    }

    private fun onParkPointClick(marker: MarkerWrapper, parkPoint: ParkPoint): Boolean {
        if (!tryLoginIfNot())
            return false
        parkPointMapDelegate.select(marker)
        presenter.getParkPointInfo(parkPoint, mainMapFragment.searchCenter)
        return false
    }

    override fun onGetParkPointInfoSuccess(resultData: ParkData) {
//        routeLine?.let { routeLineMapDelegate.setRouteLine(it) }
        (supportFragmentManager.findFragmentById(R.id.park_point_info_fragment) as ParkPointInfoFragment).setParkPointInfo(resultData)
        mainMapFragment.fixedSearchCenter()
        parkPointInfoBehavior.state = BottomSheetBehavior.STATE_EXPANDED
    }

    private fun getNearGeo() {
        val latLng = mainMapFragment.searchCenter
        MyLogUtil.Log("1111","地理位置："+latLng.lat+","+latLng.lng)
        presenter.getNearGeo(latLng.lat, latLng.lng, 5000)
    }
    override fun onGetNearGeoSuccess(areaData: getAreaData) {
//        if (geoList.isNotEmpty()) geoMapDelegate.setGeo(geoList)
        if (areaData.isNotEmpty()) {
            geoMapDelegate.setGeoNew(areaData)
        }
    }

    private fun getNearParkPoints() {
        val latLng = mainMapFragment.searchCenter
        presenter.getNearParkPoints(latLng.lat, latLng.lng, 500)
    }

    override fun onGetNearParkPointsSuccess(areaData: getAreaData) {
        parkPointMapDelegate.setParkPoints(emptyList())
        if (areaData.isNotEmpty()) {
            parkPointMapDelegate.setPartAreaData(areaData)
        }
    }

    override fun onGetNearProhibitsSuccess(areaData: getAreaData) {
        prohibitAreaMapDelegate.setProhibitAreaData(emptyList())
        prohibitAreaMapDelegate.setProhibitAreaData(areaData)
    }
    override fun onGetNearBikesSuccess(resultData: VehicleData) {}
    override fun onGetPhoneInfoSuccess(phoneInfoList: List<PhoneInfo>?) {}
    override fun onGetUnreadMessageCount(unreadCount: Int) {}

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }
    private fun checkGoogleNetAvailable() {
        presenter.checkGoogleNetAvailable()
    }

    override fun onGoogleNetNotAvailable() {
        alert {
            title = getString(R.string.dialog_tip)
            message = getString(R.string.str_google_net_not_available_tips)
            isCancelable = false
            positiveButton(R.string.i_know){}
        }.show()
    }

}