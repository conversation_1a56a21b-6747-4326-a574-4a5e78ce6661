package com.tbit.uqbike.activity.model

import com.google.gson.Gson
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.map.base.IRouteLine
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.entity.CounrtyNowData
import com.tbit.uqbike.map.bean.Location
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.model.RouteSearchModel
import com.tbit.uqbike.resqmodel.CurrencyModel
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy

object MainRenTalModel {
    /**
     * 获取国家 lld
     */
    fun getCountrylld(onSuccess : (data: String) -> Unit = {}){
        //是否有本地数据
        if (SpUtil.Companion.getInstance().find(Constant.SpKey.SP_COUNTRY).isNullOrEmpty()){
            var location = LocationModel.lastLocation
            var latLng = LatLng()
            try {
                latLng.lat = location!!.latitude
                latLng.lng = location!!.longitude
            }catch (e : NullPointerException){
                location = Location(0f, 0f, 0.0, 0.0, -1,"","")
                latLng.lat = 0.0
                latLng.lng = 0.0
            }
            if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
                latLng = GPSUtil.bd09_To_gps84(location!!.latitude,location!!.longitude)
            }
            latLng.lat = GPSUtil.retain6(latLng.lat)
            latLng.lng = GPSUtil.retain6(latLng.lng)
            CurrencyModel.getCountry(latLng.lat,latLng.lng).subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===当前运营国家 信息=="+ it.toString())
                    val resultData = Gson().fromJson(it.toString(), CounrtyNowData::class.java)
                    if (resultData != null){
                        if (latLng.lat != 0.0 && latLng.lng != 0.0){
                            Glob.CruuencylldReqs = true
                        }
                        Glob.CurrencyUnit = resultData.currency
                        Glob.CurrencyLld = resultData.lld
                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_COUNTRY_LOCAL,Glob.CurrencyLld)
                        if(!resultData.lld.isNullOrEmpty()){
                            SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_COUNTRY_sel, Glob.CurrencyLld);
                        }
                    }
//                    getMainData()
                    onSuccess("")
                }
            ).toCancelable()
        }else{
            MyLogUtil.Log("0000","===当前选择 国家=="+ SpUtil.Companion.getInstance().find(Constant.SpKey.SP_COUNTRY))
            Glob.CurrencyUnit = SpUtil.Companion.getInstance().find(Constant.SpKey.SP_COUNTRY_UNIT).toString()
            Glob.CurrencyLld = SpUtil.Companion.getInstance().find(Constant.SpKey.SP_COUNTRY).toString()
            Glob.CruuencylldReqs = true
//            getMainData()
            onSuccess("")
        }
    }

    fun WalkingSearch(latLngMy : LatLng,latLngTag : LatLng,onSuccess : (data: IRouteLine) -> Unit = {}){
        RouteSearchModel.walkingSearch(latLngMy, latLngTag).subscribeBy(
            onNext = {
                if (it != null){
                    MyLogUtil.Log("1111","===获取骑行路线信息==")
//                    setNavInfoView(it,latLngMy,latLngTag,false,isP)
                    onSuccess(it)
                }
            },
            onError = {
                val errMsg = ErrHandler.getErrMsg(it)
                MyToastUtil.toast(errMsg)
            }
        ).toCancelable()
    }
}