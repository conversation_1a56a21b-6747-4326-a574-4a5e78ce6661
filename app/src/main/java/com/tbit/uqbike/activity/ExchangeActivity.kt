package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import com.google.gson.Gson
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityCostAppealBinding
import com.tbit.uqbike.databinding.ActivityExchangeBinding
import com.tbit.uqbike.entity.ExtendData
import com.tbit.uqbike.entity.GiftCardData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.fragment.CameraPermissionFragment
import com.tbit.uqbike.resqmodel.ExtendModel
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.intentFor

class ExchangeActivity : BaseActivity() {
    companion object {
        private const val EXTRA_TYPE = "EXTRA_TYPE"
        val TYPE_GIFTCARD = 1 // 礼品卡
        val TYPE_EXTEND = 2   // 推广码
        val TYPE_RIDECARD = 3   // 骑行卡
        fun createIntent(context: Context, type: Int? = null): Intent {
            return context.intentFor<ExchangeActivity>(EXTRA_TYPE to type)
        }
    }
    private val type: Int by bindExtra(EXTRA_TYPE)
    private lateinit var binding: ActivityExchangeBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this);
        binding = ActivityExchangeBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_exchange)
        exchangeType = type.toString()
        if(type == TYPE_GIFTCARD){
            binding.appbarLayout.toolbarTitle.text = getString(R.string.gift_card_exchange)
            binding.edit1Exchange.hint = getString(R.string.s_hint_giftcard)
            binding.ryEt2.visibility = View.VISIBLE
            binding.imgExchage.visibility = View.VISIBLE
            binding.buttonConfirm.visibility = View.VISIBLE
        }else if(type == TYPE_EXTEND){
            binding.appbarLayout.toolbarTitle.text = getString(R.string.s_extend)
            binding.edit1Exchange.hint = getString(R.string.s_hint_extend)

            ExtendModel.isExchange()
                .subscribeBy(
                    onNext = {
                        MyLogUtil.Log("1111","======= 推广码是否兑换 ==="+it.toString())
                        var resultData = Gson().fromJson(it.toString(), ExtendData::class.java)
                        if(!resultData.code.isNullOrEmpty()){
                            binding.edit1Exchange.setHint(resultData.code.toString())
                            binding.edit1Exchange.isEnabled = false
                        }else{
                            binding.imgExchage.visibility = View.VISIBLE
                            binding.buttonConfirm.visibility = View.VISIBLE
                        }
                    }
                ).toCancelable()
        }else if(type == TYPE_RIDECARD){
            binding.appbarLayout.toolbarTitle.text = getString(R.string.s_ridecare_exchange_title)
            binding.edit1Exchange.hint = getString(R.string.s_input_ridecardnum)
            binding.ryEt2.visibility = View.VISIBLE
            binding.imgExchage.visibility = View.VISIBLE
            binding.buttonConfirm.visibility = View.VISIBLE
        }
        binding.appbarLayout.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.appbarLayout.toolbar.setNavigationOnClickListener { finish() }

        binding.imgExchage.clickDelay {
            (supportFragmentManager.findFragmentById(R.id.fragment_camera_permission) as CameraPermissionFragment).requestPermission {
                startActivity(ScanForBorrowActivity.createIntent(this, ScanForBorrowActivity.TYPE_EXTEND))
            }
//            (fragment_camera_permission as CameraPermissionFragment).requestPermission {
//                startActivity(ScanForBorrowActivity.createIntent(this, ScanForBorrowActivity.TYPE_EXTEND))
//            }
        }
        binding.edit1Exchange.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                if(type == TYPE_RIDECARD){
                    if (binding.edit1Exchange.text.toString().length > 0 && binding.edit2Exchange.text.toString().length > 0){
                        binding.buttonConfirm.setTextColor(resources.getColor(R.color.white))
                    }else{
                        binding.buttonConfirm.setTextColor(resources.getColor(R.color.white50))
                    }
                }else{
                    if (binding.edit1Exchange.text.toString().length > 0){
                        binding.buttonConfirm.setTextColor(resources.getColor(R.color.white))
                    }else{
                        binding.buttonConfirm.setTextColor(resources.getColor(R.color.white50))
                    }
                }
            }
        })

        binding.edit2Exchange.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                if(type == TYPE_RIDECARD){
                    if (binding.edit1Exchange.text.toString().length > 0 && binding.edit2Exchange.text.toString().length > 0){
                        binding.buttonConfirm.setTextColor(resources.getColor(R.color.white))
                    }else{
                        binding.buttonConfirm.setTextColor(resources.getColor(R.color.white50))
                    }
                }else{
                    if (binding.edit1Exchange.text.toString().length > 0){
                        binding.buttonConfirm.setTextColor(resources.getColor(R.color.white))
                    }else{
                        binding.buttonConfirm.setTextColor(resources.getColor(R.color.white50))
                    }
                }
            }
        })
        binding.buttonConfirm.setOnClickListener {
            inputValue_event = binding.edit1Exchange.text.toString()
            if(type == TYPE_GIFTCARD){
                var cardNum = binding.edit1Exchange.text.toString()
                var cardPwd = binding.edit2Exchange.text.toString()
                if(cardNum.isNullOrEmpty()){
                    MyToastUtil.toast(getString(R.string.s_hint_giftcard))
                    return@setOnClickListener
                }
//                if(cardPwd.isNullOrEmpty()){
//                    MyToastUtil.toast(getString(R.string.s_input_cardpwd))
//                    return@setOnClickListener
//                }
//                MyLogUtil.Log("1111","===提交礼品卡=="+cardNum+","+cardPwd)
                loadingDialogHelper.show {  }
                ExtendModel.sumitExchange(ExtendModel.TYPE_SUMIT_GIFTCARD,cardNum,cardPwd)
                    .subscribeBy(
                        onNext = {
                            loadingDialogHelper.dismiss()
                            MyLogUtil.Log("1111","======= 提交礼品卡 ==="+it.toString())
                            var resultData = Gson().fromJson(it.toString(), GiftCardData::class.java)
                            if (resultData!= null){
                                MyToastUtil.toast(getString(R.string.s_suc_giftcard,AppUtil.getFloat2(resultData.present_amount).toString()+resultData.currency))
                            }else{
                                MyToastUtil.toast(getString(R.string.s_exchange_suc))
                            }
                            finish()
                        },
                        onError = {
                            loadingDialogHelper.dismiss()
                        }
                    ).toCancelable()
            }else if(type == TYPE_EXTEND){
                var cardNum = binding.edit1Exchange.text.toString()
                if(cardNum.isNullOrEmpty()){
                    MyToastUtil.toast(getString(R.string.s_hint_extend))
                    return@setOnClickListener
                }
//                MyLogUtil.Log("1111","===提交推广码=="+cardNum)
                loadingDialogHelper.show {  }
                ExtendModel.sumitExchange(ExtendModel.TYPE_SUMIT_EXTEND,cardNum,"")
                    .subscribeBy(
                        onNext = {
                            loadingDialogHelper.dismiss()
                            MyLogUtil.Log("1111","======= 提交推广码 ==="+it.toString())
                            var resultData = Gson().fromJson(it.toString(), GiftCardData::class.java)
                            if (resultData!= null){
//                                MyToastUtil.toast(getString(R.string.s_suc_giftcard,AppUtil.getFloat2(resultData.present_amount).toString()+resultData.currency))
                                MyToastUtil.toast(getString(R.string.submit_success))
                            }else{
                                MyToastUtil.toast(getString(R.string.submit_success))
                            }
                            finish()
                        },
                        onError = {
                            loadingDialogHelper.dismiss()
                        }
                    ).toCancelable()
            }else if(type == TYPE_RIDECARD){
                var cardNum = binding.edit1Exchange.text.toString()
                var cardPwd = binding.edit2Exchange.text.toString()
                MyLogUtil.Log("1111","===骑行卡兑换==="+cardNum+","+cardPwd)
                loadingDialogHelper.show {  }
                ExtendModel.sumitExchangeRideCard(cardNum,cardPwd)
                    .subscribeBy(
                        onNext = {
                            loadingDialogHelper.dismiss()
                            MyLogUtil.Log("1111","======= 提交骑行卡 ==="+it.toString())
                            MyToastUtil.toast(getString(R.string.s_exchange_suc))
                            finish()
                        },
                        onError = {
                            loadingDialogHelper.dismiss()
                        }
                    ).toCancelable()
            }
        }

    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: BaseEventData) {
        if(event.code == EventUtil.EVENT_EXTENDSCAN){
            binding.edit1Exchange.setText(event.msg)
            binding.edit1Exchange.setSelection(binding.edit1Exchange.text.toString().length)
        }
    }
    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }
}