package com.tbit.uqbike.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.google.gson.Gson
import com.stripe.android.model.ConsumerPaymentDetails.Card.Companion.type
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.databinding.ActivityScanBinding
import com.tbit.uqbike.entity.QRData
import com.tbit.uqbike.entity.ScanData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.fragment.ScanFragment
import com.tbit.uqbike.mvp.model.LastScanInfoModel
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.utils.MachineUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.UrlDecodeUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.toast

class ScanMachineNoActivity : BaseActivity() {

    companion object {
        private val BUNDLE_RESULT = "bundle_machineNO"

        fun getMachineNOFromResult(data: Intent): String? {
            return data.getStringExtra(BUNDLE_RESULT)
        }
    }


    private lateinit var binding: ActivityScanBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityScanBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_scan)
//        setSupportActionBar(toolbar)
//        supportActionBar?.setDisplayShowTitleEnabled(false)
//        toolbar_title.text = getString(R.string.scan)
//        toolbar.setNavigationIcon(R.drawable.icon_back)
//        toolbar.setNavigationOnClickListener { onBackPressed() }
        binding.imgBack.setOnClickListener { finish() }
        try {
            val fragment_scan = (supportFragmentManager.findFragmentById(R.id.fragment_scan) as ScanFragment)
            fragment_scan.ly_manual?.visibility = View.GONE
            (fragment_scan).onScanSuccessListener = ::onScanSuccess
        }catch (e: NullPointerException){}
    }

    private fun onScanSuccess(result: String) {
        val machineNO = MachineUtil.getMachineNOFromQrCode(result)
        if(machineNO == null || machineNO.isNullOrEmpty()) {
//            toast(R.string.qr_code_invalid_hint)
//            (fragment_scan as ScanFragment).startScan()
            loadingDialogHelper.show {  }
            ComModel.sumitQRcode(result).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===二维码扫码 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), QRData::class.java)
                    if (resultData != null){
                        if (resultData.action == 1){
                            startActivity<WebActionActivity>(WebActionActivity.TITLE to "",
                                WebActionActivity.URL to UrlDecodeUtil().getParm(resultData.content),WebActionActivity.IsHideHead to true)
                        }else{
                            vehicle_number = resultData.content
                            val intent = Intent()
                            intent.putExtra(BUNDLE_RESULT, vehicle_number)
                            setResult(Activity.RESULT_OK, intent)
                            finish()
                        }
                    }
                },
                onError = {
                    loadingDialogHelper.dismiss()
                }
            ).toCancelable()
        } else {
//            LastScanInfoModel.uploadLastScanInfo(machineNO)

            if(result.contains(FlavorConfig.NET.EVENT_URL)){
                loadingDialogHelper.show {}
                ComModel.getVechio(machineNO).subscribeBy(
                    onNext = {
                        loadingDialogHelper.dismiss()
                        MyLogUtil.Log("1111","===获取车辆编号 信息=="+it.toString())
                        val resultData = Gson().fromJson(it.toString(), ScanData::class.java)
                        if (resultData.data.contains("80434718")){
                            binding.imgBack.post { MyToastUtil.toast(getString(R.string.s_appdown)) }
                        }else{
                            vehicle_number = resultData.data
                            val intent = Intent()
                            intent.putExtra(BUNDLE_RESULT, vehicle_number)
                            setResult(Activity.RESULT_OK, intent)
                            finish()
                        }
                    },
                    onError = {
                        loadingDialogHelper.dismiss()
                    }
                ).toCancelable()
            }else{
                vehicle_number = machineNO
                val intent = Intent()
                intent.putExtra(BUNDLE_RESULT, vehicle_number)
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }
    }
}