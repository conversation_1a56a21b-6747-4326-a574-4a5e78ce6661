package com.tbit.uqbike.activity

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.LeaveMsgRecordAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityLeaveMsgRecordBinding
import com.tbit.uqbike.dialog.LeaveMsgDialog
import com.tbit.uqbike.entity.Comment
import com.tbit.uqbike.entity.CommentListData
import com.tbit.uqbike.entity.LeaveMsgConData
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.FaultModel
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip
import java.util.Collections

class LeaveMsgRecordActivity  : BaseActivity(){

    private val adapter by lazy { LeaveMsgRecordAdapter(this@LeaveMsgRecordActivity) }
    private lateinit var binding: ActivityLeaveMsgRecordBinding
    var comment_id = 0
    var content_id = 0
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLeaveMsgRecordBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_leave_msg_record)

        binding.toolbars.toolbarTitle.text = getString(R.string.s_leavemsg)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { finish() }

        loadingDialogHelper.show {  }
        FaultModel.getLeaveMsgData().subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===获取留言信息数据 信息=="+ it.toString())
                var resultData = Gson().fromJson(it.toString(), LeaveMsgConData::class.java)
                if (resultData != null){
                    comment_id = resultData.comment_id
                    content_id = resultData.content_id
                    getData()
                }
            },
            onError = {loadingDialogHelper.dismiss()}
        ).toCancelable()

        val spacing = dip(10)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        binding.rcv.layoutManager = LinearLayoutManager(this)
        binding.rcv.adapter = loadMoreWrapper(adapter)

        adapter.setOnItemClickListener {}
        binding.capaLayout.toContent()
//        binding.smartLayout.setOnRefreshListener {
//            loadMore()
//        }

        binding.rcv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val layoutManager = recyclerView.layoutManager as LinearLayoutManager?
                if (layoutManager != null && layoutManager.findFirstVisibleItemPosition() == 0) {
                    // 已经滑动到顶部
//                    MyLogUtil.Log("1111", "================================已经滑动到顶部"+isloadMore)
                    if (isloadMore) loadMore()
                }
            }
        })

        binding.btnLeavemsg.setOnClickListener {
            var leaveDialog = LeaveMsgDialog(this@LeaveMsgRecordActivity)
            leaveDialog.onSucListener = {
                refresh()
            }
            lifecycleDialogHelper.show(leaveDialog)
        }
    }
    private fun refresh(){
        lastLoaded = false
        lastId = 0
        dataList.clear()
        adapter.source = dataList
//        adapter.notifyDataSetChanged()
        getData(true)
    }
    private fun loadMore(){
        //        if (!lastLoaded)
        getData(false)
    }
    private fun loadMoreWrapper(adapter: RecyclerView.Adapter<out RecyclerView.ViewHolder>)
            : LoadMoreWrapper<out RecyclerView.ViewHolder> {
        val loadMoreAdapter = LoadMoreWrapper(adapter)
        loadMoreAdapter.setOnLoadMoreListener{ onLoadMore() }
        return loadMoreAdapter
    }

    private fun onLoadMore() {
//        if (!lastLoaded)
//            getData(false)
    }
    private var isloadMore = false //是否可以加载更多
    private var lastLoaded = false
    private val loadMoreView by lazy { View(this).apply { layoutParams = ViewGroup.LayoutParams(1, 1) } }
    val dataList = mutableListOf<Comment>()
    var lastId = 0
    private val pageSize = Glob.pageNum
    private fun getData(showLoading: Boolean = true) {
        if (showLoading) {
            loadingDialogHelper.show { }
        }else{
            isloadMore = false
        }
        ComModel.getComment(ComModel.commentType_leaveMsg, content_id.toString(),lastId,pageSize).subscribeBy(
            onNext = {
//                binding.smartLayout.finishRefresh()
                MyLogUtil.Log("1111","===获取留言列表 信息=="+ it.toString())
                var resultData = Gson().fromJson(it.toString(), CommentListData::class.java)
                if (resultData != null && resultData.size > 0){
                    lastLoaded = resultData.size < pageSize

                    Collections.reverse(resultData)

                    dataList.addAll(0,resultData)
                    lastId = resultData.first().id

                    (binding.rcv.adapter as? LoadMoreWrapper<*>)?.setLoadMoreView(if (lastLoaded) null else loadMoreView)
                    adapter.source = dataList
                    if (showLoading){
                        adapter.notifyDataSetChanged()
//                        binding.rcv.layoutManager?.smoothScrollToPosition(binding.rcv,null,adapter.source.size - 1)
                        binding.rcv.layoutManager?.scrollToPosition(adapter.source.size - 1)
                    }else{
                        adapter.notifyItemRangeInserted(0,resultData.size)
                    }
                    binding.capaLayout.toContent()
                    isloadMore = true

//                    if (lastLoaded) MyToastUtil.toast(getString(R.string.no_more_tips))
                }else{
                    lastLoaded = true
                    if (adapter.source.size == 0) binding.capaLayout.toEmpty()
                }
                loadingDialogHelper.dismiss()
            },
            onError = {
                loadingDialogHelper.dismiss()
                isloadMore = true
//                binding.smartLayout.finishRefresh()
            }
        ).toCancelable()
    }
    override fun onDestroy() {
        super.onDestroy()
    }
}