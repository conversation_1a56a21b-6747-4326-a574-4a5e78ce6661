package com.tbit.uqbike.activity

import android.os.Bundle
import android.view.View
import com.google.gson.Gson
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityWalletBinding
import com.tbit.uqbike.entity.MyWallet
import com.tbit.uqbike.entity.RefundRecordData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.resqmodel.RefundModel
import com.tbit.uqbike.resqmodel.WalletModel
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.startActivity
import org.json.JSONObject

class WalletActivity: BaseActivity() {

    companion object {
        private const val REQUEST_DEPOSIT = 1
        private const val REQUEST_CHARGE = 2
        private const val REQUEST_GIFT_CARD = 3
        private const val REQUEST_BUY_RIDE_CARD = 4
    }
    private lateinit var binding: ActivityWalletBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWalletBinding.inflate(layoutInflater)
        setContentView(binding.root)
        EventBus.getDefault().register(this);
//        setContentView(R.layout.activity_wallet)
        setSupportActionBar(binding.toolbars.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        binding.toolbars.toolbarTitle.text = getString(R.string.wallet)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { onBackPressed() }

        binding.buttonCharge.clickDelay {
            startActivity<ChargeNewActivity>()
            val properties = JSONObject()
            MDUtil.clickEvent("wallet_recharge",properties)
        }
        binding.ryWalletWalletbalance.setOnClickListener {
            startActivity<WalletBalanceActivity>()
            val properties = JSONObject()
            MDUtil.clickEvent("balance_click",properties)
        }
        binding.ryWalletWalletpresent.setOnClickListener {
            startActivity<WalletPersentActivity>()
            val properties = JSONObject()
            MDUtil.clickEvent("gift_click",properties)
        }
        binding.tvRefunResq.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("request_refund",properties)
            startActivity<RefundActivity>()
        }

    }
    fun getRefund(){
        RefundModel.getRefundState()
            .subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","======= 获取是否支持退款 信息 ==="+ it.toString())
//                    var resultData = Gson().fromJson(it.toString(), RefundRecordData::class.java)
                    if (it != null && it.toString().contains("true")){
                        binding.tvRefunResq.visibility = View.VISIBLE
                    }
                },
                onError = {}
            ).toCancelable()
    }
    fun getMyWallet(isShowLoading : Boolean){
        if(isShowLoading){
            loadingDialogHelper!!.show {  }
        }
        WalletModel.getMywallet()
            .subscribeBy(
                onNext = {
                    loadingDialogHelper!!.dismiss()
                    MyLogUtil.Log("1111","======= 获取我的余额信息 ==="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), MyWallet::class.java)
                    wallbalance_event = AppUtil.getFloat2Double(resultData.balance).toString()
                    giftbalance_event = AppUtil.getFloat2Double(resultData.present).toString()
                    binding.textTotal.text = AppUtil.getStr_small(AppUtil.getFloat2Double(resultData.balance + resultData.present).toString()+resultData.currency,
                        resultData.currency.length)
//                    binding.textTotalUnit.text = resultData.currency
                    binding.textBalance.text = AppUtil.getStr_small(AppUtil.getFloat2Double(resultData.balance).toString()+resultData.currency,resultData.currency.length)
                    binding.textBalanceUnit.text = resultData.currency
                    binding.textCard.text = AppUtil.getStr_small(AppUtil.getFloat2Double(resultData.present).toString()+resultData.currency,resultData.currency.length)
                    binding.textCardUnit.text = resultData.currency
                    binding.tvWalletCash.text = AppUtil.getFloat2(resultData.guarantee_balance).toString()
                    binding.tvWalletCashunit.text = resultData.currency
                    if(resultData.unusable_present != 0f){
                        binding.rvMywalletUuse.visibility = View.VISIBLE
                        binding.rvMywalletUuse.text = AppUtil.getFloat2(resultData.unusable_present).toString() +
                                resultData.currency+ getString(R.string.s_nuse)
                    }else{
                        binding.rvMywalletUuse.visibility = View.INVISIBLE
                    }
                },
                onError = {
                    loadingDialogHelper!!.dismiss()
                }
            ).toCancelable()
    }
    override fun onResume() {
        super.onResume()
        getMyWallet(false)
        getRefund()
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: BaseEventData) {
        if (event.code == EventUtil.EVENT_HOME) finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }

//    override fun onGetDepositSuccess(adDeposit: AdDeposit?) {
//        loadingDialogHelper.dismiss()
//        this.adDeposit = adDeposit
//        val adDeposit = adDeposit ?: return
//        text_total.text = String.format("%.2f", MoneyUtil.standard2Show(adDeposit.totalMoney))
//        text_balance.text = String.format("%.2f", MoneyUtil.standard2Show(adDeposit.actualMoney))
//        text_card.text = String.format("%.2f", MoneyUtil.standard2Show(adDeposit.cardMoney))
//        updateDepositMoneyUI(adDeposit)
//        updateDepositStateUI(adDeposit)
//        if (adDeposit.showRefund == 1 && MoneyUtil.standard2Show(adDeposit.actualMoney) > 0) {
////            text_refund.visibility = View.VISIBLE
//        } else {
////            text_refund.visibility = View.GONE
//        }
//    }

}