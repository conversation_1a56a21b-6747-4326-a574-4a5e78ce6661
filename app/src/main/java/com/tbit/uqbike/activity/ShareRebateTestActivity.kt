package com.tbit.uqbike.activity

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import com.tbit.uqbike.R

/**
 * 分享返利功能测试Activity
 */
class ShareRebateTestActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_share_rebate_test)
        
        setupViews()
    }
    
    private fun setupViews() {
        // 测试本地HTML页面
        findViewById<Button>(R.id.btn_test_local_html).setOnClickListener {
            val intent = Intent(this, WebActionActivity::class.java).apply {
                putExtra(WebActionActivity.URL, "file:///android_asset/test_share_rebate.html")
                putExtra(WebActionActivity.TITLE, "分享返利测试")
                putExtra(WebActionActivity.IsHideHead, false)
            }
            startActivity(intent)
        }
        
        // 直接测试分享弹窗
        findViewById<Button>(R.id.btn_test_dialog_directly).setOnClickListener {
            testShareDialogDirectly()
        }
    }
    
    private fun testShareDialogDirectly() {
        // 创建测试数据
        val shareData = com.tbit.uqbike.entity.ShareData(
            url = "https://example.com/product/123?id=user123&username=testuser",
            text = "推荐一款超棒的智能电动车，邀请好友购买还能赚佣金！",
            product_id = 123,
            rebates = listOf("10", "5", "3", "2", "1"),
            all_rebate = 21
        )
        
        // 显示分享弹窗
        val shareDialog = com.tbit.uqbike.dialog.ShareRebateDialog(shareData)
        shareDialog.setOnShareResultListener { success, platform ->
            android.util.Log.d("ShareTest", "Share result: success=$success, platform=$platform")
        }
        
        shareDialog.show(supportFragmentManager, "ShareRebateDialog")
    }
}
