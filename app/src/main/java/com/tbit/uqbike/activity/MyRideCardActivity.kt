package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.MyRideCardAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityMyRideCardBinding
import com.tbit.uqbike.dialog.RefundDialog
import com.tbit.uqbike.entity.MyRideCardData
import com.tbit.uqbike.entity.MyRideCardDataItem
import com.tbit.uqbike.entity.getRideCardPerson
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.model.RideCardModel
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.RefundModel
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.NetUtils
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip
import org.jetbrains.anko.intentFor

class MyRideCardActivity : BaseActivity() {
    private val adapter by lazy { MyRideCardAdapter() }
    private var lastLoaded = false
    private val areaListData : HashMap<Int,String> by bindExtra(EXTRA_AREA)
    private val loadMoreView by lazy { View(this).apply { layoutParams = ViewGroup.LayoutParams(1, 1) } }
    companion object {
        private const val EXTRA_AREA = "EXTRA_AREA"
        private const val EXTRA_TYPE = "normal"
        fun createIntent(context: Context, areaListData : HashMap<Int,String>): Intent {
            return context.intentFor<MyRideCardActivity>(
                EXTRA_AREA to areaListData,
            )
        }
    }
    private lateinit var binding: ActivityMyRideCardBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMyRideCardBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_my_ride_card)

        binding.toolbars.toolbarTitle.text = getString(R.string.s_myridecard)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { finish() }

        binding.rcv.layoutManager = LinearLayoutManager(this)
        val spacing = dip(10)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        binding.rcv.adapter = loadMoreWrapper(adapter)
        adapter.onLoseCardListener = {
            startActivity(LoseRideCardActivity.createIntent(this, areaListData))
        }
        adapter.onRefundListener = {ride_card_no->
            RefundDialog.Builder(this@MyRideCardActivity).setTitle("").
            setContent(getString(R.string.s_sure_refund))
                .setLeftText(getString(R.string.s_think)).setRightText(getString(R.string.confirm)).setCanceledOnOutside(true)
                .setClickListen(object : RefundDialog.TwoSelDialog {
                    override fun leftClick() {}
                    override fun rightClick() {
                        loadingDialogHelper.show { false }
                        RefundModel.getRideCardPerson(ride_card_no).subscribeBy(
                            onNext = {
                                loadingDialogHelper!!.dismiss()
                                MyLogUtil.Log("1111","===获取是否需要提交银行卡信息 信息=="+it.toString())
                                var resultData = Gson().fromJson(it.toString(), getRideCardPerson::class.java)
                                if (resultData.need_bank_info){
                                    startActivity(RefundPersonInfoActivity.createIntent(this@MyRideCardActivity,ComModel.FormType_Refund_RideCard,ride_card_no))
                                }else{
                                    startActivity(RefunReasonActivity.createIntent(this@MyRideCardActivity,ComModel.FormType_Refund_RideCard,
                                        ride_card_no,"","",""))
                                }
                            },
                            onError = {
                                loadingDialogHelper!!.dismiss()
                                MyToastUtil.toast(ErrHandler.getErrMsg(it))
                            }
                        ).toCancelable()
                    }
                }).build().show()
        }
        findViewById<LinearLayout>(R.id.ly_ndata_card).setOnClickListener {
            startActivity(LoseRideCardActivity.createIntent(this, areaListData))
        }
        if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
            if (!NetUtils.isConnected(ContextUtil.getContext())){
                setErrorView(true)
            }else{
                setErrorView(false)
            }
        }
    }
    fun setErrorView(isNetError : Boolean){
//        MyLogUtil.Log("1111","======isNetError======"+isNetError)
        if(isNetError){
            if (NetUtils.isConnected(ContextUtil.getContext())){
                binding.capaLayout.toEmpty()
                return
            }
        }
        if (isNetError){
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_net).visibility = View.VISIBLE
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_local).visibility = View.GONE
        }else{
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_net).visibility = View.GONE
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_local).visibility = View.VISIBLE
        }
        binding.capaLayout.toError()
        binding.capaLayout.findViewById<RoundTextView>(R.id.tv_err_set_net).setOnClickListener{
            val intent = Intent(Settings.ACTION_DATA_ROAMING_SETTINGS)
            startActivity(intent)
        }
        binding.capaLayout.findViewById<RoundTextView>(R.id.tv_err_set_local).setOnClickListener{
            val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
            startActivityForResult(intent, MainActivity.REQUEST_OPEN_GPS)
        }
    }
    override fun onResume() {
        super.onResume()
        getMyRideCard()
    }
    private fun loadMoreWrapper(adapter: RecyclerView.Adapter<out RecyclerView.ViewHolder>): LoadMoreWrapper<out RecyclerView.ViewHolder> {
        val loadMoreAdapter = LoadMoreWrapper(adapter)
        loadMoreAdapter.setOnLoadMoreListener{ onLoadMore() }
        return loadMoreAdapter
    }
    private fun onLoadMore() {
        if (!lastLoaded)
            getMyRideCard(false)
    }
    var stateSup = 0
    private fun getMyRideCard(showLoading: Boolean = true) {
        if (!(!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null)) {
            adapter.source = ArrayList<MyRideCardDataItem>()
            adapter.notifyDataSetChanged()
            RideCardModel.getMyRideCardNew(EXTRA_TYPE)
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper!!.dismiss()
                        MyLogUtil.Log("1111","===获取我的骑行卡信息=="+it.toString())
                        val resultData = Gson().fromJson(it.toString(), MyRideCardData::class.java)
                        if(resultData != null && resultData.size > 0){
                            resultData.forEach{
                                it.areaName = areaListData.get(it.area_id).toString()
                                if (it.is_support == 1 && stateSup == 0){
                                    stateSup = 1
                                    it.isSupportHint = getString(R.string.s_area_use)
                                }else{
                                    if(stateSup == 0 || stateSup == 1){
                                        if(it.is_support == 0){
                                            stateSup = 2
                                            it.isSupportHint = getString(R.string.s_area_uuse)
                                        }
                                    }
                                }
                            }
                            adapter.source = resultData
                            adapter.notifyDataSetChanged()
                        }
                        if (adapter.source.isEmpty()) binding.capaLayout.toEmpty() else binding.capaLayout.toContent()
                    },
                    onError = {
                        loadingDialogHelper.dismiss()
                        setErrorView(true)
                    }
                ).toCancelable()
            if (showLoading) {
                loadingDialogHelper.show { }
            }
        }
    }
}