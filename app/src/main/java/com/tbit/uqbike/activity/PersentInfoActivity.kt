package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.WalletBalanceAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityPersentInfoBinding
import com.tbit.uqbike.entity.Item
import com.tbit.uqbike.entity.WalletBalanceData
import com.tbit.uqbike.resqmodel.WalletModel
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.startActivity

class PersentInfoActivity : BaseActivity() {
    companion object {
        private const val EXTRA_FROM_NAME = "EXTRA_FROM_NAME"
        private const val EXTRA_FROM_AREAID = "EXTRA_FROM_AREAID"
        fun createIntent(context: Context, name: String? = null, areaid: Int? = null): Intent {
            return context.intentFor<PersentInfoActivity>(EXTRA_FROM_NAME to name,EXTRA_FROM_AREAID to areaid)
        }
    }
    private val name: String by bindExtra(EXTRA_FROM_NAME)
    private val areaid: Int by bindExtra(EXTRA_FROM_AREAID)

    private var lastId = 0
    private val pageSize = Glob.pageNum
    private var lastLoaded = false
    private val adapter = WalletBalanceAdapter(WalletBalanceAdapter.TYPE_VIEW_PRESENT)
    private val listdata = mutableListOf<Item>()
    private val loadMoreView by lazy { View(this).apply { layoutParams = ViewGroup.LayoutParams(1, 1) } }
    private lateinit var binding: ActivityPersentInfoBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPersentInfoBinding.inflate(layoutInflater)
        setContentView(binding.root)

//        setContentView(R.layout.activity_wallet_balance)

        binding.layoutToolbar.toolbarTitle.text = name
        binding.layoutToolbar.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.layoutToolbar.toolbar.setNavigationOnClickListener { finish() }

        listdata.add(Item(0.0,0,0,0,""))//添加头部
        binding.rcv.layoutManager = LinearLayoutManager(this)
        binding.rcv.adapter = loadMoreWrapper(adapter)
        val spacing = dip(1)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        adapter.onGoPayListener = {
            startActivity<ChargeNewActivity>()
        }

        getListData()
    }
    private fun loadMoreWrapper(adapter: RecyclerView.Adapter<out RecyclerView.ViewHolder>)
            : LoadMoreWrapper<out RecyclerView.ViewHolder> {
        val loadMoreAdapter = LoadMoreWrapper(adapter)
        loadMoreAdapter.setOnLoadMoreListener{ onLoadMore() }
        return loadMoreAdapter
    }
    private fun onLoadMore() {
        if (!lastLoaded)
            getListData(false)
    }
    private fun getListData(showLoading: Boolean = true) {
        WalletModel.getPersentBalanceInfo(areaid,lastId,pageSize)
            .subscribeBy(
                onNext = {
                    loadingDialogHelper!!.dismiss()
                    MyLogUtil.Log("1111","======= 获取赠送余额 流水信息 ==="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), WalletBalanceData::class.java)
                    lastLoaded = resultData.items.size < pageSize
                    if(resultData.items.size > 0){
                        lastId = resultData.items.last().id
                    }
                    (binding.rcv.adapter as? LoadMoreWrapper<*>)?.setLoadMoreView(if (lastLoaded) null else loadMoreView)
                    listdata.addAll(resultData.items)
                    adapter.source = listdata
                    adapter.setWalletBalance(resultData.amount,resultData.currency)
                    adapter.notifyDataSetChanged()
                    if (adapter.source.isEmpty()) binding.capaLayout.toEmpty() else binding.capaLayout.toContent()
                },
                onError = {
                    loadingDialogHelper!!.dismiss()
                }
            ).toCancelable()
        if (showLoading) {
            loadingDialogHelper.show { }
        }
    }
}