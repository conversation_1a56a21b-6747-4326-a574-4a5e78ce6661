package com.tbit.uqbike.activity;

import android.os.Bundle;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.baidu.mapapi.bikenavi.BikeNavigateHelper;
import com.baidu.mapapi.bikenavi.adapter.IBEngineInitListener;
import com.tbit.uqbike.utils.MyLogUtil;

public class NaviActivity extends AppCompatActivity {
    private BikeNavigateHelper mNaviHelper = null;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setContentView(R.layout.activity_navi);

        //获取BikeNavigateHelper示例
        mNaviHelper = BikeNavigateHelper.getInstance();
        // 获取诱导页面地图展示View
        View view = mNaviHelper.onCreate(NaviActivity.this);

        if (view != null) {
            setContentView(view);
        }
        // 开始导航
        mNaviHelper.startBikeNavi(NaviActivity.this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        mNaviHelper.resume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mNaviHelper.pause();
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        mNaviHelper.quit();
    }
}