package com.tbit.uqbike.activity

import android.app.Activity
import android.content.Intent
import android.content.Intent.ACTION_VIEW
import android.graphics.Bitmap
import android.net.Uri
import android.net.http.SslError
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.webkit.JavascriptInterface
import android.webkit.SslErrorHandler
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.databinding.ActivityWebBinding
import com.tbit.uqbike.fragment.PayFragment
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil


class WebActivity : BaseActivity() {

    companion object {
        val TITLE: String = "title"
        val URL: String = "url"
        val HIDE_TITLE: String = "hide_title"
        val IS_PAY: String = "is_pay"
        val IS_USE_CACHE: String = "is_use_cache"
        val IS_USE_NEW: String = "IS_USE_NEW"
        val se_type: String = "se_type" //埋点 页面来源 1充值页,2购买骑行卡，3长租下单页，4骑车页换电，5其他

//        fun createIntent(context: Context, title:String, url:String,hide_title:Boolean,is_pay:Boolean,is_use_cache:String): Intent {
//            return context.intentFor<WebActivity>(TITLE to title, URL to url, HIDE_TITLE to hide_title,
//                IS_PAY to is_pay, IS_USE_CACHE to is_use_cache)
//        }
    }

    private val title: String by bindExtra(TITLE)
    private val urldata: String by bindExtra(URL)
    private val hideTitle: Boolean? by bindExtra(HIDE_TITLE)
    private val isPay: Boolean? by bindExtra(IS_PAY)
    private val isUseCache: Boolean? by bindExtra(IS_USE_CACHE)
    private val isUserNew: Boolean? by bindExtra(IS_USE_NEW)
    private val seType: String? by bindExtra(se_type)
    private var headData = ""
    private lateinit var binding: ActivityWebBinding
    private var url = ""
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWebBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_web)
        if (urldata.contains("?")){
            url = urldata+"&localtime="+System.currentTimeMillis()
        }else{
            url = urldata+"?localtime="+System.currentTimeMillis()
        }
        if (!url.contains("locale=")){
            url = url+"&locale="+ FlavorConfig.Local.language
        }
        if (seType.isNullOrEmpty()){
            url = url+""
        }else {
            url = url + "&se_type=" + seType
        }
        webUrl = url
        MyLogUtil.Log("1111","==== 地址==="+url)
        headData = FlavorConfig.NET.COM_URL
        headData = headData.substring(0,headData.length-1)
        initView()
        initWebView()
    }

    private fun initView() {
        if (hideTitle == true) {
            binding.layoutToolbar.lyLayoutToolbar.visibility = View.GONE
            return
        }

        binding.layoutToolbar.toolbarTitle.text = title
        if (isUserNew != null){
            MyLogUtil.Log("1111","====usernew==="+isUserNew)
            if (isUserNew!!){
                AutoTask()
            }
        }else{
            MyLogUtil.Log("1111","====usernew null===")
            binding.layoutToolbar.toolbar.setNavigationIcon(R.drawable.icon_back)
            binding.layoutToolbar.toolbar.setNavigationOnClickListener {
                setResult(Activity.RESULT_OK)
                finish()
            }
        }
    }
    private var AutoTime = 5;
    fun AutoTask(){
        if (binding.webView != null){
            binding.layoutToolbar.toolbarTitleL.visibility = View.VISIBLE
            binding.layoutToolbar.toolbarTitleL.text = AutoTime.toString()+"S"
            binding.webView.postDelayed({
                if (binding.webView != null){
                    if (AutoTime > 0){
                        AutoTime = AutoTime - 1
                        AutoTask()
                    }else{
                        binding.layoutToolbar.toolbarTitleL.visibility = View.GONE
                        binding.layoutToolbar.toolbarTitleL.text = ""
                        binding.layoutToolbar.toolbar.setNavigationIcon(R.drawable.icon_back)
                        binding.layoutToolbar.toolbar.setNavigationOnClickListener {
                            setResult(Activity.RESULT_OK)
                            finish()
                        }
                    }
                }
            },1000)
        }
    }

    private fun initWebView() {
        /* 设置javascript */
        val settings = binding.webView.settings
        settings.domStorageEnabled = true
        settings.javaScriptEnabled = true
        settings.allowUniversalAccessFromFileURLs = true
        settings?.setAllowUniversalAccessFromFileURLs(true);
//        if (isUseCache == null || isUseCache == false) {
//            // 不使用缓存
//            webView.clearCache(true)
//            webView.settings.cacheMode = WebSettings.LOAD_NO_CACHE
//        }
        settings?.cacheMode = WebSettings.LOAD_DEFAULT
        //允许混合模式，https当中加载http资源
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP) {
            binding.webView.settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        }
        binding.webView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        binding.webView.overScrollMode = View.OVER_SCROLL_NEVER
        /* URL */
        /** 防盗链 */
//        val extraHeaders = HashMap<String, String>()
//        extraHeaders.put("Referer", url)
//        webView.loadUrl(url, extraHeaders)

        var headers: MutableMap<String?, String?>? = HashMap()
        headers!!["Referer"] = headData
        binding.webView.loadUrl(url,headers)
        binding.webView.webViewClient = object : WebViewClient() {
            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                MyLogUtil.Log("1111","==========onPageStarted，url=$url")
                if (url!!.contains(PayFragment.PAYGOBACK) || url.equals(FlavorConfig.NET.COM_URL)) {//paypay支付网页授权重定向返回
                    finish()
                }
            }

            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                var url = ""
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    url = request?.getUrl().toString()
                } else {
                    url = request.toString();
                }
                MyLogUtil.Log("1111","==========WebResourceRequest，url=$url")

                if (url.contains(PayFragment.PAYGOBACK) || url.equals(FlavorConfig.NET.COM_URL)) {//paypay支付网页授权重定向返回
                    finish()
                    return true
                }
                return super.shouldOverrideUrlLoading(view, request)
            }
            override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                MyLogUtil.Log("1111","==========shouldOverrideUrlLoading，url=$url")

                val shouldOverrideUrl = !url.startsWith("https://") && !url.startsWith("http://")
                if(shouldOverrideUrl) {
                    try {
                        val intent: Intent
                        if (url.startsWith("intent://") && url.contains("scheme")) {
                            intent = Intent.parseUri(url, 0)
                        } else {
                            intent = Intent(ACTION_VIEW, Uri.parse(url))
                        }
                        startActivity(intent)
                    } catch (e: Exception) {
                        MyToastUtil.toast(getString(R.string.s_noapp))
                        e.printStackTrace()
                    }
                }
                return shouldOverrideUrl
            }

            override fun onReceivedSslError(view: WebView, handler: SslErrorHandler, error: SslError) {
//                if (handler != null) {
//                    handler.proceed();//忽略证书的错误继续加载页面内容，不会变成空白页面
//                }
                super.onReceivedSslError(view, handler, error)
            }

            override fun shouldInterceptRequest(view: WebView?, request: WebResourceRequest?): WebResourceResponse? {
                // 获取原始请求头部信息
                val extraHeaders = request!!.requestHeaders
//                MyLogUtil.Log("1111","====web toubu === "+headData)
                // 添加额外的头部信息
                extraHeaders["Referer"] = headData
                return super.shouldInterceptRequest(view, request)
            }
            override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                super.onReceivedError(view, request, error)
            }
        }

        binding.webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                Log.d("dd","============网页加载进度=$newProgress")
                binding.progressBar ?: return //防止页面已销毁后回调，控件引用发生空指针异常
                if (newProgress == 100) {
//                    binding.progressBar.visibility = View.GONE
                    binding.progressBar.progress = newProgress
                    binding.progressBar.postDelayed({
                           if (binding.progressBar != null) binding.progressBar.visibility = View.GONE
                    },500)
                } else {
                    binding.progressBar.visibility = View.VISIBLE
                    binding.progressBar.progress = newProgress
                }
            }
        }

        binding.webView.addJavascriptInterface(WebViewJavaScriptInterface(),"obj")
    }

    override fun onBackPressed() {
        if (isUserNew != null){
            if (isUserNew!!){
                if (AutoTime != 0) return
            }
        }
        if (isPay == true) {
            setResult(Activity.RESULT_OK)
            super.onBackPressed()
        } else {
            if (binding.webView.canGoBack()) {
                binding.webView.goBack()
            } else {
                setResult(Activity.RESULT_OK)
                super.onBackPressed()
            }
        }
    }

    inner class WebViewJavaScriptInterface {

        @JavascriptInterface
        fun jsPageClosed() {
            Log.d("dd","===========jsPageClosed")
            setResult(Activity.RESULT_OK)
            finish()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        LanguageUtil().settingLanguage(ContextUtil.getContext())
    }
}
