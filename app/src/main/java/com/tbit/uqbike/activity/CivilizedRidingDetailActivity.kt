package com.tbit.uqbike.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.LifecycleEventDispatcher
import com.tbit.maintenance.utils.bindExtra
import com.tbit.preview.ImageInfo
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.tbituser.map.listener.OnMapLoadedCallback
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.SelectPhotoAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.CivilizedRidingRecord
import com.tbit.uqbike.bean.Track
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.delegate.PolylineMapDelegate
import com.tbit.uqbike.map.bean.Location
import com.tbit.uqbike.mvp.constract.CivilizedRidingDetailContract
import com.tbit.uqbike.mvp.model.CivilizedRidingModel
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.presenter.CivilizedRidingDetailPresenter
import org.jetbrains.anko.dip
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.toast

/**
 * 非文明骑行记录详情
 */
class CivilizedRidingDetailActivity : BaseActivity(), OnMapLoadedCallback, CivilizedRidingDetailContract.View {

    companion object {
        private const val REQUEST_CODE = 1
        private const val EXTRA_DATA = "EXTRA_DATA"

        fun createIntent(context: Context, record: CivilizedRidingRecord): Intent {
            return context.intentFor<CivilizedRidingDetailActivity>(EXTRA_DATA to record)
        }
    }

    private val presenter = CivilizedRidingDetailPresenter(this)
    private val record: CivilizedRidingRecord by bindExtra(EXTRA_DATA)
    private var map: IBaseMap? = null
    private val polylineMapDelegate =  PolylineMapDelegate()
    private var data: CivilizedRidingRecord? = null
    private val eventDispatcher = LifecycleEventDispatcher()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_civilized_riding_detail)
//        setSupportActionBar(toolbar)
//        supportActionBar?.setDisplayShowTitleEnabled(false)
//        toolbar_title.text = getString(R.string.uncivilized_riding_record)
//        toolbar.setNavigationIcon(R.drawable.icon_back)
//        toolbar.setNavigationOnClickListener { onBackPressed() }
//
//        lifecycle.addObserver(presenter)
//
//        val map = FlavorConfig.mapFactory.createMap(fl_map, savedInstanceState)
//        this.map = map
//        map.setOnMapLoadedCallback(this)
//        polylineMapDelegate.setMap(map)
//
//        btn_appeal.setOnClickListener {
//            data?.let {
//                startActivityForResult(CivilizedRidingAppealActivity.createIntent(this, it),REQUEST_CODE)
//            }
//        }
//        getData()
    }

    private fun getData() {
        val cancellable = presenter.getData(record.rideId, record.lat, record.lon)
        loadingDialogHelper.show { cancellable.cancel() }
    }

    override fun onGetDataSuccess(data: CivilizedRidingRecord?) {
        loadingDialogHelper.dismiss()
        this.data = data
        if (data == null) {
            toast(R.string.data_is_null)
            return
        }
//        tv_order_no.text = data.orderNo
//        handleAppealState(data)
//        tv_time.text = data.formatAddTime
//
//        tv_type.text = CivilizedRidingModel.getTypeStr(data.type)
//        val urls = mutableListOf<String>()
//        data.photos?.forEach {
//            it.ossUrl?.let { urls.add(it) }
//        }
//        showSnapshotPic(urls)
//        eventDispatcher.putEvent(1, ::getHistoryTrack)
    }

    private fun handleAppealState(data: CivilizedRidingRecord) {
//        tv_appeal_status.setTextColor(resources.getColor(R.color.dark_grey))
//        if (data.state != null && data.state != Constant.UncivilizedRidingAppealState.NOT_APPEAL) {
//            btn_appeal.text = getString(R.string.appeal_detail)
//        }
//        when (data.state) {
//            Constant.UncivilizedRidingAppealState.NOT_APPEAL -> {
//                tv_appeal_status.text = getString(R.string.not_appeal)
//            }
//            Constant.UncivilizedRidingAppealState.APPEALING -> {
//                tv_appeal_status.text = getString(R.string.appealing)
//            }
//            Constant.UncivilizedRidingAppealState.APPEALED -> {
//                when (data.rspRet) {
//                    Constant.UncivilizedRidingAppealResult.SUCCESS -> {
//                        tv_appeal_status.text = getString(R.string.appeal_success)
//                        tv_appeal_status.setTextColor(resources.getColor(R.color.light_green))
//                    }
//                    Constant.UncivilizedRidingAppealResult.FAILED -> {
//                        tv_appeal_status.text = getString(R.string.appeal_failed)
//                        tv_appeal_status.setTextColor(resources.getColor(R.color.light_red))
//                    }
//                    else -> tv_appeal_status.text = getString(R.string.handling)
//                }
//            }
//            else -> tv_appeal_status.text = getString(R.string.unmatched_type_str_hint)
//        }
    }

    private fun showSnapshotPic(urls: List<String>) {
//        val spacing = dip(3)
//        val spanCount = 5
//        val maxCount = 3
//        val photos = mutableListOf<ImageInfo>()
//        urls.forEach {
//            photos.add(ImageInfo().apply { bigImageUrl = it })
//        }
//        val adapter = SelectPhotoAdapter(this, spanCount, maxCount, photos,"")
//        adapter.editable = false
//        rcv_image.adapter = adapter
//        rcv_image.layoutManager = GridLayoutManager(this, spanCount)
//        rcv_image.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
    }

    override fun onGetAddressSuccess(address: String?) {
        if (address.isNullOrEmpty()) {
            toast(R.string.load_place_failed)
            return
        }
//        tv_place.text = address
    }

    override fun onMapLoaded() {
        eventDispatcher.onStart()
    }

    private fun getHistoryTrack() {
        data?.let {
            presenter.getHistoryTrack(it.machineId, it.formatStartTime, it.formatEndTime)
        }
    }

    override fun onGetHistoryTrackSuccess(tracks: List<Track>) {
        if (tracks.size < 2) {
            toast(R.string.no_locating_points)
            showCurrentLocate()
        } else {
            polylineMapDelegate.setPoints(tracks.map { LatLng(it.latC, it.lonC) },false)
            polylineMapDelegate.fitMapView()
        }
    }

    private fun showCurrentLocate() {
        val location = getLocation() ?: return
        map?.showCurrentLocate(location)
    }

    private fun getLocation(): Location? {
        return map?.getCurrentLocate() ?: LocationModel.lastLocation
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        if (message.isNotEmpty()) toast(message)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            finish()
        }
    }
}