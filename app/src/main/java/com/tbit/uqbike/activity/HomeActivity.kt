package com.tbit.uqbike.activity

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.widget.CheckedTextView
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.annotation.RequiresApi
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.baidu.ar.it
import com.doule.database.CoroutinesUtil
import com.google.gson.Gson
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.maintanenceplus.utils.Selector
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.MainActivity.Companion.EXTRA_FROM_LOGIN
import com.tbit.uqbike.activity.model.MainRenTalModel
import com.tbit.uqbike.adapter.HomePageAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.base.BaseFragment
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.databinding.ActivityHomeBinding
import com.tbit.uqbike.dialog.util.MainDialogUtil
import com.tbit.uqbike.entity.MsgUread
import com.tbit.uqbike.fragment.ActFrag
import com.tbit.uqbike.fragment.HomeFrag
import com.tbit.uqbike.fragment.MineFrag
import com.tbit.uqbike.fragment.MsgFrag
import com.tbit.uqbike.fragment.PermissionFragment
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.model.MessageModel
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.zj.easyfloat.EasyFloat
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.delay
import org.jetbrains.anko.clearTop
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.singleTop
import permissions.dispatcher.PermissionUtils
import java.util.concurrent.TimeUnit
import com.lsxiao.apollo.core.annotations.Receive


class HomeActivity : BaseActivity(){
    private var adapters: HomePageAdapter? = null
    private var type:Array<BaseFragment>? = null
    private lateinit var homeFrag: HomeFrag
    private lateinit var actFrag: ActFrag
    private lateinit var msgFrag: MsgFrag
    private lateinit var mineFrag: MineFrag
    private lateinit var binding: ActivityHomeBinding

    private val checkedSelector = Selector<CheckedTextView>({ it?.isChecked = true }, { it?.isChecked = false })
    private var waitingForFirstLocation = false

    companion object {
        fun createIntent(context: Context, fromLogin: Boolean? = null): Intent {
            return context.intentFor<HomeActivity>(EXTRA_FROM_LOGIN to fromLogin).clearTop().singleTop()
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityHomeBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_home)

        waitingForFirstLocation = false

        homeFrag = HomeFrag()
        actFrag = ActFrag()
        msgFrag = MsgFrag()
        mineFrag = MineFrag()
        type = arrayOf(homeFrag,actFrag,msgFrag,mineFrag)
        adapters = HomePageAdapter(this@HomeActivity, type!!)
        binding.viewPagerMain.adapter = adapters
        binding.viewPagerMain.offscreenPageLimit = 4
        binding.viewPagerMain.registerOnPageChangeCallback(object : OnPageChangeCallback() {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                // 这里可以处理页面滑动的逻辑// position: 当前页面，从0开始 // positionOffset: 当前页面偏移的百分比 // positionOffsetPixels: 当前页面偏移的像素值
            }
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                if (binding.menuImgHome.tag != null && !binding.menuImgHome.tag.toString().isNullOrEmpty()){
                    binding.menuImgHome.setTag("")
                    ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_sy),binding.menuImgHome)
                }
                if (binding.menuImgAct.tag != null && !binding.menuImgAct.tag.toString().isNullOrEmpty()){
                    binding.menuImgAct.setTag("")
                    ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_hd),binding.menuImgAct)
                }
                if (binding.menuImgMsg.tag != null && !binding.menuImgMsg.tag.toString().isNullOrEmpty()){
                    binding.menuImgMsg.setTag("")
                    ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_xx),binding.menuImgMsg)
                }
                if (binding.menuImgMine.tag != null && !binding.menuImgMine.tag.toString().isNullOrEmpty()){
                    binding.menuImgMine.setTag("")
                    ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_wd),binding.menuImgMine)
                }
                // 页面切换完成的回调
                when(position){
                    0->{
                        checkedSelector.setSelected(binding.menuCtvHome)
                        ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_sy_1),binding.menuImgHome)
                        binding.menuImgHome.setTag("1")
                    }
                    1->{
                        checkedSelector.setSelected(binding.menuCtvAct)
                        ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_hd_1),binding.menuImgAct)
                        binding.menuImgAct.setTag("1")
                    }
                    2->{
                        checkedSelector.setSelected(binding.menuCtvMsg)
                        ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_xx_1),binding.menuImgMsg)
                        binding.menuImgMsg.setTag("1")
                    }
                    3->{
                        checkedSelector.setSelected(binding.menuCtvMine)
                        ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_wd_1),binding.menuImgMine)
                        binding.menuImgMine.setTag("1")
                    }
                }
            }
            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                // 页面滚动状态改变的回调 // state == ViewPager2.SCROLL_STATE_IDLE: 滚动停止 // state == ViewPager2.SCROLL_STATE_DRAGGING: 正在拖拽 // state == ViewPager2.SCROLL_STATE_SETTLING: 惯性滑动进行中
            }
        })
        binding.lyMenuHome.clickDelay { binding.viewPagerMain.setCurrentItem(0, false) }
        binding.lyMenuAct.clickDelay {
            if (tryLoginIfNot())
            binding.viewPagerMain.setCurrentItem(1, false)
        }
        binding.lyMenuMsg.clickDelay { if (tryLoginIfNot()) binding.viewPagerMain.setCurrentItem(2, false) }
        binding.lyMenuMine.clickDelay { if (tryLoginIfNot()) binding.viewPagerMain.setCurrentItem(3, false) }
        binding.lyMenuScan.clickDelay { if (tryLoginIfNot())
            if(homeFrag.MainState==1){
                MyToastUtil.toast(getString(R.string.s_have_riding_order))
                return@clickDelay
            }
            if(homeFrag.MainState==2){
                MyToastUtil.toast(getString(R.string.s_have_wait_pay_order))
                return@clickDelay
            }
            homeFrag.goScan() }

        if(!SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
            requestLocationPermissionAndLoadDataIfNeeded()
            checkGpsEnabled()
        }
        setUlocalView()

        if (FlavorConfig.NET.COM_URL.contains("api.gogoep.io") || FlavorConfig.NET.COM_URL.contains("api.gogoep.dev")){
            EasyFloat.layout(com.tbit.uqbike.R.layout.layout_float_view).layoutParams(initLayoutParams()).listener {}.show(this)
        }
        AutoTask()
    }

    private var isActivityRun = "1"
    @RequiresApi(Build.VERSION_CODES.O)
    private fun AutoTask(){
        if (!isActivityRun.isNullOrEmpty()){
            binding.viewPagerMain.postDelayed({
                if (!isActivityRun.isNullOrEmpty() && binding.viewPagerMain != null){
                    AutoTask()
                    if (homeFrag != null){
                        homeFrag.AutoTask()
                    }
                    if (Glob.isLogin) {
                        MessageModel.getMessageExist().subscribeBy(
                            onNext = {
                                MyLogUtil.Log("0000","=== 是否存在未读消息 信息=="+it.toString())
                                var resultData = Gson().fromJson(it.toString(), MsgUread::class.java)
                                if(resultData.is_exist_unread != 0){
                                    updaMsgNotify(true)
                                }else{
                                    updaMsgNotify(false)
                                }
                            }
                        ).toCancelable()
                    }
                }
            },5000)
        }
    }
    var isNewMsg = false
    fun updaMsgNotify(isNew : Boolean){
        isNewMsg = isNew
        if (isNew){
            binding.rlMsg.visibility = View.VISIBLE
        }else{
            binding.rlMsg.visibility = View.GONE
        }
    }
    private fun initLayoutParams(): FrameLayout.LayoutParams {
        val params = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.WRAP_CONTENT,
            FrameLayout.LayoutParams.WRAP_CONTENT
        )
        params.gravity = Gravity.BOTTOM or Gravity.END
        params.setMargins(0, params.topMargin, params.rightMargin, 500)
        return params
    }
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        Log.d("dd","==========mainActivity onNewIntent()")
        val fromLogin = intent?.getBooleanExtra(MainActivity.EXTRA_FROM_LOGIN, false)
        if (fromLogin == true) {
//            isPassProtocol = true
            homeFrag.mainAdFragment.getHomeAd()
        }
    }
    private fun checkGpsEnabled() {
        if (!LocationUtil.isGpsEnabled()) {
            MainDialogUtil.GpsDig(this@HomeActivity)
        }
    }
    @RequiresApi(Build.VERSION_CODES.O)
    private fun requestLocationPermissionAndLoadDataIfNeeded() {
        try {
            (supportFragmentManager.findFragmentById(R.id.fragment_permission) as PermissionFragment).requestPermission {
                LocationModel.onRequestPermissionSuccess()
                if (LocationModel.lastLocation != null) {
                    if (LocationUtil.isGpsEnabled()) {
                        getMapData()
                    } else {
                        checkGpsEnabled()
                    }
                } else {
                    waitingForFirstLocation = true
                    if (!LocationUtil.isGpsEnabled()) {
                        checkGpsEnabled()
                    }
                }
            }
        } catch (e: Exception) {
            MyLogUtil.Log("HomeActivity", "Error requesting permission: ${e.message}")
        }
    }
    @Receive(Constant.Event.LOCATION_UPDATE)
    fun onLocationUpdate() {
        if (waitingForFirstLocation && LocationModel.lastLocation != null) {
            waitingForFirstLocation = false
            if (LocationUtil.isGpsEnabled()) {
                getMapData()
            } else {
                checkGpsEnabled()
            }
        }
    }
    @RequiresApi(Build.VERSION_CODES.O)
    fun getMapData(){
        CoroutinesUtil.launchMain {
            MainRenTalModel.getCountrylld(onSuccess = {homeFrag.getMainData()})
        }
        setUlocalView()
    }
    @RequiresApi(Build.VERSION_CODES.O)
    override fun onRestart() {
        super.onRestart()
        homeFrag.onRestar()
        mineFrag.onRestar()
    }
    fun setUlocalView(){
        if (!Glob.isLogin){
            if (!LocationUtil.isGpsPermiss()) {
                binding.lyLocalN.visibility = View.VISIBLE
            }else{
                binding.lyLocalN.visibility = View.GONE
            }
        }else{
            binding.lyLocalN.visibility = View.GONE
        }
    }
    @RequiresApi(Build.VERSION_CODES.O)
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == MainActivity.REQUEST_OPEN_GPS) {
            if (LocationUtil.isGpsEnabled() && PermissionUtils.hasSelfPermissions(this,
                    Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION)) {
                MyLogUtil.Log("4444","====定位授权 main===11=====")
                LocationModel.onRequestPermissionSuccess()
                getMapData()
            }
//            getBlePre()
            return
        }
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == MainActivity.REQUEST_TO_RIDING) {
                finish()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        isActivityRun = ""
    }

    private var isMovePage = true
    fun setViewPageMove(isMove : Boolean){
        isMovePage = isMove
//        MyLogUtil.Log("1111","===========isMove======"+isMove)
        if (isMovePage){
            try {
                if (!binding.viewPagerMain.isUserInputEnabled){
//                    binding.vp.isUserInputEnabled = true
                    binding.viewPagerMain.isUserInputEnabled = false
                }
            }catch (e : NullPointerException){}
        }else{
            try {
                if (binding.viewPagerMain.isUserInputEnabled){
                    binding.viewPagerMain.isUserInputEnabled = false
                }
            }catch (e : NullPointerException){}
        }
    }
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        try {
            FragdispatchTouchEvent(ev)
            return super.dispatchTouchEvent(ev)
        }catch (e : NullPointerException){
            return false
        }
    }
    var startX = 0
    var startY = 0
    fun FragdispatchTouchEvent(ev: MotionEvent?) {
        when(ev?.action){
            MotionEvent.ACTION_DOWN ->{
                startX = ev.x.toInt()
                startY = ev.y.toInt()
            }
            MotionEvent.ACTION_MOVE ->{
                val endX = ev.x.toInt()
                val endY = ev.y.toInt()
                val disX = Math.abs(endX - startX)
                val disY = Math.abs(endY - startY)
                if (disX < disY) {
                    // 水平滑动距离 小于  垂直滑动距离
                    try {
                        binding.viewPagerMain.isUserInputEnabled = false
                    }catch (e : NullPointerException){}
                } else {
                }
            }
            MotionEvent.ACTION_UP ->{
                startY = 0
                startX = 0
                // 恢复 viewpage 滑动
                try {
                    homeFrag.moveUp()
                    if (isMovePage){
//                        binding.vp.isUserInputEnabled = true
                        binding.viewPagerMain.isUserInputEnabled = false
                    }
                }catch (e : NullPointerException){}
            }
//            MotionEvent.ACTION_CANCEL ->{}
        }
    }
}