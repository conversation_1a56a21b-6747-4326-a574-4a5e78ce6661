package com.tbit.uqbike.activity

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.CouponAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.Coupon
import com.tbit.uqbike.databinding.ActivityCouponBinding
import com.tbit.uqbike.mvp.constract.CouponContract
import com.tbit.uqbike.mvp.presenter.CouponPresenter
import org.jetbrains.anko.toast

class CouponActivity: BaseActivity(), CouponContract.View {

    private val presenter = CouponPresenter(this)
    private val adapter = CouponAdapter()
    private lateinit var binding: ActivityCouponBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCouponBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_coupon)
        setSupportActionBar(binding.appbarLayout.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
//        binding.appbarLayout.toolbarTitle.text = getString(R.string.coupon_title)
        binding.appbarLayout.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.appbarLayout.toolbar.setNavigationOnClickListener { onBackPressed() }

        lifecycle.addObserver(presenter)

        binding.rcv.layoutManager = LinearLayoutManager(this)
        binding.rcv.adapter = adapter

        getCoupon()
    }

    private fun getCoupon() {
        val cancellable = presenter.getCoupon()
        loadingDialogHelper.show { cancellable.cancel() }
    }

    override fun onGetCouponSuccess(coupons: List<Coupon>) {
        loadingDialogHelper.dismiss()
        adapter.source = coupons
        adapter.notifyDataSetChanged()
        if (coupons.isEmpty()) binding.capaLayout.toEmpty() else binding.capaLayout.toContent()
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }
}