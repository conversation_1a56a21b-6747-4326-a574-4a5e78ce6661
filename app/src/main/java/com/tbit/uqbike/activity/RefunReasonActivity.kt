package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.widget.CheckedTextView
import com.google.gson.Gson
import com.lsxiao.apollo.core.Apollo
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityRefunReasonBinding
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.entity.FormoptionData
import com.tbit.uqbike.entity.FormoptionDataItem
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.RefundModel
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.intentFor

class RefunReasonActivity : BaseActivity() {
    private val FeedList = mutableListOf<FormoptionDataItem>()
    private val FeedListSel = mutableListOf<FormoptionDataItem>()
    companion object {
        private const val EXTRA_TYPE = "EXTRA_TYPE"
        private const val EXTRA_DATA_ACCOUNT = "EXTRA_DATA_ACCOUNT"
        private const val EXTRA_DATA_NAME = "EXTRA_DATA_NAME"
        private const val EXTRA_DATA_DEPOSIT = "EXTRA_DATA_DEPOSIT"
        private const val EXTRA_DATA_NO = "EXTRA_DATA_NO"
        fun createIntent(context: Context, typeRefund : Int,ride_card_no : String,bank_account: String, bank_account_name: String, bank_deposit: String): Intent {
            return context.intentFor<RefunReasonActivity>(EXTRA_TYPE to typeRefund,EXTRA_DATA_NO to ride_card_no,
                EXTRA_DATA_ACCOUNT to bank_account, EXTRA_DATA_NAME to bank_account_name,EXTRA_DATA_DEPOSIT to bank_deposit)
        }
    }
    private val typeRefund : Int by bindExtra(EXTRA_TYPE)//钱包退款 ， 骑行卡退款
    private val bank_account : String by bindExtra(EXTRA_DATA_ACCOUNT)//账号
    private val bank_account_name : String by bindExtra(EXTRA_DATA_NAME)//名称
    private val bank_deposit : String by bindExtra(EXTRA_DATA_DEPOSIT)//开户行
    private val ride_card_no : String by bindExtra(EXTRA_DATA_NO)//订单编号
    private lateinit var binding: ActivityRefunReasonBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRefunReasonBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_refun_reason)
        binding.toolbars.toolbarTitle.text = getString(R.string.s_refund_reason)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener {
            finish()
        }
        binding.editRemark.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                setBtnState()
            }
        })
        MyLogUtil.Log("1111","===获取退款类型 信息=="+typeRefund)
        ComModel.getFormType(typeRefund).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取退款类型 信息=="+typeRefund+"===="+it.toString())
                var resultData = Gson().fromJson(it.toString(), FormoptionData::class.java)
                resultData.forEach {
                    FeedList.add(it)
                }
                setFlowLayout()
            }
        ).toCancelable()
        binding.btnSubmit.setOnClickListener {
            if (FeedListSel.size > 0){
                loadingDialogHelper.show { false }
                var form_options = ArrayList<Int>()
                FeedListSel.forEach {
                    form_options.add(it.id)
                }
                if (typeRefund == ComModel.FormType_Refund_RideCard){
                    RefundModel.sumitRideCardRefund(ride_card_no,bank_account,bank_account_name,bank_deposit,
                        form_options,binding.editRemark.text.toString()).subscribeBy(
                        onNext = {
                            loadingDialogHelper!!.dismiss()
                            MyLogUtil.Log("1111","===获取退款提交 信息=="+it.toString())
//                            var resultData = Gson().fromJson(it.toString(), getRideCardPerson::class.java)
                            showRefundDialog()
                        },
                        onError = {
                            loadingDialogHelper!!.dismiss()
                        }
                    ).toCancelable()
                }else{
                    RefundModel.sumitBalanceRefund(bank_account,bank_account_name,bank_deposit,
                        form_options,binding.editRemark.text.toString()).subscribeBy(
                        onNext = {
                            loadingDialogHelper!!.dismiss()
                            MyLogUtil.Log("1111","===获取退款提交 信息=="+it.toString())
//                            var resultData = Gson().fromJson(it.toString(), getRideCardPerson::class.java)
                            showRefundDialog()
                        },
                        onError = {
                            loadingDialogHelper!!.dismiss()
                        }
                    ).toCancelable()
                }
            }
        }
    }
    fun showRefundDialog(){
        val spannableString = SpannableString(getString(R.string.s_dialog_refund_cont))
        var data1 = spannableString.split("3")[0].length
        spannableString.setSpan(ForegroundColorSpan(resources.getColor(R.color.blue_namal)), data1, data1+1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        CommDialog.Builder(this@RefunReasonActivity).setTitle(getString(R.string.s_dialog_refund_title)).
        setSpanContent(spannableString)
            .setLeftText("").setRightText(getString(R.string.confirm)).setCanceledOnOutside(true)
            .setClickListen(object : CommDialog.TwoSelDialog {
                override fun leftClick() {}
                override fun rightClick() {
                    Apollo.emit(Constant.Event.REFUNDSUC)
                    finish()
                }
            }).build().show()
    }
    fun setBtnState(){
        if (FeedListSel.size > 0){
            binding.btnSubmit.setTextColor(resources.getColor(R.color.white))
        }else{
            binding.btnSubmit.setTextColor(resources.getColor(R.color.white50))
        }
    }
    private fun setFlowLayout() {
        binding.flowLayout.removeAllViews()
        for (i in 0 until FeedList.size) {
            val mInflater = LayoutInflater.from(this)
            val tv = mInflater.inflate(R.layout.flow_layout, binding.flowLayout, false) as CheckedTextView
            tv.setText(FeedList.get(i).title)
            //点击事件
            tv.setOnClickListener {
                if(FeedList.get(i).ischeck){
                    for (s in 0 until FeedListSel.size){
                        if(FeedListSel.get(s).id == FeedList.get(i).id) {
                            FeedListSel.removeAt(s)
                            break
                        }
                    }
                }else{
                    if(FeedListSel.size < FeedList.size){
                        FeedListSel.add(FeedList.get(i))
                    }else{
                        FeedListSel.removeAt(0)
                        FeedListSel.add(FeedList.get(i))
                    }
                }
                for (s in 0 until FeedList.size){
                    FeedList.get(s).ischeck = false
                    (binding.flowLayout.getChildAt(s) as CheckedTextView).isChecked = false
                    for (s_sel in 0 until FeedListSel.size){
                        if(FeedListSel.get(s_sel).id == FeedList.get(s).id) {
                            (binding.flowLayout.getChildAt(s) as CheckedTextView).isChecked = true
                            FeedList.get(s).ischeck = true
                        }
                    }
                }
                setBtnState()
            }
            binding.flowLayout.addView(tv) //添加到父View
        }
    }
}