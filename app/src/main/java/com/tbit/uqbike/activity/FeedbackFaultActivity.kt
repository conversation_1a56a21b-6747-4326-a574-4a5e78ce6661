package com.tbit.uqbike.activity

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.CheckedTextView
import android.widget.EditText
import android.widget.Toast
import androidx.recyclerview.widget.GridLayoutManager
import com.baidu.ar.it
import com.google.gson.Gson
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.luck.picture.lib.permissions.PermissionChecker
import com.luck.picture.lib.permissions.PermissionConfig
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.preview.ImageInfo
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.SelectPhotoAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityFeedbackFaultBinding
import com.tbit.uqbike.entity.AliData
import com.tbit.uqbike.entity.FaultSumitRestData
import com.tbit.uqbike.entity.FormoptionData
import com.tbit.uqbike.entity.FormoptionDataItem
import com.tbit.uqbike.entity.getAdData
import com.tbit.uqbike.fragment.CameraPermissionFragment
import com.tbit.uqbike.fragment.PermissionReadFragment
import com.tbit.uqbike.fragment.ReadMediaPermissionFragment
import com.tbit.uqbike.mvp.constract.FeedbackFaultContract
import com.tbit.uqbike.mvp.presenter.FeedbackFaultPresenter
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.startActivityForResult
import org.jetbrains.anko.toast


class FeedbackFaultActivity : BaseActivity(), FeedbackFaultContract.View {

    companion object {
        const val REQUEST_TYPE = 1
        private const val REQUEST_MACHINE_NO = 1
        private const val EXTRA_USER_CODE = "EXTRA_USER_CODE"
        private const val EXTRA_USER_CODE_ORDER = "EXTRA_USER_CODE_ORDER"

        fun createIntent(context: Context, vehicle_no: String? = null,orderNo : String): Intent {
            return context.intentFor<FeedbackFaultActivity>(EXTRA_USER_CODE to vehicle_no,EXTRA_USER_CODE_ORDER to orderNo)
        }
    }

//    private val reasons by lazy { listOf(text_reason1, text_reason2, text_reason3, text_reason4, text_reason5, text_reason7) }
    private val presenter = FeedbackFaultPresenter(this)
    private val intentUserCode: String? by bindExtra(EXTRA_USER_CODE)
    private val orderNo: String? by bindExtra(EXTRA_USER_CODE_ORDER)
    private val photos = mutableListOf<ImageInfo>()
    private val FeedList = mutableListOf<FormoptionDataItem>()

    private lateinit var binding: ActivityFeedbackFaultBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFeedbackFaultBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_feedback_fault)
        setSupportActionBar(binding.toolbars.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        binding.toolbars.toolbarTitle.text = getString(R.string.feedback_fault)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { onBackPressed() }
        binding.toolbars.toolbarTitleR.text = getString(R.string.s_sumit_record)
        binding.toolbars.toolbarTitleR.visibility = View.VISIBLE
        binding.toolbars.toolbarTitleR.setOnClickListener {
            startActivity<FaultRecordActivity>()
        }


        lifecycle.addObserver(presenter)

        if (savedInstanceState == null) {
            binding.editMachineNo.setText(intentUserCode)
        }
//        MyLogUtil.Log("1111","========== intentUserCode ==="+intentUserCode)
        if(intentUserCode == null){
            binding.imageScan.visibility = View.VISIBLE
        }else{
            binding.imageScan.visibility = View.GONE
            binding.editMachineNo.isEnabled = false
            binding.editMachineNo.setTextColor(resources.getColor(R.color.c_C0C1C3))
        }


        ComModel.getFormType(REQUEST_TYPE).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取故障上报类型 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), FormoptionData::class.java)
                resultData.forEach {
                    FeedList.add(it)
                }
                setFlowLayout()
            }
        ).toCancelable()


        ComModel.getAlConfig().subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取阿里云 配置 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), AliData::class.java)
                Glob.ACCESS_ID = resultData.accesskey_id
                Glob.ACCESS_KEY = resultData.accesskey_secret
                Glob.ACCESS_TOKEN = resultData.security_token
                Glob.ACCESS_BUCKET_NAME = resultData.bucket
                Glob.ACCESS_ENDPOINT = resultData.endpoint
                Glob.ACCESS_DOMAINNAME = resultData.host_cdn
                Glob.IMG_PREFIX = resultData.prefix
            }
        ).toCancelable()

//        image_view.setOnClickListener { selectImage() }
//        text_add_image.setOnClickListener { selectImage() }
        val spacing = dip(3)
        val spanCount = 4
        val maxCount = 1
        binding.rcvImage.adapter = SelectPhotoAdapter(this, spanCount, maxCount, photos,SelectPhotoAdapter.TYPE_FAULT)
        binding.rcvImage.layoutManager = GridLayoutManager(this, spanCount)
        binding.rcvImage.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))

        binding.imageScan.setOnClickListener {
            (supportFragmentManager.findFragmentById(R.id.camera_permission_fragment) as CameraPermissionFragment).requestPermission {
                startActivityForResult<ScanMachineNoActivity>(REQUEST_MACHINE_NO)
            }
//            (camera_permission_fragment as CameraPermissionFragment).requestPermission {
//                startActivityForResult<ScanMachineNoActivity>(REQUEST_MACHINE_NO)
//            }
        }

//        reasons.forEach { reasonView ->
//            reasonView.setOnClickListener { selector.setSelected(reasonView) }
//        }
        binding.editMachineNo.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                var isSel = false
                for (s in 0 until FeedList.size){
                    if ((binding.flowLayout.getChildAt(s) as CheckedTextView).isChecked){
                        isSel = true
                    }
                }
                if (binding.editMachineNo.text.toString().length > 0 && isSel && binding.editAddition.text.toString().length > 0){
                    binding.btnSubmit.setTextColor(resources.getColor(R.color.white))
                }else{
                    binding.btnSubmit.setTextColor(resources.getColor(R.color.white50))
                }
            }
        })

        binding.editAddition.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                var isSel = false
                for (s in 0 until FeedList.size){
                    if ((binding.flowLayout.getChildAt(s) as CheckedTextView).isChecked){
                        isSel = true
                    }
                }
                if (binding.editMachineNo.text.toString().length > 0 && isSel && binding.editAddition.text.toString().length > 0){
                    binding.btnSubmit.setTextColor(resources.getColor(R.color.white))
                }else{
                    binding.btnSubmit.setTextColor(resources.getColor(R.color.white50))
                }
            }
        })

        binding.editAddition.setOnTouchListener(View.OnTouchListener { v, event -> // 当触摸的是EditText & 当前EditText可滚动时，则将事件交给EditText处理；
            if (v.id == R.id.edit_addition && canVerticalScroll(binding.editAddition)) {
                v.parent.requestDisallowInterceptTouchEvent(true)
                // 否则将事件交由其父类处理
                if (event.action == MotionEvent.ACTION_UP) {
                    v.parent.requestDisallowInterceptTouchEvent(false)
                }
            }
            false
        })

        binding.btnSubmit.setOnClickListener { onSubmitClick() }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            (supportFragmentManager.findFragmentById(R.id.read_media_permission_fragment) as ReadMediaPermissionFragment).requestPermission {
                binding.rcvImage.visibility = if (it) View.VISIBLE  else View.GONE
            }
        }else{
            (supportFragmentManager.findFragmentById(R.id.read_permission_fragment) as PermissionReadFragment).requestPermission {
            }
        }
    }
    private fun canVerticalScroll(editText: EditText): Boolean {
        return if (editText.lineCount > editText.maxLines) {
            true
        } else false
    }
    private fun setFlowLayout() {
        binding.flowLayout.removeAllViews()
        for (i in 0 until FeedList.size) {
            val mInflater = LayoutInflater.from(this)
            val tv = mInflater.inflate(R.layout.flow_layout, binding.flowLayout, false) as CheckedTextView
            tv.setText(FeedList.get(i).title)
            //点击事件
            tv.setOnClickListener {
                for (s in 0 until FeedList.size){
                    (binding.flowLayout.getChildAt(s) as CheckedTextView).isChecked = false
                }
                tv.isChecked = true
                FeedList.get(i).ischeck = true

                if (binding.editMachineNo.text.toString().length > 0 && binding.editAddition.text.toString().length > 0){
                    binding.btnSubmit.setTextColor(resources.getColor(R.color.white))
                }else{
                    binding.btnSubmit.setTextColor(resources.getColor(R.color.white50))
                }
//                Toast.makeText(this@FeedbackFaultActivity, FeedList.get(i).title, Toast.LENGTH_SHORT).show()
            }
            binding.flowLayout.addView(tv) //添加到父View
        }
    }
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != Activity.RESULT_OK) return

        if (requestCode == REQUEST_MACHINE_NO) {
            binding.editMachineNo.setText(data?.let { ScanMachineNoActivity.getMachineNOFromResult(it) })
        }

    }

    private fun onSubmitClick() {
        val machineNO = binding.editMachineNo.text.toString()
        if(machineNO.equals("")){
            Toast.makeText(ContextUtil.getContext(),getString(R.string.machine_no_not_null),Toast.LENGTH_SHORT).show()
            return
        }

        var type = ""
        FeedList.forEach {
            if(it.ischeck){
                type = it.id.toString()
            }
        }
        if(type.equals("")){
            Toast.makeText(ContextUtil.getContext(),getString(R.string.please_select_fault_type),Toast.LENGTH_SHORT).show()
            return
        }
        val listType = ArrayList<Int>()
        listType.add(type.toInt())

        val remark = binding.editAddition.text.toString().trim()
        if (remark.isNullOrEmpty()){
            MyToastUtil.toast(getString(R.string.s_sumitneed))
            return
        }
        val listImg = ArrayList<String>()
        if(photos.size > 0){
            if(!photos.get(0).bigImageUrl.contains("http")){
                Toast.makeText(ContextUtil.getContext(),getString(R.string.s_sumitimg_fail),Toast.LENGTH_SHORT).show()
                return
            }
            listImg.add(photos.get(0).bigImageUrl)
        }
//        MyLogUtil.Log("1111","=== 故障上报提交 ==="+machineNO+","+type+","+remark+","+listImg.get(0))
        val cancellable = presenter.feedbackFault(machineNO, listType, remark, orderNo,listImg)
        loadingDialogHelper.show { cancellable.cancel() }

    }

    override fun onFeedbackFaultSuccess(data : String) {
        loadingDialogHelper.dismiss()
        toast(getString(R.string.thanks_for_feedback))
        val resultData = Gson().fromJson(data, FaultSumitRestData::class.java)
        if (resultData != null){
            startActivity(FaultInfoActivity.createIntent(this, resultData.id))
        }
        finish()
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }
}