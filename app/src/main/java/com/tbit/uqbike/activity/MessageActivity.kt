package com.tbit.uqbike.activity

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.baidu.ar.it
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.MessageAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityMessageBinding
import com.tbit.uqbike.entity.MsgListDataItem
import com.tbit.uqbike.mvp.constract.MessageContract
import com.tbit.uqbike.mvp.presenter.MessagePresenter
import com.tbit.uqbike.utils.MDUtil
import com.umeng.message.PushAgent
import org.jetbrains.anko.dip
import org.jetbrains.anko.toast
import org.json.JSONObject

class MessageActivity: BaseActivity(), MessageContract.View {

    private val presenter = MessagePresenter(this)
    private val adapter by lazy {MessageAdapter()}
    private var messageState = 0
    private var lastLoaded = false
    private val loadMoreView by lazy { View(this).apply { layoutParams = ViewGroup.LayoutParams(1, 1) } }

    private lateinit var binding: ActivityMessageBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMessageBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_message)

        binding.layoutToolbar.toolbarTitle.text = getString(R.string.my_message)
        binding.layoutToolbar.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.layoutToolbar.toolbar.setNavigationOnClickListener { finish() }

        lifecycle.addObserver(presenter)

//        adapter.setOnItemClickListener { onMessageClick(it) }
        adapter.setOnMyItemClickListener { msgListDataItem, i ->
            onMessageClick(msgListDataItem,i)
        }
        binding.rcv.layoutManager = LinearLayoutManager(this)
        val spacing = dip(10)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        binding.rcv.adapter = loadMoreWrapper(adapter)

        binding.imgMsgNotify.setOnClickListener { binding.lyNotify.visibility = View.GONE }
        binding.rvMsgNotify.setOnClickListener {
            var api = PushAgent.getInstance(this@MessageActivity)
            api.openNotificationSettings()
        }

        getMessage()

    }

    override fun onResume() {
        super.onResume()
        var api = PushAgent.getInstance(this@MessageActivity)
        if (!api.isNotificationEnabled){
            binding.lyNotify.visibility = View.VISIBLE
        }else{
            binding.lyNotify.visibility = View.GONE
        }
    }

    private fun onMessageClick(message: MsgListDataItem,pos : Int) {
        //消息类型:1故障上报，2意见反馈，3骑行费用申诉，4短时长骑行订单反馈，5余额退款，6骑行卡退款,9押金退还，10邀请成功
        if (message.type == 2 || message.type == 3 || message.type == 8){
            if ((message.type == 3 || message.type == 8) && !message.relevance.isNullOrEmpty()){
                startActivity(CostInfoActivity.createIntent(this,message.relevance.toInt()))
            }else{
                startActivity(MessageDetailActivity.createIntent(this, message.id))
            }
        }else if(message.type == 5 || message.type == 6 || message.type == 9 || message.type == 10 || message.type == 11){
            startActivity(MessageRefundDetailActity.createIntent(this, message.id))
        }else if(message.type == 1){
            if (!message.relevance.isNullOrEmpty()){
                startActivity(FaultInfoActivity.createIntent(this,message.relevance.toInt()))
            }
        }
        adapter.source.get(pos).read_time = 1
        adapter.notifyItemChanged(pos)

        val properties = JSONObject()
        properties.put("message_id", message.id) // 设置商品 ID
        MDUtil.clickEvent("InternalMessageClick",properties)
    }

    private fun loadMoreWrapper(adapter: RecyclerView.Adapter<out RecyclerView.ViewHolder>)
            : LoadMoreWrapper<out RecyclerView.ViewHolder> {
        val loadMoreAdapter = LoadMoreWrapper(adapter)
        loadMoreAdapter.setOnLoadMoreListener{ onLoadMore() }
        return loadMoreAdapter
    }

    private fun onLoadMore() {
        if (!lastLoaded)
            getMessage(false)
    }

    private fun getMessage(showLoading: Boolean = true) {
        val cancellable = presenter.getMessage()
        if (showLoading) {
            loadingDialogHelper.show { }
        }
    }

    override fun onGetMessageSuccess(messages: List<MsgListDataItem>, lastLoaded: Boolean) {
        loadingDialogHelper.dismiss()
        this.lastLoaded = lastLoaded
        (binding.rcv.adapter as? LoadMoreWrapper<*>)?.setLoadMoreView(if (lastLoaded) null else loadMoreView)
        adapter.source = messages
        adapter.notifyDataSetChanged()
        if (messages.isEmpty() && presenter.lastUserMsgId == 0) binding.capaLayout.toEmpty() else binding.capaLayout.toContent()
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        if (message.isNotEmpty()) toast(message)
    }
}