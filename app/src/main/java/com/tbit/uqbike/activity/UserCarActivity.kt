package com.tbit.uqbike.activity

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import com.tbit.maintenance.utils.bindExtra
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityMineBinding
import com.tbit.uqbike.databinding.ActivityUserCarBinding
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import org.json.JSONObject

class UserCarActivity : BaseActivity() {
    private lateinit var binding: ActivityUserCarBinding
    companion object {
        val TYPE : String = "title"
        val UNIT : String = "UNIT"
        val TYPE_NEW = 1;//新手指引
        val TYPE_STOP = 2;//临时停车
    }
    private val type: Int by bindExtra(TYPE)
    private val unitData: String by bindExtra(UNIT)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityUserCarBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_user_car)

        binding.btnUsercar.setOnClickListener {
            if (timeData == 0){
                val properties = JSONObject()
                MDUtil.clickEvent("got_it_click",properties)
                finish()
            }
        }
        exchangeType = type.toString()
        if (type == TYPE_NEW){
            binding.tvUsecarTitle1.text = getString(R.string.s_usecar_new1)
            var unit = getString(R.string.s_usecar_new_unit1)
            var content = getString(R.string.s_usecar_new2)
            val spannableString = SpannableString(content)
            if (content.contains(unit.toString())){
                var data1 = spannableString.split(unit.toString())[0].length
                spannableString.setSpan(ForegroundColorSpan(resources.getColor(R.color.c_E00000)), data1, data1+unit.toString().length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            binding.tvUsecarHint1.text = spannableString

            binding.tvUsecarTitle2.text = getString(R.string.s_usecar_new3)
            var unit2 = getString(R.string.s_usecar_new_unit2)
            var content2 = getString(R.string.s_usecar_new4)
            val spannableString2 = SpannableString(content2)
            if (content.contains(unit2.toString())){
                var data2 = spannableString2.split(unit2.toString())[0].length
                spannableString2.setSpan(ForegroundColorSpan(resources.getColor(R.color.c_E00000)), data2, data2+unit2.toString().length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            binding.tvUsecarHint2.text = spannableString2

            ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_usecar_new1),binding.imgUsecar1)
            ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_usecar_new2),binding.imgUsecar2)
        }else{
            binding.tvUsecarTitle1.text = getString(R.string.s_usecar_stop1)
            binding.tvUsecarHint1.text = getString(R.string.s_usecar_stop2)

            binding.tvUsecarTitle2.text = getString(R.string.s_usecar_stop3)
            var unit2 = getString(R.string.s_usecar_stop_unit1)
            var unit3 = unitData
            var content2 = getString(R.string.s_usecar_stop4,unit3)
            val spannableString2 = SpannableString(content2)
            if (content2.contains(unit2.toString())){
                var data2 = spannableString2.split(unit2.toString())[0].length
                spannableString2.setSpan(ForegroundColorSpan(resources.getColor(R.color.c_E00000)), data2, data2+unit2.toString().length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }

            if (content2.contains(unit3.toString())){
                var data3 = spannableString2.split(unit3.toString())[0].length
                spannableString2.setSpan(ForegroundColorSpan(resources.getColor(R.color.c_E00000)), data3, data3+unit3.toString().length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            binding.tvUsecarHint2.text = spannableString2

            ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_usecar_stop1),binding.imgUsecar1)
            ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_usecar_stop2),binding.imgUsecar2)
        }

        AutoTask()
    }
    var timeData = 6
    private val cancelDelay = 1000L
    private val handler = Handler(Looper.getMainLooper())
    private val onAutoTask = Runnable {
        AutoTask()
    }
    fun AutoTask(){
        handler.postDelayed(onAutoTask, cancelDelay)
//        MyLogUtil.Log("1111","========AutoTask==========")
        if (timeData > 0){
            timeData = timeData - 1
            binding.btnUsercar.text = getString(R.string.i_know)+"  ("+timeData+")"
        }else{
            binding.btnUsercar.setTextColor(resources.getColor(R.color.white))
            binding.btnUsercar.text = getString(R.string.i_know)
        }
    }

    override fun onBackPressed() {
        if (timeData != 0) return
        super.onBackPressed()
    }
    override fun onDestroy() {
        super.onDestroy()
        handler.removeCallbacks(onAutoTask)
        handler.removeCallbacksAndMessages(null)
    }
}