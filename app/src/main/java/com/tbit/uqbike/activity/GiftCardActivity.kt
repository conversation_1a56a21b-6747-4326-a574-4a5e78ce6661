package com.tbit.uqbike.activity

import android.app.Activity
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.GiftCardAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.GiftCard
import com.tbit.uqbike.databinding.ActivityGiftCardBinding
import com.tbit.uqbike.dialog.EditDialogFragment
import com.tbit.uqbike.mvp.constract.GiftCardContract
import com.tbit.uqbike.mvp.presenter.GiftCardPresenter
import org.jetbrains.anko.toast

class GiftCardActivity: BaseActivity(), GiftCardContract.View {

    private val presenter = GiftCardPresenter(this)
    private val adapter = GiftCardAdapter()
    private val inputCardNODialog = EditDialogFragment()
    private lateinit var binding: ActivityGiftCardBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGiftCardBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_gift_card)
        setSupportActionBar(binding.appbarLayout.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        binding.appbarLayout.toolbarTitle.text = getString(R.string.gift_card_title)
        binding.appbarLayout.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.appbarLayout.toolbar.setNavigationOnClickListener { onBackPressed() }

        lifecycle.addObserver(presenter)

        binding.rcv.layoutManager = LinearLayoutManager(this)
        binding.rcv.adapter = adapter

        inputCardNODialog.title = getString(R.string.please_input_gift_card_no)
        inputCardNODialog.onConfirmListener = {
            if (checkCardNO(it)) {
                exchangeGiftCard(it)
                inputCardNODialog.dismissAllowingStateLoss()
            }
        }

        binding.btnExchange.setOnClickListener { showInputCardNoDialog() }
        getGiftCard()
    }

    private fun showInputCardNoDialog() {
        inputCardNODialog.message = ""
        lifecycleDialogHelper.show(inputCardNODialog)
    }

    private fun checkCardNO(cardNO: String): Boolean {
        var result = false
        when {
            cardNO.isNullOrEmpty() -> toast(getString(R.string.please_input_gift_card_no))
            else -> result = true
        }
        return result
    }

    private fun getGiftCard() {
        val cancellable = presenter.getGiftCards()
        loadingDialogHelper.show { cancellable.cancel() }
    }

    override fun onGetGiftCardSuccess(giftCards: List<GiftCard>) {
        loadingDialogHelper.dismiss()
        adapter.source = giftCards
        adapter.notifyDataSetChanged()
        if (giftCards.isEmpty()) binding.capaLayout.toEmpty() else binding.capaLayout.toContent()
    }

    private fun exchangeGiftCard(cardNO: String) {
        val cancellable = presenter.exchangeGiftCard(cardNO)
        loadingDialogHelper.show { cancellable.cancel() }
    }

    override fun onExchangeGiftCardSuccess() {
        showErrMsg(getString(R.string.gift_card_exchange_success))
        setResult(Activity.RESULT_OK)
        getGiftCard()
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }
}