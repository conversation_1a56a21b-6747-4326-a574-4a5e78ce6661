package com.tbit.uqbike.activity

import android.os.Bundle
import android.view.MotionEvent
import com.google.android.material.tabs.TabLayoutMediator
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.PropsPageAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityTransactionDetailBinding
import com.tbit.uqbike.widget.SimpleTabLay

class TransactionDetailActivity: BaseActivity() {

    var tabLayoutMediator: TabLayoutMediator? = null
    private var adapters: PropsPageAdapter? = null
    private var type:Array<String>? = null
    private lateinit var binding: ActivityTransactionDetailBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTransactionDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_transaction_detail)

        type = arrayOf(getString(R.string.recharge),getString(R.string.ride_card))

        adapters = PropsPageAdapter(this@TransactionDetailActivity, type!!)
        binding.vpProps.adapter = adapters
        tabLayoutMediator = TabLayoutMediator(binding.tabLayout!!, binding.vpProps, true,true) { tab, position ->
            binding.tabLayout.initTab(tab, type!![position], SimpleTabLay.TYPE_TRANSACTION) }
        tabLayoutMediator!!.attach()

        binding.imgBack.setOnClickListener { finish() }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        try {
            FragdispatchTouchEvent(ev)
            return super.dispatchTouchEvent(ev)
        }catch (e : NullPointerException){
            return false
        }
    }
    var startX = 0
    var startY = 0
    fun FragdispatchTouchEvent(ev: MotionEvent?) {
        when(ev?.action){
            MotionEvent.ACTION_DOWN ->{
                startX = ev.x.toInt()
                startY = ev.y.toInt()
            }
            MotionEvent.ACTION_MOVE ->{
                val endX = ev.x.toInt()
                val endY = ev.y.toInt()
                val disX = Math.abs(endX - startX)
                val disY = Math.abs(endY - startY)
                if (disX < disY) {
                    // 水平滑动距离 小于  垂直滑动距离
                    try {
                        binding.vpProps.isUserInputEnabled = false
                    }catch (e : NullPointerException){}
                } else {
                }
            }
            MotionEvent.ACTION_UP ->{
                startY = 0
                startX = 0
                // 恢复 viewpage 滑动
                try {
                    binding.vpProps.isUserInputEnabled = true
                }catch (e : NullPointerException){}
            }
            MotionEvent.ACTION_CANCEL ->{}
        }
    }

}