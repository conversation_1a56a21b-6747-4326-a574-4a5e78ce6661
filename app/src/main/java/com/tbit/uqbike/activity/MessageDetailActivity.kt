package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import androidx.annotation.RequiresApi
import androidx.recyclerview.widget.GridLayoutManager
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.preview.ImageInfo
import com.tbit.preview.ImagePreviewActivity
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.imgexplainAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityMessageDetailBinding
import com.tbit.uqbike.entity.MsgInfoData
import com.tbit.uqbike.mvp.model.MessageModel
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip
import org.jetbrains.anko.intentFor
import java.io.Serializable

class MessageDetailActivity: BaseActivity() {

    companion object {

        private const val EXTRA_MESSAGE = "EXTRA_MESSAGE"

        fun createIntent(context: Context, msgid : Int): Intent {
            return context.intentFor<MessageDetailActivity>(
                EXTRA_MESSAGE to msgid
            )
        }
    }

    private val msgid: Int by bindExtra(EXTRA_MESSAGE)
    private lateinit var binding: ActivityMessageDetailBinding

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMessageDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_message_detail)

        binding.layoutToolbar.toolbarTitle.text = getString(R.string.s_msginfo)
        binding.layoutToolbar.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.layoutToolbar.toolbar.setNavigationOnClickListener { finish() }

        MessageModel.getMessageInfo(msgid)
            .subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","=== 获取消息详情 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), MsgInfoData::class.java)

                    binding.tvMsginfoTime.text = TimeFormatUtil.transToStringBysq(resultData.info.create_time.value.toLong())

                    if (!resultData.response.isNullOrEmpty()){
                        binding.lyMsginfoDispose.visibility = View.VISIBLE
                        binding.tvMsginfoDisposeTime.text = TimeFormatUtil.transToStringBysq(resultData.create_time.toLong())
                        binding.tvMsginfoDisposeCont.text = resultData.response
                    }
                    //消息类型:1故障上报，2意见反馈，3骑行费用申诉，4短时长骑行订单反馈
                    if(resultData.type == 2){
                        //2意见反馈
                        binding.tvMsginfoTimeL.text = getString(R.string.s_feedback_time)
                        binding.tvMsginfoL1.text = getString(R.string.s_feedback_type)
                        if(resultData.info.form_options.value.size > 1){
                            var info = ""
                            for (i in 0 until resultData.info.form_options.value.size) {
                                info = info + resultData.info.form_options.value.get(i) +"、"
                            }
                            info = info.substring(0,info.length - 1)
                            binding.tvMsginfoR1.text = info
                        }else{
                            if (resultData.info.form_options.value.size > 0){
                                binding.tvMsginfoR1.text = resultData.info.form_options.value[0]
                            }
                        }

//                        MyLogUtil.Log("1111","=== 获取消息详情 1信息=="+resultData.info.info.value)
                        if (!resultData.info.info.value.isNullOrEmpty() || resultData.info.image != null){
                            binding.lvMsginfo3.visibility = View.VISIBLE
                            binding.tvMsginfoL3.text = getString(R.string.s_feecback_cont)
                        }
                        if(!resultData.info.info.value.isNullOrEmpty()){
//                            binding.lvMsginfo3.visibility = View.VISIBLE
//                            binding.tvMsginfoL3.text = getString(R.string.s_feecback_cont)
                            binding.tvMsginfoR3.visibility = View.VISIBLE
                            binding.tvMsginfoR3.text = resultData.info.info.value
                        }else{
                            binding.tvMsginfoR3.visibility = View.GONE
                        }
//                        if(resultData.info.image != null  && resultData.info.image.value.size > 0){
//                            binding.lyMsginfoImg.visibility = View.VISIBLE
////                            ImageLoad.loadimg(resultData.info.image.value[0],binding.imgMsginfo)
//                        }

                    }else if(resultData.type == 3){
                        //费用申诉
                        binding.tvMsginfoTimeL.text = getString(R.string.s_appeal_time)
                        binding.lvMsginfo2.visibility = View.VISIBLE
                        binding.tvMsginfoL1.text = getString(R.string.s_ride_order)
                        binding.tvMsginfoL2.text = getString(R.string.s_appeal_reason)

                        binding.tvMsginfoR1.text = resultData.info.order_no.value
                        if(resultData.info.form_options.value.size > 1){
                            var info = ""
                            for (i in 0 until resultData.info.form_options.value.size) {
                                info = info + resultData.info.form_options.value.get(i) +"、"
                            }
                            info = info.substring(0,info.length - 1)
                            if (resultData.info.form_options.pid_value.size > 0){
                                binding.tvMsginfoR2.text = resultData.info.form_options.pid_value[0]+"-"+info
                            }else{
                                binding.tvMsginfoR2.text = info
                            }
                        }else{
                            if (resultData.info.form_options.pid_value.size > 0){
                                binding.tvMsginfoR2.text = resultData.info.form_options.pid_value[0]+"-"+resultData.info.form_options.value[0]
                            }else{
                                binding.tvMsginfoR2.text = resultData.info.form_options.value[0]
                            }
                        }

                        if (!resultData.info.info.value.isNullOrEmpty() || resultData.info.image != null){
                            binding.lvMsginfo3.visibility = View.VISIBLE
                            binding.tvMsginfoL3.text = getString(R.string.appeal_content)
                        }
                        if(!resultData.info.info.value.isNullOrEmpty()){
//                            binding.lvMsginfo3.visibility = View.VISIBLE
//                            binding.tvMsginfoL3.text = getString(R.string.appeal_content)
                            binding.tvMsginfoR3.visibility = View.VISIBLE
                            binding.tvMsginfoR3.text = resultData.info.info.value
                        }else{
                            binding.tvMsginfoR3.visibility = View.GONE
                        }

//                        if(resultData.info.image != null && resultData.info.image.value.size > 0){
//                            binding.lyMsginfoImg.visibility = View.VISIBLE
////                            ImageLoad.loadimg(resultData.info.image.value[0],binding.imgMsginfo)
//                        }
                    }
                    if(resultData.info.image != null  && resultData.info.image.value.size > 0){
                        binding.lyMsginfoImg.visibility = View.VISIBLE

                        val spacing = dip(3)
                        val spanCount = 4
                        var adapterImgExample = imgexplainAdapter(this@MessageDetailActivity)
                        binding.rcvImageExplain.adapter = adapterImgExample
                        binding.rcvImageExplain.layoutManager = GridLayoutManager(this, spanCount)
                        binding.rcvImageExplain.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))

                        var photoData = ArrayList<ImageInfo>()
                        resultData.info.image.value?.forEach {
                            var imgData = ImageInfo()
                            imgData.bigImageUrl = it
                            photoData.add(imgData)
                        }
                        adapterImgExample.source = photoData
                        adapterImgExample.notifyDataSetChanged()
                    }

//                    binding.imgMsginfo.setOnClickListener {
//                        var imgList = ArrayList<ImageInfo>()
//                        var imgData = ImageInfo()
//                        imgData.bigImageUrl = resultData.info.image.value[0]
//                        imgData.thumbnailUrl = resultData.info.image.value[0]
//                        imgList.add(imgData)
//                        val intent = Intent(this@MessageDetailActivity, ImagePreviewActivity::class.java)
//                        val bundle = Bundle()
//                        bundle.putSerializable(ImagePreviewActivity.IMAGE_INFO, imgList as Serializable)
//                        bundle.putInt(ImagePreviewActivity.CURRENT_ITEM, 0)
//                        bundle.putBoolean(ImagePreviewActivity.USER_CACHE, true)
//                        intent.putExtras(bundle)
//                        startActivity(intent)
//                        overridePendingTransition(0, 0)
//                    }
                }
            ).toCancelable()



//        text_title.text = message.title
//        text_time.text = message.formatCreateTime
//        text_content.text = message.content
    }
}