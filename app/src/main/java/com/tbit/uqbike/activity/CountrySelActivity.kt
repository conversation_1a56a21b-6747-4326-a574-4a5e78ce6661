package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.CountrySelAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityCountrySelBinding
import com.tbit.uqbike.entity.CountryListData
import com.tbit.uqbike.entity.CountryListDataItem
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.resqmodel.CurrencyModel
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus

class CountrySelActivity : BaseActivity() {
    private var adapter: CountrySelAdapter?=null
    private var entityList:ArrayList<CountryListDataItem>?= ArrayList()
    var selCountrylld = ""
    var selCountryUnit = ""
    private lateinit var binding: ActivityCountrySelBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCountrySelBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_country_sel)

        setSupportActionBar(binding.appbarLayout.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        binding.appbarLayout.toolbarTitle.text = getString(R.string.s_change_country)
        binding.appbarLayout.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.appbarLayout.toolbar.setNavigationOnClickListener { onBackPressed() }

        initRecycleview()
        binding.buttonConfirm.setOnClickListener {
            if(!selCountrylld.isNullOrEmpty()){
//                MyToastUtil.toast(selCountry)
                SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_COUNTRY, selCountrylld);
                SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_COUNTRY_sel, selCountrylld);
                SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_COUNTRY_LOCAL, selCountrylld)
                SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_COUNTRY_UNIT, selCountryUnit);
                binding.buttonConfirm.postDelayed({restartApp(this@CountrySelActivity)},200)
            }
        }

        loadingDialogHelper.show {  }
        CurrencyModel.getCountryList().subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===当前运营国家列表 信息=="+ it.toString())
                val resultData = Gson().fromJson(it.toString(), CountryListData::class.java)
                if (resultData != null){
                    entityList = resultData
                    adapter?.source = entityList!!
                    if (!SpUtil.Companion.getInstance().find(Constant.SpKey.SP_COUNTRY_sel).isNullOrEmpty()){
                        adapter?.source?.forEach {
                            if (SpUtil.Companion.getInstance().find(Constant.SpKey.SP_COUNTRY_sel).toString().equals(it.lld)){
                                it.isUse = true
                                country_id_event = it.lld
                            }
                        }
                    }
                    adapter?.notifyDataSetChanged()
                }
            },
            onError = {loadingDialogHelper.dismiss()}
        ).toCancelable()
    }
    private fun initRecycleview(){
        adapter= CountrySelAdapter()
        adapter!!.setOnMyItemClickListener { CountryListDataItem, pos ->
            var itemData = CountryListDataItem
            if (!itemData.isUse){
                for (i in 0 until entityList!!.size) {
                    entityList!!.get(i).isUse = false
                }
                entityList!!.get(pos).isUse = true
            }
            selCountrylld = itemData.lld
            country_id_event = itemData.lld
            selCountryUnit = itemData.currency
//            if(pos == 0){
//                selLanguage = ""
//            }
            adapter!!.notifyDataSetChanged()
        }

        var manager= LinearLayoutManager(this, RecyclerView.VERTICAL,false)
        binding.ry.layoutManager=manager
        binding.ry.adapter=adapter
    }

    fun restartApp(context: Context) {
        binding.ry.postDelayed({
            val intent = Intent(this@CountrySelActivity, SplashActivity::class.java)
            startActivity(intent)
            android.os.Process.killProcess(android.os.Process.myPid())
        },100)
        EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_EXIT,""))
        finish()
//        val intent = Intent(this@CountrySelActivity, SplashActivity::class.java)
//        startActivity(intent)
//        android.os.Process.killProcess(android.os.Process.myPid())

    }
}