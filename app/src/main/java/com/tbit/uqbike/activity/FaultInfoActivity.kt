package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.luck.picture.lib.utils.ToastUtils
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.preview.ImageInfo
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.FaultInfoMsgAdapter
import com.tbit.uqbike.adapter.ImageInfoAdapter
import com.tbit.uqbike.adapter.imgexplainAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityFaultInfoBinding
import com.tbit.uqbike.dialog.CostSumitDialog
import com.tbit.uqbike.entity.Comment
import com.tbit.uqbike.entity.CommentListData
import com.tbit.uqbike.entity.CostAppealSetData
import com.tbit.uqbike.entity.FaultInfoData
import com.tbit.uqbike.entity.FaultListData
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.FaultModel
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip
import org.jetbrains.anko.intentFor

class FaultInfoActivity : BaseActivity() {

    private lateinit var binding: ActivityFaultInfoBinding
    companion object {
        private const val EXTRA_DATA = "EXTRA_DATA"
        fun createIntent(context: Context, data: Int): Intent {
            return context.intentFor<FaultInfoActivity>(EXTRA_DATA to data)
        }
    }
    private val id: Int by bindExtra(EXTRA_DATA)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFaultInfoBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_fault_info)

        binding.toolbars.toolbarTitle.text = getString(R.string.s_record_info)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { finish() }

//        MyToastUtil.toast("======id====="+id)

        loadingDialogHelper.show {  }
        FaultModel.getFaultInfo(id).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===获取故障详情 信息=="+ it.toString())
                var resultData = Gson().fromJson(it.toString(), FaultInfoData::class.java)
                if (resultData != null){
                    binding.tvInfoState.text = resultData.status_name
                    //状态：0待处理，,1已处理-通过，2已处理-未通过
                    when(resultData.status){
                        0 -> {
                            ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_dcl),binding.imgInfo)
                            binding.tvInfoState.setTextColor(resources.getColor(R.color.c_F86125))
                        }
                        1 -> {
                            ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_ycl),binding.imgInfo)
                            binding.tvInfoState.setTextColor(resources.getColor(R.color.blue_namal))
                        }
                        2 -> {
                            ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_ybh),binding.imgInfo)
                            binding.tvInfoState.setTextColor(resources.getColor(R.color.c_838588))
                        }
                    }
                    binding.tvInfoCarno.text = resultData.vehicle_no
                    binding.tvInfoType.text = resultData.form_option_str
                    if (!resultData.info.isNullOrEmpty()) binding.tvInfoCont.text = resultData.info
                    if (resultData.attachments.size > 0){
                        val spacing = dip(3)
                        val spanCount = 4
                        var adapterImgExample = ImageInfoAdapter(this@FaultInfoActivity)
                        binding.rcvImageExplain.adapter = adapterImgExample
                        binding.rcvImageExplain.layoutManager = GridLayoutManager(this, spanCount)
                        binding.rcvImageExplain.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))

                        var photoData = ArrayList<ImageInfo>()
                        resultData.attachments.forEach {
                            var imgData = ImageInfo()
                            imgData.bigImageUrl = it
                            photoData.add(imgData)
                        }
                        adapterImgExample.source = photoData
                        adapterImgExample.notifyDataSetChanged()
                    }

                }
            },
            onError = {loadingDialogHelper.dismiss()}
        ).toCancelable()

        ComModel.getComment(ComModel.commentType_fault,id.toString(),0,100).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取评论列表 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), CommentListData::class.java)
                if (resultData.size > 0){
                    var adapter = FaultInfoMsgAdapter()
                    binding.rcvExplain.adapter = adapter
                    binding.rcvExplain.layoutManager = LinearLayoutManager(this)
                    adapter.source = resultData
                    adapter.notifyDataSetChanged()
                }
            }
        ).toCancelable()
    }
}