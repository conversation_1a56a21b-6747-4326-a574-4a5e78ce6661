package com.tbit.uqbike.activity

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.baidu.ar.it
import com.google.gson.Gson
import com.lsxiao.apollo.core.annotations.Receive
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.OrderCostRecordAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityCostRecordBinding
import com.tbit.uqbike.entity.FaultListData
import com.tbit.uqbike.entity.FaultListDataItem
import com.tbit.uqbike.entity.ReportNumData
import com.tbit.uqbike.entity.RidingRecordData
import com.tbit.uqbike.entity.RidingRecordDataItem
import com.tbit.uqbike.mvp.model.RideModel
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.ComModel.FormType_Cost
import com.tbit.uqbike.resqmodel.FaultModel
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip
import org.jetbrains.anko.startActivity

class OrderCostRecordActivity  : BaseActivity(){

    private val adapter by lazy { OrderCostRecordAdapter() }
    private lateinit var binding: ActivityCostRecordBinding
    private var selPos = -1
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCostRecordBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_order_cost_record)

        binding.toolbars.toolbarTitle.text = getString(R.string.s_selorder_cost)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { finish() }
        binding.toolbars.toolbarTitleR.text = getString(R.string.s_costrecord)
        binding.toolbars.toolbarTitleR.visibility = View.VISIBLE
        binding.toolbars.toolbarTitleR.setOnClickListener {
            startActivity<CostRecordActivity>()
        }

        getData(true)

        val spacing = dip(10)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        binding.rcv.layoutManager = LinearLayoutManager(this)
        binding.rcv.adapter = loadMoreWrapper(adapter)

        adapter.setOnMyItemClickListener { ridingRecordDataItem, i ->  }

        adapter.setOnMyItemClickListener { ridingRecordDataItem, i ->
            loadingDialogHelper.show {  }
            selPos = i
            var orderNo = ridingRecordDataItem.order_no
            var vehicle_no = ridingRecordDataItem.vehicle_no
            ComModel.getReportNum(ComModel.Report_Cost,orderNo,vehicle_no).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===用户反馈统计 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), ReportNumData::class.java)
                    if (resultData != null && resultData.num == 0){
                        startActivity(CostAppealActivity.createIntent(this, vehicle_no,orderNo))
                    }else{
                        MyToastUtil.toast(getString(R.string.s_sumit_suc))
                    }
                },
                onError = {loadingDialogHelper.dismiss()}
            ).toCancelable()
        }
        binding.capaLayout.toContent()
        binding.smartLayout.setOnRefreshListener {
            lastLoaded = false
            lastId = 0
            dataList.clear()
            adapter.source = dataList
            adapter.notifyDataSetChanged()
            getData(true)
        }
    }
    @Receive(Constant.Event.EVENT_COST_DEL)
    fun EVENT_COST_DEL(){
        if (selPos != -1){
            dataList.removeAt(selPos)
            adapter.source = dataList
            adapter.notifyItemChanged(selPos)
        }
    }
    private fun loadMoreWrapper(adapter: RecyclerView.Adapter<out RecyclerView.ViewHolder>)
            : LoadMoreWrapper<out RecyclerView.ViewHolder> {
        val loadMoreAdapter = LoadMoreWrapper(adapter)
        loadMoreAdapter.setOnLoadMoreListener{ onLoadMore() }
        return loadMoreAdapter
    }

    private fun onLoadMore() {
        if (!lastLoaded)
            getData(false)
    }
    private var lastLoaded = false
    private val loadMoreView by lazy { View(this).apply { layoutParams = ViewGroup.LayoutParams(1, 1) } }
    val dataList = mutableListOf<RidingRecordDataItem>()
    var lastId = 0
    private val pageSize = Glob.pageNum
    private fun getData(showLoading: Boolean = true) {
        if (showLoading) {
            loadingDialogHelper.show { }
        }
        RideModel.getRideRecordNew(lastId, pageSize,1).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                binding.smartLayout.finishRefresh()
                MyLogUtil.Log("1111","===获取可申诉列表 信息=="+ it.toString())
                var resultData = Gson().fromJson(it.toString(), RidingRecordData::class.java)
                if (resultData != null && resultData.size > 0){
                    lastLoaded = resultData.size < pageSize
                    dataList.addAll(resultData)
                    lastId = resultData.last().id

                    (binding.rcv.adapter as? LoadMoreWrapper<*>)?.setLoadMoreView(if (lastLoaded) null else loadMoreView)
                    adapter.source = dataList
                    adapter.notifyDataSetChanged()
                    binding.capaLayout.toContent()

//                    if (lastLoaded) MyToastUtil.toast(getString(R.string.no_more_tips))
                }else{
                    lastLoaded = true
                    if (adapter.source.size == 0) binding.capaLayout.toEmpty()
                }
            }
        ).toCancelable()
    }

    override fun onDestroy() {
        super.onDestroy()
    }
}