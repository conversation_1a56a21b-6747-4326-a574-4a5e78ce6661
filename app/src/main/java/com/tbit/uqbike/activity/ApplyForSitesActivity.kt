package com.tbit.uqbike.activity

import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import com.doule.database.CoroutinesUtil
import com.google.gson.Gson
import com.tbit.maintenance.map.listener.GeoSearchListener
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.map.base.IBaseMap
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.tbituser.map.bean.MapStatus
import com.tbit.tbituser.map.listener.OnMapLoadedCallback
import com.tbit.tbituser.map.listener.OnMapStatusChangeListener
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.databinding.ActivityApplyForSitesBinding
import com.tbit.uqbike.delegate.GeoMapDelegate
import com.tbit.uqbike.delegate.ParkPointWithRangeMapDelegate
import com.tbit.uqbike.delegate.ProhibitAreaWithRangeMapDelegate
import com.tbit.uqbike.entity.GetInAreaData
import com.tbit.uqbike.entity.getAdressData
import com.tbit.uqbike.entity.getAreaData
import com.tbit.uqbike.map.bean.Location
import com.tbit.uqbike.mvp.constract.ApplyForSitesContract
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.presenter.ApplyForSitesPresenter
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.delay
import org.jetbrains.anko.toast

class ApplyForSitesActivity: BaseActivity(), ApplyForSitesContract.View, GeoSearchListener,
    OnMapStatusChangeListener, OnMapLoadedCallback {

    private val presenter = ApplyForSitesPresenter(this)
    private var map: IBaseMap? = null
    private val geoMapDelegate = GeoMapDelegate()
    private val parkPointMapDelegate = ParkPointWithRangeMapDelegate()
    private val prohibitAreaMapDelegate = ProhibitAreaWithRangeMapDelegate()
    private val geoSearch by lazy { FlavorConfig.mapFactory.createGeoSearch(this, this) }
    private lateinit var binding: ActivityApplyForSitesBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityApplyForSitesBinding.inflate(layoutInflater)
        setContentView(binding.root)

//        setContentView(R.layout.activity_apply_for_sites)
        setSupportActionBar(binding.toolbars.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        binding.toolbars.toolbarTitle.text = getString(R.string.site_apply_title)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { onBackPressed() }

        lifecycle.addObserver(presenter)

        val map = FlavorConfig.mapFactory.createMap(binding.flMap, savedInstanceState)
        this.map = map
        map.setOnMapLoadedCallback(this)
        map.addOnMapStatusChangeListener(this)
        geoMapDelegate.setMap(map)
        parkPointMapDelegate.setMap(map)
        prohibitAreaMapDelegate.setMap(map)

        binding.editApplyReason.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                if (binding.editApplyReason.text.toString().length > 0){
                    binding.btnSubmit.setTextColor(resources.getColor(R.color.white))
                }else{
                    binding.btnSubmit.setTextColor(resources.getColor(R.color.white50))
                }
            }
        })

        binding.btnSubmit.setOnClickListener { onApplySitesClick() }
    }

    override fun onDestroy() {
        geoSearch.destroy()
        super.onDestroy()
    }

    override fun onMapLoaded() {
        CoroutinesUtil.launchMain {
            delay(500)
            showCurrentLocate()
        }
    }

    private fun showCurrentLocate() {
        val location = getLocation() ?: return
        map?.showCurrentLocate(location)
    }

    private fun getLocation(): Location? {
        return map?.getCurrentLocate() ?: LocationModel.lastLocation
    }

    override fun onMapStatusChangeStart(status: MapStatus, reason: Int) {

    }

    override fun onMapStatusChange(status: MapStatus) {

    }

    var isFirstChange = false
    override fun onMapStatusChangeFinish(status: MapStatus) {
        if(Glob.isGoogleServiceAvailable && Glob.isGoogleNetAvailable != null && Glob.isGoogleNetAvailable!!){
//            status.target = GPSUtil.gcj02_To_Gps84_new(status.target.lat,status.target.lng)
        }
        if (isFirstChange){
            MyLogUtil.Log("1111","======================onMapStatusChangeFinish============")
            CoroutinesUtil.launchMain {
                delay(100)
                geoSearch.reverseGeoCode(status.target)
                presenter.getNearGeo(status.target.lat, status.target.lng, 5000)
                presenter.getNearParkPoints(status.target.lat, status.target.lng, 500)
                presenter.getNearProhibits(status.target.lat, status.target.lng)

                setSearchCenter(status.target)
            }
        }
        isFirstChange = true
    }
    var searchCenter = LatLng()
        private set
    private fun setSearchCenter(center: LatLng) {
        searchCenter = center
        if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
            searchCenter = GPSUtil.bd09_To_gps84(searchCenter.lat,searchCenter.lng)
        }else{
//                searchCenter = GPSUtil.gcj02_To_Gps84_new(searchCenter.lat,searchCenter.lng)
        }
        IsOutArea()
    }
    fun IsOutArea(){
//        MyLogUtil.Log("3333","===判断中心点坐标======"+searchCenter.lat+",,"+searchCenter.lng)
        ComModel.getInarea(searchCenter.lat,searchCenter.lng).subscribeBy(
            onNext = {
                MyLogUtil.Log("3333","===判断中心点坐标是否在运营区 信息=="+it.toString())
                val resultData: GetInAreaData = Gson().fromJson(it.toString(), GetInAreaData::class.java)
                if(resultData != null && resultData.area_id == 0L){
                    binding.tvAreaout.visibility = View.VISIBLE
                }else{
                    binding.tvAreaout.visibility = View.GONE
                }
            }
        ).toCancelable()
    }

    override fun onGetReverseGeoCodeResult(success: Boolean, address: String?) {
        MyLogUtil.Log("1111","=======onGetReverseGeoCodeResult ====")
//        text_address.text = if (success) address else getString(R.string.unknown_address)
//        val location = getLocation() ?: return
//        MyLogUtil.Log("1111","===位置更新请求 11==="+location.city+","+location.latitude+"-"+location.longitude)
        var latLng = LatLng()
        latLng.lat = searchCenter.lat
        latLng.lng = searchCenter.lng
        if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
            latLng = GPSUtil.bd09_To_gps84(searchCenter.lat,searchCenter.lng)
        }
        latLng.lat = GPSUtil.retain6(latLng.lat)
        latLng.lng = GPSUtil.retain6(latLng.lng)
        ComModel.getAdress(latLng.lat,latLng.lng).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===当前位置地址 信息=="+it.toString())
                val resultData = Gson().fromJson(it.toString(), getAdressData::class.java)
                if (resultData != null){
                    binding.textAddress.text = resultData.address
                }
            }
        ).toCancelable()
    }

    override fun onGetNearGeoSuccess(areaData: getAreaData) {
//        geoMapDelegate.setGeo(geoList)
        if (areaData.isNotEmpty()) {
            geoMapDelegate.setGeoNew(areaData)
        }
    }

    override fun onGetNearParkPointsSuccess(areaData: getAreaData) {
//        parkPointMapDelegate.setParkPoints(parkPoints)
        if (areaData.isNotEmpty()) {
            parkPointMapDelegate.setPartAreaData(areaData)
        }
    }

    override fun onGetNearProhibitsSuccess(areaData: getAreaData) {
        prohibitAreaMapDelegate.setProhibitAreaData(areaData)
    }

    private fun onApplySitesClick() {
        if (binding.tvAreaout.visibility == View.VISIBLE){
            MyToastUtil.toast(getString(R.string.s_p_inarea))
            return
        }
        val point_name = binding.textAddress.text.toString()
        val reason = binding.editApplyReason.text.toString().trim { it <= ' ' }
        var latLng = map?.getTargetLocation()
        if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
            latLng = GPSUtil.bd09_To_gps84(latLng!!.lat,latLng!!.lng)
        }

        if (TextUtils.isEmpty(reason)) {
            toast(R.string.site_apply_reason_not_null)
            return
        }

        if (latLng == null) {
            toast(R.string.site_apply_positioning)
            return
        }


        val cancellable = presenter.applyForSites(latLng.lat, latLng.lng,point_name, reason)
        loadingDialogHelper.show { cancellable.cancel() }
    }

    override fun onApplySitesSuccess() {
        loadingDialogHelper.dismiss()
        toast(R.string.site_apply_application_received)
        finish()
    }

    override fun onApplySitesFailed(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }
}