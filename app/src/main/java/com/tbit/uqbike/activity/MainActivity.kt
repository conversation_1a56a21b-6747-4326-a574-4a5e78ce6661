package com.tbit.uqbike.activity
import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.MenuItem
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.NonNull
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import com.doule.database.CoroutinesUtil
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.gson.Gson
import com.lsxiao.apollo.core.annotations.Receive
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.map.base.IRouteLine
import com.tbit.maintenance.map.bean.InfoWindowOption
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.map.base.MarkerWrapper
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.Glob
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.model.MainRenTalModel
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.AdDeposit
import com.tbit.uqbike.bean.BikeState
import com.tbit.uqbike.bean.ParkPoint
import com.tbit.uqbike.bean.PhoneInfo
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.databinding.ActivityMainBinding
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.dialog.CustomPhoneCallDialog
import com.tbit.uqbike.dialog.InviteDialog
import com.tbit.uqbike.dialog.UserHelpBottomSheep
import com.tbit.uqbike.dialog.kfBottomSheep
import com.tbit.uqbike.dialog.util.MainDialogUtil
import com.tbit.uqbike.dialog.util.RidingDialogUtil
import com.tbit.uqbike.entity.BatterComData
import com.tbit.uqbike.entity.CarInfoData
import com.tbit.uqbike.entity.Coupon
import com.tbit.uqbike.entity.MyOrderStateData
import com.tbit.uqbike.entity.ParkData
import com.tbit.uqbike.entity.RIG_TYPE_NEWUSER
import com.tbit.uqbike.entity.RigisterData
import com.tbit.uqbike.entity.SysMinAmount
import com.tbit.uqbike.entity.VehicleData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.entity.getAreaData
import com.tbit.uqbike.entity.isFirstOrderData
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.entity.parkingData
import com.tbit.uqbike.entity.type_coupon_invite
import com.tbit.uqbike.entity.type_coupon_money
import com.tbit.uqbike.entity.type_coupon_ridecard
import com.tbit.uqbike.fragment.BikeInfoFragment
import com.tbit.uqbike.fragment.CameraPermissionFragment
import com.tbit.uqbike.fragment.ClockFragment
import com.tbit.uqbike.fragment.MainAdFragment
import com.tbit.uqbike.fragment.MainAdFullFragment
import com.tbit.uqbike.fragment.MainBusinessFragment
import com.tbit.uqbike.fragment.MainMapFragment
import com.tbit.uqbike.fragment.ParkPointInfoFragment
import com.tbit.uqbike.fragment.PermissionFragment
import com.tbit.uqbike.fragment.RidingFragment
import com.tbit.uqbike.fragment.UpdateFragment
import com.tbit.uqbike.map.bean.Location
import com.tbit.uqbike.mvp.constract.MainContract
import com.tbit.uqbike.mvp.model.BikeModel
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.model.RouteSearchModel
import com.tbit.uqbike.mvp.model.UserModel
import com.tbit.uqbike.mvp.presenter.MainPresenter
import com.tbit.uqbike.onepasslogin.Utils
import com.tbit.uqbike.qrcode.CommonUtils
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.OrderModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.roundview.RoundLinearLayout
import com.tbit.uqbike.utils.BleUtils
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.NetUtils
import com.tbit.uqbike.utils.UrlDecodeUtil
import com.zj.easyfloat.EasyFloat
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.subjects.PublishSubject
import kotlinx.coroutines.delay
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.clearTop
import org.jetbrains.anko.dip
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.singleTop
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.toast
import org.json.JSONObject
import permissions.dispatcher.PermissionUtils
import java.util.concurrent.TimeUnit

class MainActivity: BaseActivity(), MainContract.View {
    companion object {
        var isColdStar = "1"
        val EXTRA_FROM_LOGIN = "EXTRA_FROM_LOGIN"
        val REQUEST_TO_RIDING = 1
        val REQUEST_OPEN_GPS = 2
        fun createIntent(context: Context, fromLogin: Boolean? = null): Intent {
            return context.intentFor<MainActivity>(EXTRA_FROM_LOGIN to fromLogin).clearTop().singleTop()
        }
    }
    private var isFirstOrder = false
    private var exitTime = 0L
    private var debugClickCount = 0
    private var lastDebugClickTime = 0L
    private var isPendingPaymentFromIntent: Boolean = false // Add this line
    //    private val bleCheckFragment by lazy { supportFragmentManager.findFragmentById(R.id.fragment_ble_check) as BleCheckFragment }
    private val mainMapFragment by lazy { supportFragmentManager.findFragmentById(R.id.main_map_fragment) as MainMapFragment }
    private val mainBusinessFragment by lazy { supportFragmentManager.findFragmentById(R.id.main_upda_fragment) as MainBusinessFragment }
    private val updateFragment by lazy { supportFragmentManager.findFragmentById(R.id.fragment_update) as UpdateFragment }
    private val mainAdFragment by lazy { supportFragmentManager.findFragmentById(R.id.main_ad_fragment) as MainAdFragment }
    private val mainAdFullFragment by lazy { supportFragmentManager.findFragmentById(R.id.main_adfull_fragment) as MainAdFullFragment }
    private val bikeInfoFragment by lazy { supportFragmentManager.findFragmentById(R.id.bike_info_fragment) as BikeInfoFragment }
    private val bikeClockFragment by lazy { supportFragmentManager.findFragmentById(R.id.bike_clock_fragment) as ClockFragment }
    private val bikeRidingFragment by lazy { supportFragmentManager.findFragmentById(R.id.bike_riding_fragment) as RidingFragment }
    private val geoMapDelegate by lazy { mainMapFragment.geoMapDelegate }
    private val bikeMapDelegate by lazy { mainMapFragment.bikeMapDelegate }
    private val parkPointMapDelegate by lazy { mainMapFragment.parkPointMapDelegate }
    private val prohibitAreaMapDelegate by lazy { mainMapFragment.prohibitAreaMapDelegate }
    private val routeLineMapDelegate by lazy { mainMapFragment.routeLineMapDelegate }
    private val presenter = MainPresenter(this)
    //    private var loadDataAction :KFunction0<Unit> = ::getBikes
    private val parkPointInfoBehavior by lazy { BottomSheetBehavior.from(supportFragmentManager.findFragmentById(R.id.park_point_info_fragment)!!.requireView()) }
    private val bikeInfoBehavior by lazy { BottomSheetBehavior.from(supportFragmentManager.findFragmentById(R.id.bike_info_fragment)!!.requireView()) }
    private val bikeClockBehavior by lazy { BottomSheetBehavior.from(supportFragmentManager.findFragmentById(R.id.bike_clock_fragment)!!.requireView()) }
    private val bikeRidingBehavior by lazy { BottomSheetBehavior.from(supportFragmentManager.findFragmentById(R.id.bike_riding_fragment)!!.requireView()) }
    private var searchMenu: MenuItem? = null
    private var adDeposit = Glob.adDeposit
    private val publish = PublishSubject.create<Int>()
    private val hasBookBike get() = bikeInfoFragment.bookInfo != null
    private val loadDataDisposable = publish.sample(1, TimeUnit.SECONDS)
        .observeOn(AndroidSchedulers.mainThread())
        .subscribeBy(onNext = { loadDataImpl() })
    private var isPassProtocol = false

    val infoWindowView by lazy { layoutInflater.inflate(R.layout.layout_info_nav, null)}
    var tvNavP : TextView? = null
    var tvNavInfo : TextView? = null
    var line_nav: LinearLayout? = null
    var rlyNav : RoundLinearLayout? = null
    var markerNav: MarkerWrapper? = null
    var img_nav: ImageView? = null
    //    var userDialog = NewUserDialog.newInstance("","", "")
//    var rideDialog = NewUserDialog.newInstance("","", "")
    var parking_Data : parkingData? = null

    private val behaviorCallback = object : BottomSheetBehavior.BottomSheetCallback() {
        override fun onStateChanged(@NonNull bottomSheet: View, newState: Int) {
            //拖动
            if (newState == BottomSheetBehavior.STATE_DRAGGING) {//判断为向下拖动行为时，则强制设定状态为展开
//                parkPointInfoBehavior.setState(BottomSheetBehavior.STATE_EXPANDED );
                if (parkPointInfoBehavior !=null && parkPointInfoBehavior.state == BottomSheetBehavior.STATE_DRAGGING){
                    parkPointInfoBehavior.setState(BottomSheetBehavior.STATE_EXPANDED )
                }
                if (bikeInfoBehavior !=null && bikeInfoBehavior.state == BottomSheetBehavior.STATE_DRAGGING){
                    bikeInfoBehavior.setState(BottomSheetBehavior.STATE_EXPANDED )
                }
                if (bikeClockBehavior !=null && bikeClockBehavior.state == BottomSheetBehavior.STATE_DRAGGING){
                    bikeClockBehavior.setState(BottomSheetBehavior.STATE_EXPANDED )
                }
//                onTopStatesHide()
            }
            if (newState == BottomSheetBehavior.STATE_EXPANDED){
                if (bottomSheet.id == R.id.bike_info_fragment) bikeInfoFragment.onPageStar()
                if (bottomSheet.id == R.id.park_point_info_fragment) (supportFragmentManager.findFragmentById(R.id.park_point_info_fragment) as ParkPointInfoFragment).onPageStar()
            }
            if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                onTopStatesHide()
                if (bottomSheet.id == R.id.bike_info_fragment) bikeInfoFragment.onPageEnd()
                if (bottomSheet.id == R.id.park_point_info_fragment) (supportFragmentManager.findFragmentById(R.id.park_point_info_fragment) as ParkPointInfoFragment).onPageEnd()
            }
        }
        override fun onSlide(@NonNull bottomSheet: View, slideOffset: Float) { }
    }
    private lateinit var binding: ActivityMainBinding
    var isHomeState = true //是否在主页 false 为骑行页
    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

//        setContentView(com.tbit.uqbike.R.layout.activity_main)
        EventBus.getDefault().register(this);
        if (!SpUtil.Companion.getInstance().find(Constant.SpKey.SP_COUNTRY_LOCAL).isNullOrEmpty()){
            Glob.CurrencyLld = SpUtil.Companion.getInstance().find(Constant.SpKey.SP_COUNTRY_LOCAL).toString()
        }
        lifecycle.addObserver(presenter)
        bikeRidingBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)
        bikeClockBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)
        bikeInfoBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)
        parkPointInfoBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)

        infoWindowView.isClickable = false
        tvNavP = infoWindowView.findViewById<TextView>(R.id.tv_nav_p)
        line_nav = infoWindowView.findViewById<LinearLayout>(R.id.line_nav)
        tvNavInfo = infoWindowView.findViewById<TextView>(R.id.tv_nav_info)
        rlyNav = infoWindowView.findViewById<RoundLinearLayout>(R.id.rly_nav)
        img_nav = infoWindowView.findViewById<ImageView>(R.id.img_nav)
        var isn = false
        binding.imageMy.clickDelay {
            MyLogUtil.Log("1111", "tttttddddd")
            finish();
            return@clickDelay;

            val properties = JSONObject()
            MDUtil.clickEvent("back_home_click",properties)
            isHomeState = true
            mainAdFragment.goHome() // 这个是另一个广告位
            binding.layoutMainBottomMenu.imgGohome.visibility = View.VISIBLE
            binding.imgMenuBg.visibility = View.VISIBLE
            binding.lyBomMenu.visibility = View.VISIBLE
            binding.rlMainRiding.visibility = View.GONE
            onTopStatesHide()
            bikeInfoBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)
            parkPointInfoBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)
            bikeRidingBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)
            bikeClockBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)
            setBomInviteVis()
            setUlocalView()
//            mainAdFullFragment.getData() // 移除这里的调用，或者确认是否必要并添加来源
        }
        binding.layoutMainBottomMenu.imgGohome.clickDelay {
            MyLogUtil.Log("7777","====返回首页 点击=======")
            val properties = JSONObject()
            MDUtil.clickEvent("back_home_click",properties)
            isHomeState = true
            mainAdFragment.goHome()
            binding.layoutMainBottomMenu.imgGohome.visibility = View.VISIBLE
            binding.imgMenuBg.visibility = View.VISIBLE
            binding.lyBomMenu.visibility = View.VISIBLE
            binding.rlMainRiding.visibility = View.GONE
            onTopStatesHide()
            bikeInfoBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)
            parkPointInfoBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)
            bikeRidingBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)
            bikeClockBehavior.setState(BottomSheetBehavior.STATE_HIDDEN)
            setBomInviteVis()
            setUlocalView()
//            mainAdFullFragment.getData() // 移除这里的调用，或者确认是否必要并添加来源
        }
        binding.layoutMainBottomMenu.imgGohome.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("extension_icon_click",properties)
            hiddenUi()
        }
        binding.ryBomInvite.setOnClickListener {
            val properties = JSONObject()
            properties.put("page_redirect", UrlDecodeUtil().getParm(Glob.UrlInvite)) // 设置商品 名称
            MDUtil.clickEvent("fixed_position",properties)
            startActivity<WebActionActivity>(WebActionActivity.TITLE to getString(R.string.s_invite),
                WebActionActivity.URL to UrlDecodeUtil().getParm(Glob.UrlInvite),WebActionActivity.IsHideHead to true,
                WebActionActivity.se_type to "4")
        }
        bikeMapDelegate.onMarkerClickListener = ::onBikeClick
        parkPointMapDelegate.onMarkerClickListener = ::onParkPointClick
        mainMapFragment.onAreaIdSucListener = {
            this.onAreaIdSucListener("MainActivity_MapListener") // 调用 MainActivity 定义的方法
        }
        mainMapFragment.onAreaIdFailListener = {
            this.onAreaIdFailListener("MainActivity_MapListener") // 调用 MainActivity 定义的方法
        }
        mainMapFragment.onSearchCenterChangeListener = { onSearchCenterChange() }
        mainMapFragment.onMapClickListener = { onGetDepositSuccess(Glob.adDeposit) }
        mainMapFragment.onMsgUresdListener = {
            mainAdFragment.updaMsg(it)
        }
        mainBusinessFragment.onUpdaListener={
            updateFragment.setUpdaDown(it)
        }

        binding.layoutMainBottomMenu.imageLocate.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("position_icon_click",properties)
            if(!IsAuth()) return@clickDelay
            if(!hasLocationPermission()){
                openLocal()
                return@clickDelay
            }
            showCurrentLocate()
        }
        binding.layoutMainBottomMenu.imageRefresh.clickDelay { loadData() }
        binding.lyMenuBalance.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("wallet_icon_click",properties)
            if(!hasLocationPermission()){
                openLocal()
                return@clickDelay
            }
            if (tryLoginIfNot()) startActivity<WalletActivity>()
        }
        binding.lyMenuCost.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("billing_rules_icon_click",properties)
            if (tryLoginIfNot()) {
                loadingDialogHelper!!.show {  }
                PageModel.getPageUrl(PageModel.billing_rules).subscribeBy(
                    onNext = {
                        loadingDialogHelper!!.dismiss()
                        MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                        var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                        startActivity<WebActivity>(WebActivity.TITLE to getString(R.string.billing_rules),
                            WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
                    },
                    onError = {loadingDialogHelper!!.dismiss()}
                ).toCancelable()
            }
        }
//        binding.layoutMainBottomMenu
        binding.lyMenuMore.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("more_icon_click",properties)
            if(!IsAuth()) return@clickDelay
            if(!hasLocationPermission()){
                openLocal()
                return@clickDelay
            }
            if (tryLoginIfNot()) {
                lifecycleDialogHelper.show(UserHelpBottomSheep(this@MainActivity))
            }
        }
//        binding.layoutMainBottomMenu
        binding.lyMenuHotline.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("contact_icon_click",properties)
            if(!IsAuth()) return@clickDelay
            if(!hasLocationPermission()){
                openLocal()
                return@clickDelay
            }
//            MainDialogUtil.kfDig(loadingDialogHelper,lifecycleDialogHelper)
            if (tryLoginIfNot()) {
                lifecycleDialogHelper.show(kfBottomSheep(this@MainActivity))
            }
        }

//        var isShowMain = true
//        binding.rlMsgNotify.clickDelay {
//            if(!IsAuth()) return@clickDelay
//            if (tryLoginIfNot()) startActivity<MessageActivity>()

//            var carRentalDialog = CodeDialog("")
//            lifecycleDialogHelper.show(carRentalDialog)

//            if (Settings.canDrawOverlays(ContextUtil.getContext())){
//                val textWindow = TitleTextWindow(this,"jdfksjfksd")
//                textWindow.show()
//            }else{
//                // 在Activity或Fragment中请求权限
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
//                    val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:$packageName"))
//                    startActivityForResult(intent, 200)
//                }
//            }
//        }
//        userDialog.onGoListener = { binding.rlScanner.performClick()}
//        rideDialog.onGoListener = { binding.rlScanner.performClick()}
        binding.lyRidingBtn.setOnClickListener { binding.rvSeeOrder.performClick() }
        binding.rvSeeOrder.clickDelay {
            if(MainState == 2){
                var isRental = false
                if (MainOrderState == Constant.OrderType.RENTAL_Y) isRental = true
                startActivity(RecordDetailActivity.createIntent(this@MainActivity,orderNo,false,isRental))
            }else if(MainState == 1){
                binding.layoutMainBottomMenu.imgGohome.performClick()
            }
        }
        binding.rlScanner.clickDelay {
            if (Glob.isRental){
                val properties = JSONObject()
                MDUtil.clickEvent("scan_rent_car_click",properties)
            }else{
                val properties = JSONObject()
                MDUtil.clickEvent("scan_ride_bike_click",properties)
            }
            if(!IsAuth()) return@clickDelay
            if (tryLoginIfNot()) {
                if (!hasCamPermission()){
                    if (SpUtil.getInstance().find(Constant.SpKey.SP_PERM_CAM).isNullOrEmpty()){
                        SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_PERM_CAM,"1")
                    }else{
                        CommDialog.Builder(this@MainActivity).setTitle(getString(R.string.s_per_carm_title)).setContent(getString(R.string.s_per_carm_cont))
                            .setLeftText(ResUtil.getString(R.string.cancel)).setRightText(getString(R.string.s_set)).setCanceledOnOutside(true)
                            .setClickListen(object : CommDialog.TwoSelDialog {
                                override fun leftClick() {}
                                override fun rightClick() {
                                    LocationUtil.gotoLocalPermiss(this@MainActivity)
                                }
                            }).build().show()
                        return@clickDelay
                    }
                }

                (supportFragmentManager.findFragmentById(R.id.fragment_camera_permission) as CameraPermissionFragment).requestPermission {
                    if(!hasLocationPermission()){
                        openLocal()
                    }else{
                        if (!LocationUtil.isGpsEnabled()) {
                            MainDialogUtil.GpsDig(this@MainActivity)
                            return@requestPermission
                        }
                        if(MainState == 2){
                            var isRental = false
                            if (MainOrderState == Constant.OrderType.RENTAL_Y) isRental = true
//                            MainDialogUtil.RecordDetailDig(this@MainActivity,orderNo,isRental)
                            startActivity(RecordDetailActivity.createIntent(this@MainActivity,orderNo,false,isRental))
                        }else if(MainState == 1){
//                            binding.rlMainRiding.performClick()
                            binding.layoutMainBottomMenu.imgGohome.performClick()
                        }else{
                            loadingDialogHelper.show {  }
                            UserModel.getCreatRideOrder()
                                .subscribeBy(
                                    onNext = {
                                        loadingDialogHelper.dismiss()
                                        MyLogUtil.Log("1111","用户是否可以创建订单==="+it.toString())
                                        if (!it.toString().equals("[]")){
                                            val resultData = Gson().fromJson(it.toString(), isFirstOrderData::class.java)
                                            if (resultData != null) {
                                                isFirstOrder = resultData.is_first
                                                if (resultData.is_first_temp){
                                                    SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_NEWUSERCAR,"1")
                                                }else{
                                                    SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_NEWUSERCAR,"")
                                                }
                                            }
                                        }else{
                                            isFirstOrder = false
                                        }
                                        if (Glob.isRental){
                                            goToScanBorrow()
                                        }else{
                                            RidingDialogUtil.showRideCardDialog(this@MainActivity, onNotify = {goToScanBorrow()})
                                        }
                                    },
                                    onError = {
                                        val errCode = ErrHandler.getErrCode(it)
                                        if (errCode == Constant.ErrCode.RIDE_LOW){
                                            //sys_minimum_amount 最低骑行额度
                                            val listKey = arrayOf("sys_minimum_amount")
                                            ComModel.getConfig(listKey).subscribeBy(
                                                onNext = {
                                                    loadingDialogHelper.dismiss()
                                                    MyLogUtil.Log("1111","===获取最低骑行额度 信息=="+it.toString())
                                                    var resultData = Gson().fromJson(it.toString(), SysMinAmount::class.java)
                                                    MainDialogUtil.MinAmountDig(this@MainActivity,resultData.sys_minimum_amount)
                                                },
                                                onError = {loadingDialogHelper.dismiss()}
                                            ).toCancelable()
                                        }else{
                                            MyToastUtil.toast(ErrHandler.getErrMsg(it))
                                            loadingDialogHelper.dismiss()
                                        }
                                    }
                                ).toCancelable()
                        }
                    }
                }

            }

        }

        if(!SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
            delayRequestLocationPermission()
            checkGpsEnabled()
//            bleCheckFragment.check {
//                MyLogUtil.Log("1111","======= 蓝牙是否可用  ==="+it)
//            }
        }
        parkPointInfoBehavior.setBottomSheetCallback(behaviorCallback)
        bikeInfoFragment.onCanListener = {
            bikeInfoBehavior.setState(BottomSheetBehavior.STATE_HIDDEN );
            if(!it.equals("0")){
                bikeClockBehavior.state = BottomSheetBehavior.STATE_EXPANDED
//                ly_over.visibility = View.VISIBLE
                bikeClockFragment.sumitOrderData(it)
            }else{
                binding.rlScanner.postDelayed({goToScanBorrow()},200)
            }
        }
        var oldBatter = 0
        bikeInfoBehavior.setBottomSheetCallback(behaviorCallback)
        bikeClockFragment.onOrderListener = {
            bikeClockBehavior.setState(BottomSheetBehavior.STATE_HIDDEN );
            if(!it.equals("0")){
                //开锁成功 返回订单号
                RestBick()
                oldBatter = 0
                isFirstGetOrderState = false
                if (isHomeState){
                    binding.layoutMainBottomMenu.imgGohome.performClick()
                }
                binding.lyOver.postDelayed({
                    bikeRidingFragment.getConfigByRiding(true,Glob.isRental)
                    bikeRidingFragment.getOrderInfo(it,false)
                    orderNo = it
                    MainState = 1
                    bikeRidingBehavior.state = BottomSheetBehavior.STATE_EXPANDED
                    setViewRiding()
                    binding.rlMainRiding.visibility = View.GONE
                    setStopingVis()
                },800)
            }else{
                binding.lyOver.visibility = View.GONE
            }
        }

        bikeClockBehavior.setBottomSheetCallback(behaviorCallback)
        bikeRidingFragment.onRidingListener = {orderNo,isUserEnd,isRental_MyOrder->
            if (!orderNo.isNullOrEmpty()){
                RestBick()
                bikeRidingBehavior.setState(BottomSheetBehavior.STATE_HIDDEN );
                setViewNomal()
                binding.rlMainRiding.visibility = View.GONE
                binding.lyStoping.visibility = View.GONE
                startActivity(RecordDetailActivity.createIntent(this, orderNo,isUserEnd,isRental_MyOrder))
                setBomRidingView(false,false)
            }
        }
        var resultDataBatter : BatterComData? = null
        val listKey = arrayOf("sys_oper_electric")
        ComModel.getConfig(listKey).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取电池信息 信息=="+it.toString())
                resultDataBatter = Gson().fromJson(it.toString(), BatterComData::class.java)
            }
        ).toCancelable()

        var getRideStateTime = 0
        bikeRidingFragment.onRidingBatterListener = {orderNo,batter->
//            MyLogUtil.Log("1111","=====当前骑行车辆======"+SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO))
            getRideStateTime ++
            if (getRideStateTime == 5){
                getRideStateTime = 0
                getRideState()
            }
            setUlocalView()
            mainAdFragment.setTopState()
            if(SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO).isNullOrEmpty() && orderNo.isNullOrEmpty()){
//                MyLogUtil.Log("1111","= orderNo null ==")
                binding.rlBatter.visibility = View.GONE
                oldBatter = 0
            }else{
                if (resultDataBatter != null){
//                    MyLogUtil.Log("1111",orderNo+","+batter+","+SpUtil.getInstance().find(Constant.SpKey.SP_VECHNO))
                    if (binding.rlMainRiding.isShown  || binding.rlMainRiding.visibility == View.VISIBLE){
                        binding.rlBatter.visibility = View.GONE
                    }else{
                        binding.rlBatter.visibility = View.VISIBLE
                        if (oldBatter != batter){
                            oldBatter = batter
                            if (batter >= Constant.BatLevel.Bat_H){
                                binding.tvBatter.setTextColor(resources.getColor(com.tbit.uqbike.R.color.c_00C420))
                                ImageLoad.loadimg(resources.getDrawable(com.tbit.uqbike.R.drawable.icon_battery_green),binding.imgBatter)
                            }else if(batter >= Constant.BatLevel.Bat_M){
                                binding.tvBatter.setTextColor(resources.getColor(com.tbit.uqbike.R.color.c_FF9500))
                                ImageLoad.loadimg(resources.getDrawable(com.tbit.uqbike.R.drawable.icon_battery_orange),binding.imgBatter)
                            }else {
                                binding.tvBatter.setTextColor(resources.getColor(com.tbit.uqbike.R.color.c_ED0000))
                                ImageLoad.loadimg(resources.getDrawable(com.tbit.uqbike.R.drawable.icon_battery_red),binding.imgBatter)
                            }
                            binding.tvBatter.text = batter.toString()+"%"
                        }
                    }
                }else{
//                    MyLogUtil.Log("1111","= resultDataBatter null ==")
                    val listKey = arrayOf("sys_oper_electric")
                    ComModel.getConfig(listKey).subscribeBy(
                        onNext = {
                            MyLogUtil.Log("1111","===获取电池信息 信息=="+it.toString())
                            resultDataBatter = Gson().fromJson(it.toString(), BatterComData::class.java)
                        }
                    ).toCancelable()
                }
            }
        }
        bikeRidingFragment.onPowerListener = {
            if (it.equals(Constant.SUC)){
                binding.lyPowering.visibility = View.VISIBLE
            }else{
                binding.lyPowering.visibility = View.GONE
            }
        }
        bikeRidingFragment.onRidingOpenBleListener = {binding.tvCamOpen.performClick()}
        bikeRidingFragment.onRidingKFListener = {binding.layoutMainBottomMenu.imageConsumerHotline.performClick()}
        bikeRidingBehavior.setBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
            override fun onStateChanged(@NonNull bottomSheet: View, newState: Int) {
                if (newState == BottomSheetBehavior.STATE_HIDDEN || newState == BottomSheetBehavior.STATE_DRAGGING) {
                    binding.lyOver.visibility = View.GONE
                    if (newState == BottomSheetBehavior.STATE_DRAGGING) {//判断为向下拖动行为时，则强制设定状态为展开
                        bikeRidingBehavior.setState(BottomSheetBehavior.STATE_EXPANDED );
                        return
                    }
                    if(MainState == 1){
                        if (newState == BottomSheetBehavior.STATE_HIDDEN){
//                            binding.rlMainRiding.visibility = View.VISIBLE
                            binding.lyStoping.visibility = View.GONE
                            binding.rlBatter.visibility = View.GONE
                            setViewNomal()
                        }else if (newState == BottomSheetBehavior.STATE_EXPANDED)else{
                            binding.lyStoping.visibility = View.VISIBLE
                        }
                    }
                    onTopStatesHide()
                }else{ }
                if (newState == BottomSheetBehavior.STATE_HIDDEN){
                    bikeRidingFragment.onPageEnd()
                }
                if (newState == BottomSheetBehavior.STATE_EXPANDED){
                    bikeRidingFragment.onPageStar()
                }
            }
            override fun onSlide(@NonNull bottomSheet: View, slideOffset: Float) {}//状态变化
        })
        binding.lyOver.clickDelay {
            IsAuth()
        }
        if(!SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
            presenter.updateCountryCodeInfo()
        }
//        binding.rlMainRiding.setOnClickListener {
//            binding.rlMainRiding.visibility = View.GONE
//            setStopingVis()
//            bikeRidingFragment.getOrderInfo(orderNo,false)
//            bikeRidingBehavior.state = BottomSheetBehavior.STATE_EXPANDED
//        }

//        if (FlavorConfig.NET.COM_URL.contains("api.gogoep.io") || FlavorConfig.NET.COM_URL.contains("api.gogoep.dev")){
//            EasyFloat.layout(com.tbit.uqbike.R.layout.layout_float_view).layoutParams(initLayoutParams()).listener {}.show(this)
//        }
        AutoRidingMyLocat()

        if (PermissionUtils.hasSelfPermissions(this, Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION)) {
            getBlePre()
        }
        binding.tvCamOpen.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("turn_bluetooth",properties)
            if (LanguageUtil.isHarmonyOs()){
                if (!isBle){
                    val intent = Intent()
                    intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                    val uri = Uri.fromParts("package", ContextUtil.getContext().getPackageName(), null)
                    intent.data = uri
                    startActivity(intent)
                    Toast.makeText(ContextUtil.getContext(), getString(R.string.s_open_ble_pre),
                        Toast.LENGTH_SHORT).show()
                    return@clickDelay
                }
            }else{
//                bleCheckFragment.setShowDialog(true)
//                bleCheckFragment.check({
//                    isBle = it
//                    if (isBle){
//                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE, "1");
//                    }else{
//                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE_DEFY, "1");
//                    }
//                })
            }

            if (isBle){
                MainDialogUtil.BleDig(this@MainActivity)
            }
        }
        setUlocalView()
        binding.btnLocalU.setOnClickListener {
            LocationUtil.gotoLocalPermiss(this@MainActivity)
        }
        hiddenUi()
    }

    fun hiddenUi(){
        isHomeState = false
        mainAdFragment.goRide()
        setBomRidingView(false,false)
        binding.layoutMainBottomMenu.imgGohome.visibility = View.GONE
        binding.imgMenuBg.visibility = View.GONE
        binding.lyBomMenu.visibility = View.GONE
        binding.ryBomInvite.visibility = View.GONE
        if (MainState == 1){
            bikeRidingBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            setRidingViewState()
        }
        binding.ryScanBle.postDelayed({
            if (!isBle){
                isBle = hasBluetoothPermission(ContextUtil.getContext())
                if (isBle){
                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE, "1");
                }
                MyLogUtil.Log("1111","==hasBluetoothPermission=="+isBle)
            }
            if(!BleUtils.isBleOpened() || !isBle){
//                    binding.ryScanBle.visibility = View.VISIBLE
                binding.ryScanBle.visibility = View.GONE
            }else{
                binding.ryScanBle.visibility = View.GONE
            }
        },200)
        HindeParkAndCar()
    }

    private fun getBlePre(){
        if (LanguageUtil.isHarmonyOs()){
            if (SpUtil.Companion.getInstance().find(Constant.SpKey.SP_BLE_PLE_DEFY).isNullOrEmpty()){
//                bleCheckFragment.check{
//                    isBle = it
//                    MyLogUtil.Log("1111","=====1111蓝牙权限==="+isBle)
//                    if (isBle){
//                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE, "1");
//                    }else{
//                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE_DEFY, "1");
//                    }
//                }
            }
        }else{
//            bleCheckFragment.check{
//                isBle = it
//                MyLogUtil.Log("1111","=====1111蓝牙权限==="+isBle)
//                if (isBle){
//                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE, "1");
//                }else{
//                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE_DEFY, "1");
//                }
//            }
        }
        if(!BleUtils.isBleOpened() || !isBle){
//            binding.ryScanBle.visibility = View.VISIBLE
        }

    }
    var isBle = false
    private var isRiding = false //是否骑行中
    private var oldLocal = Location(0.0F,0.0F,0.0,0.0,0,"","")
    fun AutoRidingMyLocat(){
        if (binding.rlMainRiding != null){
            binding.rlMainRiding.postDelayed({
                AutoRidingMyLocat()
                if (binding.rlMainRiding != null){
                    if (binding.rlMainRiding.isShown || binding.rlMainRiding.visibility == View.VISIBLE){
                        isRiding = true
                    }else{
                        isRiding = (bikeRidingBehavior.state == BottomSheetBehavior.STATE_EXPANDED)
                    }
//                    MyLogUtil.Log("1111","===是否骑行中="+isRiding)
                    if(isRiding && mainMapFragment != null){
                        var local_new = mainMapFragment.getLocation()
                        if (local_new != null){
//                            MyLogUtil.Log("1111","===是否骑行中距离="+GPSUtil.getDistance(oldLocal.longitude,oldLocal.latitude,local_new.longitude,local_new.latitude))
//                            MyToastUtil.toast("距离："+GPSUtil.getDistance(oldLocal.longitude,oldLocal.latitude,local_new.longitude,local_new.latitude))
                            if (GPSUtil.getDistance(oldLocal.longitude,oldLocal.latitude,local_new.longitude,local_new.latitude) >= 50){
                                oldLocal.longitude = local_new.longitude
                                oldLocal.latitude = local_new.latitude
                                mainMapFragment.showCurrentLocate()
                            }
                        }
                    }
                }
            },10000)
        }
    }
//    private fun initLayoutParams(): FrameLayout.LayoutParams {
//        val params = FrameLayout.LayoutParams(
//            FrameLayout.LayoutParams.WRAP_CONTENT,
//            FrameLayout.LayoutParams.WRAP_CONTENT
//        )
//        params.gravity = Gravity.BOTTOM or Gravity.END
//        params.setMargins(0, params.topMargin, params.rightMargin, 500)
//        return params
//    }

    fun IsAuth() : Boolean{
        if(SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
            startActivity<AuthActivity>()
            finish()
            return false
        }
        return true
    }
    private fun checkGpsEnabled() {
        if (!LocationUtil.isGpsEnabled()) {
            MainDialogUtil.GpsDig(this@MainActivity)
        }
    }
    private fun delayRequestLocationPermission() {
        Observable.timer(2, TimeUnit.SECONDS)
            .observeOn(AndroidSchedulers.mainThread()).subscribeBy(
                onComplete = {
                    (supportFragmentManager.findFragmentById(R.id.fragment_permission) as PermissionFragment).requestPermission {
                        if (LocationUtil.isGpsEnabled()) {
                            LocationModel.onRequestPermissionSuccess()
                            getMapData()
                            if(binding.layoutMainBottomMenu.imageLocate != null){
                                binding.layoutMainBottomMenu.imageLocate.postDelayed({binding.layoutMainBottomMenu.imageLocate.performClick()},1000)
                            }
                        }
                        getBlePre()
                    }
                }
            )
    }

    private fun goToScanBorrow() {
//        startActivity<ScanForBorrowActivity>()
        startActivity(ScanForBorrowActivity.createIntent(this, ScanForBorrowActivity.TYPE_CAR,true))
    }

    var MainState = 0 //0正常 ， 1存在进行中订单，  2存在未支付订单
    var MainStateTypeIsRental = false //是否是 长租订单
    var MainOrderState = 1 //1 短租，4长租
    var orderNo = "" //未结束订单的  订单号
    @RequiresApi(Build.VERSION_CODES.O)
    override fun onResume() {
        super.onResume()
        Glob.isAppRun = true
        if(SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
            binding.lyOver.visibility = View.VISIBLE
            binding.imageScanner.visibility = View.GONE
            binding.textScanner.text = getString(com.tbit.uqbike.R.string.s_ride_service)
        }else{
            binding.lyOver.visibility = View.GONE
        }
        mainAdFragment.setHeadImg()
        setBomInviteVis()
        presenter.getGoogleNetTimeout()
        getRideState() // Add this line
    }
    fun setBomInviteVis(){
        if (Glob.IsInvite && isHomeState){
            binding.ryBomInvite.visibility = View.VISIBLE
            binding.tvActname.text = Glob.ActivityName
            ImageLoad.loadimg(resources.getDrawable(R.drawable.gift_invite_new),binding.imgHomeInvite)
        }else{
            binding.ryBomInvite.visibility = View.GONE
        }
    }
    @Receive(Constant.Event.EVENT_ORDERFISH)
    fun EVENT_ORDERFISH(){
        MainState = 0
    }
    var isFirstGetOrderState = true
    @RequiresApi(Build.VERSION_CODES.O)
    fun getRideState(){
        if (Glob.isLogin){
            if (!NetUtils.isConnected(ContextUtil.getContext())) return
            OrderModel.getMyOrderStatus().subscribeBy(
                onNext = {
                    MyLogUtil.Log("7777","===是否存在未结束的骑行订单 信息=="+it.toString())
                    val resultData: MyOrderStateData = Gson().fromJson(it.toString(), MyOrderStateData::class.java)
                    if (resultData != null){
                        //骑行订单状态：1进行中，2待支付
                        MainState = resultData.status
                        orderNo = resultData.order_no
                        MainOrderState = resultData.type
                        if (MainOrderState == Constant.OrderType.RENTAL_Y){
                            MainStateTypeIsRental = true
                        }else{
                            MainStateTypeIsRental = false
                        }
                        if (resultData.status == 1 || resultData.status == 2){
                            ridingTime = resultData.order_time
                            ridingPrice = resultData.amount
                        }
                        if(resultData.status == 1){
                            if (isFirstGetOrderState){
                                MyLogUtil.Log("7777","===是否存在未结束的骑行订单 信息=="+isFirstGetOrderState)
                                isFirstGetOrderState = false
                                binding.layoutMainBottomMenu.imgGohome.performClick()
                            }
                            setBomRidingView(true,false)
                        }else{
                            binding.rlMainRiding.visibility = View.GONE
                            binding.lyStoping.visibility = View.GONE
                            SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_VECHNO, "");

                            if (resultData.status == 2){
                                binding.imageScanner.visibility = View.GONE
                                binding.textScanner.text = getString(R.string.s_order_npay)+"("+AppUtil.getFloat2(resultData.amount)+Glob.CurrencyUnit+")"
                                setBomRidingView(true,true)
                            }else{
                                binding.imageScanner.visibility = View.VISIBLE
                                if (Glob.isRental){
                                    binding.textScanner.text = getString(R.string.scan_lease)
                                }else{
                                    binding.textScanner.text = getString(com.tbit.uqbike.R.string.scan_unlock)
                                }
                                setBomRidingView(false,false)
                            }
                        }
                    }
                }
            ).toCancelable()
        }
    }
    fun setUlocalView(){
        if (!Glob.isLogin){
            if (!LocationUtil.isGpsPermiss()) {
                binding.lyLocalN.visibility = View.VISIBLE
            }else{
                binding.lyLocalN.visibility = View.GONE
            }
        }else{
            binding.lyLocalN.visibility = View.GONE
        }
    }
    //设置骑行中界面
    @RequiresApi(Build.VERSION_CODES.O)
    fun setRidingViewState(){
        if(bikeRidingBehavior.state != BottomSheetBehavior.STATE_EXPANDED){
            setViewRiding()
            binding.lyStoping.visibility = View.GONE
            bikeRidingFragment.getConfigByRiding(false,MainStateTypeIsRental)
            bikeRidingFragment.getOrderInfo(orderNo,false)
            binding.lyStoping.postDelayed({
                bikeRidingBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            },200)
        }else{
            binding.rlMainRiding.visibility = View.GONE
        }
        setStopingVis()
    }
    fun hasBluetoothPermission(context: Context?): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 检查ACCESS_FINE_LOCATION权限
            (ContextCompat.checkSelfPermission(context!!, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED // 或者检查ACCESS_COARSE_LOCATION权限
                    || ContextCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED)
        } else {
            // 在API 23以下，直接检查蓝牙权限已经足够
            ContextCompat.checkSelfPermission(context!!, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
        }
    }
    override fun onStop() {
        super.onStop()
        //取消未读消息动画，隐藏未读消息图标
        onGetUnreadMessageCount(0)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        Log.d("dd","==========mainActivity onNewIntent()")
        val fromLogin = intent?.getBooleanExtra(EXTRA_FROM_LOGIN, false)
        if (fromLogin == true) {
            isPassProtocol = true
            mainAdFragment.getHomeAd()
        }
    }
    @RequiresApi(Build.VERSION_CODES.O)
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: BaseEventData) {
        MyLogUtil.Log("1111", "==== event message11111 ==="+event.code+","+event.msg)
        if(event.code == EventUtil.EVENT_NOTITY){
            MyLogUtil.Log("1111", "收到了")
            val resultData: CarInfoData = Gson().fromJson(event.msg, CarInfoData::class.java)
            bikeInfoFragment.setBikeInfo(resultData,true)
            binding.lyOver.postDelayed({
                if (Glob.isRental){
//                    var carRentalDialog = CarRentalDialog(this@MainActivity,resultData)
//                    lifecycleDialogHelper.show(carRentalDialog)

                    startActivity<WebActionActivity>(WebActionActivity.TITLE to getString(R.string.s_sureorder),
                        WebActionActivity.URL to UrlDecodeUtil().getParm(FlavorConfig.NET.H5_URL+"checkOrder?vehicle_no="+resultData.vehicle_no+
                                "&remaining_mileage="+resultData.remaining_mileage+ "&park_name="+resultData.park_name+"&park_area_id="+resultData.park_id+"&locale="+
                                FlavorConfig.Local.language+"&currency="+ Glob.CurrencyUnit))
                }else{
                    bikeInfoBehavior.state = BottomSheetBehavior.STATE_EXPANDED
                }
                binding.rlMainRiding.visibility = View.GONE
                binding.lyStoping.visibility = View.GONE
            },800)
            binding.lyOver.postDelayed({
                if (isFirstOrder && !Glob.isRental){
                    startActivity<UserCarActivity>(UserCarActivity.TYPE to UserCarActivity.TYPE_NEW,UserCarActivity.UNIT to "")
                }
            },1000)
            if (!hasBookBike) {
                mainMapFragment.fixedSearchCenter()
            }
        }else if(event.code == EventUtil.EVENT_LOGINOUT){
            bikeRidingBehavior.setState(BottomSheetBehavior.STATE_HIDDEN );
            setViewNomal()
            bikeRidingFragment.endOrderState()
            binding.lyOver.visibility = View.GONE
            binding.rlMainRiding.visibility = View.GONE
            binding.lyStoping.visibility = View.GONE
            binding.rlBatter.visibility = View.GONE
            binding.lyPowering.visibility = View.GONE
            onTopStatesHide()
            if (Glob.isRental){
                binding.textScanner.text = getString(R.string.scan_lease)
            }else{
                binding.textScanner.text = getString(com.tbit.uqbike.R.string.scan_unlock)
            }
            setBomRidingView(false,false)
        }else if(event.code == EventUtil.EVENT_RIDINGINFO){
            binding.lyOver.postDelayed({
                bikeRidingFragment.getConfigByRiding(false, MainStateTypeIsRental)
                bikeRidingFragment.getOrderInfo(orderNo,false)
                bikeRidingBehavior.state = BottomSheetBehavior.STATE_EXPANDED
                setViewRiding()
                binding.rlMainRiding.visibility = View.GONE
                setStopingVis()
            },800)
        }else if(event.code == EventUtil.EVENT_LOCAL){
            MyLogUtil.Log("4444","====定位授权 main========")
            LocationModel.onRequestPermissionSuccess()
            getMapData()
            if(binding.layoutMainBottomMenu.imageLocate != null){
                binding.layoutMainBottomMenu.imageLocate.performClick()
            }
            getBlePre()
        } else if(event.code == EventUtil.EVENT_INFOVIEW){
            MyLogUtil.Log("1111","====infoview click========")
            AppUtil.startNav(this@MainActivity,latLngMy!!,latLngTag!!,isWalk)
        } else if(event.code == EventUtil.EVENT_GORIDE){
            binding.layoutMainBottomMenu.imgGohome.performClick()
        }else if (event.code == EventUtil.EVENT_LEASESUC){
            isFirstGetOrderState = true
//            if (isHomeState){
//                binding.lyOver.postDelayed({
//                    MainState = 1
//                    binding.layoutMainBottomMenu.imgGohome.performClick()
//                },500)
//            }
        }
        if(event.code == EventUtil.EVENT_EXIT) finish()
    }
    @RequiresApi(Build.VERSION_CODES.O)
    fun setStopingVis(){
        if (bikeRidingFragment.isOpenUser){
            binding.tvHintRidingbom.text = getString(R.string.s_riding_tophint)
        }else{
            binding.tvHintRidingbom.text = getString(R.string.s_udevice)
        }
        if (!MainStateTypeIsRental){
            binding.lyStoping.visibility = View.VISIBLE
        }
    }
    var isLocalper = false
    override fun onRestart() {
        super.onRestart()
        getMapData()
        mainAdFragment.onRestart()
        if(!isLocalper && hasLocationPermission()) {
            isLocalper = true
            LocationModel.onRequestPermissionSuccess()
//            getMapData()
            if(binding.layoutMainBottomMenu.imageLocate != null){
                binding.layoutMainBottomMenu.imageLocate.performClick()
            }
        }
        if (binding.rlMainRiding != null) {
            var isRidingNowByRestar = false
            if (binding.rlMainRiding.isShown || binding.rlMainRiding.visibility == View.VISIBLE) {
                isRidingNowByRestar = true
            } else {
                isRidingNowByRestar = (bikeRidingBehavior.state == BottomSheetBehavior.STATE_EXPANDED)
            }
//            MyLogUtil.Log("1111","======onRestart======="+isRidingNowByRestar)
            if (isRidingNowByRestar) {
                mainMapFragment.showCurrentLocate()
                if (mainMapFragment.getLocation() != null){
                    mainMapFragment.setSearchCenter(LatLng(mainMapFragment.getLocation()!!.latitude,mainMapFragment.getLocation()!!.longitude))
                }
                mainMapFragment.IsOutArea()
            }
        }
    }
    override fun onDestroy() {
        loadDataDisposable.dispose()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
        Glob.isAppRun = false
        isColdStar = "0"
        super.onDestroy()
    }

    override fun onGetDepositSuccess(adDeposit: AdDeposit?) {
        this.adDeposit = adDeposit
        MyLogUtil.Log("4444","====地图加载 ========")
        searchMenu?.isVisible = adDeposit?.modelType != Constant.ModelType.PROHIBIT_AREA
    }
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            com.tbit.uqbike.R.id.action_search -> startActivity<SearchParkPointActivity>()
            android.R.id.home -> startActivity<MineActivity>()
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onBackPressed() {
        if (!isHomeState){
            binding.imageMy.performClick()
        }else{
            if ((SystemClock.elapsedRealtime() - exitTime) > 2000) {
                toast(com.tbit.uqbike.R.string.quit_double_check_tips)
                exitTime = SystemClock.elapsedRealtime()

                // 调试功能：连续点击返回键5次打开分享返利测试
                val currentTime = SystemClock.elapsedRealtime()
                if (currentTime - lastDebugClickTime < 1000) {
                    debugClickCount++
                } else {
                    debugClickCount = 1
                }
                lastDebugClickTime = currentTime

                if (debugClickCount >= 5) {
                    debugClickCount = 0
                    startActivity(Intent(this, ShareRebateTestActivity::class.java))
                }
            } else {
                finish()
            }
        }
    }
    private fun showCurrentLocate() {
        mainMapFragment.showCurrentLocate()
    }
    private fun onSearchCenterChange() {
        if (!hasBookBike) {
            loadData()
        }
    }
    private fun loadData() {
        publish.onNext(1)
    }
    var oldLatlng = LatLng()
    var isMoveMapFirst = true
    private fun loadDataImpl() {
        val latLng = mainMapFragment.searchCenter
        MyLogUtil.Log("4444","地图移动=："+latLng.lat+","+latLng.lng)
        if(!SpUtil.getInstance().find(Constant.SpKey.SP_AUTH).isNullOrEmpty()){
            if(GPSUtil.getDistance(oldLatlng.lng,oldLatlng.lat,latLng.lng,latLng.lat) > 10){
                mainMapFragment.IsOutArea()
            }
            MyLogUtil.Log("1111","地图移动====================："+isMoveMapFirst)
            binding.lyOver.postDelayed({isMoveMapFirst = false},2000)
            if(!isMoveMapFirst){
                if(GPSUtil.getDistance(oldLatlng.lng,oldLatlng.lat,latLng.lng,latLng.lat) > 50){
                    oldLatlng = latLng
//                    routeLineMapDelegate.clean()
                    bikeMapDelegate.clear()
                    bikeMapDelegate.select(null)
                    parkPointMapDelegate.clear()
                    parkPointMapDelegate.select(null)
                    prohibitAreaMapDelegate.clear()
                    prohibitAreaMapDelegate.select(null)
                    getNearGeo()
                    getNearParkPointsAndProhibits()
                    getBikes()
                    binding.layoutMainBottomMenu.imageRefresh.animate().rotationBy(360f).setDuration(1000).start()
                }
            }
        }
    }
    private fun RestBick(){
        bikeMapDelegate.clear()
        bikeMapDelegate.select(null)
        getBikes()
    }
    private fun onMapClick(latLng: LatLng) {
        onMapDialogHide()
//        if(bikeRidingBehavior.state == BottomSheetBehavior.STATE_EXPANDED){
//            bikeRidingBehavior.state = BottomSheetBehavior.STATE_HIDDEN
//            binding.rlMainRiding.visibility = View.VISIBLE
//            binding.lyStoping.visibility = View.GONE
//        }
        bikeMapDelegate.select(null)
        parkPointMapDelegate.select(null)
        if (markerNav != null){
            mainMapFragment.map!!.showInfoWindow(InfoWindowOption(infoWindowView, markerNav!!, dip(-0f),false))
            markerNav = null
        }
        if (routeLineMapDelegate != null){
            routeLineMapDelegate.clean()
        }
    }
    fun onMapDialogHide(){
        if(bikeInfoBehavior.state == BottomSheetBehavior.STATE_EXPANDED){
            bikeInfoBehavior.state = BottomSheetBehavior.STATE_HIDDEN
            RouteSearchModel.canlSearch()
        }
        if(parkPointInfoBehavior.state == BottomSheetBehavior.STATE_EXPANDED){
            parkPointInfoBehavior.state = BottomSheetBehavior.STATE_HIDDEN
            RouteSearchModel.canlSearch()
        }
    }
    private fun onTopStatesHide() {
        mainMapFragment.autoSearchCenter()
    }
    private fun onBikeClick(marker: MarkerWrapper, bikeState: BikeState): Boolean {
//        MyLogUtil.Log("1111","======bikeState======"+bikeState?.mileageRemain)
        val properties = JSONObject()
        properties.put("vehicle_number",bikeState?.mileageRemain)
        MDUtil.clickEvent("vehicle_icon_click",properties)
        if (!tryLoginIfNot())
            return false
        if (markerNav != null){
            mainMapFragment.map!!.showInfoWindow(InfoWindowOption(infoWindowView, markerNav!!, dip(-0f),false))
            markerNav = null
        }
//        MyLogUtil.Log("1111","======bikeState======"+bikeState)
        bikeMapDelegate.select(marker)
        parkPointMapDelegate.select(null)
        onMapDialogHide()
        val latLng = mainMapFragment.searchCenter
        BikeModel.getBikesInfo(bikeState?.mileageRemain,latLng.lat,latLng.lng,BikeModel.isScan_N,Glob.isRental).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取车辆信息=="+it.toString())
                val resultData: CarInfoData = Gson().fromJson(it.toString(), CarInfoData::class.java)
                bikeInfoFragment.setBikeInfo(resultData,false)
                bikeInfoBehavior.state = BottomSheetBehavior.STATE_EXPANDED
                if (!hasBookBike) {
                    mainMapFragment.fixedSearchCenter()
                }
                LinePlan(marker,bikeState.latC,bikeState.lonC,false,0)
            },
            onError = {
                val errMsg = ErrHandler.getErrMsg(it)
                MyToastUtil.toast(errMsg)
            }
        ).toCancelable()

        //谷歌地图路径规划收费，暂时注释
//        routeSearch(LatLng(bikeState.latC, bikeState.lonC)) {}
        return false
    }
    private fun onParkPointClick(marker: MarkerWrapper, parkPoint: ParkPoint): Boolean {
//        MyLogUtil.Log("1111","======parkPoint======"+parkPoint.name)
        val properties = JSONObject()
        properties.put("p_point_name",parkPoint.name)
        MDUtil.clickEvent("p_point_icon_click",properties)
        if (!tryLoginIfNot())
            return false
        if (markerNav != null){
            mainMapFragment.map!!.showInfoWindow(InfoWindowOption(infoWindowView, markerNav!!, dip(-0f),false))
            markerNav = null
        }
        MyLogUtil.Log("1111","===onParkPointClick==")
        onMapDialogHide()
        parkPointMapDelegate.select(marker)
        presenter.getParkPointInfo(parkPoint, mainMapFragment.searchCenter)
        bikeMapDelegate.select(null)
        LinePlan(marker,parkPoint.latC,parkPoint.lonC,true,parkPoint.parkPointId)

        return false
    }
    fun LinePlan(marker: MarkerWrapper,lat : Double, lng : Double, isP : Boolean,parkid : Int){
        MyLogUtil.Log("1111","===LinePlan==")
        var latLngTag = LatLng(lat,lng)
        this.markerNav = marker
        markerNav!!.setPosition(latLngTag)
//        mainMapFragment.map!!.showInfoWindow(InfoWindowOption(infoWindowView, markerNav!!, dip(-35f)))
        var Mylocal = mainMapFragment.getLocation()
        var latLngMy = LatLng(Mylocal!!.latitude,Mylocal!!.longitude)
//        if (tvNavInfo != null) tvNavInfo?.text = ""
//        if (rlyNav != null) rlyNav?.setOnClickListener {  }

        var isRidingNow = false
        if (binding.rlMainRiding.isShown || binding.rlMainRiding.visibility == View.VISIBLE){
            isRidingNow = true
        }else{
            isRidingNow = (bikeRidingBehavior.state == BottomSheetBehavior.STATE_EXPANDED)
        }
        if (isRidingNow){
            ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_riding),img_nav!!)
//            RouteSearchModel.bikingSearch(latLngMy, latLngTag)
            MainRenTalModel.WalkingSearch(latLngMy,latLngTag, onSuccess = {
                setNavInfoView(it,latLngMy,latLngTag,false,isP,parkid)
            })
        }else{
            ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_walk),img_nav!!)
            MainRenTalModel.WalkingSearch(latLngMy,latLngTag, onSuccess = {
                setNavInfoView(it, latLngMy, latLngTag, true, isP,parkid)
            })
        }
    }
    private var latLngMy : LatLng? = null
    private var latLngTag : LatLng? = null
    private var isWalk = false
    @RequiresApi(Build.VERSION_CODES.O)
    fun setNavInfoView(it : IRouteLine, latLngMy : LatLng, latLngTag :LatLng, isWalk : Boolean, isP : Boolean, parkid : Int){
        this.latLngTag = latLngTag
        this.latLngMy = latLngMy
        this.isWalk = isWalk
        CoroutinesUtil.launchMain {
            if (tvNavInfo != null) tvNavInfo?.text = AppUtil.getFloat2(it.getDistance().toFloat()/1000)+getString(R.string.distance_unit)+"\n"+
                    it.getDuration()/60+getString(R.string.min)
            if (rlyNav != null) {
                rlyNav?.clickDelay {
                    AppUtil.startNav(this@MainActivity,latLngMy,latLngTag,isWalk)
                }
            }
            if (Glob.isRental && MainState == 1 && isP){
                if (parking_Data != null){
//                    MyLogUtil.Log("1111","==parkid===="+parkid+"-"+bikeRidingFragment!!.park_id)
                    if (parkid == bikeRidingFragment!!.park_id){
                        tvNavP?.text = getString(R.string.s_p_disp,"0"+Glob.CurrencyUnit)
                        tvNavP?.visibility = View.VISIBLE
                        line_nav?.visibility = View.VISIBLE
                    }else{
                        tvNavP?.text = getString(R.string.s_p_disp,AppUtil.getFloat2(parking_Data?.lease_amount_config!!.in_other_parking)+Glob.CurrencyUnit)
                        tvNavP?.visibility = View.VISIBLE
                        line_nav?.visibility = View.VISIBLE
                    }
                }
                try {
                    rlyNav?.delegate?.cornerRadius_TR = CommonUtils.dp2px(0)
                }catch (e : NullPointerException){}
            }else{
                try {
                    rlyNav?.delegate?.cornerRadius_TR = CommonUtils.dp2px(7)
                    tvNavP?.visibility = View.GONE
                    line_nav?.visibility = View.GONE
                }catch (e : NullPointerException){}
            }
            if (isP){
                mainMapFragment.map!!.showInfoWindow(InfoWindowOption(infoWindowView, markerNav!!, dip(-40f)))
            }else{
                mainMapFragment.map!!.showInfoWindow(InfoWindowOption(infoWindowView, markerNav!!, dip(-45f)))
            }
            delay(150)
            if (it.geAllPoints() != null && it.geAllPoints().size > 0){
                routeLineMapDelegate.setRouteLine(it)
            }
        }
    }
    override fun onGetParkPointInfoSuccess(resultData: ParkData) {
//        routeLine?.let { routeLineMapDelegate.setRouteLine(it) }
        (supportFragmentManager.findFragmentById(R.id.park_point_info_fragment) as ParkPointInfoFragment).setParkPointInfo(resultData)
        mainMapFragment.fixedSearchCenter()
        parkPointInfoBehavior.state = BottomSheetBehavior.STATE_EXPANDED
    }
    private fun getNearGeo() {
        val latLng = mainMapFragment.searchCenter
//        MyLogUtil.Log("1111","地理位置："+latLng.lat+","+latLng.lng)
        presenter.getNearGeo(latLng.lat, latLng.lng, 5000)
    }
    override fun onGetNearGeoSuccess(areaData: getAreaData) {
//        if (geoList.isNotEmpty()) geoMapDelegate.setGeo(geoList)
        if (areaData.isNotEmpty()) {
            if (areaData.size > 0){
                geoMapDelegate.setGeoNew(areaData)
            }
        }
    }
    private fun getNearParkPointsAndProhibits() {
        getNearParkPoints()
        getNearProhibitsImpl(false)
    }
    private fun getNearParkPoints() {
        val latLng = mainMapFragment.searchCenter
        presenter.getNearParkPoints(latLng.lat, latLng.lng, 500)
    }
    override fun onGetNearParkPointsSuccess(areaData: getAreaData) {
        if (areaData.isNotEmpty()) {
            if (areaData.size > 0){
                parkPointMapDelegate.setPartAreaData(areaData)
            }
        }
    }
    private fun getNearProhibits() {
        getNearProhibitsImpl(true)
    }
    private fun getNearProhibitsImpl(isProhibitMode: Boolean) {
        val latLng = mainMapFragment.searchCenter
        presenter.getNearProhibits(latLng.lat, latLng.lng)
    }
    override fun onGetNearProhibitsSuccess(areaData: getAreaData) {
        if (areaData.isNotEmpty()) {
            if (areaData.size > 0){
                prohibitAreaMapDelegate.setProhibitAreaData(areaData)
            }
        }
    }
    private fun getBikes() {
        val bookInfo = bikeInfoFragment.bookInfo
        if (bookInfo == null) {
            getNearBikes()
        }
    }
    private fun getNearBikes() {
        val latLng = mainMapFragment.searchCenter
        presenter.getNearBikes(latLng.lat, latLng.lng, 500)
    }
    override fun onGetNearBikesSuccess(resultData: VehicleData) {
        bikeMapDelegate.setBickData(resultData)
    }
    override fun onGetPhoneInfoSuccess(phoneInfoList: List<PhoneInfo>?) {
        lifecycleDialogHelper.show(CustomPhoneCallDialog.newInstance(phoneInfoList))
    }
    override fun onGetUnreadMessageCount(unreadCount: Int) {}
    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }
    @RequiresApi(Build.VERSION_CODES.O)
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_OPEN_GPS) {
            if (LocationUtil.isGpsEnabled() && PermissionUtils.hasSelfPermissions(this,
                    Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION)) {
                MyLogUtil.Log("4444","====定位授权 main===11=====")
                LocationModel.onRequestPermissionSuccess()
                getMapData()
                if(binding.layoutMainBottomMenu.imageLocate != null){
                    binding.layoutMainBottomMenu.imageLocate.performClick()
                }
            }
            getBlePre()
            return
        }
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == REQUEST_TO_RIDING) {
                finish()
            }
        }
    }
    @RequiresApi(Build.VERSION_CODES.O)
    fun getMapData(){
        CoroutinesUtil.launchMain {
            delay(600)
            //获取国家  lld
            MainRenTalModel.getCountrylld(onSuccess = {getMainData()})
//            getMainData()
        }
        setUlocalView()
    }
    var isFistLocalUpda = true
    @RequiresApi(Build.VERSION_CODES.O)
    @Receive(Constant.Event.LOCATION_UPDATE)
    fun onLocationUpdate() {
        if (isFistLocalUpda){
            isFistLocalUpda = false
            MyLogUtil.Log("1111","===当前运营国家 信息  LOCATION_UPDATE==")
            MainRenTalModel.getCountrylld(onSuccess = {getMainData()})
        }
    }
    // 获取主页数据 及设置
    @RequiresApi(Build.VERSION_CODES.O)
    fun getMainData(){
        mainMapFragment.sumitLocal(false)
        getNearGeo()
        getNearParkPointsAndProhibits()
        getBikes()
        mainBusinessFragment.sumitEvent(mainMapFragment.getLocation())
        mainMapFragment.IsOutArea()

        if (Glob.isLogin) {
            getRideState()

            ComModel.getNewRegister().subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取新手奖励 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), RigisterData::class.java)
                    if (resultData != null){
                        if (resultData.is_display){
                            var hintData = ""
                            var listData = ArrayList<Coupon>()
                            if (resultData.type == RIG_TYPE_NEWUSER){
                                if (resultData.present_amount != 0){
                                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_NEWUSER,resultData.present_amount.toString())
                                    hintData = getString(R.string.s_get) +AppUtil.formatAmount(resultData.present_amount.toLong())+Glob.CurrencyUnit+ getString(R.string.s_ridemoney)
                                    listData.add(Coupon(0,0f,0,0f,
                                        AppUtil.formatAmount(resultData.present_amount.toLong())+Glob.CurrencyUnit+ getString(R.string.s_ridemoney),0,
                                        0,0,false,type_coupon_money,""))
                                }
                                if (resultData!!.ride_card != null){
                                    if (resultData.ride_card!!.number > 0){
                                        if (hintData.isNullOrEmpty()){
                                            hintData = getString(R.string.s_get) + getString(R.string.s_get_ridecard,resultData.ride_card.number.toString())
                                        }else{
                                            hintData = hintData + "、"+ getString(R.string.s_get_ridecard,resultData.ride_card.number.toString())
                                        }
                                        listData.add(Coupon(resultData.ride_card.ride_card_id,0f,0,0f,
                                            resultData.ride_card.name,0, 0,0,false,type_coupon_ridecard,""))
                                    }
                                }
                            }
                            if(resultData.coupon.size > 0){
                                if (hintData.isNullOrEmpty()){
                                    hintData = getString(R.string.s_get) + getString(R.string.s_get_invite,resultData.coupon.size.toString())
                                }else{
                                    hintData = hintData + "、"+ getString(R.string.s_get_invite,resultData.coupon.size.toString())
                                }
                                resultData.coupon.forEach {
                                    listData.add(Coupon(it.user_coupon_id,it.amount,it.is_min_spend,it.min_spend_amount, it.name,it.end_time,
                                        0,it.type,false,type_coupon_invite,""))
                                }
                            }

                            var inviteDialog = InviteDialog.newInstance(hintData)
                            if (resultData.type == RIG_TYPE_NEWUSER){
                                inviteDialog.type = InviteDialog.TYPE_NEWUSER
                            }else{
                                inviteDialog.type = InviteDialog.TYPE_INVITE
                            }
                            inviteDialog.listData = listData
                            lifecycleDialogHelper.show(inviteDialog)
                        }
                    }
                },
                onError = {}
            ).toCancelable()
        }

//        bikeRidingFragment.getConfigByRiding(false,true)
//        bikeRidingBehavior.state = BottomSheetBehavior.STATE_EXPANDED
    }
    override fun onGoogleNetNotAvailable() {}
    @RequiresApi(Build.VERSION_CODES.O)
    fun setViewRiding(){
        //骑行中界面样式
        binding.rlMainRiding.postDelayed({
            binding.layoutMainBottomMenu.rlLocal.setPadding(0,0,0,bikeRidingFragment.getHight())
        },200)
    }
    fun setViewNomal(){
        //非骑行中样式
        binding.rlMainRiding.postDelayed({
            binding.layoutMainBottomMenu.rlLocal.setPadding(0,0,0,Utils.dip2px(ContextUtil.getContext(),10f).toInt())
        },200)
    }

    /**
     * 首页 展示骑行中
     * @param isRiding 是否骑行中
     * @param isPaying 是否支付中
     */
    var ridingTime = 0L //骑行时间 长租待支付不需要该参数
    var ridingPrice = 0f //骑行金额
    fun setBomRidingView(isRiding : Boolean,isPaying : Boolean){
        if (isRiding){
            if (isHomeState){
                binding.lyRidingBtn.visibility = View.VISIBLE
                binding.lyUriding.visibility = View.GONE
            }else{
                binding.lyRidingBtn.visibility = View.GONE
                binding.lyUriding.visibility = View.VISIBLE
            }
//            ImageLoad.loadimg(resources.getDrawable(com.tbit.uqbike.R.drawable.ic_riding_gif),binding.imageScanner1)
            if (isPaying){
                binding.rvSeeOrder.text = getString(R.string.s_gopay)
                if (MainStateTypeIsRental){
                    binding.tvRidingTitle.text = getString(R.string.s_rental_end)
//                    binding.tvRidingCont.text = "超时费用"+"9THB"
                    var unit = AppUtil.getFloat2(ridingPrice)+Glob.CurrencyUnit
                    var content = getString(R.string.s_cost_rental_upay)+unit
                    val spannableString = AppUtil.getSpanStr(unit,content,R.color.c_F86125)
                    binding.tvRidingCont.text = spannableString
                }else{
                    binding.tvRidingTitle.text = getString(R.string.s_riding_end)
//                    binding.tvRidingCont.text = "已骑行"+"2"+"分钟"+"，"+"骑行费用"+"9THB"
                    var unit2 = AppUtil.getFloat2(ridingPrice)+Glob.CurrencyUnit
                    var content2 = getString(R.string.s_cost_upay)+unit2
                    val spannableString = AppUtil.getSpanStr(unit2,content2,R.color.c_F86125)
                    binding.tvRidingCont.text = spannableString
                }
            }else{
                binding.rvSeeOrder.text = getString(R.string.s_see)
                if (MainStateTypeIsRental){
                    binding.tvRidingTitle.text = getString(R.string.s_rentaling)
//                    binding.tvRidingCont.text = "已租赁"+"9"+"分钟"
                    var unit = ridingTime.toString()+getString(R.string.min)
                    var content = getString(R.string.s_rental_has)+unit
                    val spannableString = AppUtil.getSpanStr(unit,content,R.color.blue_namal)
                    binding.tvRidingCont.text = spannableString
                }else{
                    binding.tvRidingTitle.text = getString(R.string.riding)
//                    binding.tvRidingCont.text = "已骑行"+"2"+"分钟"+"，"+"骑行费用"+"9THB"
                    var unit = ridingTime.toString()+getString(R.string.min)
                    var content = getString(R.string.s_riding_has)+unit
                    var unit2 = AppUtil.getFloat2(ridingPrice)+Glob.CurrencyUnit
                    var content2 = getString(R.string.travel_consumption)+unit2
                    content = content +", "+ content2
                    val spannableString = AppUtil.getSpanStr2(content,unit,unit2,R.color.blue_namal,R.color.blue_namal)
                    binding.tvRidingCont.text = spannableString
                }
            }
        }else{
            binding.lyRidingBtn.visibility = View.GONE
            binding.lyUriding.visibility = View.VISIBLE
        }
    }
    fun HindeParkAndCar(){
        onMapClick(LatLng())
    }

    // 假设 area_id 的获取和监听器设置在此 Activity 的某个地方，例如 onCreate 或 onResume
    // 我们需要在设置监听器的代码附近添加日志
    // 并且在 MainMapFragment 通知此 Activity 获取 area_id 结果的地方添加日志

    // 为了演示，我们假设 MainMapFragment 通过接口回调通知 MainActivity
    // 并且 MainActivity 实现了这个接口

    // 示例：假设有一个方法处理 area_id 获取结果
    private fun handleAreaIdResult(success: Boolean, areaId: Long) {
        if (success) {
            onAreaIdSucListener("handleAreaIdResult_Success")
        } else {
            onAreaIdFailListener("handleAreaIdResult_Fail")
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun onAreaIdSucListener(callSource: String){
        if (Glob.area_id == 0L) return
    }
    fun onAreaIdFailListener(callSource: String){
        if (Glob.area_id == 0L) return
    }

}