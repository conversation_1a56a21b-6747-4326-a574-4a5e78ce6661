package com.tbit.uqbike.activity

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.provider.Settings.ACTION_DATA_ROAMING_SETTINGS
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.Glob
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.TransactionLogAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityRideCardNewBinding
import com.tbit.uqbike.dialog.RideCardBuyDialog
import com.tbit.uqbike.dialog.RideCardInfoDialog
import com.tbit.uqbike.dialog.RideCardWallBuyDialog
import com.tbit.uqbike.entity.AreaInfoData
import com.tbit.uqbike.entity.ChargeMethodData
import com.tbit.uqbike.entity.EnoughData
import com.tbit.uqbike.entity.RideCardData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.fragment.PayFragment
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.model.RideCardModel
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.OrderModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.resqmodel.PayModel
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.NetUtils
import com.tbit.uqbike.utils.UrlDecodeUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.dip
import org.jetbrains.anko.startActivity
import org.json.JSONObject


class RideCardNewActivity : BaseActivity() {
    private val adapter by lazy { TransactionLogAdapter() }
    private var lastLoaded = false
    val areaListData = HashMap<Int,String>()
    lateinit var rideCardBuyDialog : RideCardBuyDialog
    //    lateinit var rideCardInfoDialog : RideCardInfoDialog
    var Selpos = -1
    private val loadMoreView by lazy { View(this).apply { layoutParams = ViewGroup.LayoutParams(1, 1) } }
    private lateinit var binding: ActivityRideCardNewBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRideCardNewBinding.inflate(layoutInflater)
        setContentView(binding.root)
        EventBus.getDefault().register(this);
//        setContentView(R.layout.activity_ride_card_new)

//        toolbar_title.text = getString(R.string.ride_card)
//        toolbar.setNavigationIcon(R.drawable.icon_back)
//        toolbar.setNavigationOnClickListener { finish() }
        binding.tvRidecardExchange.setOnClickListener { startActivity(ExchangeActivity.createIntent(this, ExchangeActivity.TYPE_RIDECARD)) }
        rideCardBuyDialog = RideCardBuyDialog(loadingDialogHelper)
        binding.ivClose.setOnClickListener { finish() }
        binding.rcv.layoutManager = LinearLayoutManager(this)
        val spacing = dip(10)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        binding.rcv.adapter = loadMoreWrapper(adapter)
        binding.rlMyridcard.setOnClickListener{startActivity(MyRideCardActivity.createIntent(this, areaListData))}

        if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
            if (!NetUtils.isConnected(ContextUtil.getContext())){
                setErrorView(true)
            }else{
                setErrorView(false)
            }
        }else{
            loadingDialogHelper.show { }
            ComModel.getAreaInfo().subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取运营区简易 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), AreaInfoData::class.java)
                    resultData.forEach {
                        areaListData.put(it.id,it.name)
                    }
                    getTransactionLog(false)
//                if (!Glob.isRental){
//                    getTransactionLog(false)
//                }else{
//                    loadingDialogHelper!!.dismiss()
//                }
                },
                onError = {
                    loadingDialogHelper!!.dismiss()
                    setErrorView(true)
                }
            ).toCancelable()
        }

        adapter.onBuyNoteListener = {
            loadingDialogHelper.show {  }
            PageModel.getPageUrl(PageModel.ride_card_purchase_instructions).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                    startActivity<WebActivity>(WebActivity.TITLE to getString(R.string.buy_notes),
                        WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
                },
                onError = {loadingDialogHelper.dismiss()}
            ).toCancelable()
        }
        var finalPrice = 0f
        adapter.onBuyCardListener = {
//            MyLogUtil.Log("1111","===onBuyCardListener===="+it)
            val properties = JSONObject()
            properties.put("card_id", adapter.source.get(it).id) // 设置商品 ID
            properties.put("originalPrice", adapter.source.get(it).original_price) // 设置商品 ID
            MDUtil.clickEvent("select_card",properties)
            Selpos = it
            try {
                finalPrice = adapter.source.get(it).final_price
                var rideCardInfoDialog = RideCardInfoDialog(this@RideCardNewActivity,loadingDialogHelper,adapter.source.get(it))
                rideCardInfoDialog.onBuyCardInfoListener={price,ids->
//                    MyLogUtil.Log("1111","===onBuyCardInfoListener===="+price)
                    rideCardBuyDialog.setRideCardID("",adapter.source.get(Selpos).id,price.replace(",","").toFloat(),adapter.source.get(Selpos).currency,ids)
                    lifecycleDialogHelper.show(rideCardBuyDialog)
                }
                rideCardInfoDialog.onDisCardInfoListener = {
                    binding.rcv.postDelayed({
                        it.final_price = finalPrice
                    },500)
                }
                lifecycleDialogHelper.show(rideCardInfoDialog)
            }catch (e : RuntimeException){}
        }
        rideCardBuyDialog.onOrderSucListener = {
            getTransactionLog(false)
        }
        rideCardBuyDialog.onPayCardListener = { orderNo ->
            loadingDialogHelper.show { }
            ComModel.getPayMethod().subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取支付 信息=="+it.toString())
                    loadingDialogHelper.dismiss()
                    var chargeModthData = Gson().fromJson(it.toString(), ChargeMethodData::class.java)
                    val payFragment = PayFragment(loadingDialogHelper)
                    payFragment.setData(chargeModthData!!,orderNo,PayFragment.TYPE_EXRIDECARD)
                    lifecycleDialogHelper.show(payFragment)
                },
                onError = {loadingDialogHelper!!.dismiss()}
            ).toCancelable()
        }

        rideCardBuyDialog.onPayCardWalltListener = { orderNo,price ->
            walltPay(orderNo,price)
        }
    }
    fun walltPay(orderNo : String,price : Float){
        loadingDialogHelper.show { }
        OrderModel.getMyWallet(orderNo)
            .subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","=== 余额是否足够 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), EnoughData::class.java)
                    var s_btn_r = getString(R.string.s_sure_buy)
                    if(resultData.is_enough == 1){

                    }else{
                        s_btn_r = getString(R.string.confirm_recharge)
                    }
                    if (price == 0f){
                        goWalletPay(orderNo)
                    }else{
                        RideCardWallBuyDialog.Builder(this@RideCardNewActivity)
                            .setTitle(getString(R.string.s_wallbuy)).setLeftText(getString(R.string.cancel)).setRightText(s_btn_r)
                            .setAmountUnit(resultData.currency).setIsenough(resultData.is_enough).setBalanceRidecard(resultData.amount)
                            .setBalanceWallt(resultData.balance).setCanceledOnOutside(true)
                            .setClickListen(object : RideCardWallBuyDialog.TwoSelDialog {
                                override fun leftClick() {}
                                override fun rightClick() {
                                    if(resultData.is_enough == 1){
                                        // 钱包余额购买
                                        val properties = JSONObject()
                                        MDUtil.clickEvent("wallet_pay_sure",properties)
                                        goWalletPay(orderNo)
                                    }else{
                                        // 去充值
                                        val properties = JSONObject()
                                        MDUtil.clickEvent("jump_recharge",properties)
                                        startActivity<ChargeNewActivity>()
                                    }
                                }
                            }).build().show()
                    }
                },
                onError = {loadingDialogHelper.dismiss()}
            ).toCancelable()
    }
    fun goWalletPay(orderNo : String){
        loadingDialogHelper.show { }
        PayModel.payRideCard(orderNo,Glob.PAY_WALLET).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===骑行卡支付 信息=="+it.toString())
//                                                var chargeData = Gson().fromJson(it.toString(), ChargeData::class.java)
                MyToastUtil.toast(getString(R.string.s_buy_suc))
                startActivity(RideCardPayResultActivity.createIntent(this@RideCardNewActivity,
                    RideCardPayResultActivity.TYPE_SUC,orderNo))
            },
            onError = {
                loadingDialogHelper.dismiss()

                startActivity(RideCardPayResultActivity.createIntent(this@RideCardNewActivity,
                    RideCardPayResultActivity.TYPE_FAIL,orderNo))
            }
        ).toCancelable()
    }
    private fun loadMoreWrapper(adapter: RecyclerView.Adapter<out RecyclerView.ViewHolder>): LoadMoreWrapper<out RecyclerView.ViewHolder> {
        val loadMoreAdapter = LoadMoreWrapper(adapter)
        loadMoreAdapter.setOnLoadMoreListener{ onLoadMore() }
        return loadMoreAdapter
    }
    private fun onLoadMore() {
        if (!lastLoaded)
            getTransactionLog(false)
    }

    override fun onRestart() {
        super.onRestart()
        getTransactionLog(false)
    }
    private fun getTransactionLog(showLoading: Boolean = true) {
        if (!(!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null)) {
            RideCardModel.getRideCardNew()
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper!!.dismiss()
                        MyLogUtil.Log("1111","===获取骑行卡套餐信息=="+it.toString())
                        val resultData = Gson().fromJson(it.toString(), RideCardData::class.java)
                        if(resultData != null && resultData.size > 0){
                            resultData.forEach{
                                it.areaName = areaListData.get(it.area_id).toString()
                            }
                            adapter.source = resultData
                            adapter.notifyDataSetChanged()
                        }
                        if (adapter.source.isEmpty()) binding.capaLayout.toEmpty() else binding.capaLayout.toContent()
                    }
                    , onError = {
                        loadingDialogHelper!!.dismiss()
                        setErrorView(true)
                    }
                ).toCancelable()
            if (showLoading) {
                loadingDialogHelper.show { }
            }
        }
    }
    fun setErrorView(isNetError : Boolean){
//        MyLogUtil.Log("1111","======isNetError======"+isNetError)
        if(isNetError){
            if (NetUtils.isConnected(ContextUtil.getContext())){
                binding.capaLayout.toEmpty()
                return
            }
        }
        if (isNetError){
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_net).visibility = View.VISIBLE
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_local).visibility = View.GONE
        }else{
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_net).visibility = View.GONE
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_local).visibility = View.VISIBLE
        }
        binding.capaLayout.toError()
        binding.capaLayout.findViewById<RoundTextView>(R.id.tv_err_set_net).setOnClickListener{
            val intent = Intent(ACTION_DATA_ROAMING_SETTINGS)
            startActivity(intent)
        }
        binding.capaLayout.findViewById<RoundTextView>(R.id.tv_err_set_local).setOnClickListener{
            val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
            startActivityForResult(intent, MainActivity.REQUEST_OPEN_GPS)
        }
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: BaseEventData) {
        if (event.code == EventUtil.EVENT_HOME) finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        LanguageUtil().settingLanguage(ContextUtil.getContext())
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }
}