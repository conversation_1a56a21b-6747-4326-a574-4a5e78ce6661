package com.tbit.uqbike.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.bindExtra
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.CivilizedRidingRecord
import com.tbit.uqbike.mvp.constract.CivilizedRidingAppealContract
import com.tbit.uqbike.mvp.presenter.CivilizedRidingAppealPresenter
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.toast

/**
 * 非文明骑行申诉/申诉详情
 */
class CivilizedRidingAppealActivity : BaseActivity(), CivilizedRidingAppealContract.View {

    companion object {
        private const val EXTRA_DATA = "EXTRA_DATA"

        fun createIntent(context: Context, data: CivilizedRidingRecord): Intent {
            return context.intentFor<CivilizedRidingAppealActivity>(EXTRA_DATA to data)
        }
    }

    private val presenter = CivilizedRidingAppealPresenter(this)
    private val data: CivilizedRidingRecord by bindExtra(EXTRA_DATA)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_civilized_riding_appeal)
//        setSupportActionBar(toolbar)
//        supportActionBar?.setDisplayShowTitleEnabled(false)
//        toolbar.setNavigationIcon(R.drawable.icon_back)
//        toolbar.setNavigationOnClickListener { onBackPressed() }
//
//        if (data.state == Constant.UncivilizedRidingAppealState.NOT_APPEAL) {
//            toolbar_title.text = getString(R.string.uncivilized_riding_appeal)
//            tv_reply_hint.visibility = View.GONE
//            tv_reply.visibility = View.GONE
//            btn_submit.visibility = View.VISIBLE
//            btn_back.visibility = View.GONE
//        } else {
//            toolbar_title.text = getString(R.string.appeal_detail)
//            tv_reply_hint.visibility = View.VISIBLE
//            tv_reply.visibility = View.VISIBLE
//            btn_submit.visibility = View.GONE
//            btn_back.visibility = View.VISIBLE
//            et_appeal.setText(data.reason)
//            et_appeal.isEnabled = false
//            tv_reply.text = data.rspMsg
//        }
//
//        btn_submit.setOnClickListener {
//            val content = et_appeal.text.toString().trim()
//            if (content.isEmpty()) {
//                toast(R.string.civilized_riding_appeal_hint)
//                return@setOnClickListener
//            }
//            val cancellable = presenter.uncivilizedRidingAppeal(data.rideId, data.orderNo, content)
//            loadingDialogHelper.show { cancellable.cancel() }
//        }
//
//        btn_back.setOnClickListener { finish() }

    }

    override fun appealSuccess() {
        loadingDialogHelper.dismiss()
        toast(R.string.submit_success)
        setResult(Activity.RESULT_OK)
        finish()
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        if (message.isNotEmpty()) toast(message)
    }
}