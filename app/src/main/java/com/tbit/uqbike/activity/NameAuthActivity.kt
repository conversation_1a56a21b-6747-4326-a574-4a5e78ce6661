package com.tbit.uqbike.activity

import android.app.Activity
import android.os.Bundle
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.mvp.constract.NameAuthContract
import com.tbit.uqbike.mvp.presenter.NameAuthPresenter
import org.jetbrains.anko.toast
import java.util.regex.Pattern

class NameAuthActivity: BaseActivity(), NameAuthContract.View {

    private val presenter = NameAuthPresenter(this)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_idverification)
//        setSupportActionBar(toolbar)
//        supportActionBar?.setDisplayShowTitleEnabled(false)
//        toolbar_title.text = getString(R.string.name_verify_title)
//        toolbar.setNavigationIcon(R.drawable.icon_back)
//        toolbar.setNavigationOnClickListener { onBackPressed() }
//
//        lifecycle.addObserver(presenter)
//
//        button_verify.setOnClickListener { onNameAuthClick() }
    }

    private fun onNameAuthClick() {
//        val name = edit_name.text.toString()
//        val idNO = edit_id.text.toString()
//        if (check(name, idNO)) {
//            val cancellable = presenter.nameAuth(idNO, name)
//            loadingDialogHelper.show { cancellable.cancel() }
//        }
    }

    private fun check(name: String, idNO: String): Boolean {
        var result = false
        when {
//            name.isEmpty() || idNO.isEmpty() -> toast(R.string.incomplete_information)
//            !checkIdNO(idNO) -> toast(R.string.id_num_not_meet_the_rule)
            else -> result = true
        }
        return result
    }

    private fun checkIdNO(idNO: String): Boolean {
        val p = Pattern.compile("^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$")
        return p.matcher(idNO).find()
    }

    override fun onNameAuthSuccess() {
        loadingDialogHelper.dismiss()
        setResult(Activity.RESULT_OK)
        finish()
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }
}