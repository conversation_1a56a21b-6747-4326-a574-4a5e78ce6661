package com.tbit.uqbike.activity

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.WalletBalanceAdapter
import com.tbit.uqbike.adapter.WalletBalanceAdapter.Companion.TYPE_VIEW_WALLET
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityWalletBalanceBinding
import com.tbit.uqbike.entity.Item
import com.tbit.uqbike.entity.WalletBalanceData
import com.tbit.uqbike.resqmodel.WalletModel
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip
import org.jetbrains.anko.startActivity
import org.json.JSONObject

class WalletBalanceActivity : BaseActivity() {
    private var lastId = 0
    private val pageSize = Glob.pageNum
    private var lastLoaded = false
    private val adapter = WalletBalanceAdapter(TYPE_VIEW_WALLET)
    private val listdata = mutableListOf<Item>()
    private val loadMoreView by lazy { View(this).apply { layoutParams = ViewGroup.LayoutParams(1, 1) } }
    private lateinit var binding: ActivityWalletBalanceBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWalletBalanceBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_wallet_balance)

        binding.layoutToolbar.toolbarTitle.text = getString(R.string.s_walletbalance)
        binding.layoutToolbar.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.layoutToolbar.toolbar.setNavigationOnClickListener { finish() }

        listdata.add(Item(0.0,0,0,0,""))//添加头部
        binding.rcv.layoutManager = LinearLayoutManager(this)
        binding.rcv.adapter = loadMoreWrapper(adapter)
        val spacing = dip(1)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        adapter.onGoPayListener = {
            startActivity<ChargeNewActivity>()
            val properties = JSONObject()
            MDUtil.clickEvent("go_recharge",properties)
        }

        getListData()
    }
    private fun loadMoreWrapper(adapter: RecyclerView.Adapter<out RecyclerView.ViewHolder>)
            : LoadMoreWrapper<out RecyclerView.ViewHolder> {
        val loadMoreAdapter = LoadMoreWrapper(adapter)
        loadMoreAdapter.setOnLoadMoreListener{ onLoadMore() }
        return loadMoreAdapter
    }
    private fun onLoadMore() {
        if (!lastLoaded)
            getListData(false)
    }
    private fun getListData(showLoading: Boolean = true) {
        WalletModel.getBalancedetail(lastId,pageSize)
            .subscribeBy(
                onNext = {
                    loadingDialogHelper!!.dismiss()
                    MyLogUtil.Log("1111","======= 获取钱包余额 流水信息 ==="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), WalletBalanceData::class.java)
                    lastLoaded = resultData.items.size < pageSize
                    if(resultData.items.size > 0){
                        lastId = resultData.items.last().id
                    }
                    (binding.rcv.adapter as? LoadMoreWrapper<*>)?.setLoadMoreView(if (lastLoaded) null else loadMoreView)
                    listdata.addAll(resultData.items)
                    adapter.source = listdata
                    wallbalance_event = AppUtil.getFloat2Double(resultData.amount)
                    adapter.setWalletBalance(resultData.amount,resultData.currency)
                    adapter.notifyDataSetChanged()
                    if (adapter.source.isEmpty()) binding.capaLayout.toEmpty() else binding.capaLayout.toContent()
                },
                onError = {
                    loadingDialogHelper!!.dismiss()
                }
            ).toCancelable()
        if (showLoading) {
            loadingDialogHelper.show { }
        }
    }
}