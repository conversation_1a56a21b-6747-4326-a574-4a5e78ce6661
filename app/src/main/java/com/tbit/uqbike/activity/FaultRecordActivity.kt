package com.tbit.uqbike.activity

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.baidu.ar.it
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.FaultRecordAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityFaultRecordBinding
import com.tbit.uqbike.entity.FaultListData
import com.tbit.uqbike.entity.FaultListDataItem
import com.tbit.uqbike.entity.FormoptionData
import com.tbit.uqbike.entity.RidingRecordDataItem
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.FaultModel
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip

class FaultRecordActivity : BaseActivity(){

    private val adapter by lazy { FaultRecordAdapter() }
    private lateinit var binding: ActivityFaultRecordBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFaultRecordBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_fault_record)

        binding.toolbars.toolbarTitle.text = getString(R.string.s_sumit_record)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { finish() }

        getData(true)

        val spacing = dip(10)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        binding.rcv.layoutManager = LinearLayoutManager(this)
        binding.rcv.adapter = loadMoreWrapper(adapter)

        adapter.setOnItemClickListener {
            startActivity(FaultInfoActivity.createIntent(this,it.id))
        }
        binding.capaLayout.toContent()
        binding.smartLayout.setOnRefreshListener {
            lastLoaded = false
            lastId = 0
            dataList.clear()
            adapter.source = dataList
            adapter.notifyDataSetChanged()
            getData()
        }
    }
    private fun loadMoreWrapper(adapter: RecyclerView.Adapter<out RecyclerView.ViewHolder>)
            : LoadMoreWrapper<out RecyclerView.ViewHolder> {
        val loadMoreAdapter = LoadMoreWrapper(adapter)
        loadMoreAdapter.setOnLoadMoreListener{ onLoadMore() }
        return loadMoreAdapter
    }

    private fun onLoadMore() {
        if (!lastLoaded)
            getData(false)
    }

    private var lastLoaded = false
    private val loadMoreView by lazy { View(this).apply { layoutParams = ViewGroup.LayoutParams(1, 1) } }
    val dataList = mutableListOf<FaultListDataItem>()
    var lastId = 0
    private val pageSize = Glob.pageNum
    private fun getData(showLoading: Boolean = true) {
        if (showLoading) {
            loadingDialogHelper.show { }
        }
        FaultModel.getRecord(FaultModel.sumtType_Fault,lastId,pageSize).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                binding.smartLayout.finishRefresh()
                MyLogUtil.Log("1111","===获取故障上报列表 信息=="+ it.toString())
                var resultData = Gson().fromJson(it.toString(), FaultListData::class.java)
                if (resultData != null && resultData.size > 0){
                    lastLoaded = resultData.size < pageSize
                    dataList.addAll(resultData)
                    lastId = resultData.last().id

                    (binding.rcv.adapter as? LoadMoreWrapper<*>)?.setLoadMoreView(if (lastLoaded) null else loadMoreView)
                    adapter.source = dataList
                    adapter.notifyDataSetChanged()
                    binding.capaLayout.toContent()

//                    if (lastLoaded) MyToastUtil.toast(getString(R.string.no_more_tips))
                }else{
                    lastLoaded = true
                    if (adapter.source.size == 0) binding.capaLayout.toEmpty()
                }
            },
            onError = {
                loadingDialogHelper.dismiss()
                binding.smartLayout.finishRefresh()
            }
        ).toCancelable()
    }

    override fun onDestroy() {
        super.onDestroy()
    }
}