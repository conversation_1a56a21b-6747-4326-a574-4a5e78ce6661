package com.tbit.uqbike.activity

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.CivilizedRidingRecordAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.CivilizedRidingRecord
import com.tbit.uqbike.mvp.constract.CivilizedRidingRecordContract
import com.tbit.uqbike.mvp.presenter.CivilizedRidingRecordPresenter
import org.jetbrains.anko.toast

/**
 * 非文明骑行记录
 */
class CivilizedRidingRecordActivity : BaseActivity(), CivilizedRidingRecordContract.View {

    private val presenter = CivilizedRidingRecordPresenter(this)
    private val adapter = CivilizedRidingRecordAdapter()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_civilized_riding_record)
//        setSupportActionBar(toolbar)
//        supportActionBar?.setDisplayShowTitleEnabled(false)
//        toolbar_title.text = getString(R.string.uncivilized_riding_record)
//        toolbar.setNavigationIcon(R.drawable.icon_back)
//        toolbar.setNavigationOnClickListener { onBackPressed() }
//
//        lifecycle.addObserver(presenter)
//
//        rcv.layoutManager = LinearLayoutManager(this)
//        rcv.adapter = adapter
//        adapter.setOnItemClickListener { startActivity(CivilizedRidingDetailActivity.createIntent(this, it)) }
//
//        getData()
    }

    private fun getData() {
        val cancellable = presenter.getRecord()
        loadingDialogHelper.show { cancellable.cancel() }
    }

    override fun onGetRecordSuccess(data: List<CivilizedRidingRecord>?) {
        loadingDialogHelper.dismiss()
//        if (data.isNullOrEmpty()) {
//            capaLayout.toEmpty()
//        } else {
//            capaLayout.toContent()
//            adapter.source = data
//            adapter.notifyDataSetChanged()
//        }
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
//        capaLayout.toError()
    }
}