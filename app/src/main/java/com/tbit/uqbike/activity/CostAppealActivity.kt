package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckedTextView
import android.widget.Toast
import androidx.recyclerview.widget.GridLayoutManager
import com.baidu.ar.it
import com.google.gson.Gson
import com.lsxiao.apollo.core.Apollo
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.luck.picture.lib.permissions.PermissionChecker
import com.luck.picture.lib.permissions.PermissionConfig
import com.tbit.maintanenceplus.utils.InputMethodUtils
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.preview.ImageInfo
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.App
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.SelectPhotoAdapter
import com.tbit.uqbike.adapter.imgexplainAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.custom.CountryCodeWindow
import com.tbit.uqbike.databinding.ActivityCostAppealBinding
import com.tbit.uqbike.entity.AliData
import com.tbit.uqbike.entity.Child
import com.tbit.uqbike.entity.CostAppealSetData
import com.tbit.uqbike.entity.CostAppealSetDataItem
import com.tbit.uqbike.entity.CountryData
import com.tbit.uqbike.entity.CountryDataItem
import com.tbit.uqbike.entity.Extend
import com.tbit.uqbike.entity.FaultSumitRestData
import com.tbit.uqbike.entity.getAdressData
import com.tbit.uqbike.fragment.PermissionReadFragment
import com.tbit.uqbike.fragment.ReadMediaPermissionFragment
import com.tbit.uqbike.mvp.constract.CostAppealContract
import com.tbit.uqbike.mvp.model.CountryCodeModel
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.presenter.CostAppealPresenter
import com.tbit.uqbike.qrcode.CommonUtils
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.ComModel.FormType_Cost
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.toast
import org.json.JSONObject

/**
 * 费用申诉
 */
class CostAppealActivity : BaseActivity(), CostAppealContract.View {

    companion object {
        const val REQUEST_TYPE = 3
        private const val EXTRA_ORDERNO = "EXTRA_ORDERNO"
        private const val EXTRA_VEHICLENO = "EXTRA_VEHICLENO"

        fun createIntent(context: Context, vehicle_no: String, order_no: String): Intent {
            return context.intentFor<CostAppealActivity>(EXTRA_VEHICLENO to vehicle_no,EXTRA_ORDERNO to order_no)
        }

    }

    private val vehicle_no: String by bindExtra(EXTRA_VEHICLENO)
    private val order_no: String by bindExtra(EXTRA_ORDERNO)
    private val presenter = CostAppealPresenter(this)
    private val photos = mutableListOf<ImageInfo>()
    private val FeedList = mutableListOf<CostAppealSetDataItem>()
    private lateinit var binding: ActivityCostAppealBinding
    private var adapterImgExample = imgexplainAdapter(this@CostAppealActivity)
    private var MyAdress = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCostAppealBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_cost_appeal)
        setSupportActionBar(binding.toolbars.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        binding.toolbars.toolbarTitle.text = getString(R.string.cost_appeal)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { onBackPressed() }

        lifecycle.addObserver(presenter)

        ComModel.getFormType2(FormType_Cost).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取费用申诉类型 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), CostAppealSetData::class.java)
                resultData.forEach {
                    FeedList.add(it)
                }
                setFlowLayout()
            }
        ).toCancelable()

        ComModel.getAlConfig().subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取阿里云 配置 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), AliData::class.java)
                Glob.ACCESS_ID = resultData.accesskey_id
                Glob.ACCESS_KEY = resultData.accesskey_secret
                Glob.ACCESS_TOKEN = resultData.security_token
                Glob.ACCESS_BUCKET_NAME = resultData.bucket
                Glob.ACCESS_ENDPOINT = resultData.endpoint
                Glob.ACCESS_DOMAINNAME = resultData.host_cdn
                Glob.IMG_PREFIX = resultData.prefix
            }
        ).toCancelable()

        val spacing = dip(3)
        val spanCount = 4
        binding.rcvImage.layoutManager = GridLayoutManager(this, spanCount)
        binding.rcvImage.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))

        binding.rcvImageExplain.adapter = adapterImgExample
        binding.rcvImageExplain.layoutManager = GridLayoutManager(this, spanCount)
        binding.rcvImageExplain.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))


        binding.btnSubmit.clickDelay {
            val properties = JSONObject()
            MDUtil.clickEvent("expense_appeal_sure_click",properties)
            onSubmitClick()
        }

        var location = LocationModel.lastLocation
        var latLng = LatLng()
        latLng.lat = location!!.latitude
        latLng.lng = location!!.longitude
        if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
            latLng = GPSUtil.bd09_To_gps84(location.latitude,location.longitude)
        }
        latLng.lat = GPSUtil.retain6(latLng.lat)
        latLng.lng = GPSUtil.retain6(latLng.lng)
        ComModel.getAdress(latLng.lat,latLng.lng).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===当前位置地址 信息=="+it.toString())
                val resultData = Gson().fromJson(it.toString(), getAdressData::class.java)
                if (resultData != null){
                    MyAdress = resultData.address
                }
            }
        ).toCancelable()

        countryCodeWindow.setOnItemClickListener { onSelectCountryCode(it) }
        binding.ivCountryCode.setOnClickListener { binding.textCountryCode.performClick() }
        binding.textCountryCode.setOnClickListener {
            showCountryCodeWindow()
        }



        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            (supportFragmentManager.findFragmentById(R.id.read_media_permission_fragment) as ReadMediaPermissionFragment).requestPermission {
                if (!it) {
                    binding.tvImageTitle.visibility = View.GONE
                    binding.rcvImage.visibility = View.GONE
                }
            }
        }else{
            (supportFragmentManager.findFragmentById(R.id.read_permission_fragment) as PermissionReadFragment).requestPermission {
            }
//            val readPermissionArray = PermissionConfig.getReadPermissionArray(ContextUtil.getContext(), SelectMimeType.ofImage())
//            if (!PermissionChecker.isCheckReadStorage(SelectMimeType.ofImage(), ContextUtil.getContext())){
//                PermissionChecker.getInstance().requestPermissions(supportFragmentManager.findFragmentById(R.id.read_media_permission_fragment),
//                    readPermissionArray, null)
//            }
        }
    }
    private val countryCodeWindow by lazy {
        CountryCodeWindow(this, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }
    private inline fun onSelectCountryCode(countryCodeInfo: CountryDataItem) {
        CountryCodeModel.countryCodeInfo = countryCodeInfo
        binding.textCountryCode.text = countryCodePlus(countryCodeInfo.country_code)
        countryCodeWindow.dismiss()
    }
    private inline fun showCountryCodeWindow() {
        loadingDialogHelper.show {  }
        CountryCodeModel.getAllCountryCodeInfoFromNet()
            .subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    val resultData: CountryData = Gson().fromJson(it.toString(), CountryData::class.java)
//                    view.onGetCountryCodeInfoSuccess(resultData)
                    setCountryCodeDatas(resultData)

                    InputMethodUtils.hideInput(ContextUtil.getContext(),binding.textCountryCode)
                    countryCodeWindow.width = CommonUtils.getScreenWidth(this@CostAppealActivity)
                    countryCodeWindow.height = CommonUtils.getScreenHeight(this@CostAppealActivity)
                    countryCodeWindow.showAtLocation(binding.llCountryCode, Gravity.BOTTOM,0,0)
                },
                onError = {
                    loadingDialogHelper.dismiss()
                }
            ).toCancelable()
    }

    fun setCountryCodeDatas(data: List<CountryDataItem>){
        if(SpUtil.getInstance().find(Constant.SpKey.SP_COUNTRY_SEL).isNullOrEmpty()){
            binding.textCountryCode.text = AppUtil.getPhoneCode()
        }else{
            val oldCode = CountryCodeModel.countryCodeInfo.country_code
            MyLogUtil.Log("1111","国家码数据"+oldCode)
            val target = data.firstOrNull {
                it.country_code == oldCode }
            if (target != null) {
                var code = oldCode.toInt()
                binding.textCountryCode.text = countryCodePlus(code.toString())
            }
        }
    }
    private fun countryCodePlus(code: String?): String {
        if (code.isNullOrEmpty()) {
            return ""
        }
        return "+"+code.toInt()
    }
    private fun setFlowLayout() {
        binding.flowLayout.removeAllViews()
        for (i in 0 until FeedList.size) {
            val mInflater = LayoutInflater.from(this)
            val tv = mInflater.inflate(R.layout.flow_cost_layout, binding.flowLayout, false) as CheckedTextView
            tv.setText(FeedList.get(i).title)
            //点击事件
            tv.setOnClickListener {
                for (s in 0 until FeedList.size){
                    (binding.flowLayout.getChildAt(s) as CheckedTextView).isChecked = false
                }
                tv.isChecked = true
                FeedList.get(i).ischeck = true
                binding.ryFlow.visibility = View.VISIBLE
                if(FeedList.get(i).child_list != null && FeedList.get(i).child_list.size > 0){
                    setFlowChildLayout(FeedList.get(i).child_list)
                }else{
                    setFlowChildLayout(ArrayList<Child>())
                }
                setViewGone()
                sumitChildExtendData = null
                type = ""
                binding.editRemark.setText("")
                photos.clear()
            }
            binding.flowLayout.addView(tv) //添加到父View

        }
    }
    var sumitChildExtendData : Extend? = null
    private fun setFlowChildLayout(FeedListChild : List<Child>) {
        binding.flowLayoutchild.removeAllViews()
        for (i in 0 until FeedListChild.size) {
            val mInflater = LayoutInflater.from(this)
            val tv = mInflater.inflate(R.layout.flow_cost_child_layout, binding.flowLayoutchild, false) as CheckedTextView
            tv.setText(FeedListChild.get(i).title)
            //点击事件
            tv.setOnClickListener {
                for (s in 0 until FeedListChild.size){
                    (binding.flowLayoutchild.getChildAt(s) as CheckedTextView).isChecked = false
                }
                tv.isChecked = true
                FeedListChild.get(i).ischeck = true
                type = FeedListChild.get(i).id.toString()
                binding.btnSubmit.setTextColor(resources.getColor(R.color.white))
                binding.editRemark.setHint(FeedListChild.get(i).desc)
                binding.llCountryCode.visibility = View.VISIBLE
                if (FeedListChild.get(i).extend != null){
                    sumitChildExtendData = FeedListChild.get(i).extend
                    if (sumitChildExtendData?.remark_show != null && sumitChildExtendData?.remark_show == 1){
                        binding.lyRemarkTitle.visibility = View.VISIBLE
                        binding.lyRemark.visibility = View.VISIBLE
                        if (sumitChildExtendData?.remark_required == 1){
                            binding.tvInputRemarkY.visibility = View.VISIBLE
                        }else{
                            binding.tvInputRemarkY.visibility = View.GONE
                        }
                    }else{
                        binding.lyRemarkTitle.visibility = View.GONE
                        binding.lyRemark.visibility = View.GONE
                        binding.tvInputRemarkY.visibility = View.GONE
                    }

                    if (sumitChildExtendData?.img_show != null && sumitChildExtendData?.img_show == 1){
                        binding.tvImageTitle.visibility = View.VISIBLE
                        var unit = sumitChildExtendData!!.img_limit
                        var content = getString(R.string.s_cost_img_title,unit.toString())
                        if (sumitChildExtendData?.img_required == 1){
                            content = content + getString(R.string.s_input_y)
                        }
                        val spannableString = SpannableString(content)
                        if (content.contains(unit.toString())){
                            var data1 = spannableString.split(unit.toString())[0].length
                            spannableString.setSpan(ForegroundColorSpan(resources.getColor(R.color.blue_namal)), data1, data1+unit.toString().length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                        }
                        binding.tvImageTitle.text = spannableString

                        binding.rcvImage.visibility = View.VISIBLE
//                        if (sumitChildExtendData?.img_required == 1){
//                            binding.tvInputImgY.visibility = View.VISIBLE
//                        }else{
//                            binding.tvInputImgY.visibility = View.GONE
//                        }

                        binding.tvImageTitle.text = spannableString

                        val spanCount = 4
                        val maxCount = sumitChildExtendData?.img_limit!!
                        var adapt = SelectPhotoAdapter(this, spanCount, maxCount, photos,SelectPhotoAdapter.TYPE_APPEAL)
                        adapt.isWate = true
                        adapt.carnum = vehicle_no
                        adapt.adress = MyAdress
                        if (sumitChildExtendData?.img_type == 1) adapt.isCam = true
                        binding.rcvImage.adapter = adapt
                    }else{
                        binding.tvImageTitle.visibility = View.GONE
                        binding.rcvImage.visibility = View.GONE
                        binding.tvInputImgY.visibility = View.GONE
                    }

                    if (sumitChildExtendData?.img_list != null && sumitChildExtendData?.img_list?.size!! > 0){
                        var photoData = ArrayList<ImageInfo>()
                        sumitChildExtendData?.img_list!!.forEach {
                            var imgData = ImageInfo()
                            imgData.bigImageUrl = it.url
                            photoData.add(imgData)
                        }
                        adapterImgExample.source = photoData
                        adapterImgExample.notifyDataSetChanged()
                        binding.lyRemarkExplain.visibility = View.VISIBLE
                    }else{
                        binding.lyRemarkExplain.visibility = View.GONE
                    }
                }else{
                    setViewGone()
                }
            }
            binding.flowLayoutchild.addView(tv) //添加到父View

        }
    }
    fun setViewGone(){
        binding.lyRemarkTitle.visibility = View.GONE
        binding.lyRemark.visibility = View.GONE
        binding.llCountryCode.visibility = View.GONE
        binding.tvInputRemarkY.visibility = View.GONE
        binding.tvImageTitle.visibility = View.GONE
        binding.rcvImage.visibility = View.GONE
        binding.tvInputImgY.visibility = View.GONE
        binding.lyRemarkExplain.visibility = View.GONE
    }
    private fun countryCodeNoPlus(code: String?): String {
        return code?.substring(1) ?: ""
    }
    var type = ""
    private fun onSubmitClick() {
//        FeedList.forEach {
//            if(it.ischeck){
//                type = it.id.toString()
//            }
//        }
        if(type.equals("")){
            Toast.makeText(App.context,getString(R.string.please_select_appeal_reason_first),Toast.LENGTH_SHORT).show()
            return
        }
        var text_country_code = binding.textCountryCode.text.toString()
        var edit_phone_number = binding.editPhoneNumber.text.toString()

        if (text_country_code.isNullOrEmpty() || edit_phone_number.isNullOrEmpty()){
            Toast.makeText(App.context,getString(R.string.phone_number_hint),Toast.LENGTH_SHORT).show()
            return
        }

        val listType = ArrayList<Int>()
        listType.add(type.toInt())

        val remark = binding.editRemark.text.toString().trim()
        val listImg = ArrayList<String>()
        if (sumitChildExtendData != null){
            if (sumitChildExtendData?.remark_required == 1){
                if(remark.isNullOrEmpty()){
                    MyToastUtil.toast(getString(R.string.s_input_remark))
                    return
                }
            }
            if (sumitChildExtendData?.img_required == 1){
                if (photos.size == 0){
                    MyToastUtil.toast(getString(R.string.s_sumit_pic))
                    return
                }
                if (photos.size != sumitChildExtendData?.img_limit){
                    MyToastUtil.toast(getString(R.string.s_sumit_pic_err))
                    return
                }
            }
            if(photos.size > 0){
                photos.forEach {
                    if(!it.bigImageUrl.contains("http")){
                        Toast.makeText(App.context, getString(R.string.s_sumitimg_fail),Toast.LENGTH_SHORT).show()
                        return
                    }
                    listImg.add(it.bigImageUrl)
                }
            }
        }

//        MyLogUtil.Log("1111","=== 费用申诉提交 ==="+vehicle_no+","+order_no+","+type+","+remark+","+listImg.size)
//        listImg.forEach {
//            MyLogUtil.Log("1111","=== 费用申诉提交 img==="+it)
//        }
        val cancellable = presenter.appeal(vehicle_no, listType, remark, order_no,listImg,countryCodeNoPlus(text_country_code).toInt(),edit_phone_number.toLong())
        loadingDialogHelper.show { cancellable.cancel() }
    }

//    private fun check(reason: String?, remark: String): Boolean {
//        var result = false
//        when {
//            reason.isNullOrEmpty() -> toast(R.string.please_select_appeal_reason_first)
//            remark.isNullOrEmpty() -> toast(R.string.enter_desc_remark_hint)
//            else -> result = true
//        }
//        return result
//    }

    override fun appealSuccess(data : String) {
        loadingDialogHelper.dismiss()
        Apollo.emit(Constant.Event.EVENT_COST_DEL)
        toast(getString(R.string.submit_success))
        val resultData = Gson().fromJson(data, FaultSumitRestData::class.java)
        if (resultData != null){
            startActivity(CostInfoActivity.createIntent(this, resultData.id))
        }
        finish()
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }
}