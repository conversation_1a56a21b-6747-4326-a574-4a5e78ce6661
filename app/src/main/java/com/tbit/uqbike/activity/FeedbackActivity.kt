package com.tbit.uqbike.activity

import android.Manifest
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.CheckedTextView
import android.widget.Toast
import androidx.recyclerview.widget.GridLayoutManager
import com.baidu.ar.it
import com.google.gson.Gson
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.luck.picture.lib.permissions.PermissionChecker
import com.luck.picture.lib.permissions.PermissionConfig
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.preview.ImageInfo
import com.tbit.uqbike.App
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.SelectPhotoAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityApplyForSitesBinding
import com.tbit.uqbike.databinding.ActivityFeedbackBinding
import com.tbit.uqbike.entity.AliData
import com.tbit.uqbike.entity.FormoptionData
import com.tbit.uqbike.entity.FormoptionDataItem
import com.tbit.uqbike.fragment.PermissionReadFragment
import com.tbit.uqbike.fragment.ReadMediaPermissionFragment
import com.tbit.uqbike.mvp.constract.FeedbackContract
import com.tbit.uqbike.mvp.presenter.FeedbackPresenter
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip
import org.jetbrains.anko.toast

class FeedbackActivity : BaseActivity(), FeedbackContract.View {

    private val presenter = FeedbackPresenter(this)
    private val photos = mutableListOf<ImageInfo>()
    private val FeedList = mutableListOf<FormoptionDataItem>()
    companion object {
        const val REQUEST_TYPE = 2
        private const val EXTRA_ORDERNO = "EXTRA_ORDERNO"
        private const val EXTRA_VEHICLENO = "EXTRA_VEHICLENO"
    }
    private lateinit var binding: ActivityFeedbackBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFeedbackBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_feedback)
        setSupportActionBar(binding.toolbars.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        binding.toolbars.toolbarTitle.text = getString(R.string.feedback)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { onBackPressed() }

        lifecycle.addObserver(presenter)

        ComModel.getFormType(REQUEST_TYPE).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取意见反馈类型 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), FormoptionData::class.java)
                resultData.forEach {
                    FeedList.add(it)
                }
                setFlowLayout()
            }
        ).toCancelable()

        ComModel.getAlConfig().subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取阿里云 配置 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), AliData::class.java)
                Glob.ACCESS_ID = resultData.accesskey_id
                Glob.ACCESS_KEY = resultData.accesskey_secret
                Glob.ACCESS_TOKEN = resultData.security_token
                Glob.ACCESS_BUCKET_NAME = resultData.bucket
                Glob.ACCESS_ENDPOINT = resultData.endpoint
                Glob.ACCESS_DOMAINNAME = resultData.host_cdn
                Glob.IMG_PREFIX = resultData.prefix
            }
        ).toCancelable()

        val spacing = dip(3)
        val spanCount = 4
        val maxCount = 1
        binding.rcvImage.adapter = SelectPhotoAdapter(this, spanCount, maxCount, photos,SelectPhotoAdapter.TYPE_FEEDBACK)
        binding.rcvImage.layoutManager = GridLayoutManager(this, spanCount)
        binding.rcvImage.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))

        binding.btnSubmit.setOnClickListener { onSubmitClick() }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            (supportFragmentManager.findFragmentById(R.id.read_media_permission_fragment) as ReadMediaPermissionFragment).requestPermission {
                if (!it) {
                    binding.tvImageTitle.visibility = View.GONE
                    binding.rcvImage.visibility = View.GONE
                }
            }
        }else{
            (supportFragmentManager.findFragmentById(R.id.read_permission_fragment) as PermissionReadFragment).requestPermission {
            }
        }
    }

    private fun onSubmitClick() {
        var type = ""
        FeedList.forEach {
            if(it.ischeck){
                type = it.id.toString()
            }
        }
        if(type.equals("")){
            Toast.makeText(ContextUtil.getContext(),getString(R.string.please_select_reason_first),Toast.LENGTH_SHORT).show()
            return
        }
        val listType = ArrayList<Int>()
        listType.add(type.toInt())

        val remark = binding.editAddition.text.toString().trim()
        val listImg = ArrayList<String>()
        if(photos.size > 0){
            if(!photos.get(0).bigImageUrl.contains("http")){
                Toast.makeText(ContextUtil.getContext(),getString(R.string.s_sumitimg_fail),Toast.LENGTH_SHORT).show()
                return
            }
            listImg.add(photos.get(0).bigImageUrl)
        }
//        MyLogUtil.Log("1111","=== 意见反馈提交 ==="+type+","+remark+",")

        val cancelable = presenter.feedback("", listType, remark, "",listImg)
        loadingDialogHelper.show { cancelable.cancel() }
    }
    private fun setFlowLayout() {
        binding.flowLayout.removeAllViews()
        for (i in 0 until FeedList.size) {
            val mInflater = LayoutInflater.from(this)
            val tv = mInflater.inflate(R.layout.flow_layout, binding.flowLayout, false) as CheckedTextView
            tv.setText(FeedList.get(i).title)
            //点击事件
            tv.setOnClickListener {
                for (s in 0 until FeedList.size){
                    (binding.flowLayout.getChildAt(s) as CheckedTextView).isChecked = false
                }
                tv.isChecked = true
                FeedList.get(i).ischeck = true
                binding.btnSubmit.setTextColor(resources.getColor(R.color.white))
            }
            binding.flowLayout.addView(tv) //添加到父View
        }
    }
    override fun onFeedbackSuccess() {
        loadingDialogHelper.dismiss()
        toast(R.string.thanks_for_feedback)
        finish()
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }
}