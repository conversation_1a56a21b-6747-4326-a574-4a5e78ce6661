package com.tbit.uqbike.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.net.Uri
import android.net.http.SslError
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.webkit.SslErrorHandler
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.baidu.ar.it
import com.baidu.ar.ku.value
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.doule.database.CoroutinesUtil
import com.google.android.material.internal.ViewUtils.dpToPx
import com.google.gson.Gson
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.stripe.android.model.ConsumerPaymentDetails.Card.Companion.type
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.fullScreen
import com.tbit.maintenance.utils.toCancelable
import com.tbit.preview.GlideEngine
import com.tbit.uqbike.App
import com.tbit.uqbike.Glob
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.SelectPhotoAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.databinding.ActivityWebActionBinding
import com.tbit.uqbike.databinding.ActivityWebBinding
import com.tbit.uqbike.dialog.PicSumitDialog
import com.tbit.uqbike.entity.AliData
import com.tbit.uqbike.entity.ExchangePayStateData
import com.tbit.uqbike.entity.JsData
import com.tbit.uqbike.entity.JsGoNativeData
import com.tbit.uqbike.entity.JsImgData
import com.tbit.uqbike.entity.OrderResultData
import com.tbit.uqbike.entity.PayLianLianData
import com.tbit.uqbike.entity.RentalRechData
import com.tbit.uqbike.entity.TextData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.CouponData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.entity.jsChoseCoupon
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.entity.protocolData
import com.tbit.uqbike.entity.saveImgData
import com.tbit.uqbike.entity.shareUrlData
import com.tbit.uqbike.file.FileUtil
import com.tbit.uqbike.fragment.PayFragment
import com.tbit.uqbike.fragment.PermissionReadFragment
import com.tbit.uqbike.fragment.ReadMediaPermissionFragment
import com.tbit.uqbike.fragment.HomeFrag
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.OrderModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.resqmodel.PayModel
import com.tbit.uqbike.utils.AliyunUploadFile
import com.tbit.uqbike.utils.ClipboardUtil
import com.tbit.uqbike.utils.HashTools
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.PhotoUtil.drawTextToBitmap
import com.tbit.uqbike.utils.PhotoUtil.fileToBitmap
import com.tbit.uqbike.utils.PhotoUtil.viewToBitmap
import com.tbit.uqbike.utils.UrlDecodeUtil
import com.tbit.uqbike.web.js.JsApi
import com.tbit.uqbike.web.js.util.webUtil
import com.tbit.uqbike.web.js.util.webUtil.REQUEST_CODE_CHOOSE
import com.tbit.uqbike.web.js.util.webUtil.Type_uploadCertification
import com.tbit.uqbike.web.js.util.webUtil.selectCamera
import com.tbit.uqbike.web.js.util.webUtil.selectImage
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.delay
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.act
import org.jetbrains.anko.startActivity
import top.zibin.luban.Luban
import top.zibin.luban.OnCompressListener
import wendu.dsbridge.CompletionHandler
import wendu.dsbridge.DWebView
import wendu.dsbridge.OnReturnValue
import java.io.File
import java.util.UUID
import android.widget.FrameLayout


class WebActionActivity : BaseActivity() {

    companion object {
        val TITLE: String = "title"
        val URL: String = "url"
        val IsHideHead: String = "isShowHead"
        val se_type: String = "se_type" //埋点 页面来源 1banner,2弹框，3启动页，4固定位，5其他
    }
    private val title: String by bindExtra(TITLE)
    private val urldata: String by bindExtra(URL)
    private val isHideHead: Boolean? by bindExtra(IsHideHead)
    private val seType: String? by bindExtra(se_type)
    private lateinit var binding: ActivityWebActionBinding
    private var url = ""
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWebActionBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 临时隐藏首页H5区
        val homeFrag = supportFragmentManager.fragments.find { it is HomeFrag }
        if (homeFrag != null) {
            val h5Area = homeFrag.view?.findViewById<FrameLayout>(R.id.layout_h5_area)
            h5Area?.visibility = View.GONE
        }
        
        url = intent.getStringExtra(URL) ?: ""
        if (urldata.contains("?")){
            url = urldata+"&localtime="+System.currentTimeMillis()
        }else{
            url = urldata+"?localtime="+System.currentTimeMillis()
        }
        if (!url.contains("locale=")){
            url = url+"&locale="+ FlavorConfig.Local.language
        }
        if (seType.isNullOrEmpty()){
            url = url+""
        }else {
            url = url + "&se_type=" + seType
        }
        MyLogUtil.Log("1111","==== 地址==="+url)
        if (isHideHead != null && isHideHead!!){
            binding.toolbar.visibility = View.GONE
            binding.lyTop.visibility = View.VISIBLE
        }else{
            binding.toolbar.visibility = View.VISIBLE
            binding.lyTop.visibility = View.GONE
            binding.toolbarTitle.text = title
            binding.imgBack.setOnClickListener{
                setResult(Activity.RESULT_OK)
                finish()
            }
        }

        initWebView()
        // Disable the loading dialog here as WebView has its own progress indication
        /*
        loadingDialogHelper.show {  }
        */

        if (url.contains("checkOrder")){
            ComModel.getAlConfig().subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取阿里云 配置 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), AliData::class.java)
                    Glob.ACCESS_ID = resultData.accesskey_id
                    Glob.ACCESS_KEY = resultData.accesskey_secret
                    Glob.ACCESS_TOKEN = resultData.security_token
                    Glob.ACCESS_BUCKET_NAME = resultData.bucket
                    Glob.ACCESS_ENDPOINT = resultData.endpoint
                    Glob.ACCESS_DOMAINNAME = resultData.host_cdn
                    Glob.IMG_PREFIX = resultData.prefix
                }
            ).toCancelable()
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            (supportFragmentManager.findFragmentById(R.id.read_media_permission_fragment) as ReadMediaPermissionFragment).requestPermission {}
        }else{
            (supportFragmentManager.findFragmentById(R.id.read_permission_fragment) as PermissionReadFragment).requestPermission {}
        }

//        var type = "imgpath"
//        var img = "5555"
//        var jsonString = "{" +
//                "\"type\": \""+type+"\"," +
//                "\"data\":{" +
//                "\"amount\": \""+img+"\"," +
//                "\"order_no\": \""+img+"\"" +
//                "}" +
//                "}"// JSON字符串
//        MyLogUtil.Log("1111","======"+jsonString)
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: BaseEventData) {}
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: CouponData) {
        if (event != null){
            if (binding.webView != null){
                var CouponidList = ArrayList<Int>()//选择的优惠券ID
                event.couponids.forEach {
                    CouponidList.add(it.toInt())
                }
                var data_str = Gson().toJson(CouponidList)
                //需要传值  拼json字符串 放入 数组里面
                binding.webView.callHandler("changeCoupons", arrayOf<Any>(data_str), object : OnReturnValue<String?> {
                    override fun onValue(retValue: String?) {
                        MyLogUtil.Log("1111","======call js=====")
                    }
                })
            }
        }
    }

    fun goPay(amount : Float){
        CoroutinesUtil.launchMain { loadingDialogHelper.show { false }}
        var latLng = OrderModel.getLocalData()
        var flag = ""
        OrderModel.sumitExchargeOrder(flag, amount,latLng!!.lat,latLng!!.lng)
            .subscribeBy(
                onNext = {
//                    loadingDialogHelper!!.dismiss()
                    MyLogUtil.Log("1111","===创建充值订单信息=="+ it.toString())
                    val resultDataPay = Gson().fromJson(it.toString(), OrderResultData::class.java)
                    PayModel.payExchangeByll(resultDataPay.order_no, Glob.PAY_LIANLIAN, PayFragment.PAYGOBACK,PayModel.type_pay_rental).subscribeBy(
                        onNext = {
                            MyLogUtil.Log("1111","===充值支付lianlian 信息=="+it.toString())
                            var resultData = Gson().fromJson(it.toString(), PayLianLianData::class.java)
                            startActivity<WebActivity>(WebActivity.TITLE to getString(R.string.s_order_pay), WebActivity.URL to resultData.link_url,
                                WebActivity.IS_PAY to true,WebActivity.se_type to "3")
                            orderNoData = resultDataPay.order_no
                        },
                        onError = {
                            CoroutinesUtil.launchMain {loadingDialogHelper.dismiss()}
                        }
                    ).toCancelable()
                },
                onError = {
                    CoroutinesUtil.launchMain {loadingDialogHelper.dismiss()}
                }
            ).toCancelable()
    }
    var orderNoData = ""
    var getPayStateTime = 2000L
    override fun onRestart() {
        super.onRestart()
        MyLogUtil.Log("1111","====onRestart======")
        if (!orderNoData.isNullOrEmpty()){
            getPayState(orderNoData)
        }
    }
    fun getPayState(orderNo : String){
        PayModel.getPayExchangeState(orderNo).subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===充值第三方支付结果 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), ExchangePayStateData::class.java)
                //第三方支付结果：1支付进行中，2支付失败，3支付成功，4支付取消
                when (resultData.third_paid_result) {
                    1 -> {
                        if(binding.webView != null){
                            binding.webView.postDelayed({ getPayState(orderNo) },getPayStateTime)
                        }
                    }
                    2 -> {
                        orderNoData = ""
                        CoroutinesUtil.launchMain {loadingDialogHelper.dismiss()}
                        MyToastUtil.toast(getString(R.string.str_pay_failed))
                    }
                    3 -> {
                        orderNoData = ""
                        CoroutinesUtil.launchMain {loadingDialogHelper.dismiss()}
                        calljs("")
                    }
                    4 -> {
                        orderNoData = ""
                        CoroutinesUtil.launchMain {loadingDialogHelper.dismiss()}
                        MyToastUtil.toast(getString(R.string.str_pay_failed))
                    }
                }
            },
            onError = {
                if(binding.webView != null){
                    binding.webView.postDelayed({
                        if(binding.webView!= null){ getPayState(orderNo) }
                    },getPayStateTime)
                }
            }
        ).toCancelable()
    }
    fun calljs(value : String){
        if (binding.webView != null){
            //需要传值  拼json字符串 放入 数组里面
            binding.webView.callHandler("jsAsyn", arrayOf<Any>("1"), object : OnReturnValue<String?> {
                override fun onValue(retValue: String?) {
                    MyLogUtil.Log("1111","======call js=====")
                }
            })
        }
    }
    fun calljsByImgPath(jsmeth : String,value : String){
        if (binding.webView != null){
            //需要传值  拼json字符串 放入 数组里面
            binding.webView.callHandler(jsmeth, arrayOf<Any>(value), object : OnReturnValue<String?> {
                override fun onValue(retValue: String?) {
                    MyLogUtil.Log("1111","======call js=====")
                }
            })
        }
    }
    @RequiresApi(Build.VERSION_CODES.M)
    private fun initWebView() {
        DWebView.setWebContentsDebuggingEnabled(true)
        var jsApi = JsApi()
        binding.webView.addJavascriptObject(jsApi, null)
        /* 设置javascript */
        val settings = binding.webView.settings
        settings.domStorageEnabled = true
        settings.javaScriptEnabled = true
        settings.allowUniversalAccessFromFileURLs = true
        settings.offscreenPreRaster = true
        // 不使用缓存
//        binding.webView.clearCache(true)
        binding.webView.settings.cacheMode = WebSettings.LOAD_DEFAULT
        binding.webView.setLayerType(View.LAYER_TYPE_HARDWARE, null)
        binding.webView.overScrollMode = View.OVER_SCROLL_NEVER
        settings?.setAllowUniversalAccessFromFileURLs(true);
        //允许混合模式，https当中加载http资源
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP) {
            binding.webView.settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        }
        binding.webView.loadUrl(url)
        jsApi.setOnItemClickListener(object : JsApi.OnClickListener {
            override fun onClickItem(msg: String?, handler : CompletionHandler<String>) {
                MyLogUtil.Log("1111", "js call me =====$msg")
                webUtil.goJsMethod(this@WebActionActivity,handler,msg!!)
                webUtil.onGoPayListener = {
                    goPay(it)
                }
            }
        })

        binding.webView.setWebViewClient(object : WebViewClient() {
            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                MyLogUtil.Log("1111","==========onPageStarted，url=$url")
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                MyLogUtil.Log("1111","==========onPageFinished，url=$url")
                super.onPageFinished(view, url)
            }
            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                return super.shouldOverrideUrlLoading(view, request)
            }
            override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                MyLogUtil.Log("1111","==========shouldOverrideUrlLoading，url=$url")
                val shouldOverrideUrl = !url.startsWith("https://") && !url.startsWith("http://")
                if(shouldOverrideUrl) {
                    try {
                        val intent: Intent
                        if (url.startsWith("intent://") && url.contains("scheme")) {
                            intent = Intent.parseUri(url, 0)
                        } else {
                            intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                        }
                        startActivity(intent)
                    } catch (e: Exception) {
                        MyToastUtil.toast(getString(R.string.s_noapp))
                        e.printStackTrace()
                    }
                }
                if (url.contains("line.me/ti/p")){
                    var intent = Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    startActivity(intent);
                }
                return shouldOverrideUrl
            }
            override fun onReceivedSslError(view: WebView, handler: SslErrorHandler, error: SslError) {
//                if (handler != null) {
//                    handler.proceed();//忽略证书的错误继续加载页面内容，不会变成空白页面
//                }
                super.onReceivedSslError(view, handler, error)
            }
            override fun shouldInterceptRequest(view: WebView?, request: WebResourceRequest?): WebResourceResponse? {
                // 获取原始请求头部信息
                return super.shouldInterceptRequest(view, request)
            }

            override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                super.onReceivedError(view, request, error)
                MyLogUtil.Log("1111","==========onReceivedError=====")
            }
        })
//
        binding.webView.setWebChromeClient(object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                Log.d("dd", "============网页加载进度=$newProgress")
                if (newProgress == 100) {
                    binding.webView.postDelayed({
                        if (binding.webView != null) loadingDialogHelper.dismiss()
                    },500)
                }
            }
        })
        handleSoftInput()
    }
    fun getjsResult(isSuc: Boolean): String{
        var result = ""
        if (isSuc){
            result = "{\"result\":\"1\"}"
        }else{
            result = "{\"result\":\"0\"}"
        }
        return result
    }
    private lateinit var layoutParams: ConstraintLayout.LayoutParams
    private var currentHeight = 0
    //处理webView软键盘显示
    private fun handleSoftInput() {
        layoutParams = binding.webView.layoutParams as ConstraintLayout.LayoutParams
        val decorView = window.decorView
        decorView.viewTreeObserver.addOnGlobalLayoutListener {
            val rect = Rect()
            decorView.getWindowVisibleDisplayFrame(rect)
            val keyboardMinHeight = dpToPx(100f)
            val screenHeight =
                if (hasNavigationBar(decorView)) resources.displayMetrics.heightPixels else decorView.height
            val rectHeight = if (hasNavigationBar(decorView)) rect.height() else rect.bottom
            val heightDiff = screenHeight - rectHeight
//            视图树变化高度大于100dp,认为键盘弹出
//            currentHeight防止界面频繁刷新,降低帧率,耗电
            if (currentHeight != heightDiff && heightDiff > keyboardMinHeight) {
                // 键盘弹出
                currentHeight = heightDiff
                layoutParams.bottomMargin = currentHeight
                binding.webView.requestLayout()
            } else if (currentHeight != heightDiff && heightDiff < keyboardMinHeight) {
                //键盘收起
                currentHeight = 0
                layoutParams.bottomMargin = currentHeight
                binding.webView.requestLayout()
            }
        }
    }
    private fun dpToPx(value: Float): Int {
        return (0.5f + value * resources.displayMetrics.density).toInt()
    }
    // 是否显示导航栏
    private fun hasNavigationBar(view: View): Boolean{
        val compact = ViewCompat.getRootWindowInsets(view.findViewById(android.R.id.content))
        compact?.apply {
            return isVisible(WindowInsetsCompat.Type.navigationBars()) && getInsets(
                WindowInsetsCompat.Type.navigationBars()
            ).bottom > 0
        }
        return false
    }
    override fun onBackPressed() {
        if (binding.webView.canGoBack()) {
            binding.webView.goBack()
        } else {
            setResult(Activity.RESULT_OK)
            super.onBackPressed()
        }
    }
    fun shareText(title: String, text: String) {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, text)
            putExtra(Intent.EXTRA_TITLE, title)
            type = "text/plain"
        }
//        val shareIntent = Intent.createChooser(sendIntent, "分享到")
        val shareIntent = Intent.createChooser(sendIntent, "")
        startActivity(shareIntent)
    }
    fun showPicSelDig(){
        var picDialog = PicSumitDialog(false)
        picDialog.onBtnListener = {
            when (it) {
                0 -> selectCamera()
                1 -> selectImage()
            }
        }
        lifecycleDialogHelper.show(picDialog)
    }
    protected open fun selectCamera() {
        PictureSelector.create(this)
            .openCamera(SelectMimeType.ofImage())// 全部.PictureMimeType.ofAll()、图片.ofImage()、视频.ofVideo()、音频.ofAudio()
            .forResultActivity(REQUEST_CODE_CHOOSE)//结果回调onActivityResult code
    }

    protected open fun selectImage() {
        PictureSelector.create(this)
            .openGallery(SelectMimeType.ofImage())
            .setMaxSelectNum(1)// 最大图片选择数量
            .setMinSelectNum(1)// 最小选择数量
            .setImageSpanCount(3)// 每行显示个数
            .setSelectionMode(SelectModeConfig.MULTIPLE)// 多选 or 单选
            .setImageEngine(GlideEngine.createGlideEngine())
            .isPreviewImage(false)// 是否可预览图片
            .isSelectZoomAnim(true)// 图片列表点击 缩放效果 默认true
            .isDisplayCamera(false)//是否显示相机入口
//            .setCompressEngine(ImageFileCompressEngine())// 是否压缩
            .forResult(REQUEST_CODE_CHOOSE)//结果回调onActivityResult code
    }
    fun addImage(paths: List<String>) {
        Log.e("1111","==========选择图片==="+paths.size)
        if (paths.size == 0) return
        var newPath = paths.get(0)
        sumitNum = 0
        isLoading = true
        Luban.with(this@WebActionActivity)
            .load(File(newPath))
            .ignoreBy(100)
            .setCompressListener(object : OnCompressListener {
                override fun onStart() {}
                override fun onSuccess(file: File) {
                    MyLogUtil.Log("1111","=====drawTextToBitmap===onSuccess======")
//                    bigImageUrl = file.absolutePath
                    sumitImg(file.absolutePath,"certificationUrl")
                    var newPathWate = drawTextToBitmap(this@WebActionActivity,file)
                    binding.webView.postDelayed({sumitImg(newPathWate!!,"certificationUrlwate")},900)
                }
                override fun onError(e: Throwable) {
                    MyLogUtil.Log("1111","=====drawTextToBitmap===onError======")
//                    bigImageUrl = newPath
                    sumitImg(newPath,"certificationUrl")
                }
            }).launch()
    }
    var sumitNum = 0
    var isLoading = true
    var listJsImg = ArrayList<JsImgData>()
    fun sumitImg(newPath : String,jsMeth : String){
        if (sumitNum == 0 && isLoading) {
            isLoading = false
            listJsImg.clear()
            CoroutinesUtil.launchMain {loadingDialogHelper.show {  }}
        }
        var photoPath = SelectPhotoAdapter.TYPE_PROFILE
        var sale = UUID.randomUUID().toString().substring(0,4);//
        var fileName = Glob.IMG_PREFIX+"/"+photoPath+"/"+ HashTools.digestBySHA1(""+System.currentTimeMillis() + sale)
        MyLogUtil.Log("1111","==== 图片上传 fileName=="+fileName)
        AliyunUploadFile(object : AliyunUploadFile.AliyunUploadView {
            override fun UploadSuccess(url: String?) {
                sumitNum++
                MyLogUtil.Log("1111","==== 图片上传成功 =="+url+"=="+sumitNum)
                listJsImg.add(JsImgData(jsMeth,url!!))
                if (sumitNum == 2) CoroutinesUtil.launchMain {
                    listJsImg.forEach {
//                        calljsByImgPath(jsMeth,url!!)
                        MyLogUtil.Log("1111","===  jsmethod  ===="+it.jsmethod+"-"+it.url)
                        calljsByImgPath(it.jsmethod,it.url)
                    }
                    loadingDialogHelper.dismiss()
                }
            }
            override fun Uploaddefeated(error: String?) {
                sumitNum++
                if (sumitNum == 2) CoroutinesUtil.launchMain {loadingDialogHelper.dismiss()}
                runOnUiThread {  MyToastUtil.toast(ResUtil.getString(R.string.s_sumitimg_fail))}
                MyLogUtil.Log("1111","==== 图片上传失败 =="+error)
            }
        }).UploadFile(
            App.context, Glob.ACCESS_ID, Glob.ACCESS_KEY, Glob.ACCESS_TOKEN, Glob.ACCESS_ENDPOINT, Glob.ACCESS_BUCKET_NAME,
            fileName, newPath)
    }

    /**
     * 添加水印并保存到手机新目录
     */
//    fun drawTextToBitmap(gContext: Context, gBitmap: Bitmap, gText: String): String? {
    @SuppressLint("MissingInflatedId", "NewApi")
    fun drawTextToBitmap(gContext: Context, file: File): String? {
        val resources = gContext.resources
        val scale = resources.displayMetrics.density
//        val bitmap = gBitmap.copy(Bitmap.Config.ARGB_8888, true)
        val bitmap = fileToBitmap(file).copy(Bitmap.Config.ARGB_8888, true)
        val srcWidth: Int = bitmap.getWidth()
        val srcHeight: Int = bitmap.getHeight()

        val rootView = LinearLayout(gContext)
//        val density = ContextUtil.getContext().resources.displayMetrics.density//防止不同屏幕密度下UI效果不同做的一个缩放
//        MyLogUtil.Log("1111","======density========="+density)
        val view = LayoutInflater.from(gContext).inflate(R.layout.layout_watemark_zj,rootView)
        val watermark = viewToBitmap(view)

        val watermarkWidth: Int = watermark.getWidth()
        val watermarkHeight: Int = watermark.getHeight()

        val bm = Bitmap.createBitmap(srcWidth, srcHeight, bitmap.config)
        val canvas = Canvas(bm)
        // 将原图绘制到新的Bitmap上
        canvas.drawBitmap(bitmap, 0f, 0f, null)
        // 将水印绘制到右下角
        canvas.drawBitmap(watermark, (srcWidth - watermarkWidth).toFloat()/2, (srcHeight - watermarkHeight).toFloat()/2, null)
//        canvas.drawBitmap(watermark, 40f, (srcHeight - watermarkHeight).toFloat()-40f, null)
        return FileUtil.saveImageToGallery(this@WebActionActivity,bm)
    }
    fun viewToBitmap(view: View): Bitmap {
        val measureSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        view.measure(measureSpec, measureSpec)
//        val density = ContextUtil.getContext().resources.displayMetrics.density//防止不同屏幕密度下UI效果不同做的一个缩放
        //由于是生成的，未进行测量，所有需要进行measure操作
//        var measuredWidth = (view.measuredWidth * density).toInt()
//        var measuredHeight = (view.measuredHeight * density).toInt()
        var measuredWidth = (view.measuredWidth).toInt()
        var measuredHeight = (view.measuredHeight).toInt()
        view.layout(0, 0, measuredWidth, measuredHeight)
        // 创建一个和给定View相同大小的空Bitmap
//        val bitmap = Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
        val bitmap = Bitmap.createBitmap(measuredWidth, measuredHeight, Bitmap.Config.ARGB_8888)
        // 使用Canvas来将View的内容绘制到Bitmap上
        val canvas = Canvas(bitmap)
        // 确保View被重绘
        view.draw(canvas)
        return bitmap
    }
    fun fileToBitmap(file: File): Bitmap {
        val options = BitmapFactory.Options()
        options.inPreferredConfig = Bitmap.Config.ARGB_8888
        return BitmapFactory.decodeFile(file.absolutePath, options)
    }
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE_CHOOSE) {
            MyLogUtil.Log("1111","requestCode="+requestCode+",resultCode="+resultCode)
            val selectList = PictureSelector.obtainSelectorList(data)
            MyLogUtil.Log("1111","selectList="+selectList.size)
            selectList.map {
                val localMedia = it
                if (localMedia.isCompressed) localMedia.compressPath else localMedia.realPath
            }.let { addImage(it) }
        }
    }
    override fun onDestroy() {
        super.onDestroy()
        LanguageUtil().settingLanguage(ContextUtil.getContext())
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }
}