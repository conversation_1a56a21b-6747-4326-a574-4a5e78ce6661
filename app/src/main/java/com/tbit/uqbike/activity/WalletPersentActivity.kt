package com.tbit.uqbike.activity

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.WalletPersentAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityWalletPersentBinding
import com.tbit.uqbike.entity.WalletPersentData
import com.tbit.uqbike.entity.WalletPersentDataItem
import com.tbit.uqbike.resqmodel.WalletModel
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip

class WalletPersentActivity : BaseActivity() {
    private var lastId = 0
    private val pageSize = Glob.pageNum
    private var lastLoaded = false
    private val adapter = WalletPersentAdapter()
    private val listdata = mutableListOf<WalletPersentDataItem>()
    private val loadMoreView by lazy { View(this).apply { layoutParams = ViewGroup.LayoutParams(1, 1) } }
    private lateinit var binding: ActivityWalletPersentBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWalletPersentBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_wallet_persent)

        binding.layoutToolbar.toolbarTitle.text = getString(R.string.s_giftbalance)
        binding.layoutToolbar.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.layoutToolbar.toolbar.setNavigationOnClickListener { finish() }

        binding.rcv.layoutManager = LinearLayoutManager(this)
        binding.rcv.adapter = loadMoreWrapper(adapter)
        val spacing = dip(10)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        adapter.onGoPersentInfoListener = {pos ->
            startActivity(PersentInfoActivity.createIntent(this, listdata.get(pos).name,listdata.get(pos).area_id))
        }

        getListData()
    }
    private fun loadMoreWrapper(adapter: RecyclerView.Adapter<out RecyclerView.ViewHolder>)
            : LoadMoreWrapper<out RecyclerView.ViewHolder> {
        val loadMoreAdapter = LoadMoreWrapper(adapter)
        loadMoreAdapter.setOnLoadMoreListener{ onLoadMore() }
        return loadMoreAdapter
    }
    private fun onLoadMore() {
        if (!lastLoaded)
            getListData(false)
    }
    private fun getListData(showLoading: Boolean = true) {
        WalletModel.getPersentBalance(lastId,pageSize)
            .subscribeBy(
                onNext = {
                    loadingDialogHelper!!.dismiss()
                    MyLogUtil.Log("1111","======= 获取赠送余额 信息 ==="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), WalletPersentData::class.java)
                    lastLoaded = true
                    if(resultData.size > 0){
                        lastId = resultData.last().area_id
                    }
                    (binding.rcv.adapter as? LoadMoreWrapper<*>)?.setLoadMoreView(if (lastLoaded) null else loadMoreView)
                    listdata.addAll(resultData)
                    adapter.source = listdata
                    adapter.notifyDataSetChanged()
                    if (adapter.source.isEmpty()) binding.capaLayout.toEmpty() else binding.capaLayout.toContent()
                },
                onError = {
                    loadingDialogHelper!!.dismiss()
                }
            ).toCancelable()
        if (showLoading) {
            loadingDialogHelper.show { }
        }
    }
}