package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.App
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.InviteAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityRideCardPayResultBinding
import com.tbit.uqbike.entity.Coupon
import com.tbit.uqbike.entity.GiveCoupon
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.dip
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.startActivity

class RideCardPayResultActivity : BaseActivity() {
    companion object {
        val TYPE_SUC = 1
        val TYPE_FAIL = 0
        private const val EXTRA_TYPE = "EXTRA_TYPE"
        private const val EXTRA_ORDERNO = "EXTRA_ORDERNO"
        fun createIntent(context: Context, type : Int,orderNo : String): Intent {
            return context.intentFor<RideCardPayResultActivity>(EXTRA_TYPE to type,EXTRA_ORDERNO to orderNo)
        }
    }
    private val type: Int by bindExtra(EXTRA_TYPE)
    private val orderNo: String by bindExtra(EXTRA_ORDERNO)
    private lateinit var binding: ActivityRideCardPayResultBinding
    private val adapter by lazy { InviteAdapter() }
    var listData = ArrayList<Coupon>()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRideCardPayResultBinding.inflate(layoutInflater)
        setContentView(binding.root)
        EventBus.getDefault().register(this);
//        setContentView(R.layout.activity_ride_card_pay_result)

        setSupportActionBar(binding.appbarLayout.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        binding.appbarLayout.toolbarTitle.text = getString(R.string.s_payresult)
        binding.appbarLayout.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.appbarLayout.toolbar.setNavigationOnClickListener { onBackPressed() }

        if (type == TYPE_SUC){
            ImageLoad.loadimg(App.context.resources.getDrawable(R.drawable.ic_pay_suc),binding.imgPaystate)
            binding.tvPaystate.text = getString(R.string.s_buy_suc)
            binding.btnPay.text = getString(R.string.s_go_ride)

            ComModel.getOrderGifeCoupon(orderNo).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===获取当前订单 发放的优惠券=="+ it.toString())
                    val resultData = Gson().fromJson(it.toString(), GiveCoupon::class.java)
                    if (resultData != null && resultData.coupon.size > 0){

                        binding.rlCoupon.visibility = View.VISIBLE
                        binding.rcv.layoutManager = LinearLayoutManager(ContextUtil.getContext())
                        val spacing = dip(5)
                        val spanCount = 1
                        binding.rcv.addItemDecoration(
                            GridSpacingItemDecoration(spanCount, spacing, false)
                        )
                        binding.rcv.adapter = adapter

                        binding.tvNewuserdigHint.text = getString(R.string.s_get) + getString(R.string.s_get_invite,resultData.number.toString())
                        listData.addAll(resultData.coupon)
                        adapter.source = listData
                        adapter.notifyDataSetChanged()
                    }
                },
                onError = {
                    loadingDialogHelper.dismiss()
                }
            ).toCancelable()

        }else{
            ImageLoad.loadimg(App.context.resources.getDrawable(R.drawable.ic_pay_fail),binding.imgPaystate)
            binding.tvPaystate.text = getString(R.string.s_buy_fail)
            binding.btnPay.text = getString(R.string.back)
            binding.rlCoupon.visibility = View.GONE
        }
        binding.btnPay.setOnClickListener {
            if (type == TYPE_SUC){
                startActivity<MainActivity>()
//                EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_HOME,""))
//                finish()
            }else{
                finish()
            }
        }
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: BaseEventData) {}
    override fun onDestroy() {
        super.onDestroy()
        LanguageUtil().settingLanguage(ContextUtil.getContext())
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }
}