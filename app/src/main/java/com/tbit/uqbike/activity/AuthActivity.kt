package com.tbit.uqbike.activity

import android.os.Bundle
import com.google.gson.Gson
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.App
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityAuthBinding
import com.tbit.uqbike.dialog.PllicyDialog
import com.tbit.uqbike.entity.UidData
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.utils.DeviceIdUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.UrlDecodeUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.startActivity

class AuthActivity : BaseActivity() {
    private lateinit var binding: ActivityAuthBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAuthBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.
//        )
        binding.tvAuthService.setOnClickListener {showServiceTerms() }

        binding.tvAuthPrivacy.setOnClickListener { showPrivacyPolicy() }
        binding.btnBomN.setOnClickListener {
            PllicyDialog.Builder(this@AuthActivity)
                .setTitle(getString(R.string.s_dialog_plicytitle))
                .setContent(getString(R.string.s_dialog_plicycont))
                .setLeftText(getString(R.string.s_n_agree_w))
                .setRightText(getString(R.string.s_agree_w))
                .setCanceledOnOutside(true)
                .setClickListen(object : PllicyDialog.TwoSelDialog {
                    override fun leftClick() {
                        goMain(false)
                    }
                    override fun rightClick() {
                        goMain(true)
                    }
                    override fun privacyPolicyClick() { showPrivacyPolicy() }
                    override fun serviceTerms() { showServiceTerms() }
                }).build().show()
        }
        binding.btnBomY.setOnClickListener {
            goMain(true)
        }
    }
    fun goMain(isAuth : Boolean){
        if(isAuth){
            App.initThird()
            ComModel.getUserUid(DeviceIdUtil.createDeviceId(this@AuthActivity)).subscribeBy(
                onNext = {
//                    MyLogUtil.Log("1111","===uuid 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), UidData::class.java)
                    if (resultData != null){
                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.DEVICE_ID, resultData.uuid);
                    }
                }
            ).toCancelable()
            SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_AUTH,"1")
            getAdDeposit()
        }
        if (Glob.isGoogleServiceAvailable && Glob.isGoogleNetAvailable == null) {
            startActivity<SplashLoadingActivity>()
        } else {
            startActivity(MainActivity.createIntent(this))
        }
        finish()
    }
    private fun getAdDeposit() {
        LocationModel.requestLocation()
            .subscribeBy(
                onNext = {
//                            DepositModel.getDeposit(it.latitude, it.longitude).subscribeBy(onError = {})
                },
                onError = {}
            )
    }
    private fun showPrivacyPolicy() {
        loadingDialogHelper.show {  }
        PageModel.getPageUrlUAuth(PageModel.privacy_policy).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                startActivity<WebActivity>(
                    WebActivity.TITLE to getString(R.string.str_privacy_policy).replace("《","").replace("》",""),
                    WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
            },
            onError = {
                val errMsg = ErrHandler.getErrMsg(it)
                MyLogUtil.Log("1111","===获取说明页链接 出错=="+errMsg)
                loadingDialogHelper.dismiss()}
        ).toCancelable()
    }

    private fun showServiceTerms() {
        loadingDialogHelper.show {  }
        PageModel.getPageUrlUAuth(PageModel.user_policy).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                startActivity<WebActivity>(
                    WebActivity.TITLE to getString(R.string.str_service_terms).replace("《","").replace("》",""),
                    WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
            },
            onError = {loadingDialogHelper.dismiss()}
        ).toCancelable()
    }
}