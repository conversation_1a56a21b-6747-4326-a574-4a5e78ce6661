package com.tbit.uqbike.activity

import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import com.baidu.ar.it
import com.google.gson.Gson
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.stripe.android.model.ConsumerPaymentDetails.Card.Companion.type
import com.tbit.maintanenceplus.utils.TimeFormatUtil
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.addToComposite
import com.tbit.maintenance.utils.toCancelable
import com.tbit.preview.GlideEngine
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.SelectPhotoAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityPersonBinding
import com.tbit.uqbike.dialog.BirthDayDialog
import com.tbit.uqbike.dialog.SelCountryCodeWindow
import com.tbit.uqbike.dialog.personEditDialog
import com.tbit.uqbike.entity.AliData
import com.tbit.uqbike.entity.CountryData
import com.tbit.uqbike.entity.SelIdentityData
import com.tbit.uqbike.entity.SexListData
import com.tbit.uqbike.entity.UserEntity
import com.tbit.uqbike.entity.personItemSelData
import com.tbit.uqbike.mvp.model.CountryCodeModel
import com.tbit.uqbike.mvp.model.UserModel
import com.tbit.uqbike.qrcode.CommonUtils
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.UserModelNew
import com.tbit.uqbike.utils.AliyunUploadFile
import com.tbit.uqbike.utils.HashTools
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.web.js.util.webUtil
import io.reactivex.rxkotlin.subscribeBy
import org.json.JSONObject
import top.zibin.luban.Luban
import top.zibin.luban.OnCompressListener
import java.io.File
import java.util.UUID

class PersonActivity : BaseActivity() {
    private lateinit var binding: ActivityPersonBinding
    private var birthData_Str = ""
    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPersonBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_person)

        binding.toolbars.toolbarTitle.text = getString(R.string.s_person_info)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { finish() }

        ComModel.getAlConfig().subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取阿里云 配置 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), AliData::class.java)
                Glob.ACCESS_ID = resultData.accesskey_id
                Glob.ACCESS_KEY = resultData.accesskey_secret
                Glob.ACCESS_TOKEN = resultData.security_token
                Glob.ACCESS_BUCKET_NAME = resultData.bucket
                Glob.ACCESS_ENDPOINT = resultData.endpoint
                Glob.ACCESS_DOMAINNAME = resultData.host_cdn
                Glob.IMG_PREFIX = resultData.prefix
            }
        ).toCancelable()

        binding.lyPersonHeatimg.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("headgear_click",properties)
            PictureSelector.create(this@PersonActivity)
                .openGallery(SelectMimeType.ofImage())
                .setMaxSelectNum(1)// 最大图片选择数量
                .setMinSelectNum(1)// 最小选择数量
                .setImageSpanCount(3)// 每行显示个数
                .setSelectionMode(SelectModeConfig.MULTIPLE)// 多选 or 单选
                .setImageEngine(GlideEngine.createGlideEngine())
                .isPreviewImage(false)// 是否可预览图片
                .isSelectZoomAnim(true)// 图片列表点击 缩放效果 默认true
                .isDisplayCamera(true)//是否显示相机入口
//            .setCompressEngine(ImageFileCompressEngine())// 是否压缩
                .forResult(webUtil.REQUEST_CODE_CHOOSE)//结果回调onActivityResult code
        }

        binding.lyPersonBirthday.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("birth_click",properties)
            var birthDialog : BirthDayDialog? = null
            if (birthData_Str.isNullOrEmpty() || !birthData_Str.contains("-")){
                var timeData = TimeFormatUtil.transToStringBysqData(System.currentTimeMillis()/1000)
                MyLogUtil.Log("1111","=========日期1======="+timeData)
                var birthDatas = timeData.split("-")
                var years = birthDatas[0]
                var months = birthDatas[1]
                var days = birthDatas[2]
                birthDialog = BirthDayDialog(this@PersonActivity,years.toInt()-1900,months.toInt()-1,days.toInt()-1)
            }else{
                var birthDatas = birthData_Str.split("-")
                var years = birthDatas[0]
                var months = birthDatas[1]
                var days = birthDatas[2]
                MyLogUtil.Log("1111","=========日期======="+years+","+months+","+days)
                birthDialog = BirthDayDialog(this@PersonActivity,years.toInt()-1900,months.toInt()-1,days.toInt()-1)
            }
            birthDialog.onItemListener = {
                loadingDialogHelper.show {  }
                UserModelNew.putPersonInfo("",0,"",it,0)
                    .subscribeBy(
                        onNext = {
                            loadingDialogHelper.dismiss()
                            getUser()
                        },
                        onError = {loadingDialogHelper.dismiss()}
                    ).toCancelable()
            }
            lifecycleDialogHelper.show(birthDialog)
        }

        binding.lyPersonSex.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("gender_click",properties)
            UserModelNew.getSexList().subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取性别列表 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), SexListData::class.java)
                    if (resultData != null && resultData.size > 0){
                        var selData = ArrayList<personItemSelData>()
                        resultData.forEach {
                            var itemData = personItemSelData(it.code,it.value)
                            selData.add(itemData)
                        }
                        var personDialog = personEditDialog(this@PersonActivity,selData)
                        personDialog.onItemListener = {
                            personDialog.dismissAllowingStateLoss()
//                            MyToastUtil.toast(it.code.toString()+","+it.value)
                            loadingDialogHelper.show {  }
                            UserModelNew.putPersonInfo("",it.code,"","",0)
                                .subscribeBy(
                                    onNext = {
                                        loadingDialogHelper.dismiss()
                                        getUser()
                                    },
                                    onError = {loadingDialogHelper.dismiss()}
                                ).toCancelable()
                        }
                        lifecycleDialogHelper.show(personDialog)
                    }
                }
            ).toCancelable()
        }

        binding.lyPersonSf.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("identity_click",properties)
            UserModelNew.getIdentityList().subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取身份列表 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), SelIdentityData::class.java)
                    if (resultData != null && resultData.size > 0){
                        var selData = ArrayList<personItemSelData>()
                        resultData.forEach {
                            var itemData = personItemSelData(it.code,it.value)
                            selData.add(itemData)
                        }
                        var personDialog = personEditDialog(this@PersonActivity,selData)
                        personDialog.onItemListener = {
                            personDialog.dismissAllowingStateLoss()
//                            MyToastUtil.toast(it.code.toString()+","+it.value)
                            loadingDialogHelper.show {  }
                            UserModelNew.putPersonInfo("",0,"","",it.code)
                                .subscribeBy(
                                    onNext = {
                                        loadingDialogHelper.dismiss()
                                        getUser()
                                    },
                                    onError = {loadingDialogHelper.dismiss()}
                                ).toCancelable()
                        }
                        lifecycleDialogHelper.show(personDialog)
                    }
                }
            ).toCancelable()
        }

        binding.lyPersonCountry.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("country_click",properties)
            showCountryCodeWindow()
        }

        countryCodeWindow.setOnItemClickListener {
//            MyToastUtil.toast(it.country_code+it.title)
            countryCodeWindow.dismiss()
            loadingDialogHelper.show {  }
            UserModelNew.putPersonInfo("",0,it.country_code,"",0)
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper.dismiss()
                        getUser()
                    },
                    onError = {loadingDialogHelper.dismiss()}
                ).toCancelable()
        }
        getUser()
    }
    fun getUser(){
        loadingDialogHelper.show {  }
        UserModel.getUser()
            .subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    val resultData: UserEntity = Gson().fromJson(it.toString(), UserEntity::class.java)
                    if (resultData != null){
                        if (!resultData.avatar.isNullOrEmpty()) ImageLoad.loadimg(resultData.avatar,binding.imgPerson)

                        binding.tvPersonAccount.text = if (!resultData.phone.toString().isNullOrEmpty() && resultData.phone != 0L) resultData.phone.toString()
                        else resultData.user_name

                        birthData_Str = resultData.birthdate
                        binding.tvPersonBirthdate.text = resultData.birthdate
                        binding.tvPersonSex.text = resultData.gender_text
                        binding.tvPersonCountry.text = resultData.country
                        binding.tvPersonSf.text = resultData.identity_text
                        binding.tvPersonThird.text = resultData.auth_third_party
                    }
                },
                onError = {loadingDialogHelper.dismiss()}
            ).toCancelable()
    }
    private val countryCodeWindow by lazy {
        SelCountryCodeWindow(this, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }

    private inline fun showCountryCodeWindow() {
        loadingDialogHelper.show {  }
        CountryCodeModel.getAllCountryCodeInfoFromNet()
            .subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    val resultData: CountryData = Gson().fromJson(it.toString(), CountryData::class.java)
                    countryCodeWindow.setData(resultData)
                    countryCodeWindow.width = CommonUtils.getScreenWidth(this@PersonActivity)
                    countryCodeWindow.height = CommonUtils.getScreenHeight(this@PersonActivity)
                    countryCodeWindow.showAtLocation(binding.lyPersonCountry, Gravity.BOTTOM,0,0)
                },
                onError = {
                    loadingDialogHelper.dismiss()
                }
            ).toCancelable()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == webUtil.REQUEST_CODE_CHOOSE && resultCode == Activity.RESULT_OK) {
            val selectList = PictureSelector.obtainSelectorList(data)
            MyLogUtil.Log("1111","selectList="+selectList.size)
            selectList.map {
                val localMedia = it
                if (localMedia.isCompressed) localMedia.compressPath else localMedia.realPath
            }.let { addImage(it) }
        }
    }
    fun addImage(paths: List<String>) {
        Log.e("1111","==========选择图片==="+paths.size)
        if (paths.size == 0) return
        var newPath = paths.get(0)
        Luban.with(this@PersonActivity)
            .load(File(newPath))
            .ignoreBy(100)
            .setCompressListener(object : OnCompressListener {
                override fun onStart() {}
                override fun onSuccess(file: File) {
                    MyLogUtil.Log("1111","=====drawTextToBitmap===onSuccess======")
                    sumitImg(file.absolutePath)
                }
                override fun onError(e: Throwable) {
                    MyLogUtil.Log("1111","=====drawTextToBitmap===onError======")
                    sumitImg(newPath)
                }
            }).launch()
    }

    fun sumitImg(newPath : String){
        runOnUiThread {loadingDialogHelper.show {}}
        var photoPath = SelectPhotoAdapter.TYPE_USER
        var sale = UUID.randomUUID().toString().substring(0,4);//
        var fileName = Glob.IMG_PREFIX+"/"+photoPath+"/"+ HashTools.digestBySHA1(""+System.currentTimeMillis() + sale)
        MyLogUtil.Log("1111","==== 图片上传 fileName=="+fileName)
        AliyunUploadFile(object : AliyunUploadFile.AliyunUploadView {
            override fun UploadSuccess(url: String?) {
                MyLogUtil.Log("1111","==== 图片上传成功 =="+url+"==")
                runOnUiThread {
                    UserModelNew.putPersonInfo(url!!,0, "","",0)
                        .subscribeBy(
                            onNext = {
                                loadingDialogHelper.dismiss()
                                getUser()
                            },
                            onError = {loadingDialogHelper.dismiss()}
                        ).toCancelable()
                }
            }
            override fun Uploaddefeated(error: String?) {
                loadingDialogHelper.dismiss()
                runOnUiThread {  MyToastUtil.toast(ResUtil.getString(R.string.s_sumitimg_fail))}
                MyLogUtil.Log("1111","==== 图片上传失败 =="+error)
            }
        }).UploadFile(
            ContextUtil.getContext(), Glob.ACCESS_ID, Glob.ACCESS_KEY, Glob.ACCESS_TOKEN, Glob.ACCESS_ENDPOINT, Glob.ACCESS_BUCKET_NAME,
            fileName, newPath)
    }
}