package com.tbit.uqbike.activity

import android.os.Bundle
import com.lsxiao.apollo.core.annotations.Receive
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.fullScreen
import com.tbit.uqbike.Glob
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivitySplashLoadingBinding
import com.tbit.uqbike.utils.MyLogUtil

class SplashLoadingActivity : BaseActivity() {
    var isSave : Boolean? = null
    private lateinit var binding: ActivitySplashLoadingBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySplashLoadingBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_splash_loading)
        fullScreen()
        isSave = true
        binding.splLoading.postDelayed({
            if (isSave != null){
                Glob.isGoogleNetAvailable = false
                startActivity(MainActivity.createIntent(this))
                finish()
                MyLogUtil.Log("1111","=================SplashLoadingActivity========")
            }
        },5 * 1000)
    }

    override fun onDestroy() {
        super.onDestroy()
        isSave = null
    }
    @Receive(Constant.Event.CHECK_GOOGLE_NET_RESULT)
    fun checkGoogleNetResult() {
        startActivity(MainActivity.createIntent(this))
        finish()
    }
}