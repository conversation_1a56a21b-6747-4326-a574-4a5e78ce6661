package com.tbit.uqbike.activity

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.RidingRecordAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityRidingRecordBinding
import com.tbit.uqbike.entity.RidingRecordDataItem
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.mvp.constract.RidingRecordContract
import com.tbit.uqbike.mvp.presenter.RidingRecordPresenter
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.NetUtils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.dip
import org.jetbrains.anko.toast

class RidingRecordActivity: BaseActivity(), RidingRecordContract.View {

    private val presenter = RidingRecordPresenter(this)
    private val adapter by lazy { RidingRecordAdapter() }
    private var lastLoaded = false
    private val loadMoreView by lazy { View(this).apply { layoutParams = ViewGroup.LayoutParams(1, 1) } }


    private lateinit var binding: ActivityRidingRecordBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this);
        binding = ActivityRidingRecordBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_riding_record)

        binding.toolbars.toolbarTitle.text = getString(R.string.ride_record)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { finish() }

        lifecycle.addObserver(presenter)
        val spacing = dip(10)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        binding.rcv.layoutManager = LinearLayoutManager(this)
        binding.rcv.adapter = loadMoreWrapper(adapter)

        adapter.setOnItemClickListener {
            if(it.type == Constant.OrderType.RENTAL_Y){
                startActivity(RecordDetailActivity.createIntent(this, it.order_no,false,true ))
            }else{
                if(it.status == 1){
//                    EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_FINSH,""));
//                    EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_RIDINGINFO,""));
//                    finish()
                    startActivity(RecordDetailActivity.createIntent(this, it.order_no,false, false))
                }else{
                    startActivity(RecordDetailActivity.createIntent(this, it.order_no,false, false))
                }
            }
        }

    }

    override fun onResume() {
        super.onResume()
        if (adapter.source.size == 0){
            getRidingRecord()
        }
    }

    private fun loadMoreWrapper(adapter: RecyclerView.Adapter<out RecyclerView.ViewHolder>)
            : LoadMoreWrapper<out RecyclerView.ViewHolder> {
        val loadMoreAdapter = LoadMoreWrapper(adapter)
        loadMoreAdapter.setOnLoadMoreListener{ onLoadMore() }
        return loadMoreAdapter
    }

    private fun onLoadMore() {
        if (!lastLoaded)
            getRidingRecord(false)
    }

    private fun getRidingRecord(showLoading: Boolean = true) {
        val cancellable = presenter.getRidingRecord()
        if (showLoading) {
            loadingDialogHelper.show { cancellable.cancel() }
        }
    }

    override fun onGetRidingRecordSuccess(ridingRecords: List<RidingRecordDataItem>, lastLoaded: Boolean) {
        loadingDialogHelper.dismiss()
        this.lastLoaded = lastLoaded
        (binding.rcv.adapter as? LoadMoreWrapper<*>)?.setLoadMoreView(if (lastLoaded) null else loadMoreView)
        adapter.source = ridingRecords
        adapter.notifyDataSetChanged()
//        if (lastLoaded) toast(R.string.no_more_tips)
        if (ridingRecords.isEmpty()) binding.capaLayout.toEmpty() else binding.capaLayout.toContent()
    }
    fun setErrorView(isNetError : Boolean){
//        MyLogUtil.Log("1111","======isNetError======"+isNetError)
        if(isNetError){
            if (NetUtils.isConnected(ContextUtil.getContext())){
                binding.capaLayout.toEmpty()
                return
            }
        }
        if (isNetError){
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_net).visibility = View.VISIBLE
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_local).visibility = View.GONE
        }else{
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_net).visibility = View.GONE
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_local).visibility = View.VISIBLE
        }
        binding.capaLayout.toError()
        binding.capaLayout.findViewById<RoundTextView>(R.id.tv_err_set_net).setOnClickListener{
            val intent = Intent(Settings.ACTION_DATA_ROAMING_SETTINGS)
            startActivity(intent)
        }
        binding.capaLayout.findViewById<RoundTextView>(R.id.tv_err_set_local).setOnClickListener{
            val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
            startActivityForResult(intent, MainActivity.REQUEST_OPEN_GPS)
        }
    }
    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
        setErrorView(true)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: BaseEventData) {
        if (event.code == EventUtil.EVENT_HOME) finish()
        if(event.code == EventUtil.EVENT_RIDINGRECORD) {
            presenter.lastId = 0
            presenter.data.clear()
            getRidingRecord(false)
        }
    }
    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }
}