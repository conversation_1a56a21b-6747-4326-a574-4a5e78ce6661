package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.ViewGroup
import android.view.WindowManager
import com.google.gson.Gson
import com.tbit.maintanenceplus.utils.InputMethodUtils
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.custom.RefundWindow
import com.tbit.uqbike.databinding.ActivityRefundPersonInfoBinding
import com.tbit.uqbike.entity.getBandData
import com.tbit.uqbike.qrcode.CommonUtils
import com.tbit.uqbike.resqmodel.RefundModel
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.intentFor

class RefundPersonInfoActivity : BaseActivity(), TextWatcher {
    private val refundWindow by lazy {
        RefundWindow(this, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT)
    }
    companion object {
        private const val EXTRA_TYPE = "EXTRA_TYPE"
        private const val EXTRA_DATA_NO = "EXTRA_DATA_NO"
        fun createIntent(context: Context,typeRefund : Int,ride_card_no : String): Intent {
            return context.intentFor<RefundPersonInfoActivity>(EXTRA_TYPE to typeRefund,EXTRA_DATA_NO to ride_card_no)
        }
    }
    private val typeRefund : Int by bindExtra(EXTRA_TYPE)//钱包退款 ， 骑行卡退款
    private val ride_card_no : String by bindExtra(EXTRA_DATA_NO)//订单编号
    private lateinit var binding: ActivityRefundPersonInfoBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRefundPersonInfoBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_refund_person_info)
        binding.toolbars.toolbarTitle.text = getString(R.string.s_refund_person_title)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener {
            finish()
        }
        RefundModel.getbank().subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取开户行信息 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), getBandData::class.java)
                if (resultData != null && resultData.size > 0){
                    refundWindow.setData(resultData)
                }
            },
            onError = {}
        ).toCancelable()
        if(!SpUtil.getInstance().find(Constant.SpKey.SP_REFUND_DEPOSIT).isNullOrEmpty()){
            binding.textCountryCode.text = SpUtil.getInstance().find(Constant.SpKey.SP_REFUND_DEPOSIT)
        }
        if(!SpUtil.getInstance().find(Constant.SpKey.SP_REFUND_NAME).isNullOrEmpty()){
            binding.etRefundName.setText(SpUtil.getInstance().find(Constant.SpKey.SP_REFUND_NAME))
        }
        if(!SpUtil.getInstance().find(Constant.SpKey.SP_REFUND_ACCOUNT).isNullOrEmpty()){
            binding.etRefundAccount.setText(SpUtil.getInstance().find(Constant.SpKey.SP_REFUND_ACCOUNT))
        }
        binding.etRefundName.addTextChangedListener(this)
        binding.etRefundAccount.addTextChangedListener(this)
        refundWindow.setOnItemClickListener {
            binding.textCountryCode.text = it
            refundWindow.dismiss()
            setBtnState()
        }
        binding.llCountryCode.setOnClickListener { showCountryCodeWindow() }
        binding.btnSubmit.setOnClickListener {
            var deposit = binding.textCountryCode.text.toString()
            var name = binding.etRefundName.text.toString()
            var account = binding.etRefundAccount.text.toString()
//            if (name.containsEmoji() || name.contains(' ')){
//                MyToastUtil.toast(getString(R.string.s_refunt_input_err))
//                return@setOnClickListener
//            }
//            if (account.containsEmoji() || account.contains(' ')){
//                MyToastUtil.toast(getString(R.string.s_refunt_input_err))
//                return@setOnClickListener
//            }
            if (deposit.length > 0 && name.length > 0 && account.length > 0){
                startActivity(RefunReasonActivity.createIntent(this@RefundPersonInfoActivity, typeRefund,
                    ride_card_no,account,name,deposit))
                SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_REFUND_DEPOSIT,deposit)
                SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_REFUND_NAME,name)
                SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_REFUND_ACCOUNT,account)
                finish()
            }
        }
        setBtnState()
    }

    fun String.containsEmoji(): Boolean {
//        return this.any { char ->
//            char.isEmoji()
//        }
        return false
    }

    fun Char.isEmoji(): Boolean {
        return this.toString().matches(Regex("[^\\u0020-\\u007E]")) // 不在基本多语言面内的Unicode字符
    }
    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
    override fun afterTextChanged(s: Editable?) {setBtnState()}
    fun setBtnState(){
        if (binding.textCountryCode.text.toString().length > 0 && binding.etRefundName.text.toString().length > 0
            && binding.etRefundAccount.text.toString().length > 0){
            binding.btnSubmit.setTextColor(resources.getColor(R.color.white))
        }else{
            binding.btnSubmit.setTextColor(resources.getColor(R.color.white50))
        }
    }
    private inline fun showCountryCodeWindow() {
        InputMethodUtils.hideInput(ContextUtil.getContext(),binding.textCountryCode)
        refundWindow.width = CommonUtils.getScreenWidth(this@RefundPersonInfoActivity)
        refundWindow.height = CommonUtils.getScreenHeight(this@RefundPersonInfoActivity)
//        countryCodeWindow.showAsDropDown(ll_country_code)
        refundWindow.showAtLocation(binding.ivCountryCode, Gravity.BOTTOM,0,0)

        /**
         * 点击popupWindow让背景变暗
         */
        val lp: WindowManager.LayoutParams = <EMAIL>().getAttributes()
        lp.alpha = 0.7f //代表透明程度，范围为0 - 1.0f
        <EMAIL>().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
        <EMAIL>().setAttributes(lp)

        refundWindow.setOnDismissListener {
            lp.alpha = 1.0f;
            <EMAIL>().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            <EMAIL>().setAttributes(lp);
        }
    }
}