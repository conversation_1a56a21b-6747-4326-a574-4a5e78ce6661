package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.provider.Settings
import android.text.Editable
import android.text.Html
import android.text.TextWatcher
import android.view.View
import android.widget.LinearLayout
import androidx.recyclerview.widget.GridLayoutManager
import com.baidu.ar.it
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.Glob
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.RechargeAdapter
import com.tbit.uqbike.adapter.RechargeNewAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityApplyForSitesBinding
import com.tbit.uqbike.databinding.ActivityChargeNewBinding
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.entity.ChargeData
import com.tbit.uqbike.entity.ChargeLimitData
import com.tbit.uqbike.entity.ChargeMethodData
import com.tbit.uqbike.entity.OrderResultData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.entity.pageData
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.fragment.PayFragment
import com.tbit.uqbike.map.bean.Location
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.onepasslogin.Utils
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.OrderModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.roundview.RoundTextView
import com.tbit.uqbike.utils.ClickableTextUtil
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.NetUtils
import com.tbit.uqbike.utils.UrlDecodeUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.startActivity
import org.json.JSONObject


class ChargeNewActivity : BaseActivity(){
    //    private val payFragment by lazy { pay_fragment as PayFragment }
//    private val payBehavior by lazy { BottomSheetBehavior.from(pay_fragment.requireView()) }
    private lateinit var payFragment : PayFragment
    private val adapter = RechargeNewAdapter()
    var chargeData : ChargeData? = null
    var chargeModthData : ChargeMethodData? = null
    private val PayMoney: Float? by bindExtra(EXTRA_CHARGE)//需要支付的钱
    private val goWallet: Boolean? by bindExtra(EXTRA_CHARGE_WALLET)//是否需要跳转钱包界面
    private var Money_Min = 1L
    private var Money_Max = 5000L
    private lateinit var binding: ActivityChargeNewBinding
    companion object {
        private const val EXTRA_CHARGE = "EXTRA_CHARGE"
        private const val EXTRA_CHARGE_WALLET = "EXTRA_CHARGE_WALLET"
        fun createIntent(context: Context, PayMoney : Float): Intent {
            return context.intentFor<ChargeNewActivity>(
                EXTRA_CHARGE to PayMoney
            )
        }

        fun createIntent(context: Context, PayMoney : Float, goWallet : Boolean): Intent {
            return context.intentFor<ChargeNewActivity>(
                EXTRA_CHARGE to PayMoney, EXTRA_CHARGE_WALLET to goWallet
            )
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityChargeNewBinding.inflate(layoutInflater)
        setContentView(binding.root)
        EventBus.getDefault().register(this);
//        setContentView(R.layout.activity_charge_new)
//        setSupportActionBar(toolbar)
//        supportActionBar?.setDisplayShowTitleEnabled(false)
//        toolbar_title.text = ResUtil.getString(R.string.s_charge_title)
//        toolbar.setNavigationIcon(R.drawable.icon_back)
//        toolbar.setNavigationOnClickListener { onBackPressed() }

        payFragment = PayFragment(loadingDialogHelper)
        binding.tvLowHint.text = getString(R.string.s_exchange_limit,Glob.CurrencyUnit.toString())
        binding.tvChargeUnit.text = Glob.CurrencyUnit
        binding.ivClose.setOnClickListener { finish() }

        binding.rcvCharge.layoutManager = createLayoutManager()
        val spanCount = 2 // 3 columns
        val spacing = Utils.dip2px(ContextUtil.getContext(),10f)
        val includeEdge = false
        binding.rcvCharge.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, includeEdge))
        binding.rcvCharge.adapter = adapter
//        adapter.source = rechargeConfigs
//        adapter.notifyDataSetChanged()
//        MyLogUtil.Log("1111","====="+PayMoney)

        getPayData()

        ComModel.getPayMethod().subscribeBy(
            onNext = {
                MyLogUtil.Log("1111","===获取支付 信息=="+it.toString())
                chargeModthData = Gson().fromJson(it.toString(), ChargeMethodData::class.java)
            }
        ).toCancelable()

        binding.btnPay.clickDelay {
            inputValue_event = binding.etChargeNum.text.toString()
            val properties = JSONObject()
            MDUtil.clickEvent("confirm_recharge",properties)
            if (!binding.etChargeNum.text.toString().isNullOrEmpty()){
                MyLogUtil.Log("1111","==etChargeNum====="+binding.etChargeNum.text.toString())
                var chargeNum = binding.etChargeNum.text.toString().toFloat()
                if (chargeNum < Money_Min){
                    MyToastUtil.toast(getString(R.string.s_paynum_err))
                    return@clickDelay
                }
                if (chargeNum > 30){
                    goPay()
                }else{
                    goPay()

//                    try {
//                        CommDialog.Builder(this@ChargeNewActivity).setTitle(getString(R.string.dialog_tip)).
//                        setContent(getString(R.string.s_pay_yh_fail))
//                            .setLeftText(ResUtil.getString(R.string.cancel)).setRightText(getString(
//                                R.string.confirm)).setCanceledOnOutside(true)
//                            .setClickListen(object : CommDialog.TwoSelDialog {
//                                override fun leftClick() {}
//                                override fun rightClick() {
//                                    goPay()
//                                }
//                            }).build().show()
//                    }catch (e : NullPointerException){}

                }
            }
        }

        adapter.onClickListener = { it,id->
            val properties = JSONObject()
            properties.put("package_id",id)
            properties.put("originalPrice",it)
            MDUtil.clickEvent("recharge_package",properties)
            if (it.toFloat() >= Money_Min && it.toFloat() <= Money_Max){
                binding.etChargeNum.setText(kotlin.math.ceil(it.toFloat()).toInt().toString())
                binding.etChargeNum.setSelection(binding.etChargeNum.text.toString().length)
            }else{
                MyToastUtil.toast(getString(R.string.s_paynum_err))
            }
        }
        binding.etChargeNum.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                var strData = binding.etChargeNum.text.toString()
//                if (strData.contains(".")) {
//                    // 检查是否有多个小数点
//                    if (strData.split(".").size > 2) {
//                        // 移除多余的小数点
//                        binding.etChargeNum.setText(strData.subSequence(0,strData.length-1))
//                        binding.etChargeNum.setSelection(binding.etChargeNum.text.toString().length)
//                        strData = binding.etChargeNum.text.toString()
//                    }
//                }
                if (strData.length > 0){
                    binding.etChargeNum.setTypeface(Typeface.DEFAULT_BOLD)
                    if (strData.toFloat() > Money_Max) {
                        binding.etChargeNum.setText(kotlin.math.ceil(Money_Max.toFloat()).toInt().toString())
                        binding.etChargeNum.setSelection(binding.etChargeNum.text.toString().length)
                    }
                    if (strData.toFloat() < 30){
//                        binding.tvLowHint.visibility = View.VISIBLE
                        binding.tvLowHint.visibility = View.GONE
                    }else{
                        binding.tvLowHint.visibility = View.GONE
                    }
//                    if (countDecimalPlaces(strData) > 2) {
//                        binding.etChargeNum.setText(strData.substring(0,strData.length-1))
//                        binding.etChargeNum.setSelection(binding.etChargeNum.text.toString().length)
//                    }
                    binding.btnPay.setTextColor(resources.getColor(R.color.white))
                    var isCom = false
                    adapter.source.forEachIndexed { index, it ->
                        it.isSel = false
                        if ((it.recharge_amount.toFloat() == strData.toFloat()) && !isCom){
                            it.isSel = true
                            isCom = true
                        }
                    }
                    adapter.notifyDataSetChanged()
                }else{
                    binding.etChargeNum.setTypeface(Typeface.DEFAULT)
                    binding.btnPay.setTextColor(resources.getColor(R.color.white50))
                    binding.tvLowHint.visibility = View.GONE
                    adapter.source.forEachIndexed { index, it ->
                        it.isSel = false
                    }
                    adapter.notifyDataSetChanged()
                }
            }
        })


        ClickableTextUtil.makeClickable(binding.tvPayHint, getString(R.string.s_pay_exchange_xy), ::showPayPolicy)
        AutoTask()

//        payBehavior.setBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
//            override fun onStateChanged(@NonNull bottomSheet: View, newState: Int) {
//                //拖动
//                if (newState == BottomSheetBehavior.STATE_DRAGGING) {//判断为向下拖动行为时，则强制设定状态为展开
//                    payBehavior.setState(BottomSheetBehavior.STATE_EXPANDED );
//                }
//                if (newState == BottomSheetBehavior.STATE_HIDDEN) { }
//            }
//            override fun onSlide(@NonNull bottomSheet: View, slideOffset: Float) { }
//        })
    }

    var oldData_str = ""
    var oldDataConfig_str = ""
    var oldDataPage_str = ""
    fun getPayData(){
        if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
            if (!NetUtils.isConnected(ContextUtil.getContext())){
                setErrorView(true)
            }else{
                setErrorView(false)
            }
        }else{
            //recharge_items 充值项, sys_paymethod 支付方式
            val listKey = arrayOf("recharge_items","sys_paymethod")
            ComModel.getConfig(listKey).subscribeBy(
                onNext = {
                    if (binding.btnPay != null){
                        if (!oldData_str.equals(it.toString())){
                            oldData_str = it.toString()
                            MyLogUtil.Log("1111","===获取充值项 信息=="+it.toString())
                            chargeData = Gson().fromJson(it.toString(), ChargeData::class.java)
                            chargeData!!.recharge_items.forEach {
//                    MyLogUtil.Log("1111","====="+PayMoney)
                                if(PayMoney != null && PayMoney != 0f){
                                    if(it.recharge_amount.toFloat() < PayMoney!!){
                                        it.isSatisfy = false
                                    }else{
                                        it.isSatisfy = true
                                    }
                                }else{
                                    it.isSatisfy = true
                                }
                            }
                            adapter.source = chargeData!!.recharge_items
//                adapter.source.first().isSel = true
                            adapter.notifyDataSetChanged()
                            if (adapter.source.isEmpty()) binding.capaLayout.toEmpty() else binding.capaLayout.toContent()
                        }else{
                            MyLogUtil.Log("1111","===获取充值项 信息==common")
                            if (!oldData_str.isNullOrEmpty()){
                                binding.capaLayout.toContent()
                            }
                        }
                    }
                },
                onError = {setErrorView(true)}
            ).toCancelable()


            val listKeyLimit = arrayOf("recharge_amount_limit")
            ComModel.getConfig(listKeyLimit).subscribeBy(
                onNext = {
                    if (!oldDataConfig_str.equals(it.toString())){
                        oldDataConfig_str = it.toString()
                        MyLogUtil.Log("1111","===获取充值项现在 信息=="+it.toString())
                        var resultData = Gson().fromJson(it.toString(), ChargeLimitData::class.java)
                        if (binding.btnPay != null){
                            if (resultData != null){
                                Money_Min = resultData.recharge_amount_limit.min
                                Money_Max = resultData.recharge_amount_limit.max
//                                binding.etChargeNum.setHint(getString(R.string.s_pay_setmy)+"("+(Money_Min.toString()+"-"+Money_Max.toString())+")")
                                binding.etChargeNum.setHint(getString(R.string.s_pay_setmy)+"("+(AppUtil.formatAmount(Money_Min.toLong())+"-"+AppUtil.formatAmount(Money_Max.toLong()))+")")
                                setPayMoney()
                            }else{
//                                binding.etChargeNum.setHint(getString(R.string.s_pay_setmy)+"("+(Money_Min.toString()+"-"+Money_Max.toString())+")")
                                binding.etChargeNum.setHint(getString(R.string.s_pay_setmy)+"("+(AppUtil.formatAmount(Money_Min.toLong())+"-"+AppUtil.formatAmount(Money_Max.toLong()))+")")
                                setPayMoney()
                            }
                        }
                    }
                },
                onError = {
                    if (binding.btnPay != null){
//                        binding.etChargeNum.setHint(getString(R.string.s_pay_setmy)+"("+(Money_Min.toString()+"-"+Money_Max.toString())+")")
                        binding.etChargeNum.setHint(getString(R.string.s_pay_setmy)+"("+(AppUtil.formatAmount(Money_Min.toLong())+"-"+AppUtil.formatAmount(Money_Max.toLong()))+")")
                        setPayMoney()
                    }
                }
            ).toCancelable()

            ComModel.getPayMethod().subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取支付 信息=="+it.toString())
                    chargeModthData = Gson().fromJson(it.toString(), ChargeMethodData::class.java)
                }
            ).toCancelable()

            PageModel.getPage(PageModel.recharge_instructions,PageModel.isH5).subscribeBy(
                onNext = {
                    if (!oldDataPage_str.equals(it.toString())){
                        oldDataPage_str = it.toString()
                        MyLogUtil.Log("1111","===获取充值说明 信息=="+it.toString())
                        var resultData = Gson().fromJson(it.toString(), pageData::class.java)
                        if (binding.btnPay != null){
                            //                tv_charge_info.text = resultData.content
                            if (resultData != null && resultData.content.length > 0){
                                binding.tvChargeExpend.visibility = View.VISIBLE
                                var htmlFormattedText = Html.fromHtml(resultData.content)
                                binding.tvChargeInfo.text = htmlFormattedText
                            }
                        }
                    }
                }
            ).toCancelable()
        }
    }
    private fun AutoTask(){
        if (binding.btnPay != null){
            binding.btnPay.postDelayed({
                if (binding.btnPay != null){
                    AutoTask()
                    getPayData()
                }
            },5000)
        }
    }

    fun setErrorView(isNetError : Boolean){
//        MyLogUtil.Log("1111","======isNetError======"+isNetError)
        if(isNetError){
            if (NetUtils.isConnected(ContextUtil.getContext())){
                binding.capaLayout.toEmpty()
                return
            }
        }
        if (isNetError){
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_net).visibility = View.VISIBLE
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_local).visibility = View.GONE
        }else{
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_net).visibility = View.GONE
            binding.capaLayout.findViewById<LinearLayout>(R.id.ly_err_local).visibility = View.VISIBLE
        }
        binding.capaLayout.toError()
        binding.capaLayout.findViewById<RoundTextView>(R.id.tv_err_set_net).setOnClickListener{
            val intent = Intent(Settings.ACTION_DATA_ROAMING_SETTINGS)
            startActivity(intent)
        }
        binding.capaLayout.findViewById<RoundTextView>(R.id.tv_err_set_local).setOnClickListener{
            val intent = Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS)
            startActivityForResult(intent, MainActivity.REQUEST_OPEN_GPS)
        }
    }
    private fun showPayPolicy() {
        loadingDialogHelper.show {  }
        PageModel.getPageUrl(PageModel.pay_policy).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                startActivity<WebActivity>(
                    WebActivity.TITLE to getString(R.string.s_pay_exchange_xy).replace("《","").replace("》",""),
                    WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
            },
            onError = {loadingDialogHelper.dismiss()}
        ).toCancelable()
    }
    fun setPayMoney(){
        if(PayMoney != null && PayMoney != 0f){
            if (PayMoney!! >= Money_Min && PayMoney!! <= Money_Max){
                binding.etChargeNum.setText(kotlin.math.ceil(PayMoney!!).toInt().toString())
                binding.etChargeNum.setSelection(binding.etChargeNum.text.toString().length)
            }
        }
    }
    fun countDecimalPlaces(str: String): Int {
        val decimalIndex = str.indexOf('.')
        return if (decimalIndex == -1) 0 else str.length - decimalIndex - 1
    }
    fun goPay(){
        try {
            var flag = ""
            adapter.source.forEachIndexed { index, it ->
                if(it.isSel){
                    flag = it.flag
                }
            }
            var location = LocationModel.lastLocation
            var latLng = LatLng()
            try {
                latLng.lat = location!!.latitude
                latLng.lng = location!!.longitude
            }catch (e : NullPointerException){
                location = Location(0f, 0f, 0.0, 0.0, -1,"","")
                latLng.lat = 0.0
                latLng.lng = 0.0
            }
            if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
                latLng = GPSUtil.bd09_To_gps84(location!!.latitude,location!!.longitude)
            }
            latLng.lat = GPSUtil.retain6(latLng.lat)
            latLng.lng = GPSUtil.retain6(latLng.lng)

//        MyLogUtil.Log("1111","====充值=="+flag+","+et_charge_num.text.toString())
            loadingDialogHelper!!.show {  }
            OrderModel.sumitExchargeOrder(flag,binding.etChargeNum.text.toString().toFloat(),latLng!!.lat,latLng!!.lng)
                .subscribeBy(
                    onNext = {
//                    loadingDialogHelper!!.dismiss()
                        MyLogUtil.Log("1111","===创建充值订单信息=="+it.toString())
                        val resultData = Gson().fromJson(it.toString(), OrderResultData::class.java)
                        if (goWallet != null && goWallet as Boolean){
                            payFragment.goWallet = true
                        }else{
                            payFragment.goWallet = false
                        }
                        payFragment.setData(chargeModthData!!,resultData.order_no,PayFragment.TYPE_EXCHANGE)
                        lifecycleDialogHelper.show(payFragment)
                    },
                    onError = {
                        loadingDialogHelper!!.dismiss()
                    }
                ).toCancelable()
        }catch (e : NullPointerException){}

    }
    private fun createLayoutManager(): GridLayoutManager {
        val layoutManager = GridLayoutManager(this, 2)
        layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if (adapter.getItemViewType(position) == RechargeAdapter.TYPE_MANUAL) 2 else 1
            }
        }
        return layoutManager
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: BaseEventData) {
        if (event.code == EventUtil.EVENT_HOME) finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }
}