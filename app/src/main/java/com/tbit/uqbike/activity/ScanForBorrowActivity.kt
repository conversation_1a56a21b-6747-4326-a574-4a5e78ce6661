package com.tbit.uqbike.activity

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.widget.Toast
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.Glob
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.BikeState
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.databinding.ActivityScanBinding
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.entity.QRData
import com.tbit.uqbike.entity.ScanData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.fragment.BleCheckFragment
import com.tbit.uqbike.fragment.ScanFragment
import com.tbit.uqbike.mvp.constract.InputUserCodeContract
import com.tbit.uqbike.mvp.model.BikeModel
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.presenter.InputUserCodePresenter
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.utils.BleUtils
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MachineUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.UrlDecodeUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.toast
import org.json.JSONObject


class ScanForBorrowActivity: BaseActivity(), InputUserCodeContract.View {
    private val from_riding_page: Boolean? by bindExtra(ScanForBorrowActivity.FROM_RIDING_PAGE)//是否需要跳转钱包界面
    companion object {

        private const val REQUEST_BORROW_BIKE = 1
        private const val EXTRA_TYPE = "EXTRA_TYPE"
        val TYPE_CAR = 1 // 车 扫码
        val TYPE_EXTEND = 2   // 推广码，礼品卡 ,骑行卡 扫码

        private const val FROM_RIDING_PAGE = "FROM_RIDING_PAGE"//1从骑行页面入口进来  0不是从骑行页面入口进来

        fun createIntent(context: Context, type: Int? = null,from_riding_page:Boolean? = false): Intent {
            return context.intentFor<ScanForBorrowActivity>(EXTRA_TYPE to type,FROM_RIDING_PAGE to from_riding_page)
        }
    }

    private lateinit var binding: ActivityScanBinding

    private val type: Int by bindExtra(EXTRA_TYPE)
    val bleCheckFragment by lazy { supportFragmentManager.findFragmentById(R.id.fragment_ble_check) as BleCheckFragment }
    val fragment_scan by lazy {supportFragmentManager.findFragmentById(R.id.fragment_scan) as ScanFragment}
    var isBle = false

    var scanTime = 20 * 1000L
    private val presenter = InputUserCodePresenter(this)
    //    override fun setBarState() {
////        super.setBarState()
//        showBarTextColorIsBlack(true)
//    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityScanBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_scan)
        EventBus.getDefault().register(this);
        lifecycle.addObserver(presenter)
        fragment_scan.text_manual?.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("click_enter",properties)
            //打开手动输入界面
            MyLogUtil.Log("1111","=====是否从骑行入口进来==="+from_riding_page)
//            startActivity<InputForBorrowActivity>()
            startActivity(InputForBorrowActivity.createIntent(this, from_riding_page))
            finish()
        }
        binding.imgBack.setOnClickListener { finish() }
        (supportFragmentManager.findFragmentById(R.id.fragment_scan) as ScanFragment).onScanSuccessListener = ::onScanSuccess

        if(type == TYPE_CAR){
//            var bleCheckFragment = BleCheckFragment()
            if (LanguageUtil.isHarmonyOs()){
                if (SpUtil.Companion.getInstance().find(Constant.SpKey.SP_BLE_PLE_DEFY).isNullOrEmpty()){
                    bleCheckFragment.check{
                        isBle = it
                        MyLogUtil.Log("1111","=====1111蓝牙权限==="+isBle)
                        if (isBle){
                            SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE, "1");
                        }else{
                            SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE_DEFY, "1");
                        }
                    }
                }
            }else{
                bleCheckFragment.check{
                    isBle = it
                    MyLogUtil.Log("1111","=====1111蓝牙权限==="+isBle)
                    if (isBle){
                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE, "1");
                    }else{
                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE_DEFY, "1");
                    }
                }
            }

            MyLogUtil.Log("1111","=====蓝牙权限==="+isBle)
            if (isBle){
                SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE, "1");
            }
            if(!BleUtils.isBleOpened() || !isBle){
                fragment_scan.ry_scan_ble?.visibility = View.VISIBLE
            }
            fragment_scan.ry_scan_bom?.visibility = View.VISIBLE
            fragment_scan.zxing_status_view?.visibility = View.VISIBLE
            binding.imgBack.postDelayed(onRunnable,scanTime);
        }else{
            fragment_scan.text_manual?.visibility = View.GONE
            fragment_scan.ly_manual?.visibility = View.GONE
        }
//    zxing_viewfinder_view.setScan

        fragment_scan.tv_cam_open?.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("turn_bluetooth",properties)
            if (LanguageUtil.isHarmonyOs()){
                if (!isBle){
                    val intent = Intent()
                    intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                    val uri = Uri.fromParts("package", ContextUtil.getContext().getPackageName(), null)
                    intent.data = uri
                    startActivity(intent)
                    Toast.makeText(ContextUtil.getContext(), getString(R.string.s_open_ble_pre),Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }
            }else{
                bleCheckFragment.setShowDialog(true)
                bleCheckFragment.check({
                    isBle = it
                    if (isBle){
                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE, "1");
                    }else{
                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE_DEFY, "1");
                    }
                })
            }

            if (isBle){
                CommDialog.Builder(this@ScanForBorrowActivity).setTitle(getString(R.string.s_ble_title)).
                setContent(getString(R.string.s_ble_cont))
                    .setLeftText(ResUtil.getString(com.tbit.uqbike.R.string.cancel)).setRightText(getString(
                        com.tbit.uqbike.R.string.s_opengo)).setCanceledOnOutside(true)
                    .setClickListen(object : CommDialog.TwoSelDialog {
                        override fun leftClick() {}
                        override fun rightClick() {
                            val intent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
                            startActivity(intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK))
                        }
                    }).build().show()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if(type == TYPE_CAR){
//            bleCheckFragment.setShowDialog(false)
//            bleCheckFragment.check({ isBle = it })
            fragment_scan.ry_scan_ble?.postDelayed({
                if (!isBle){
                    isBle = hasBluetoothPermission(ContextUtil.getContext())
                    if (isBle){
                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_BLE_PLE, "1");
                    }
                    MyLogUtil.Log("1111","==hasBluetoothPermission=="+isBle)
                }
                if(!BleUtils.isBleOpened() || !isBle){
                    fragment_scan.ry_scan_ble?.visibility = View.VISIBLE
                }else{
                    fragment_scan.ry_scan_ble?.visibility = View.GONE
                }
            },200)
        }
    }
    fun hasBluetoothPermission(context: Context?): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 检查ACCESS_FINE_LOCATION权限
            (ContextCompat.checkSelfPermission(context!!, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED // 或者检查ACCESS_COARSE_LOCATION权限
                    || ContextCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED)
        } else {
            // 在API 23以下，直接检查蓝牙权限已经足够
            ContextCompat.checkSelfPermission(context!!, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        onResume()
        if(resultCode == Activity.RESULT_OK) {
            finish()
        } else {
//            (fragment_scan as ScanFragment).startScan()
        }
    }
    var onRunnable = Runnable {
        CommDialog.Builder(this@ScanForBorrowActivity).setTitle(getString(R.string.s_scan_fail)).setContent(getString(R.string.s_scanfail_input))
            .setLeftText(getString(R.string.cancel)).setRightText(getString(R.string.s_scan_input))
            .setCanceledOnOutside(true)
            .setClickListen(object : CommDialog.TwoSelDialog {
                override fun leftClick() { StrarScan() }
                override fun rightClick() {
                    startActivity<InputForBorrowActivity>()
                    finish()
                }
            })
            .setOnOUtCllickListener {
                StrarScan()
            }.build().show()
    }
    private fun onScanSuccess(result: String) {
        val userCode = MachineUtil.getMachineNOFromQrCode(result)
        binding.imgBack.removeCallbacks(onRunnable);
//            if(userCode == null || userCode.isNullOrEmpty() || !MachineUtil.checkMachineNO(userCode))
        if((userCode == null || userCode.isNullOrEmpty())) {
//            (fragment_scan as ScanFragment).Myzxingview.pause()
//            CommDialog.Builder(this@ScanForBorrowActivity).setTitle(getString(R.string.s_scan_ugogo)).setContent(getString(R.string.s_scan_usave))
//                .setLeftText("").setRightText(getString(R.string.i_know)).setCanceledOnOutside(true)
//                .setClickListen(object : CommDialog.TwoSelDialog {
//                    override fun leftClick() {}
//                    override fun rightClick() {StrarScan()}
//                })
//                .setOnOUtCllickListener {
//                    StrarScan()
//                }.build().show()

            (supportFragmentManager.findFragmentById(R.id.fragment_scan) as ScanFragment).Myzxingview.stopSpot()
            loadingDialogHelper.show {  }
            ComModel.sumitQRcode(result).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===二维码扫码 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), QRData::class.java)
                    if (resultData != null){
                        if(type == TYPE_CAR){
                            if (resultData.action == 1){
                                startActivity<WebActionActivity>(WebActionActivity.TITLE to "",
                                    WebActionActivity.URL to UrlDecodeUtil().getParm(resultData.content),WebActionActivity.IsHideHead to true)
                            }else{
                                getBikeState(resultData.content)
                                vehicle_number = resultData.content
                            }
                        }else{
                            // 推广，礼品卡
                            EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_EXTENDSCAN,resultData.content));
                            finish()
                        }
                    }else{
                        binding.imgBack.postDelayed({ if (binding.imgBack != null){ StrarScan() } },1000)
                    }
                },
                onError = {
                    loadingDialogHelper.dismiss()
                    binding.imgBack.postDelayed({ if (binding.imgBack != null){ StrarScan() } },1000)
                }
            ).toCancelable()
        }else if(userCode.equals("1")){
            (supportFragmentManager.findFragmentById(R.id.fragment_scan) as ScanFragment).Myzxingview.stopSpot()
            binding.imgBack.postDelayed({ if (binding.imgBack != null){ StrarScan() } },1000)
        } else {
//            (fragment_scan as ScanFragment).Myzxingview.pause()
            (supportFragmentManager.findFragmentById(R.id.fragment_scan) as ScanFragment).Myzxingview.stopSpot()
//            LastScanInfoModel.uploadLastScanInfo(userCode)
            if(result.contains(FlavorConfig.NET.EVENT_URL)){
                loadingDialogHelper.show {}
                ComModel.getVechio(userCode).subscribeBy(
                    onNext = {
                        loadingDialogHelper.dismiss()
                        MyLogUtil.Log("1111","===获取车辆编号 信息=="+it.toString())
                        val resultData = Gson().fromJson(it.toString(), ScanData::class.java)
                        if (resultData.data.contains("80434718")){
                            binding.imgBack.post { MyToastUtil.toast(getString(R.string.s_appdown)) }
                            binding.imgBack.postDelayed({ if (binding.imgBack != null){ StrarScan() } },1000)
                        }else{
                            if(type == TYPE_CAR){
                                vehicle_number = resultData.data
                                getBikeState(resultData.data)
                            }else{
                                // 推广，礼品卡
                                EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_EXTENDSCAN,resultData.data));
                                finish()
                            }
                        }
                    },
                    onError = {
                        loadingDialogHelper.dismiss()
                        binding.imgBack.postDelayed({ if (binding.imgBack != null){ StrarScan() } },1000)
                    }
                ).toCancelable()
            }else{
                if(type == TYPE_CAR){
                    vehicle_number = userCode
                    getBikeState(userCode)
                }else{
                    // 推广，礼品卡
                    EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_EXTENDSCAN,result));
                    finish()
                }
            }
        }


    }
    fun StrarScan(){
        binding.imgBack.removeCallbacks(onRunnable);
//        (fragment_scan as ScanFragment).Myzxingview.resume()
        (supportFragmentManager.findFragmentById(R.id.fragment_scan) as ScanFragment).Myzxingview.startSpot()
        binding.imgBack.postDelayed(onRunnable,scanTime);
    }
    private fun getBikeState(userCode: String) {
        var location = LocationModel.lastLocation
        var latLng = LatLng()
        latLng.lat = location!!.latitude
        latLng.lng = location!!.longitude
        if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
            latLng = GPSUtil.bd09_To_gps84(location.latitude,location.longitude)
        }
        latLng.lat = GPSUtil.retain6(latLng.lat)
        latLng.lng = GPSUtil.retain6(latLng.lng)
        BikeModel.getBikesInfo(userCode,latLng!!.lat,latLng!!.lng,BikeModel.isScan_Y,Glob.isRental)
            .subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取车辆信息=="+it.toString())
//                    val resultData: CarInfoData = Gson().fromJson(it.toString(), CarInfoData::class.java)
                    EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_NOTITY,it.toString()));
                    loadingDialogHelper.dismiss()
                    finish()
                },
                onError = {
                    loadingDialogHelper.dismiss()
                    val errCode = ErrHandler.getErrCode(it)
                    if(errCode == Constant.ErrCode.SCAN_MUCH){
                        val errMsg = ErrHandler.getErrMsg(it)
                        CommDialog.Builder(this@ScanForBorrowActivity).setTitle(getString(R.string.dialog_tip)).setContent(errMsg)
                            .setLeftText("").setRightText(getString(R.string.i_know)).setCanceledOnOutside(true)
                            .setClickListen(object : CommDialog.TwoSelDialog {
                                override fun leftClick() {}
                                override fun rightClick() {StrarScan()}
                            })
                            .setOnOUtCllickListener {
                                StrarScan()
                            }.build().show()
                    }else{
                        binding.imgBack.postDelayed({ if (binding.imgBack != null){ StrarScan() } },1000)
                    }
                }
            ).toCancelable()
        loadingDialogHelper.show {}

//        val cancellable = presenter.getBikeState(userCode)
//        loadingDialogHelper.show {
//            cancellable.cancel()
////            (fragment_scan as ScanFragment).startScan()
//        }
    }

    override fun onGetBikeStateSuccess(bikeState: BikeState) {
        loadingDialogHelper.dismiss()
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
//        (fragment_scan as ScanFragment).startScan()
    }

    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onMsgEvent(event: BaseEventData) {}
    override fun onDestroy() {
        super.onDestroy()
        if(binding.imgBack != null){
            binding.imgBack.removeCallbacks(onRunnable);
        }
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }
}