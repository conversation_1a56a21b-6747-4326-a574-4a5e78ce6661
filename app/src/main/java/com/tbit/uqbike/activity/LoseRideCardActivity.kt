package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.LostRideCardAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityLoseRideCardBinding
import com.tbit.uqbike.entity.MyRideCardData
import com.tbit.uqbike.mvp.model.RideCardModel
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip
import org.jetbrains.anko.intentFor

class LoseRideCardActivity : BaseActivity() {
    private val adapter by lazy { LostRideCardAdapter() }
    private var lastLoaded = false
    private val areaListData : HashMap<Int,String> by bindExtra(EXTRA_AREA)
    private val loadMoreView by lazy { View(this).apply { layoutParams = ViewGroup.LayoutParams(1, 1) } }
    companion object {
        private const val EXTRA_AREA = "EXTRA_AREA"
        private const val EXTRA_TYPE = "failure"
        fun createIntent(context: Context, areaListData : HashMap<Int,String>): Intent {
            return context.intentFor<LoseRideCardActivity>(
                EXTRA_AREA to areaListData,
            )
        }
    }
    private lateinit var binding: ActivityLoseRideCardBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoseRideCardBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_lose_ride_card)

        binding.toolbars.toolbarTitle.text = getString(R.string.s_lose_card)
        binding.toolbars.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.toolbars.toolbar.setNavigationOnClickListener { finish() }

        binding.rcv.layoutManager = LinearLayoutManager(this)
        val spacing = dip(10)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        binding.rcv.adapter = loadMoreWrapper(adapter)

        getMyRideCard()
    }

    private fun loadMoreWrapper(adapter: RecyclerView.Adapter<out RecyclerView.ViewHolder>): LoadMoreWrapper<out RecyclerView.ViewHolder> {
        val loadMoreAdapter = LoadMoreWrapper(adapter)
        loadMoreAdapter.setOnLoadMoreListener{ onLoadMore() }
        return loadMoreAdapter
    }
    private fun onLoadMore() {
        if (!lastLoaded)
            getMyRideCard(false)
    }
    private fun getMyRideCard(showLoading: Boolean = true) {
        RideCardModel.getMyRideCardNew(EXTRA_TYPE)
            .subscribeBy(
                onNext = {
                    loadingDialogHelper!!.dismiss()
                    MyLogUtil.Log("1111","===获取失效骑行卡信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), MyRideCardData::class.java)
                    if(resultData != null && resultData.size > 0){
                        resultData.forEach{
                            it.areaName = areaListData.get(it.area_id).toString()
                        }
                        adapter.source = resultData
                        adapter.notifyDataSetChanged()
                    }
                    if (adapter.source.isEmpty()) binding.capaLayout.toEmpty() else binding.capaLayout.toContent()
                }
            ).toCancelable()
        if (showLoading) {
            loadingDialogHelper.show { }
        }
    }
}