package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.baidu.ar.it
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.UseInviteAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivitySelInviteBinding
import com.tbit.uqbike.entity.Coupon
import com.tbit.uqbike.entity.InviteData
import com.tbit.uqbike.entity.SelCoupon
import com.tbit.uqbike.entity.couponType
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.CouponData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.utils.ImageLoad
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.dip
import org.jetbrains.anko.intentFor
import org.json.JSONObject

class SelInviteActivity(): BaseActivity() {

    companion object {
        val EXTRA_TYPE_RIDECARD = 1 // 骑行卡
        val EXTRA_TYPE_RIDEORDER = 2 // 骑行订单
        private const val EXTRA_TYPE = "EXTRA_TYPE"
        private const val EXTRA_DATA_ID = "EXTRA_DATA_ID"
        private const val EXTRA_DATA_AMOUNT = "EXTRA_DATA_AMOUNT"
        private const val EXTRA_DATA_PRICE = "EXTRA_DATA_PRICE"
        private const val EXTRA_DATA_CouponID = "EXTRA_DATA_CouponID"
        private const val EXTRA_DATA_ISRENTAL = "EXTRA_DATA_ISRENTAL"//是否长租
        fun createIntent(context: Context, type : Int, id : String,amount : Float,price : Float, Couponid : ArrayList<String>,isRental : Boolean = false): Intent {
            return context.intentFor<SelInviteActivity>(EXTRA_TYPE to type,EXTRA_DATA_ID to id,
                EXTRA_DATA_AMOUNT to amount,EXTRA_DATA_PRICE to price,EXTRA_DATA_CouponID to Couponid,EXTRA_DATA_ISRENTAL to isRental)
        }
    }
    private val type : Int by bindExtra(EXTRA_TYPE)//优惠类型
    private val id : String by bindExtra(EXTRA_DATA_ID)// 当前id 可使用的优惠券
    private val amount : Float by bindExtra(EXTRA_DATA_AMOUNT)// 当前id 付费价格
    private val price : Float by bindExtra(EXTRA_DATA_PRICE)// 当前id 售价
    private val isRental : Boolean by bindExtra(EXTRA_DATA_ISRENTAL)//是否长租
    private val CouponidList : ArrayList<String> by bindExtra(EXTRA_DATA_CouponID)// 当前使用的优惠券总数

    private val adapter by lazy { UseInviteAdapter() }
    private var lastLoaded = false
    private val loadMoreView by lazy {
        View(this).apply {
            layoutParams = ViewGroup.LayoutParams(1, 1)
        }
    }

    private lateinit var binding: ActivitySelInviteBinding
    var datas = ArrayList<Coupon>()
    var dataSel = ArrayList<Coupon>()
    var price_now = 0f // 当前价格
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this);
        binding = ActivitySelInviteBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_sel_invite)

        binding.layoutToolbar.toolbarTitle.text = getString(R.string.s_invite_sel)
        binding.layoutToolbar.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.layoutToolbar.toolbar.setNavigationOnClickListener { finish() }

        price_now = amount
        binding.tvSelinviteUnit.text = Glob.CurrencyUnit

//        MyLogUtil.Log("1111","=========="+price_now+","+price)
        binding.rcv.layoutManager = LinearLayoutManager(this)
        val spacing = dip(10)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        binding.rcv.adapter = adapter
        adapter.setOnMyItemClickListener { CouponData, i ->
//            MyLogUtil.Log("1111","=====price_now000====="+price_now)
            if (!(CouponData.type == 1 || CouponData.type == 2)){
                MyToastUtil.toast("Not supported yet")
                return@setOnMyItemClickListener
            }

            if (!CouponData.isSel){
                if (dataSel.size > 0){
                    var couponDataSel = dataSel.last()
                    if (couponDataSel?.is_superposed == 0){
                        MyToastUtil.toast(getString(R.string.s_coupon_u_other))
                        return@setOnMyItemClickListener
                    }
                }
                if (price_now <= 0f){
                    MyToastUtil.toast(getString(R.string.s_coupon_zero))
                    return@setOnMyItemClickListener
                }
                if (CouponData.is_superposed == 0 && dataSel.size > 0){
                    MyToastUtil.toast(getString(R.string.s_coupon_u_other))
                    return@setOnMyItemClickListener
                }
                if (CouponData.is_min_spend == 1){
                    //有门槛
                    if (price_now < CouponData.min_spend_amount){
                        MyToastUtil.toast(getString(R.string.s_coupon_u_min))
                        return@setOnMyItemClickListener
                    }
                }

                val properties = JSONObject()
                properties.put("coupon_id",CouponData.user_coupon_id)
                MDUtil.clickEvent("selected_click",properties)

                CouponData.isSel = true
                dataSel.add(CouponData)
                price_now = price_now - CouponData.amount
//                if (price_now < 0) price_now = 0f
                ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_sel_n),binding.imgInviteUuse)
            }else{
                CouponData.isSel = false
                dataSel.remove(CouponData)
                price_now = price_now + CouponData.amount
            }
//            MyLogUtil.Log("1111","=====price_now1111====="+price_now)
            adapter.notifyDataSetChanged()
            setCouponAmount()
        }
        binding.imgInviteUuse.setOnClickListener {
            dataSel.clear()
            ImageLoad.loadimg(resources.getDrawable(R.drawable.ic_sel_y),binding.imgInviteUuse)
            price_now = price
            datas.forEach {
                it.isSel = false
            }
            adapter.notifyDataSetChanged()
            setCouponAmount()
        }
        binding.btnSubmit.setOnClickListener {
            if (price_now < 0f) price_now = 0f
            MyLogUtil.Log("1111","=====price_now2222====="+price_now+"----"+dataSel.size)
            var ids = ArrayList<String>()
            var priceCoup = 0f
            var typeName = ""//买一送一卡 名称
            dataSel.forEach {
//                MyLogUtil.Log("1111","=============="+it.user_coupon_id)
                ids.add(it.user_coupon_id.toString())
                priceCoup = priceCoup + it.amount
                if (it.type == couponType){
                    typeName = it.type_name
                }
            }
            EventBus.getDefault().post(CouponData(price_now,priceCoup,ids,typeName));
            finish()
        }
        getData()

    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: CouponData) {
    }
    fun getData(){
        if (type == EXTRA_TYPE_RIDECARD){
            var isRentalData = 0
            if (isRental) isRentalData = 1
            ComModel.getRideCardCoupon(id,isRentalData).subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取可用优惠券 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), SelCoupon::class.java)
                    if (resultData != null && resultData.size > 0){
                        setResultData(resultData)
                    }
                },
                onError = {}
            ).toCancelable()
        }else if(type == EXTRA_TYPE_RIDEORDER){
            ComModel.getRideOrderCoupon(id).subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取可用优惠券 信息=="+it.toString())
                    val resultData = Gson().fromJson(it.toString(), SelCoupon::class.java)
                    if (resultData != null && resultData.size > 0){
                        setResultData(resultData)
                    }
                },
                onError = {}
            ).toCancelable()
        }
    }
    fun setResultData(resultData : SelCoupon){
        resultData.forEach {
            var couponData = it
            if (CouponidList.size > 0){
                CouponidList.forEach {
                    if (it.equals(couponData.user_coupon_id.toString())){
                        couponData.isSel = true
                        dataSel.add(couponData)

                        val properties = JSONObject()
                        properties.put("coupon_id",couponData.user_coupon_id)
                        MDUtil.clickEvent("selected_click",properties)
                    }
                }
            }
            datas.add(couponData)
        }
        adapter.source = datas
        adapter.notifyDataSetChanged()
        setCouponAmount()
    }
    //设置 优惠金额
    fun setCouponAmount(){
        if (dataSel.size > 0){
            if (dataSel.get(0).type == couponType){
                binding.tvSelinviteCoupamount.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15f)
                binding.tvSelinviteCoupamount.text = dataSel.get(0).name
                binding.tvSelinviteUnit.visibility = View.GONE
            }else{
                var selAmount = 0f
                dataSel.forEach {
                    selAmount = selAmount + it.amount
                }
                binding.tvSelinviteCoupamount.setTextSize(TypedValue.COMPLEX_UNIT_SP, 24f)
                binding.tvSelinviteCoupamount.text = AppUtil.getFloat2(selAmount)
                binding.tvSelinviteUnit.visibility = View.VISIBLE
            }
        }else{
            binding.tvSelinviteCoupamount.setTextSize(TypedValue.COMPLEX_UNIT_SP, 24f)
            binding.tvSelinviteCoupamount.text = "0.00"
            binding.tvSelinviteUnit.visibility = View.VISIBLE
        }
    }
    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }
}