package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.SpUtil.Companion.getInstance
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.LangAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityLanguageBinding
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.entity.langEntity
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class LanguageActivity : BaseActivity() {
    companion object {
        val LANG_ZH = "简体中文"
        val LANG_EN = "English"
        val LANG_TH = "ภาษาไทย"
        val LANG_ID = "indonesia"
        val LANG_ML = "Malaysia"
        val LANG_KR = "한국어"
    }
    private var adapter: LangAdapter?=null
    private var entityList:ArrayList<langEntity>?= ArrayList()
    var selLanguage = ""
    private lateinit var binding: ActivityLanguageBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLanguageBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_language)
        EventBus.getDefault().register(this)

        setSupportActionBar(binding.appbarLayout.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        binding.appbarLayout.toolbarTitle.text = getString(R.string.s_changelang)
        binding.appbarLayout.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.appbarLayout.toolbar.setNavigationOnClickListener { onBackPressed() }

        initRecycleview()
        getData()
        binding.buttonConfirm.setOnClickListener {
            if(!selLanguage.isNullOrEmpty()){
//                MyToastUtil.toast(selLanguage)
                language_event = selLanguage
                SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_LANG, selLanguage)
//                MyToastUtil.toast(SpUtil.Companion.getInstance().find(Constant.SpKey.SP_LANG).toString())
                binding.buttonConfirm.postDelayed({restartApp(this@LanguageActivity)},200)
            }
        }
    }

    private fun getData() {

        entityList!!.add(langEntity(getString(R.string.s_lang_cn),LANG_ZH,false))
        entityList!!.add(langEntity(getString(R.string.s_lang_en),LANG_EN,false))
        entityList!!.add(langEntity(getString(R.string.s_lang_th),LANG_TH,false))
        entityList!!.add(langEntity(getString(R.string.s_lang_id),LANG_ID,false))
//        entityList!!.add(langEntity(getString(R.string.s_lang_ml), LANG_ML,false))
        entityList!!.add(langEntity(getString(R.string.s_lang_hy), LANG_KR,false))

        for (i in 0 until entityList!!.size) {
            //使用ThreadLocal获取主线程的数据
            val language = getInstance().find(Constant.SpKey.SP_LANG)
            if(entityList!!.get(i).hint.equals(language)){
                entityList!!.get(i).isUse = true
            }
//            if((i == langlist.size - 1) && language.isNullOrEmpty()){
//                entityList!!.get(0).isUse = true
//                selLanguage = entityList!!.get(0).name
//            }
        }
        adapter?.source = entityList!!
    }

    private fun initRecycleview(){
        adapter= LangAdapter()
        adapter!!.setOnMyItemClickListener { langEntity, pos ->
            var itemData = langEntity
            if (!itemData.isUse){
                for (i in 0 until entityList!!.size) {
                    entityList!!.get(i).isUse = false
                }
                entityList!!.get(pos).isUse = true
            }
            selLanguage = itemData.hint
//            if(pos == 0){
//                selLanguage = ""
//            }
            adapter!!.notifyDataSetChanged()
        }

        var manager= LinearLayoutManager(this, RecyclerView.VERTICAL,false)
        binding.ry.layoutManager=manager
        binding.ry.adapter=adapter
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(event: BaseEventData) {
    }

    fun restartApp(context: Context) {
        ComModel.sumitLang().subscribeBy(
            onNext = { MyLogUtil.Log("1111","===sumitLang 信息=="+it.toString()) },
            onError = {}
        ).toCancelable()
        binding.ry.postDelayed({
            LanguageUtil().settingLanguage(ContextUtil.getContext())
            val intent = Intent(this@LanguageActivity, SplashActivity::class.java)
            startActivity(intent)
            android.os.Process.killProcess(android.os.Process.myPid())
        },100)
        EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_EXIT,""))
        finish()
//        LanguageUtil().settingLanguage(ContextUtil.getContext())
//        val intent = Intent(this@LanguageActivity, SplashActivity::class.java)
//        startActivity(intent)
//        android.os.Process.killProcess(android.os.Process.myPid())
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }
}