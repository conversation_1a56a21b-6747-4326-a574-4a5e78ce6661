package com.tbit.uqbike.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.*
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import com.baidu.ar.it
import com.tbit.maintanenceplus.utils.InputMethodUtils
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.bindExtra
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.AdDeposit
import com.tbit.uqbike.bean.User
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.custom.CountryCodeWindow
import com.tbit.uqbike.databinding.ActivityBindPhoneBinding
import com.tbit.uqbike.databinding.ActivityMainBinding
import com.tbit.uqbike.entity.CountryDataItem
import com.tbit.uqbike.mvp.constract.BindPhoneContract
import com.tbit.uqbike.mvp.model.CountryCodeModel
import com.tbit.uqbike.mvp.presenter.BindPhonePresenter
import com.tbit.uqbike.qrcode.CommonUtils
import com.tbit.uqbike.utils.MyLogUtil
import org.jetbrains.anko.*
import java.lang.Exception

class BindPhoneActivity: BaseActivity(), BindPhoneContract.View, TextWatcher {

    companion object {
        private const val REQUEST_INVITE = 1
        private const val EXTRA_USER = "EXTRA_USER"
        private const val EXTRA_IDTOKEN = "EXTRA_IDTOKEN"
        private const val EXTRA_AUTHCODE = "EXTRA_AUTHCODE"

        fun createIntent(context: Context, email: String, idToken: String, authCode: String): Intent {
            return context.intentFor<BindPhoneActivity>(EXTRA_USER to email,EXTRA_IDTOKEN to idToken,EXTRA_AUTHCODE to authCode)
        }

    }

    private val email: String by bindExtra(EXTRA_USER)
    private val idToken: String by bindExtra(EXTRA_IDTOKEN)
    private val authCode: String by bindExtra(EXTRA_AUTHCODE)
    private val presenter = BindPhonePresenter(this)
    private val countryCodeWindow by lazy {
        CountryCodeWindow(this, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }
    private lateinit var binding: ActivityBindPhoneBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBindPhoneBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_bind_phone)

//        setSupportActionBar(toolbar)
//        supportActionBar?.setDisplayShowTitleEnabled(false)
//        toolbar_title.text = getString(R.string.str_bind_phone)
//        toolbar.setNavigationIcon(R.drawable.icon_back)
//        toolbar.setNavigationOnClickListener { onBackPressed() }

        lifecycle.addObserver(presenter)
        binding.ivClose.setOnClickListener { finish() }
        binding.buttonAuthCode.setOnClickListener { sendAutoCode() }
        binding.btnConfirm.setOnClickListener { bindPhone() }
        binding.editPhoneNumber.addTextChangedListener(this)
        binding.imageClearPhone.setOnClickListener { binding.editPhoneNumber.text = null }

        countryCodeWindow.setOnItemClickListener { onSelectCountryCode(it) }
        if(SpUtil.getInstance().find(Constant.SpKey.SP_COUNTRY_SEL).isNullOrEmpty()){
            binding.textCountryCode.text = AppUtil.getPhoneCode()
//            when(FlavorConfig.Local.language) {
//                LanguageUtil.LANG_US -> {binding.textCountryCode.text = countryCodePlus("+66")}
//                LanguageUtil.LANG_ZH -> {binding.textCountryCode.text = countryCodePlus("+86")}
//                LanguageUtil.LANG_TH -> {binding.textCountryCode.text = countryCodePlus("+66")}
//            }
        }else{
            binding.textCountryCode.text = countryCodePlus(CountryCodeModel.countryCodeInfo.country_code)
        }
        binding.textCountryCode.setOnClickListener { showCountryCodeWindow() }
        binding.ivCountryCode.setOnClickListener { showCountryCodeWindow() }
        presenter.updateCountryCodeInfo()

    }

    private fun showCountryCodeWindow() {
//        countryCodeWindow.width = binding.llCountryCode.width
//        countryCodeWindow.showAsDropDown(binding.llCountryCode)

        InputMethodUtils.hideInput(ContextUtil.getContext(),binding.textCountryCode)
        countryCodeWindow.width = CommonUtils.getScreenWidth(this@BindPhoneActivity)
        countryCodeWindow.height = CommonUtils.getScreenHeight(this@BindPhoneActivity)
//        countryCodeWindow.showAsDropDown(ll_country_code)
        countryCodeWindow.showAtLocation(binding.llCountryCode, Gravity.BOTTOM,0,0)
    }

    private fun onSelectCountryCode(countryCodeInfo: CountryDataItem) {
        CountryCodeModel.countryCodeInfo = countryCodeInfo
        binding.textCountryCode.text = countryCodePlus(countryCodeInfo.country_code)
        SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_COUNTRY_SEL,countryCodeInfo.country_code.toString())
        countryCodeWindow.dismiss()
    }

    private fun countryCodePlus(code: String?): String {
        if (code.isNullOrEmpty()) {
            return ""
        }
        return "+"+code.toInt()
    }

    private fun countryCodeNoPlus(code: String?): String {
        return code?.substring(1) ?: ""
    }

    override fun afterTextChanged(s: Editable?) {
        binding.imageClearPhone.visibility = if(binding.editPhoneNumber.text.isEmpty()) View.INVISIBLE else View.VISIBLE
        presenter.getAuthCodeResendDelay(binding.editPhoneNumber.text.toString())
    }

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

    }

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

    }

    override fun onGetAuthCodeResendDelay(time: Long) {
        presenter.startCountdown(time)
    }

    override fun onCountDown(time: Long) {
        if(time > 0) {
            binding.buttonAuthCode.text = getString(R.string.second_with_unit, time)
            binding.buttonAuthCode.isEnabled = false
        } else {
            binding.buttonAuthCode.text = getString(R.string.get_auth_code)
            binding.buttonAuthCode.isEnabled = true
        }
    }

    private fun sendAutoCode() {
        val code = binding.textCountryCode.text.toString()
        if (!checkCountryCode(code)) {
            return
        }
        val countryCode = countryCodeNoPlus(code)
        val phone = binding.editPhoneNumber.text.toString()
        if(checkPhone(phone)) {
            val cancellable = presenter.sendAuthCode(countryCode, phone)
            loadingDialogHelper.show { cancellable.cancel() }
        }
    }

    override fun onSendAutoCodeSuccess() {
        toast(R.string.auth_code_sent)
        loadingDialogHelper.dismiss()
        presenter.startCountdown(60)
    }

    private fun bindPhone() {
        val code = binding.textCountryCode.text.toString()
        if (!checkCountryCode(code)) {
            return
        }
        val countryCode = countryCodeNoPlus(code)
        val phone = binding.editPhoneNumber.text.toString()
        val authCodePhone = binding.editAuthCode.text.toString()
        if(checkPhone(phone) && checkAuthCode(authCodePhone)) {
            val cancellable = presenter.bindPhone(email,idToken,authCode, countryCode, phone, authCodePhone)
            loadingDialogHelper.show { cancellable.cancel() }
        }
    }

    override fun onBindPhoneSuccess() {
        loadingDialogHelper.dismiss()
        toast(R.string.str_bind_success)
        startActivity(MainActivity.createIntent(this,true))
//        getInviteRegisterSwitch(user, adDeposit)
    }

    private fun getInviteRegisterSwitch(user: User, adDeposit: AdDeposit?) {
        val cancellable = presenter.getInviteRegisterSwitch(user, adDeposit)
        loadingDialogHelper.show { cancellable.cancel() }
    }

    override fun onGetInviteRegisterSwitch(isOpen: Boolean, user: User, adDeposit: AdDeposit?) {
        loadingDialogHelper.dismiss()
    }

    private fun checkPhone(phone: String): Boolean {
        var result = true
//        try {
//            val regex = CountryCodeModel.countryCodeInfo?.regex?.let { Regex(it) }
//            result = when {
//                regex != null -> regex.matches(phone)
//                else -> phone.length == 11
//            }
//            if(!result) toast(R.string.phone_num_not_meet_the_rule)
//
//        } catch (e: Exception) {
//            e.printStackTrace()
//            toast(e.message?: getString(R.string.phone_num_not_meet_the_rule))
//        }

        return result
    }

    private fun checkAuthCode(authCode: String): Boolean {
        if(authCode.isEmpty()) {
            toast(R.string.auth_code_can_not_be_empty)
            return false
        }
        return true
    }

    private fun checkCountryCode(countryCode: String): Boolean {
        if(countryCode.isEmpty()) {
            toast(R.string.choose_country_code_hint)
            return false
        }
        return true
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != Activity.RESULT_OK) return
        if (requestCode == REQUEST_INVITE && data != null) {
            val user = data.getSerializableExtra("user") as User
            val adDeposit = data.getSerializableExtra("adDeposit") as AdDeposit?
            val intent = Intent()
            intent.putExtra("user", user)
            intent.putExtra("adDeposit", adDeposit)
            setResult(Activity.RESULT_OK, intent)
            finish()
        }
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }

}