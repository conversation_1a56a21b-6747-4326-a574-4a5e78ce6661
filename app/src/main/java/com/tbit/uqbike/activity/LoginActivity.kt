package com.tbit.uqbike.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import com.baidu.ar.it
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.auth.api.signin.GoogleSignInStatusCodes
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.tasks.Task
import com.google.android.material.tabs.TabLayoutMediator
import com.google.gson.Gson
import com.tbit.maintanenceplus.utils.InputMethodUtils
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.ContextUtil
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.addToComposite
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.Glob
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.RegisterActivity.Companion.TYPE_PWD
import com.tbit.uqbike.activity.RegisterActivity.Companion.TYPE_REG
import com.tbit.uqbike.adapter.NonePageAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.AdDeposit
import com.tbit.uqbike.bean.PhoneInfo
import com.tbit.uqbike.bean.User
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.custom.CountryCodeWindow
import com.tbit.uqbike.databinding.ActivityLoginBinding
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.dialog.CustomPhoneCallDialog
import com.tbit.uqbike.dialog.WebProtocolDialog
import com.tbit.uqbike.entity.CountryData
import com.tbit.uqbike.entity.CountryDataItem
import com.tbit.uqbike.entity.HotLineData
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.entity.pageUrlData
import com.tbit.uqbike.mvp.constract.LoginContract
import com.tbit.uqbike.mvp.model.AuthCodeModel
import com.tbit.uqbike.mvp.model.CountryCodeModel
import com.tbit.uqbike.mvp.model.NetProtocolModel.getProtocolAgreeRecord
import com.tbit.uqbike.mvp.model.NetProtocolModel.protocolAgreeOrSign
import com.tbit.uqbike.mvp.model.UserModel
import com.tbit.uqbike.mvp.model.UserModel.googleLogin
import com.tbit.uqbike.mvp.model.UserModel.loginEmailCode
import com.tbit.uqbike.mvp.presenter.LoginPresenter
import com.tbit.uqbike.qrcode.CommonUtils
import com.tbit.uqbike.resqmodel.ComModel
import com.tbit.uqbike.resqmodel.PageModel
import com.tbit.uqbike.utils.ClickableTextUtil
import com.tbit.uqbike.utils.LoginViewUtil
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.UrlDecodeUtil
import com.tbit.uqbike.widget.SimpleTabLay
import com.tbit.uqbike.widget.SwitchButtonView
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.jetbrains.anko.newTask
import org.jetbrains.anko.singleTop
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.startActivityForResult
import org.jetbrains.anko.toast
import org.json.JSONObject

class LoginActivity: BaseActivity(), LoginContract.View, TextWatcher {

    companion object {
        private const val REQUEST_NAME_AUTH = 1
        private const val REQUEST_SUCCESS_INTENT = 2
        private const val REQUEST_GOOGLE_LOGIN = 3
        private const val REQUEST_BIND_PHONE = 4
        private const val REQUEST_INVITE = 5
        private const val EXTRA_SUCCESS_INTENT = "EXTRA_SUCCESS_INTENT"
        private const val LOGIN_TYPE_PHONE = 0 //手机登录
        val LOGIN_TYPE_PHONE_CODE = 1 //手机验证码登录
        val LOGIN_TYPE_PHONE_PWD = 2 //手机密码登录
        val LOGIN_TYPE_EMAIL = 3 //邮箱登录
        val LOGIN_TYPE_EMAIL_CODE = 4 //邮箱验证码登录
        val LOGIN_TYPE_EMAIL_PWD = 5 //邮箱密码登录

        val LOGIN_MOTHOD_PHONE = "loginPhonePwd"
        val LOGIN_MOTHOD_EMAIL = "loginEmailPwd"

        fun start(context: Context, successIntent: Intent? = null) {
            UserModel.logout()
            val intent = Intent(context, LoginActivity::class.java).newTask().singleTop()
            intent.putExtra(EXTRA_SUCCESS_INTENT, successIntent)
            context.startActivity(intent)
        }
    }
    private var LoginType = LOGIN_TYPE_PHONE
    private var LoginType_phone = LOGIN_TYPE_PHONE_CODE //默认验证码登录
    private var LoginType_email = LOGIN_TYPE_EMAIL_CODE //默认验证码登录

    private val presenter = LoginPresenter(this)
    private val successIntent: Intent? by bindExtra(EXTRA_SUCCESS_INTENT)
    private val countryCodeWindow by lazy {
        CountryCodeWindow(this, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }
    private lateinit var googleSignInClient: GoogleSignInClient
    private var protocolDialog: WebProtocolDialog? = null
    private var forceReadDuration: Int = Constant.DEFAULT_FORCE_READ_DURATION
    private var protocolPassType: Int? = null
//    private val SERVER_CLIENT_ID = "986299323470-3vmd92l3c3i5hnb77ocps9ev7469vl54.apps.googleusercontent.com"
    private val SERVER_CLIENT_ID = "986299323470-krmlvfvmld9lfdegn3hvlo7v3bpbmqlt.apps.googleusercontent.com"
    private lateinit var binding: ActivityLoginBinding
    @RequiresApi(Build.VERSION_CODES.M)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_login)
//        setSupportActionBar(toolbar)
        lifecycle.addObserver(presenter)
        initGoogleSignIn()
        LoginType_Event = "1"

        var tabLayoutMediator: TabLayoutMediator? = null
        var adapters: NonePageAdapter? = null
        var type:Array<String>? = null
        type = arrayOf(getString(R.string.s_phone), getString(R.string.s_email))
        adapters = NonePageAdapter(this@LoginActivity, type!!)
        binding.vpProps.adapter = adapters
        tabLayoutMediator = TabLayoutMediator(binding.tabLayout!!, binding.vpProps, true,true) { tab, position ->
            binding.tabLayout.initTab(tab, type!![position], SimpleTabLay.TYPE_LOGIN) }
        tabLayoutMediator!!.attach()
        binding.tabLayout.setSelectListener(object : SimpleTabLay.SelectListener{
            override fun onTabClick(pos: Int) {
                super.onTabClick(pos)
                when(pos){
                    0 -> {
                        val properties = JSONObject()
                        MDUtil.clickEvent("mobile_tab_click",properties)
                        LoginType = LOGIN_TYPE_PHONE

                        binding.inclLoginPhone.lyLayLoginPhone.visibility = View.VISIBLE
                        binding.inclLoginEmail.lyLayLoginEmail.visibility = View.GONE
//                        binding.inclLoginPhone.visibility = View.VISIBLE
//                        binding.inclLoginEmail.visibility = View.GONE
                    }
                    1 -> {
                        val properties = JSONObject()
                        MDUtil.clickEvent("email_tab_click",properties)
                        LoginType = LOGIN_TYPE_EMAIL
                        binding.inclLoginPhone.lyLayLoginPhone.visibility = View.GONE
                        binding.inclLoginEmail.lyLayLoginEmail.visibility = View.VISIBLE
                    }
                }
            }
        })

        binding.ivKf.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("contact_icon_click",properties)
            loadingDialogHelper.show {  }
            val listKey = arrayOf("customer_service_number","line_id","facebook_url")
            ComModel.getConfig(listKey).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyLogUtil.Log("1111","===获取客服电话 信息=="+it.toString())
                    var resultData = Gson().fromJson(it.toString(), HotLineData::class.java)
                    var phoneInfoList = ArrayList<PhoneInfo>()
                    if (!resultData.customer_service_number.isNullOrEmpty()){
                        phoneInfoList.add(PhoneInfo(resultData.customer_service_number, Constant.CustomerServiceType.PHONE))
                    }
                    if (!resultData.line_id.isNullOrEmpty()){
                        phoneInfoList.add(PhoneInfo(resultData.line_id, Constant.CustomerServiceType.LINE))
                    }
                    if (!resultData.facebook_url.isNullOrEmpty()){
                        phoneInfoList.add(PhoneInfo(resultData.facebook_url, Constant.CustomerServiceType.FACEBOOK))
                    }
                    lifecycleDialogHelper.show(CustomPhoneCallDialog.newInstance(phoneInfoList))
                },
                onError = {loadingDialogHelper.dismiss()}
            ).toCancelable()
        }
        binding.inclLoginPhone.buttonAuthCode.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("verification_code_click",properties)
            sendAutoCode()
        }
        binding.btnLogin.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("log_in_click",properties)
            if (Glob.areaIdReqs && Glob.CruuencylldReqs){
                when(LoginType){
                    LOGIN_TYPE_PHONE->{
                        if (LoginType_phone == LOGIN_TYPE_PHONE_CODE){
                            loginPhoneCode()
                            LoginType_Event = "1"
                        }else{
                            loginPhonePwd()
                            LoginType_Event = "5"
                        }
                    }
                    LOGIN_TYPE_EMAIL->{
                        if (LoginType_email == LOGIN_TYPE_EMAIL_CODE){
                            loginEmailCode()
                            LoginType_Event = "2"
                        }else{
                            loginEmailPwd()
                            LoginType_Event = "5"
                        }
                    }
                }
            }else{
                MyToastUtil.toast(getString(R.string.s_check_local))
            }
        }
        binding.inclLoginPhone.editAuthCode.addTextChangedListener(this)
        binding.inclLoginPhone.editPhoneNumber.addTextChangedListener(this)
        binding.inclLoginPhone.editPhoneNumber.setOnFocusChangeListener { v, hasFocus ->
            val phone = binding.inclLoginPhone.editPhoneNumber.text.toString()
            if (!hasFocus && phone.isNotEmpty()) {
                checkPhone(phone)
            }
        }
        initUserAgreementUI()
        countryCodeWindow.setOnItemClickListener { onSelectCountryCode(it) }
        binding.inclLoginPhone.ivCountryCode.setOnClickListener { binding.inclLoginPhone.textCountryCode.performClick() }
        binding.inclLoginPhone.textCountryCode.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("area_code_click",properties)
            showCountryCodeWindow()
        }
        binding.inclLoginPhone.imageClearPhone.setOnClickListener { binding.inclLoginPhone.editPhoneNumber.text = null }


        var isHintPhonePwd = true
        var oldPhoneCode = ""
        var oldPhonePwd = ""
        binding.inclLoginPhone.tvLoginPhoneMode.setOnClickListener {
            if (LoginType_phone == LOGIN_TYPE_PHONE_CODE){
                //手机密码登录
                val properties = JSONObject()
                MDUtil.clickEvent("password_login_click",properties)
                oldPhoneCode = binding.inclLoginPhone.editAuthCode.text.toString()
                LoginType_phone = LOGIN_TYPE_PHONE_PWD
                LoginViewUtil.setLoginMode_pwd(binding.inclLoginPhone.tvLoginPhoneMode,binding.inclLoginPhone.buttonAuthCode,
                    binding.inclLoginPhone.imagePhonePwd,binding.inclLoginPhone.editAuthCode,oldPhonePwd,isHintPhonePwd)
            }else{
                //手机验证码登录
                oldPhonePwd = binding.inclLoginPhone.editAuthCode.text.toString()
                LoginType_phone = LOGIN_TYPE_PHONE_CODE
                LoginViewUtil.setLoginMode_code(binding.inclLoginPhone.tvLoginPhoneMode,binding.inclLoginPhone.buttonAuthCode,
                    binding.inclLoginPhone.imagePhonePwd,binding.inclLoginPhone.editAuthCode,oldPhoneCode)
            }
        }
        binding.inclLoginPhone.imagePhonePwd.setOnClickListener {
            isHintPhonePwd = !isHintPhonePwd
            if (isHintPhonePwd){
                LoginViewUtil.hidePwd(binding.inclLoginPhone.editAuthCode,binding.inclLoginPhone.imagePhonePwd)
            }else{
                LoginViewUtil.showPwd(binding.inclLoginPhone.editAuthCode,binding.inclLoginPhone.imagePhonePwd)
            }
        }

        binding.inclLoginEmail.editEmailNumber.addTextChangedListener(this)
        binding.inclLoginEmail.editEmailPwd.addTextChangedListener(this)
        binding.inclLoginEmail.imageClearEmail.setOnClickListener { binding.inclLoginEmail.editEmailNumber.text = null }
        var isHintPwd = true
        var oldEmailCode = ""
        var oldEmailPwd = ""
        binding.inclLoginEmail.tvLoginEmailMode.setOnClickListener {
            if (LoginType_email == LOGIN_TYPE_EMAIL_CODE){
                //邮箱密码登录
                val properties = JSONObject()
                MDUtil.clickEvent("password_login_click",properties)
                oldEmailCode = binding.inclLoginEmail.editEmailPwd.text.toString()
                LoginType_email = LOGIN_TYPE_EMAIL_PWD
                LoginViewUtil.setLoginMode_pwd(binding.inclLoginEmail.tvLoginEmailMode,binding.inclLoginEmail.buttonEmailCode,
                    binding.inclLoginEmail.imageEmailPwd,binding.inclLoginEmail.editEmailPwd,oldEmailPwd,isHintPwd)
            }else{
                //邮箱验证码登录
                oldEmailPwd = binding.inclLoginEmail.editEmailPwd.text.toString()
                LoginType_email = LOGIN_TYPE_EMAIL_CODE
                LoginViewUtil.setLoginMode_code(binding.inclLoginEmail.tvLoginEmailMode,binding.inclLoginEmail.buttonEmailCode,
                    binding.inclLoginEmail.imageEmailPwd,binding.inclLoginEmail.editEmailPwd,oldEmailCode)
            }
        }
        binding.inclLoginEmail.imageEmailPwd.setOnClickListener {
            isHintPwd = !isHintPwd
            if (isHintPwd){
                LoginViewUtil.hidePwd(binding.inclLoginEmail.editEmailPwd,binding.inclLoginEmail.imageEmailPwd)
            }else{
                LoginViewUtil.showPwd(binding.inclLoginEmail.editEmailPwd,binding.inclLoginEmail.imageEmailPwd)
            }
        }
        binding.inclLoginEmail.buttonEmailCode.setOnClickListener {
            if (binding.inclLoginEmail.editEmailNumber.text.toString().isNullOrEmpty()){
                MyToastUtil.toast(getString(R.string.s_input_email))
                return@setOnClickListener
            }
            loadingDialogHelper?.show{}
            AuthCodeModel.sendEmailAuthCode(binding.inclLoginEmail.editEmailNumber.text.toString())
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper.dismiss()
                        presenter.startCountdownEmail(60)
                    },
                    onError = {
                        loadingDialogHelper.dismiss()
                        val errMsg = ErrHandler.getErrMsg(it)
                    }
                ).toCancelable()
        }


        binding.tvLoginReg.setOnClickListener {
            when(LoginType){
                LOGIN_TYPE_PHONE->startActivity(RegisterActivity.createIntent(this,TYPE_REG,RegisterActivity.TYPE_SEL_PHONE,false))
                LOGIN_TYPE_EMAIL->startActivity(RegisterActivity.createIntent(this,TYPE_REG,RegisterActivity.TYPE_SEL_EMAIL,false))
            }
        }
        binding.inclLoginPhone.tvLoginPhoneForgetpwd.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("forget_password_click",properties)
            startActivity(RegisterActivity.createIntent(this,TYPE_PWD,RegisterActivity.TYPE_SEL_PHONE,false))
        }
        binding.inclLoginEmail.tvLoginEmailForgetpwd.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("forget_password_click",properties)
            startActivity(RegisterActivity.createIntent(this,TYPE_PWD,RegisterActivity.TYPE_SEL_EMAIL,false))
        }

        binding.btnGoogle.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("google_icon_click",properties)
            LoginType_Event = "3"
            if (!checkAgreementTick()) return@setOnClickListener
            val lastSignedInAccount = GoogleSignIn.getLastSignedInAccount(this)
            if (lastSignedInAccount != null) {
                var email = ""
                if (lastSignedInAccount.email != null) email = lastSignedInAccount.email!!
                googleLogin(email,lastSignedInAccount.id,lastSignedInAccount.idToken!!,lastSignedInAccount.serverAuthCode!!)
            } else {
                startActivityForResult(googleSignInClient.signInIntent, REQUEST_GOOGLE_LOGIN)
            }
//            googleLogin("oauth_unique_email2","0000002","oauth_token2","oauth_code2")
        }
        binding.ivClose.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("close_icon_click",properties)
            finish()
        }

        getProtocolPassMethodBeforeLogin()
        updateCountryCodeInfo()
        if (!SpUtil.getInstance().find(Constant.SpKey.SP_FIXPWD).isNullOrEmpty()){
            if (!SpUtil.getInstance().find(Constant.SpKey.SP_LOGINTYPE).isNullOrEmpty()){
                var loginType = SpUtil.getInstance().find(Constant.SpKey.SP_LOGINTYPE)
                MyLogUtil.Log("1111","===="+loginType)
                if (loginType!!.equals(LOGIN_MOTHOD_PHONE)){
                    binding.tabLayout.postDelayed({binding.inclLoginPhone.tvLoginPhoneMode.performClick()},50)
                }else if(loginType!!.equals(LOGIN_MOTHOD_EMAIL)){
                    binding.tabLayout.postDelayed({binding.tabLayout.getTabAt(1)!!.view.performClick()},50)
                    binding.tabLayout.postDelayed({binding.inclLoginEmail.tvLoginEmailMode.performClick()},100)
                }
            }
            SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_FIXPWD, "");
        }

        binding.tvLoginHint.setOnClickListener { ClickButton() }
        binding.ivSwitchOn.setOnBtnClick(object : SwitchButtonView.Btnclick{
            override fun selState(isSel: Boolean?) {
                if (isSel!!){
                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_TESTNET, "1");
                }else{
                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_TESTNET, "");
                }
                binding.ivSwitchOn.postDelayed({restartApp(this@LoginActivity)},500)
            }
        })
    }
    fun restartApp(context: Context) {
        EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_EXIT,""))
        finish()
        val intent = Intent(this@LoginActivity, SplashActivity::class.java)
        startActivity(intent)
        android.os.Process.killProcess(android.os.Process.myPid())
    }
    private fun initGoogleSignIn() {
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestId()
            .requestServerAuthCode(SERVER_CLIENT_ID)
            .requestIdToken(SERVER_CLIENT_ID)
            .requestEmail()
            .build()
        googleSignInClient = GoogleSignIn.getClient(this, gso)

        val lastSignedInAccount = GoogleSignIn.getLastSignedInAccount(this)
        if (lastSignedInAccount != null) {
            //已登录谷歌账户,退出登录
            googleSignInClient.signOut().addOnCompleteListener {
                if (it.isSuccessful) {
                    Log.d("dd","========退出谷歌登录成功")
                }
            }
        }
    }

    private inline fun initUserAgreementUI() {
        ClickableTextUtil.makeClickable(binding.tvUserAgreement, getString(R.string.str_privacy_policy), ::showPrivacyPolicy)
        ClickableTextUtil.makeClickable(binding.tvUserAgreement, getString(R.string.str_service_terms), ::showServiceTerms)
    }

    private inline fun showCountryCodeWindow() {
        loadingDialogHelper.show {  }
        CountryCodeModel.getAllCountryCodeInfoFromNet()
            .subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    val resultData: CountryData = Gson().fromJson(it.toString(), CountryData::class.java)
//                    view.onGetCountryCodeInfoSuccess(resultData)
                    setCountryCodeDatas(resultData)

                    InputMethodUtils.hideInput(ContextUtil.getContext(),binding.inclLoginPhone.textCountryCode)
                    countryCodeWindow.width = CommonUtils.getScreenWidth(this@LoginActivity)
                    countryCodeWindow.height = CommonUtils.getScreenHeight(this@LoginActivity)
//        countryCodeWindow.showAsDropDown(ll_country_code)
                    countryCodeWindow.showAtLocation(binding.inclLoginPhone.llCountryCode, Gravity.BOTTOM,0,0)
                },
                onError = {
                    loadingDialogHelper.dismiss()
                }
            ).toCancelable()
    }

    private inline fun onSelectCountryCode(countryCodeInfo: CountryDataItem) {
        CountryCodeModel.countryCodeInfo = countryCodeInfo
        binding.inclLoginPhone.textCountryCode.text = countryCodePlus(countryCodeInfo.country_code)
        SpUtil.getInstance().saveOrUpdate(Constant.SpKey.SP_COUNTRY_SEL,countryCodeInfo.country_code.toString())
        countryCodeWindow.dismiss()
    }

    private fun countryCodePlus(code: String?): String {
        if (code.isNullOrEmpty()) {
            return ""
        }
        return "+"+code.toInt()
    }

    private fun countryCodeNoPlus(code: String?): String {
        return code?.substring(1) ?: ""
    }

    private fun showPrivacyPolicy() {
        val properties = JSONObject()
        MDUtil.clickEvent("user_agreement_click",properties)
        loadingDialogHelper.show {  }
        PageModel.getPageUrl(PageModel.privacy_policy).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                startActivity<WebActivity>(
                    WebActivity.TITLE to getString(R.string.str_privacy_policy).replace("《","").replace("》",""),
                    WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
            },
            onError = {loadingDialogHelper.dismiss()}
        ).toCancelable()
    }

    private fun showServiceTerms() {
        val properties = JSONObject()
        MDUtil.clickEvent("privacy_policy_click",properties)
        loadingDialogHelper.show {  }
        PageModel.getPageUrl(PageModel.user_policy).subscribeBy(
            onNext = {
                loadingDialogHelper.dismiss()
                MyLogUtil.Log("1111","===获取说明页链接 信息=="+it.toString())
                var resultData = Gson().fromJson(it.toString(), pageUrlData::class.java)
                startActivity<WebActivity>(
                    WebActivity.TITLE to getString(R.string.str_service_terms).replace("《","").replace("》",""),
                    WebActivity.URL to UrlDecodeUtil().getParm(resultData.url))
            },
            onError = {loadingDialogHelper.dismiss()}
        ).toCancelable()
    }

    override fun afterTextChanged(s: Editable?) {
        when(LoginType){
            LOGIN_TYPE_PHONE->{
                binding.inclLoginPhone.imageClearPhone.visibility = if(binding.inclLoginPhone.editPhoneNumber.text.isEmpty()) View.INVISIBLE else View.VISIBLE
                if (binding.inclLoginPhone.editPhoneNumber.text.toString().length > 0){
                    binding.inclLoginPhone.editPhoneNumber.setTextSize(TypedValue.COMPLEX_UNIT_SP, 19f)
                }else{
                    binding.inclLoginPhone.editPhoneNumber.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15f)
                }
                if (binding.inclLoginPhone.editAuthCode.text.toString().length > 0 && binding.inclLoginPhone.editPhoneNumber.text.toString().length > 0){
                    binding.btnLogin.isEnabled = true
                    binding.btnLogin.setTextColor(resources.getColor(R.color.white))
                }else{
                    binding.btnLogin.isEnabled = false
                    binding.btnLogin.setTextColor(resources.getColor(R.color.white50))
                }
            }
            LOGIN_TYPE_EMAIL->{
                binding.inclLoginEmail.imageClearEmail.visibility = if(binding.inclLoginEmail.editEmailNumber.text.isEmpty()) View.INVISIBLE else View.VISIBLE
                if (binding.inclLoginEmail.editEmailNumber.text.toString().length > 0){
                    binding.inclLoginEmail.editEmailNumber.setTextSize(TypedValue.COMPLEX_UNIT_SP, 19f)
                }else{
                    binding.inclLoginEmail.editEmailNumber.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15f)
                }
                if (binding.inclLoginEmail.editEmailPwd.text.toString().length > 0 && binding.inclLoginEmail.editEmailNumber.text.toString().length > 0){
                    binding.btnLogin.isEnabled = true
                    binding.btnLogin.setTextColor(resources.getColor(R.color.white))
                }else{
                    binding.btnLogin.isEnabled = false
                    binding.btnLogin.setTextColor(resources.getColor(R.color.white50))
                }
            }
        }

//        presenter.getAuthCodeResendDelay(edit_phone_number.text.toString())
    }
    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
    private fun updateCountryCodeInfo() {
        presenter.updateCountryCodeInfo()
    }
    override fun onGetCountryCodeInfoSuccess(data: List<CountryDataItem>) {
        setCountryCodeDatas(data)
    }
    fun setCountryCodeDatas(data: List<CountryDataItem>){
        if(SpUtil.getInstance().find(Constant.SpKey.SP_COUNTRY_SEL).isNullOrEmpty()){
            binding.inclLoginPhone.textCountryCode.text = AppUtil.getPhoneCode()
        }else{
            val oldCode = CountryCodeModel.countryCodeInfo.country_code
            MyLogUtil.Log("1111","国家码数据"+oldCode)
            val target = data.firstOrNull {
                it.country_code == oldCode }
            if (target != null) {
                var code = oldCode.toInt()
                binding.inclLoginPhone.textCountryCode.text = countryCodePlus(code.toString())
            }
        }
    }

    override fun onGetAuthCodeResendDelay(time: Long) {
        presenter.startCountdown(time)
    }

    override fun onCountDown(time: Long) {
        if(time > 0) {
            binding.inclLoginPhone.buttonAuthCode.setTextColor(resources.getColor(R.color.blue_namal))
            binding.inclLoginPhone.buttonAuthCode.text = getString(R.string.second_with_unit, time)
            binding.inclLoginPhone.buttonAuthCode.isEnabled = false
        } else {
            binding.inclLoginPhone.buttonAuthCode.setTextColor(resources.getColor(R.color.blue_namal))
            binding.inclLoginPhone.buttonAuthCode.text = getString(R.string.get_auth_code)
            binding.inclLoginPhone.buttonAuthCode.isEnabled = true
            val code = binding.inclLoginPhone.editAuthCode.text.toString()
            if (code.isEmpty()) {
//                longToast(getString(R.string.str_auth_code_receive_hint))
            }
        }
    }
    override fun onCountDownEmail(time: Long) {
        if(time > 0) {
            binding.inclLoginEmail.buttonEmailCode.setTextColor(resources.getColor(R.color.blue_namal))
            binding.inclLoginEmail.buttonEmailCode.text = getString(R.string.second_with_unit, time)
            binding.inclLoginEmail.buttonEmailCode.isEnabled = false
        } else {
            binding.inclLoginEmail.buttonEmailCode.setTextColor(resources.getColor(R.color.blue_namal))
            binding.inclLoginEmail.buttonEmailCode.text = getString(R.string.get_auth_code)
            binding.inclLoginEmail.buttonEmailCode.isEnabled = true
            val code = binding.inclLoginEmail.editEmailPwd.text.toString()
            if (code.isEmpty()) {
//                longToast(getString(R.string.str_auth_code_receive_hint))
            }
        }
    }

    private fun sendAutoCode() {
        val code = binding.inclLoginPhone.textCountryCode.text.toString()
        if (!checkCountryCode(code)) {
            return
        }
        val countryCode = countryCodeNoPlus(code)
        val phone = binding.inclLoginPhone.editPhoneNumber.text.toString()
        if(checkPhone(phone)) {
            val cancellable = presenter.sendAuthCode(countryCode, phone)
            loadingDialogHelper?.show { cancellable.cancel() }
        }
    }

    override fun onSendAutoCodeSuccess() {
        toast(R.string.auth_code_sent)
        loadingDialogHelper?.dismiss()
        presenter.startCountdown(60)
    }

    private fun loginPhoneCode() {
        val code = binding.inclLoginPhone.textCountryCode.text.toString()
        if (!checkCountryCode(code)) {
            return
        }
        val countryCode = countryCodeNoPlus(code)
        val phone = binding.inclLoginPhone.editPhoneNumber.text.toString()
        val authCode = binding.inclLoginPhone.editAuthCode.text.toString()
        if(checkPhone(phone) && checkAuthCode(authCode) && checkAgreementTick()) {
            MyLogUtil.Log("1111","===手机验证码登录==="+countryCode+","+phone+","+authCode)
            val cancellable = presenter.login(phone, countryCode, authCode.toInt())
            SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_LOGINTYPE, LOGIN_MOTHOD_PHONE);
            // Disable the loading dialog here to avoid flicker after login
            /*
            loadingDialogHelper?.show { cancellable.cancel() }
            */
        }
    }
    private fun loginPhonePwd(){
        val code = binding.inclLoginPhone.textCountryCode.text.toString()
        if (!checkCountryCode(code)) {
            return
        }
        val countryCode = countryCodeNoPlus(code)
        val phone = binding.inclLoginPhone.editPhoneNumber.text.toString()
        val authCode = binding.inclLoginPhone.editAuthCode.text.toString()
        if(checkPhone(phone) && checkAuthCode(authCode) && checkAgreementTick()) {
            MyLogUtil.Log("1111","===手机密码登录==="+countryCode+","+phone+","+authCode)
            loadingDialogHelper.show { false }
            UserModel.loginPwdPhone(phone, countryCode,authCode)
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper.dismiss()
                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_LOGINTYPE, LOGIN_MOTHOD_PHONE);
                        onLoginSuccess()
                    },
                    onError = {
                        loadingDialogHelper.dismiss()
                        val errMsg = ErrHandler.getErrMsg(it)
                        showErrMsg(errMsg)
                        val errCode = ErrHandler.getErrCode(it)
                        onLoginError(errCode)
                    }
                ).toCancelable()
        }
    }
    private fun loginEmailCode() {
        val emailNum = binding.inclLoginEmail.editEmailNumber.text.toString()
        val emailPwd = binding.inclLoginEmail.editEmailPwd.text.toString()
        if(checkAgreementTick()) {
            MyLogUtil.Log("1111","==邮箱验证码登录=="+emailNum+","+emailPwd)
            loadingDialogHelper.show { false }
            UserModel.loginEmailCode(emailNum, emailPwd.toInt())
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper.dismiss()
                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_LOGINTYPE, LOGIN_MOTHOD_EMAIL);
                        onLoginSuccess()
                    },
                    onError = {
                        loadingDialogHelper.dismiss()
                        val errMsg = ErrHandler.getErrMsg(it)
                        showErrMsg(errMsg)
                        val errCode = ErrHandler.getErrCode(it)
                        onLoginError(errCode)
                    }
                ).toCancelable()
        }
    }
    private fun loginEmailPwd() {
        val emailNum = binding.inclLoginEmail.editEmailNumber.text.toString()
        val emailPwd = binding.inclLoginEmail.editEmailPwd.text.toString()
        if(checkAgreementTick()) {
            MyLogUtil.Log("1111","==邮箱密码登录=="+emailNum+","+emailPwd)
            loadingDialogHelper.show { false }
            UserModel.loginPwdEmail(emailNum, emailPwd)
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper.dismiss()
                        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_LOGINTYPE, LOGIN_MOTHOD_EMAIL);
                        onLoginSuccess()
                    },
                    onError = {
                        loadingDialogHelper.dismiss()
                        val errMsg = ErrHandler.getErrMsg(it)
                        showErrMsg(errMsg)
                        val errCode = ErrHandler.getErrCode(it)
                        onLoginError(errCode)
                    }
                ).toCancelable()
        }
    }
    override fun onLoginSuccess() {
        loadingDialogHelper.dismiss()
        ComModel.sumitLang().subscribeBy(
            onNext = { MyLogUtil.Log("1111","===sumitLang 信息=="+it.toString()) },
            onError = {}
        ).toCancelable()
        SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_FIXPWD, "");
        startActivity(HomeActivity.createIntent(this,true))
    }

    override fun onLoginError(code: Int) {
        loadingDialogHelper.dismiss()
        if(code == Constant.ErrCode.LOGIN_BLACK || code == Constant.ErrCode.LOGIN_CHECK){
            var cont = ""
            if(code == Constant.ErrCode.LOGIN_BLACK){
                //黑名单
                cont = getString(R.string.s_login_black)
            }else if(code == Constant.ErrCode.LOGIN_CHECK){
                //用户注销
                cont = getString(R.string.s_login_signout)
            }
            CommDialog.Builder(this).setTitle(ResUtil.getString(R.string.dialog_tip)).setContent(cont)
                .setLeftText(ResUtil.getString(R.string.contact_customer_service)).setRightText(getString(R.string.i_know)).setCanceledOnOutside(true)
                .setClickListen(object : CommDialog.TwoSelDialog {
                    override fun leftClick() {
                        loadingDialogHelper.show {  }
                        val listKey = arrayOf("customer_service_number","line_id","facebook_url")
                        ComModel.getConfig(listKey).subscribeBy(
                            onNext = {
                                loadingDialogHelper.dismiss()
                                MyLogUtil.Log("1111","===获取客服电话 信息=="+it.toString())
                                var resultData = Gson().fromJson(it.toString(), HotLineData::class.java)
                                var phoneInfoList = ArrayList<PhoneInfo>()
                                if (!resultData.customer_service_number.isNullOrEmpty()){
                                    phoneInfoList.add(PhoneInfo(resultData.customer_service_number, Constant.CustomerServiceType.PHONE))
                                }
                                if (!resultData.line_id.isNullOrEmpty()){
                                    phoneInfoList.add(PhoneInfo(resultData.line_id, Constant.CustomerServiceType.LINE))
                                }
                                if (!resultData.facebook_url.isNullOrEmpty()){
                                    phoneInfoList.add(PhoneInfo(resultData.facebook_url, Constant.CustomerServiceType.FACEBOOK))
                                }
                                lifecycleDialogHelper.show(CustomPhoneCallDialog.newInstance(phoneInfoList))
                            },
                            onError = {loadingDialogHelper.dismiss()}
                        ).toCancelable()
                    }
                    override fun rightClick() {}
                }).build().show()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_NAME_AUTH && successIntent != null) {
            startActivityForResult(successIntent, REQUEST_SUCCESS_INTENT)
            return
        }

        if (requestCode in listOf(REQUEST_NAME_AUTH, REQUEST_SUCCESS_INTENT)) {
            startActivity(HomeActivity.createIntent(this,true))
            return
        }

        if (requestCode == REQUEST_GOOGLE_LOGIN) {
            val task = GoogleSignIn.getSignedInAccountFromIntent(data)
            handleSignInResult(task)
            return
        }

        if (requestCode == REQUEST_BIND_PHONE && resultCode == Activity.RESULT_OK && data != null) {
            val user = data.getSerializableExtra("user") as User
            val adDeposit = data.getSerializableExtra("adDeposit") as AdDeposit?
            getProtocolPassMethodIfNeed(user, adDeposit)
        }

        if (requestCode == REQUEST_INVITE && resultCode == Activity.RESULT_OK && data != null) {
            val user = data.getSerializableExtra("user") as User
            val adDeposit = data.getSerializableExtra("adDeposit") as AdDeposit?
            getProtocolPassMethodIfNeed(user, adDeposit)
        }
    }

    private fun handleSignInResult(completedTask: Task<GoogleSignInAccount>) {
        try {
            //登录成功
            val account = completedTask.getResult(ApiException::class.java)
            Log.d("dd","===========谷歌登录成功，email=${account.email},id=${account.id},idtoken=${account.idToken}")
            var email = ""
            if (account.email != null) email = account.email!!
            googleLogin(email,account.id,account.idToken!!,account.serverAuthCode!!)
        } catch (e: ApiException) {
            // The ApiException status code indicates the detailed failure reason.
            // Please refer to the GoogleSignInStatusCodes class reference for more information.
            Log.e("dd", "==========谷歌signInResult:failed code=" + e.statusCode)
            val msg = when (e.statusCode) {
                GoogleSignInStatusCodes.SIGN_IN_CANCELLED -> getString(R.string.str_google_sign_in_cancel)
                GoogleSignInStatusCodes.SIGN_IN_FAILED -> getString(R.string.str_google_sign_in_failed)
                GoogleSignInStatusCodes.SIGN_IN_CURRENTLY_IN_PROGRESS -> getString(R.string.str_google_signing_in)
                else -> GoogleSignInStatusCodes.getStatusCodeString(e.statusCode)
            }
            toast(msg)
        }
    }

    private fun googleLogin(email : String,id: String?,IdToken: String,ServerAuthCode: String) {
        val cancellable = presenter.googleLogin(email,id,IdToken, ServerAuthCode)
        loadingDialogHelper.show { cancellable.cancel() }
    }

    override fun onGoogleLoginSuccess(isFirst: Int,email: String,IdToken: String,ServerAuthCode: String) {
        loadingDialogHelper.dismiss()
        ComModel.sumitLang().subscribeBy(
            onNext = { MyLogUtil.Log("1111","===sumitLang 信息=="+it.toString()) },
            onError = {}
        ).toCancelable()
        if (isFirst == 1){
//            toast(getString(R.string.str_bind_phone_hint))
            startActivityForResult(BindPhoneActivity.createIntent(this, email,IdToken,ServerAuthCode), REQUEST_BIND_PHONE)
        }else if(isFirst == 2){
            startActivity(RegisterActivity.createIntent(this,TYPE_REG,RegisterActivity.TYPE_SEL_PHONE,true))
            RegisterActivity.oauth_token = IdToken
            RegisterActivity.oauth_code = ServerAuthCode
            RegisterActivity.oauth_unique_email = email
        }else{
            startActivity(HomeActivity.createIntent(this,true))
        }
    }

    override fun onGetInviteRegisterSwitchSuccess(isOpen: Boolean, user: User, adDeposit: AdDeposit?) {
        loadingDialogHelper.dismiss()
    }

    override fun onGetInviteRegisterSwitchFail(user: User, adDeposit: AdDeposit?) {
        loadingDialogHelper.dismiss()
        getProtocolPassMethodIfNeed(user, adDeposit)
    }

    override fun onBackPressed() {
        startActivity(HomeActivity.createIntent(this))
    }

    private fun checkPhone(phone: String): Boolean {
        if (phone.isEmpty()) {
            toast(R.string.phone_number_hint)
            return false
        }
        var result = true
        return result
    }

    private fun checkAuthCode(authCode: String): Boolean {
        if(authCode.isEmpty()) {
            toast(R.string.auth_code_can_not_be_empty)
            return false
        }
        return true
    }

    private fun checkCountryCode(countryCode: String): Boolean {
        if(countryCode.isEmpty()) {
            toast(R.string.choose_country_code_hint)
            return false
        }
        return true
    }

    private fun checkAgreementTick(): Boolean {
        if (binding.textUserAgreement.visibility == View.VISIBLE && !binding.textUserAgreement.isChecked) {
            toast(getString(R.string.str_tick_privacy_and_agreement_tips))
            return false
        }
        return true
    }

    private fun getProtocolPassMethodBeforeLogin() {
        presenter.getProtocolPassMethodBeforeLogin()
    }

    override fun onGetProtocolPassMethodBeforeLoginSuccess(type: Int, forceReadDuration: Int) {
        protocolPassType = type
        this.forceReadDuration = forceReadDuration
    }

    private fun getProtocolPassMethodIfNeed(user: User, adDeposit: AdDeposit?) {
        presenter.updateStripePayPubKey()
        presenter.updatePaypalConfig()

        if (protocolPassType != null) {
            if (protocolPassType == Constant.ProtocolPassMethod.TICK_AGREE) {
                protocolAgreeOrSign(protocolPassType!!, user, adDeposit)
            } else {
                getProtocolAgreeRecord(protocolPassType!!, user, adDeposit)
            }
        } else {
            val cancellable = presenter.getProtocolPassMethod(user, adDeposit)
            loadingDialogHelper.show { cancellable.cancel() }
        }
    }

    override fun onGetProtocolPassMethodSuccess(type: Int, forceReadDuration: Int, user: User, adDeposit: AdDeposit?) {
        loadingDialogHelper.dismiss()
        this.forceReadDuration = forceReadDuration
        if (type == Constant.ProtocolPassMethod.TICK_AGREE) {
//            toast(R.string.login_success)
            protocolAgreeOrSign(type, user, adDeposit)
        } else {
            getProtocolAgreeRecord(type, user, adDeposit)
        }
    }

    override fun onGetProtocolPassMethodFailed(user: User, adDeposit: AdDeposit?) {
        loadingDialogHelper.dismiss()
        loginNext(user, adDeposit)
    }

    private fun protocolAgreeOrSign(type: Int, user: User, adDeposit: AdDeposit?) {
        val cancellable = presenter.protocolAgreeOrSign(type, user, adDeposit)
        loadingDialogHelper.show { cancellable.cancel() }
    }

    override fun onProtocolAgreeOrSign(user: User, adDeposit: AdDeposit?) {
        loadingDialogHelper.dismiss()
        protocolDialog?.dismissAllowingStateLoss()
        loginNext(user, adDeposit)
    }

    private fun getProtocolAgreeRecord(type: Int, user: User, adDeposit: AdDeposit?) {
        val cancellable = presenter.getProtocolAgreeRecord(type, user, adDeposit)
        loadingDialogHelper.show { cancellable.cancel() }
    }

    override fun onGetProtocolAgreeRecord(isRecord: Boolean, type: Int, user: User, adDeposit: AdDeposit?) {
        loadingDialogHelper.dismiss()
        if (protocolDialog != null) {
            if (isRecord) {
                protocolDialog?.dismissAllowingStateLoss()
                loginNext(user, adDeposit)
            } else {
                toast(getString(R.string.str_please_sign))
            }
        } else {
            if (isRecord) {
                loginNext(user, adDeposit)
            } else {
                showProtocolDialogStep1(type, user, adDeposit)
            }
        }
    }

    private fun showProtocolDialogStep1(type: Int, user: User, adDeposit: AdDeposit?) {
        val protocolDialogStep1 = WebProtocolDialog.newInstance(type,1, forceReadDuration)
        protocolDialogStep1.onFinishListener = {
            showProtocolDialogStep2(type, user, adDeposit)
            protocolDialogStep1.dismissAllowingStateLoss()
        }
        lifecycleDialogHelper.show(protocolDialogStep1)
    }

    private fun showProtocolDialogStep2(type: Int, user: User, adDeposit: AdDeposit?) {
        val protocolDialogStep2 = WebProtocolDialog.newInstance(type,2, forceReadDuration)
        protocolDialogStep2.onFinishListener = {
            if (type == Constant.ProtocolPassMethod.FORCE_READ) {
                protocolAgreeOrSign(type, user, adDeposit)
            } else {
                getProtocolAgreeRecord(type, user, adDeposit)
            }
        }
        lifecycleDialogHelper.show(protocolDialogStep2)
        this.protocolDialog = protocolDialogStep2
    }

    private fun loginNext(user: User, adDeposit: AdDeposit?) {
        if (!user.isNameAuth && adDeposit?.needNameAuth == true) {
            startActivityForResult<NameAuthActivity>(REQUEST_NAME_AUTH)
        } else if (successIntent != null) {
            startActivityForResult(successIntent, REQUEST_SUCCESS_INTENT)
        } else {
            startActivity(HomeActivity.createIntent(this,true))
        }
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper?.dismiss()
        toast(message)
    }
    //点击次数
    var count = 10
    //规定的有效时间
    var time: Long = 2000
    var mHits = LongArray(count)
    //连续点击按钮三次
    @RequiresApi(Build.VERSION_CODES.M)
    fun ClickButton() {
        //每次点击时，数组向前移动一位
        System.arraycopy(mHits, 1, mHits, 0, mHits.size - 1);
        //为数组最后一位赋值
        mHits[mHits.size - 1] = SystemClock.uptimeMillis();
        if (mHits[0] >= (SystemClock.uptimeMillis() - time)) {
            //数组重新初始化
            mHits = LongArray(count)
            if (!SpUtil.Companion.getInstance().find(Constant.SpKey.SP_TESTNET).isNullOrEmpty()){
                binding.ivSwitchOn.setSwitchStatus(true)
            }
            binding.lyChangenet.visibility = View.VISIBLE
        }
    }
}