package com.tbit.uqbike.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.tbit.maintenance.utils.bindExtra
import com.tbit.uqbike.Glob
import com.tbit.uqbike.base.BaseActivity
import org.jetbrains.anko.newTask
import org.jetbrains.anko.singleTop

class OnePassLoginSuccessActivity: BaseActivity() {

    companion object {
        private const val REQUEST_CODE = 1
        private const val EXTRA_INTENTS = "EXTRA_INTENTS"

        fun start(context: Context, successIntent: Intent? = null) {
            val intents =
                createIntents(
                    context,
                    successIntent
                )

            when (intents.size) {
                0 -> context.startActivity(MainActivity.createIntent(context).newTask())
                1 -> context.startActivity(intents[0].newTask())
                else -> realStart(
                    context,
                    intents
                )
            }
        }

        private fun createIntents(context: Context, successIntent: Intent?): MutableList<Intent> {
            val intents = mutableListOf<Intent>()
            if (isNeedNameAuth()) {
                intents.add(Intent(context, NameAuthActivity::class.java))
            }
            if (successIntent != null) {
                intents.add(successIntent)
            }
            intents.add(MainActivity.createIntent(context))
            return intents
        }

        private fun isNeedNameAuth(): Boolean {
            val user = Glob.user
            val adDeposit = Glob.adDeposit
            return user?.isNameAuth == false && adDeposit?.needNameAuth == true
        }

        private fun realStart(context: Context, intents: List<Intent>) {
            val intent = Intent(context, OnePassLoginSuccessActivity::class.java).newTask().singleTop()
            intent.putParcelableArrayListExtra(EXTRA_INTENTS, ArrayList(intents))
            context.startActivity(intent)
        }
    }

    private val extraIntents: ArrayList<Intent> by bindExtra(EXTRA_INTENTS)
    private val intents: ArrayList<Intent> = ArrayList()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initIntents(savedInstanceState)
        startIntentActivity()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putParcelableArrayList(EXTRA_INTENTS, intents)
    }

    private fun initIntents(savedInstanceState: Bundle?) {
        if (intents.isNotEmpty()) return
        intents.addAll(
            when (savedInstanceState) {
                null -> extraIntents
                else -> (savedInstanceState.getParcelableArrayList<Intent>(EXTRA_INTENTS)) ?: ArrayList()
            }
        )
    }

    private fun startIntentActivity() {
        if (intents.isEmpty()) {
            onBackPressed()
        } else {
            startActivityForResult(intents.removeAt(0),
                REQUEST_CODE
            )
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        startIntentActivity()
    }
}