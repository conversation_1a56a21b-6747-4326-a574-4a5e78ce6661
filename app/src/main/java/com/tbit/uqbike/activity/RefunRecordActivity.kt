package com.tbit.uqbike.activity

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.luck.picture.lib.decoration.GridSpacingItemDecoration
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.adapter.wrapper.LoadMoreWrapper
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.RefunRecordAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.databinding.ActivityRefunRecordBinding
import com.tbit.uqbike.entity.RefundRecordData
import com.tbit.uqbike.entity.RefundRecordDataItem
import com.tbit.uqbike.resqmodel.RefundModel
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.jetbrains.anko.dip

class RefunRecordActivity : BaseActivity() {
    private var lastId = 0
    private val pageSize = Glob.pageNum
    private var lastLoaded = false
    private val adapter = RefunRecordAdapter()
    private val listdata = mutableListOf<RefundRecordDataItem>()
    private val loadMoreView by lazy { View(this).apply { layoutParams = ViewGroup.LayoutParams(1, 1) } }
    private lateinit var binding: ActivityRefunRecordBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRefunRecordBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_refun_record)
        binding.layoutToolbar.toolbarTitle.text = getString(R.string.s_refun_record)
        binding.layoutToolbar.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.layoutToolbar.toolbar.setNavigationOnClickListener { finish() }

        binding.rcv.layoutManager = LinearLayoutManager(this)
        binding.rcv.adapter = loadMoreWrapper(adapter)
        val spacing = dip(10)
        val spanCount = 1
        binding.rcv.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, false))
        adapter.onGoPersentInfoListener = {pos ->
//            startActivity(PersentInfoActivity.createIntent(this, listdata.get(pos).name,listdata.get(pos).area_id))
        }

        getListData()
    }

    private fun loadMoreWrapper(adapter: RecyclerView.Adapter<out RecyclerView.ViewHolder>)
            : LoadMoreWrapper<out RecyclerView.ViewHolder> {
        val loadMoreAdapter = LoadMoreWrapper(adapter)
        loadMoreAdapter.setOnLoadMoreListener{ onLoadMore() }
        return loadMoreAdapter
    }
    private fun onLoadMore() {
        if (!lastLoaded)
            getListData(false)
    }
    private fun getListData(showLoading: Boolean = true) {
        RefundModel.getRefundRecord(lastId,pageSize)
            .subscribeBy(
                onNext = {
                    loadingDialogHelper!!.dismiss()
                    MyLogUtil.Log("1111","======= 获取退款记录 信息 ==="+ it.toString())
                    var resultData = Gson().fromJson(it.toString(), RefundRecordData::class.java)
                    if(resultData.size > 0){
                        lastId = resultData.last().id
                    }
                    lastLoaded = resultData.size < pageSize
                    (binding.rcv.adapter as? LoadMoreWrapper<*>)?.setLoadMoreView(if (lastLoaded) null else loadMoreView)
                    listdata.addAll(resultData)
                    adapter.source = listdata
                    adapter.notifyDataSetChanged()
                    if (adapter.source.isEmpty()) binding.capaLayout.toEmpty() else binding.capaLayout.toContent()
                },
                onError = {
                    loadingDialogHelper!!.dismiss()
                }
            ).toCancelable()
        if (showLoading) {
            loadingDialogHelper.show { }
        }
    }
}