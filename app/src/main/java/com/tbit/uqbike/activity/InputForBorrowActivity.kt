package com.tbit.uqbike.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.text.Editable
import android.text.TextWatcher
import android.view.inputmethod.InputMethodManager
import com.tbit.maintenance.utils.bindExtra
import com.tbit.maintenance.utils.toCancelable
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.Glob
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.BikeState
import com.tbit.uqbike.databinding.ActivityInputForBorrowBinding
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.mvp.constract.InputUserCodeContract
import com.tbit.uqbike.mvp.model.BikeModel
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.mvp.presenter.InputUserCodePresenter
import com.tbit.uqbike.utils.GPSUtil
import com.tbit.uqbike.utils.MDUtil
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.intentFor
import org.jetbrains.anko.startActivity
import org.jetbrains.anko.toast
import org.json.JSONObject

class InputForBorrowActivity: BaseActivity(), InputUserCodeContract.View {
    private val from_riding_page: Boolean? by bindExtra(InputForBorrowActivity.FROM_RIDING_PAGE)//是否需要跳转钱包界面
    companion object {
        private const val REQUEST_BORROW_BIKE = 1
        private const val FROM_RIDING_PAGE = "FROM_RIDING_PAGE"//1从骑行页面入口进来  0不是从骑行页面入口进来
        fun createIntent(context: Context,from_riding_page:Boolean? = false): Intent {
            return context.intentFor<InputForBorrowActivity>(
                InputForBorrowActivity.FROM_RIDING_PAGE to from_riding_page)
        }
    }

    private val presenter = InputUserCodePresenter(this)
    private var isFlashLightOpen = false
    private lateinit var binding: ActivityInputForBorrowBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInputForBorrowBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_input_for_borrow)
        EventBus.getDefault().register(this);
        setSupportActionBar(binding.appbarLayout.toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        binding.appbarLayout.toolbarTitle.text = getString(R.string.manual_input_title)
        binding.appbarLayout.toolbar.setNavigationIcon(R.drawable.icon_back)
        binding.appbarLayout.toolbar.setNavigationOnClickListener { onBackPressed() }
//        showBarTextColorIsBlack(false)

        lifecycle.addObserver(presenter)

//        binding.editSerialNumber.setOnFocusChangeListener { v, hasFocus ->
//            MyLogUtil.Log("0000","===========setOnFocusChangeListener============"+hasFocus)
//        }
        binding.editSerialNumber.requestFocus()

        showSoftInput()
        bindBtnConfirm2EditNumber()
        binding.buttonConfirm.setOnClickListener {
            val properties = JSONObject()
            MDUtil.clickEvent("Input_number_click_ok",properties)
            getBikeState()
        }
        binding.imageFlashLight.setOnClickListener { switchFlashLight() }

        binding.editSerialNumber.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
            }
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable) {
                if (binding.editSerialNumber.text.toString().length > 0){
                    vehicle_number_event = binding.editSerialNumber.text.toString()
                    binding.buttonConfirm.setTextColor(resources.getColor(R.color.white))
                    binding.editSerialNumber.setTypeface(Typeface.DEFAULT_BOLD)
                }else{
                    vehicle_number_event = ""
                    binding.buttonConfirm.setTextColor(resources.getColor(R.color.white50))
                    binding.editSerialNumber.setTypeface(Typeface.DEFAULT)
                }
            }
        })
    }
    override fun onStart() {
        super.onStart()
        binding.zxingview.startCamera()
    }

    override fun onStop() {
        super.onStop()
        binding.zxingview.stopCamera()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(resultCode == Activity.RESULT_OK) {
            finish()
        } else {
            binding.editSerialNumber.setText("")
        }
    }

    private fun bindBtnConfirm2EditNumber() {
        binding.editSerialNumber.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable) {
                binding.buttonConfirm.isEnabled = s.isNotEmpty()
            }
        })
    }

    private fun showSoftInput() {
        Handler().postDelayed({
            val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.toggleSoftInput(0, 2)
        }, 500L)
    }

    private fun getBikeState() {
        val userCode = binding.editSerialNumber.text.toString().trim()
//        val cancellable = presenter.getBikeState(userCode)
        var location = LocationModel.lastLocation
        var latLng = LatLng()
        if (location != null){
            latLng.lat = location!!.latitude
            latLng.lng = location!!.longitude
            if(!Glob.isGoogleServiceAvailable || Glob.isGoogleNetAvailable == null || !Glob.isGoogleNetAvailable!!){
                latLng = GPSUtil.bd09_To_gps84(location.latitude,location.longitude)
            }
        }else{
            latLng.lat = 0.0
            latLng.lng = 0.0
        }
        latLng.lat = GPSUtil.retain6(latLng.lat)
        latLng.lng = GPSUtil.retain6(latLng.lng)
        BikeModel.getBikesInfo(userCode,latLng!!.lat,latLng!!.lng,BikeModel.isScan_Y,Glob.isRental)
            .subscribeBy(
                onNext = {
                    MyLogUtil.Log("1111","===获取车辆信息=="+it.toString())
                    if (from_riding_page==false){
                        startActivity<MainActivity>()
                        val handler = Handler()
                        handler.postDelayed({
                            // 延迟1秒后要执行的操作
                            EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_NOTITY,it.toString()));
                        }, 1000)
                        loadingDialogHelper.dismiss()
                        finish()
                        return@subscribeBy
                    }
//                    val resultData: CarInfoData = Gson().fromJson(it.toString(), CarInfoData::class.java)
                    EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_NOTITY,it.toString()));
                    loadingDialogHelper.dismiss()
                    finish()
                },
                onError = {
                    loadingDialogHelper.dismiss()
                }
            ).toCancelable()
        loadingDialogHelper.show {}
    }
    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onMsgEvent(event: BaseEventData) {}
    override fun onGetBikeStateSuccess(bikeState: BikeState) {
        loadingDialogHelper.dismiss()
    }

    override fun showErrMsg(message: String) {
        loadingDialogHelper.dismiss()
        toast(message)
    }

    private fun switchFlashLight() {
        if (isFlashLightOpen) {
            isFlashLightOpen = false
            binding.zxingview.closeFlashlight()
        } else {
            isFlashLightOpen = true
            binding.zxingview.openFlashlight()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }
}