package com.tbit.uqbike.activity

import android.os.Bundle
import com.lsxiao.apollo.core.annotations.Receive
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.map.listener.GeoSearchListener
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.R
import com.tbit.uqbike.adapter.SuggestionAdapter
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.map.base.ISuggestionSearch
import com.tbit.uqbike.map.bean.SuggestionResult
import com.tbit.uqbike.mvp.model.LocationModel

class SearchParkPointActivity: BaseActivity(), ISuggestionSearch.Callback, GeoSearchListener {

    private val adapter = SuggestionAdapter()
    private val suggestionSearch by lazy { FlavorConfig.mapFactory.createSuggestionSearch(this, this) }
    private val geoSearch by lazy { FlavorConfig.mapFactory.createGeoSearch(this, this) }
    private var hasRequestSuggestion = false
    private var keyword: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_search_park_point)
//        setSupportActionBar(toolbar)
//        supportActionBar?.setDisplayShowTitleEnabled(false)
//        toolbar.setNavigationIcon(R.drawable.icon_back)
//        toolbar.setNavigationOnClickListener { finish() }
//
////        adapter.setOnItemClickListener { startActivity(NearParkPointActivity.createIntent(this, it.title, it.latLng)) }
//        recycler_locations.layoutManager = LinearLayoutManager(this)
//        recycler_locations.addItemDecoration(DividerItemDecoration(this, DividerItemDecoration.VERTICAL))
//        recycler_locations.adapter = adapter
//
//        initSearchView()
//        onLocationUpdate()
    }

    override fun onDestroy() {
        suggestionSearch.destroy()
        super.onDestroy()
    }

    private fun initSearchView() {
        //设置我们的SearchView
//        searchView.setIconifiedByDefault(true) //设置展开后图标的样式,这里只有两种,一种图标在搜索框外,一种在搜索框内
//        searchView.onActionViewExpanded() // 写上此句后searchView初始是可以点击输入的状态，如果不写，那么就需要点击下放大镜，才能出现输入框,也就是设置为ToolBar的ActionView，默认展开
//        searchView.requestFocus() //输入焦点
//        searchView.isSubmitButtonEnabled = false //添加提交按钮，监听在OnQueryTextListener的onQueryTextSubmit响应
//        searchView.isFocusable = true //将控件设置成可获取焦点状态,默认是无法获取焦点的,只有设置成true,才能获取控件的点击事件
//        searchView.isIconified = false //输入框内icon不显示
//        searchView.requestFocusFromTouch() //模拟焦点点击事件
//
//        //事件监听
//        searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
//
//            override fun onQueryTextSubmit(query: String?) = false
//
//            override fun onQueryTextChange(newText: String?): Boolean {
//                if (!isFinishing) {
//                    keyword = newText?.takeIf { it.isNotEmpty() }
//                    requestSuggestion()
//                }
//                return false
//            }
//        })
    }

    @Receive(Constant.Event.LOCATION_UPDATE)
    fun onLocationUpdate() {
        val location = LocationModel.lastLocation ?: return

//        if (location.address != null) {
//            text_current_location.text = getString(R.string.current_address_with_title, location.address)
//        } else {
//            geoSearch.reverseGeoCode(location.let { LatLng(it.latitude, it.longitude) })
//        }
//
//        if (!hasRequestSuggestion) {
//            requestSuggestion(location.city, keyword, LatLng(location.latitude, location.longitude))
//        }
    }

    private fun requestSuggestion() {
        val location = LocationModel.lastLocation ?: return
        val latLng = location?.takeIf { suggestionSearch.isLatLngRequired(keyword) }
            ?.let { LatLng(it.latitude, it.longitude) }
        requestSuggestion(location.city, keyword, latLng)
    }

    private fun requestSuggestion(city: String?, keyword: String?, latLng: LatLng?) {
        hasRequestSuggestion = true
        suggestionSearch.requestSuggestion(city, keyword, latLng)
    }

    override fun onGetSuggestionResult(results: List<SuggestionResult>) {
        adapter.source = results
        adapter.notifyDataSetChanged()
    }

    override fun onGetReverseGeoCodeResult(success: Boolean, address: String?) {
        if (!success) { //没有找到检索结果
//            text_current_location.text = getString(R.string.current_address_with_title, getString(R.string.unknown_address))
            return
        }

        //获取反向地理编码结果
//        text_current_location.setText(getString(R.string.current_address_with_title, address))
    }
}