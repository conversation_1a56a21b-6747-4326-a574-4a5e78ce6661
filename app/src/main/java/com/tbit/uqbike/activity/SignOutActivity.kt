package com.tbit.uqbike.activity

import android.os.Bundle
import android.view.View
import com.google.gson.Gson
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.ResUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.bean.AdDeposit
import com.tbit.uqbike.bean.User
import com.tbit.uqbike.databinding.ActivitySignOutBinding
import com.tbit.uqbike.dialog.CommDialog
import com.tbit.uqbike.entity.CountryDataItem
import com.tbit.uqbike.entity.UserEntity
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.mvp.constract.LoginContract
import com.tbit.uqbike.mvp.model.AuthCodeModel
import com.tbit.uqbike.mvp.model.UserModel
import com.tbit.uqbike.mvp.presenter.LoginPresenter
import com.tbit.uqbike.utils.MyLogUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.jetbrains.anko.toast

class SignOutActivity : BaseActivity(), LoginContract.View {
    private val presenter = LoginPresenter(this)
    var isEmailLogin = false
    private lateinit var binding: ActivitySignOutBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this);
        binding = ActivitySignOutBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_sign_out)
        lifecycle.addObserver(presenter)
        binding.buttonAuthCode.setOnClickListener { sendAutoCode() }
        binding.btnLogin.setOnClickListener { SignOut() }
        binding.ivCountryCode.setOnClickListener { binding.textCountryCode.performClick() }
        binding.ivClose.setOnClickListener { finish() }
        binding.imageClearPhone.setOnClickListener { binding.editPhoneNumber.text = null }
        if (!SpUtil.getInstance().find(Constant.SpKey.SP_LOGINTYPE).isNullOrEmpty()){
            var loginType = SpUtil.getInstance().find(Constant.SpKey.SP_LOGINTYPE)
            MyLogUtil.Log("1111","===="+loginType)
            if (loginType!!.equals(LoginActivity.LOGIN_MOTHOD_PHONE)){

            }else if(loginType!!.equals(LoginActivity.LOGIN_MOTHOD_EMAIL)){
                isEmailLogin = true
            }
        }
        UserModel.getUser()
            .subscribeBy(
                onNext = {
                    val resultData: UserEntity = Gson().fromJson(it.toString(), UserEntity::class.java)
                    if (isEmailLogin){
                        binding.editPhoneNumber.setText(resultData.user_name.toString())
                    }else{
                        binding.textCountryCode.visibility = View.VISIBLE
                        binding.ivCountryCode.visibility = View.VISIBLE
                        binding.lineCountry.visibility = View.VISIBLE
                        binding.textCountryCode.text = countryCodePlus(resultData.area_code)
                        binding.editPhoneNumber.setText(resultData.phone.toString())
                    }
                }
            ).toCancelable()
    }

    private fun countryCodePlus(code: String?): String {
        if (code.isNullOrEmpty()) {
            return ""
        }
        return "+"+code.toInt()
    }

    private fun countryCodeNoPlus(code: String?): String {
        return code?.substring(1) ?: ""
    }

    override fun onGetCountryCodeInfoSuccess(data: List<CountryDataItem>) {}

    override fun onGetAuthCodeResendDelay(time: Long) {
        presenter.startCountdown(time)
    }

    override fun onCountDownEmail(time: Long) {}
    override fun onCountDown(time: Long) {
        if(time > 0) {
            binding.buttonAuthCode.setTextColor(resources.getColor(R.color.blue_namal))
            binding.buttonAuthCode.text = getString(R.string.second_with_unit, time)
            binding.buttonAuthCode.isEnabled = false
        } else {
            binding.buttonAuthCode.setTextColor(resources.getColor(R.color.blue_namal))
            binding.buttonAuthCode.text = getString(R.string.get_auth_code)
            binding.buttonAuthCode.isEnabled = true
            val code = binding.editAuthCode.text.toString()
            if (code.isEmpty()) {
//                longToast(getString(R.string.str_auth_code_receive_hint))
            }
        }
    }

    private fun sendAutoCode() {
        if (isEmailLogin){
            AuthCodeModel.sendEmailAuthCode("")
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper.dismiss()
                        presenter.startCountdown(60)
                    },
                    onError = {
                        loadingDialogHelper.dismiss()
                        val errMsg = ErrHandler.getErrMsg(it)
                    }
                ).toCancelable()
        }else{
            val cancellable = presenter.sendAuthCode("", "")
        }
        loadingDialogHelper?.show { false }
    }

    override fun onSendAutoCodeSuccess() {
//        toast(R.string.auth_code_sent)
        loadingDialogHelper?.dismiss()
        presenter.startCountdown(60)
    }

    private fun SignOut() {
        val authCode = binding.editAuthCode.text.toString()
        if(checkAuthCode(authCode)) {
            loadingDialogHelper?.show {  }
            UserModel.SignOut(authCode.toInt())
                .subscribeBy(
                    onNext = {
                        loadingDialogHelper?.dismiss()
                        UserModel.logout()
                        EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_LOGINOUT,""));
                        EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_FINSH,""));
                        CommDialog.Builder(this@SignOutActivity).setTitle(ResUtil.getString(R.string.dialog_tip)).setContent(getString(R.string.s_sumit_await))
                            .setRightText(ResUtil.getString(R.string.i_know)).setCanceledOnOutside(true)
                            .setClickListen(object : CommDialog.TwoSelDialog {
                                override fun leftClick() {  }
                                override fun rightClick() {
                                    finish()
                                }
                            }).build().show()
                    },
                    onError = {
                        loadingDialogHelper?.dismiss()
                        val errCode = ErrHandler.getErrCode(it)
                        if (errCode == Constant.ErrCode.USIGNOUT){
                            CommDialog.Builder(this@SignOutActivity).setTitle(ResUtil.getString(R.string.dialog_tip)).setContent(getString(R.string.s_usignout))
                                .setLeftText(ResUtil.getString(R.string.cancel)).setRightText(ResUtil.getString(R.string.ok)).setCanceledOnOutside(true)
                                .setClickListen(object : CommDialog.TwoSelDialog {
                                    override fun leftClick() {  }
                                    override fun rightClick() {  }
                                }).build().show()
                        }
                    }
                ).toCancelable()

        }
    }

    private fun checkAuthCode(authCode: String): Boolean {
        if(authCode.isEmpty()) {
            toast(R.string.auth_code_can_not_be_empty)
            return false
        }
        return true
    }

    override fun onLoginSuccess() {}
    override fun onLoginError(code: Int) {}

    override fun onGoogleLoginSuccess(
        isFirst: Int,
        email: String,
        IdToken: String,
        ServerAuthCode: String
    ) {}
    override fun onGetInviteRegisterSwitchSuccess(isOpen: Boolean, user: User, adDeposit: AdDeposit?) {}
    override fun onGetInviteRegisterSwitchFail(user: User, adDeposit: AdDeposit?) {}
    override fun onGetProtocolPassMethodBeforeLoginSuccess(type: Int, forceReadDuration: Int) {}
    override fun onGetProtocolPassMethodSuccess(type: Int, forceReadDuration: Int, user: User, adDeposit: AdDeposit?) {}
    override fun onGetProtocolPassMethodFailed(user: User, adDeposit: AdDeposit?) {}
    override fun onProtocolAgreeOrSign(user: User, adDeposit: AdDeposit?) {}
    override fun onGetProtocolAgreeRecord(isRecord: Boolean, type: Int, user: User, adDeposit: AdDeposit?) {}

    override fun showErrMsg(message: String) {
        loadingDialogHelper?.dismiss()
        toast(message)
    }

    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onMsgEvent(event: BaseEventData) {}
    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().removeAllStickyEvents()
        EventBus.getDefault().unregister(this);
    }
}