package com.tbit.uqbike.activity

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import com.tbit.maintenance.config.Constant
import com.tbit.maintenance.network.ErrHandler
import com.tbit.maintenance.utils.AppUtil
import com.tbit.maintenance.utils.SpUtil
import com.tbit.maintenance.utils.toCancelable
import com.tbit.uqbike.R
import com.tbit.uqbike.base.BaseActivity
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.databinding.ActivityFixPwdBinding
import com.tbit.uqbike.entity.event.BaseEventData
import com.tbit.uqbike.entity.event.EventUtil
import com.tbit.uqbike.mvp.model.UserModel
import com.tbit.uqbike.utils.LoginViewUtil
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import io.reactivex.rxkotlin.subscribeBy
import org.greenrobot.eventbus.EventBus

class FixPwdActivity : BaseActivity(), TextWatcher {
    private var isOldPwd = false
    private lateinit var binding: ActivityFixPwdBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFixPwdBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_fix_pwd)
//        setSupportActionBar(toolbar)

        UserModel.getPwdState().subscribeBy(
            onNext = {
                if (it != null){
                    if (it.toString().contains("true")){
                        binding.lyOldPwd.visibility = View.VISIBLE
                        isOldPwd = true
                    }else{
                        binding.tvRegTitle.text = getString(R.string.s_setpwd)
                    }
                }
            },
            onError = {}
        ).toCancelable()

        binding.editFixpwdPwd0.addTextChangedListener(this)
        binding.editFixpwdPwd1.addTextChangedListener(this)
        binding.editFixpwdPwd2.addTextChangedListener(this)

        binding.editFixpwdPwd1.setOnFocusChangeListener{ v, hasFocus ->
            if (!hasFocus) {
                if (AppUtil.checkPwdLength(binding.editFixpwdPwd1.text.toString())){
                    binding.tvRegFixPwdhint1.visibility = View.GONE
                }else{
                    binding.tvRegFixPwdhint1.visibility = View.VISIBLE
                }
            }
        }

        var isHintPhonePwd0 = true
        binding.imageFixpwdPwd0.setOnClickListener {
            isHintPhonePwd0 = !isHintPhonePwd0
            if (isHintPhonePwd0){
                LoginViewUtil.hidePwd(binding.editFixpwdPwd0,binding.imageFixpwdPwd0)
            }else{
                LoginViewUtil.showPwd(binding.editFixpwdPwd0,binding.imageFixpwdPwd0)
            }
        }

        var isHintPhonePwd1 = true
        binding.imageFixpwdPwd1.setOnClickListener {
            isHintPhonePwd1 = !isHintPhonePwd1
            if (isHintPhonePwd1){
                LoginViewUtil.hidePwd(binding.editFixpwdPwd1,binding.imageFixpwdPwd1)
            }else{
                LoginViewUtil.showPwd(binding.editFixpwdPwd1,binding.imageFixpwdPwd1)
            }
        }

        var isHintPhonePwd2 = true
        binding.imageFixpwdPwd2.setOnClickListener {
            isHintPhonePwd2 = !isHintPhonePwd2
            if (isHintPhonePwd2){
                LoginViewUtil.hidePwd(binding.editFixpwdPwd2,binding.imageFixpwdPwd2)
            }else{
                LoginViewUtil.showPwd(binding.editFixpwdPwd2,binding.imageFixpwdPwd2)
            }
        }
        binding.ivClose.setOnClickListener { finish() }
        binding.btnFixpwd.setOnClickListener { fixPwd() }
    }
    override fun afterTextChanged(s: Editable?) {
        checkEdtState()
    }
    private fun checkEdtState(){
        if (binding.editFixpwdPwd1.text.toString().length > 0 && binding.editFixpwdPwd2.text.toString().length > 0){
            if (isOldPwd){
                if (binding.editFixpwdPwd0.text.toString().length > 0){
                    binding.btnFixpwd.isEnabled = true
                    binding.btnFixpwd.setTextColor(resources.getColor(R.color.white))
                }else{
                    binding.btnFixpwd.isEnabled = false
                    binding.btnFixpwd.setTextColor(resources.getColor(R.color.white50))
                }
            }else{
                binding.btnFixpwd.isEnabled = true
                binding.btnFixpwd.setTextColor(resources.getColor(R.color.white))
            }
        }else{
            binding.btnFixpwd.isEnabled = false
            binding.btnFixpwd.setTextColor(resources.getColor(R.color.white50))
        }
    }
    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

    private fun fixPwd() {
        val pwd0 = binding.editFixpwdPwd0.text.toString()
        val pwd1 = binding.editFixpwdPwd1.text.toString()
        val pwd2 = binding.editFixpwdPwd2.text.toString()
        if(!AppUtil.checkPwd(pwd1,pwd2)) return
        MyLogUtil.Log("1111","==修改密码=="+pwd0+","+pwd1+","+pwd2)
        loadingDialogHelper.show { false }
        if (isOldPwd){
            UserModel.FixPwd1(pwd0,pwd1,pwd2).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyToastUtil.toast(getString(R.string.s_fix_suc))
                    UserModel.logout()
                    EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_LOGINOUT,""));
                    EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_FINSH,""));
                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_FIXPWD, "1");
                    FlavorConfig.appRoute.login()
                    finish()
                },
                onError = {
                    loadingDialogHelper.dismiss()
                    MyToastUtil.toast(ErrHandler.getErrMsg(it))
                }
            ).toCancelable()
        }else{
            UserModel.FixPwd2(pwd1,pwd2).subscribeBy(
                onNext = {
                    loadingDialogHelper.dismiss()
                    MyToastUtil.toast(getString(R.string.s_fix_suc))
                    UserModel.logout()
                    EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_LOGINOUT,""));
                    EventBus.getDefault().post(BaseEventData(EventUtil.EVENT_FINSH,""));
                    SpUtil.Companion.getInstance().saveOrUpdate(Constant.SpKey.SP_FIXPWD, "1");
                    FlavorConfig.appRoute.login()
                    finish()
                },
                onError = {
                    loadingDialogHelper.dismiss()
                    MyToastUtil.toast(ErrHandler.getErrMsg(it))
                }
            ).toCancelable()
        }
    }

}