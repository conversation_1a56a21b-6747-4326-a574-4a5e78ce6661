package com.tbit.maintenance.utils

import android.app.Activity
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import android.os.Looper
import android.widget.Toast
import androidx.annotation.RequiresApi
import com.baidu.ar.id
import com.baidu.mapapi.bikenavi.BikeNavigateHelper
import com.baidu.mapapi.bikenavi.adapter.IBEngineInitListener
import com.baidu.mapapi.bikenavi.adapter.IBRoutePlanListener
import com.baidu.mapapi.bikenavi.model.BikeRoutePlanError
import com.baidu.mapapi.bikenavi.params.BikeNaviLaunchParam
import com.baidu.mapapi.bikenavi.params.BikeRouteNodeInfo
import com.baidu.mapapi.walknavi.WalkNavigateHelper
import com.baidu.mapapi.walknavi.adapter.IWRoutePlanListener
import com.baidu.mapapi.walknavi.model.WalkRoutePlanError
import com.baidu.mapapi.walknavi.params.WalkNaviLaunchParam
import com.baidu.mapapi.walknavi.params.WalkRouteNodeInfo
import com.doule.database.CoroutinesUtil
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import com.google.android.gms.common.GooglePlayServicesNotAvailableException
import com.google.android.gms.common.GooglePlayServicesRepairableException
import com.tbit.maintanenceplus.map.base.INavigation
import com.tbit.maintanenceplus.utils.LocationUtil
import com.tbit.maintenance.config.Constant
import com.tbit.tbituser.map.bean.LatLng
import com.tbit.uqbike.App
import com.tbit.uqbike.BuildConfig
import com.tbit.uqbike.Glob
import com.tbit.uqbike.LanguageUtil
import com.tbit.uqbike.R
import com.tbit.uqbike.activity.ChargeNewActivity
import com.tbit.uqbike.activity.MyMemberActivity
import com.tbit.uqbike.activity.NaviActivity
import com.tbit.uqbike.activity.PersonActivity
import com.tbit.uqbike.activity.RideCardNewActivity
import com.tbit.uqbike.activity.WebActionActivity
import com.tbit.uqbike.config.FlavorConfig
import com.tbit.uqbike.entity.I18nNameData
import com.tbit.uqbike.mvp.model.LocationModel
import com.tbit.uqbike.utils.AppUtils
import com.tbit.uqbike.utils.DeviceUtils
import com.tbit.uqbike.utils.MyLogUtil
import com.tbit.uqbike.utils.MyToastUtil
import com.tbit.uqbike.utils.MyToastUtil.toast
import com.tbit.uqbike.utils.NetUtils
import com.tbit.uqbike.utils.UrlDecodeUtil
import org.jetbrains.anko.startActivity
import java.io.IOException
import java.net.URI
import java.net.URISyntaxException
import java.util.Base64
import java.util.Locale
import java.text.DecimalFormat
import java.math.RoundingMode


object AppUtil {

    private val context get() = ContextUtil.getContext()

//    fun install(filePath: String, authority: String) {
//        Log.i("AppUtil", "install")
//        val apkFile = File(filePath)
//        Log.i("AppUtil", "install" + apkFile.absolutePath)
//        if (!apkFile.exists()) {
//            Log.i("AppUtil", "installApk: file is not exit")
//            return
//        }
//        val intent = Intent(Intent.ACTION_VIEW)
//        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//        val apkUri: Uri
//        // Android 7.0 以上不支持 file://协议 需要通过 FileProvider 访问 sd卡 下面的文件，所以 Uri 需要通过 FileProvider 构造，协议为 content://
//        if (Build.VERSION.SDK_INT >= 24) {
//            // content:// 协议
//            apkUri = FileProvider.getUriForFile(context, authority, apkFile)
//            //Granting Temporary Permissions to a URI
//            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
//        } else {
//            // file:// 协议
//            apkUri = Uri.fromFile(apkFile)
//        }
//        intent.setDataAndType(apkUri, "application/vnd.android.package-archive")
//        context.startActivity(intent)
//    }

    //获取消息类型
    fun getMstType(type : Int) : String{
        var typeMsg = ""
//        1 故障上报，2 意见反馈，3 骑行费用申诉，4 短时长骑行订单，5余额退款，6骑行卡退款
        when (type) {
            1 -> typeMsg = ResUtil.getString(R.string.feedback_fault)
            2 -> typeMsg = ResUtil.getString(R.string.feedback)
            3 -> typeMsg = ResUtil.getString(R.string.s_appeal)
            4 -> typeMsg = ResUtil.getString(R.string.s_order_shorttime)
            5 -> typeMsg = ResUtil.getString(R.string.s_refund_balance)
            6 -> typeMsg = ResUtil.getString(R.string.s_refund_ridecard)
            9 -> typeMsg = ResUtil.getString(R.string.s_refund_yj)
            10 -> typeMsg = ResUtil.getString(R.string.s_invite_suc)
            11 -> typeMsg = ResUtil.getString(R.string.s_win)
        }
        return typeMsg
    }

    //获取余额流水类型
    fun getBalanceType(type : Int) : String{
        var typeData = ""
        when (type) {
//            1 -> typeData = ResUtil.getString(R.string.recharge)    // 余额充值
            1 -> typeData = ResUtil.getString(R.string.s_pay_user)    // 用户充值
            2 -> typeData = ResUtil.getString(R.string.s_type_ride)    // 骑行扣除余额
            3 -> typeData = ResUtil.getString(R.string.s_dispatch_cost)   // 调度费扣除余额
            4 -> typeData = ResUtil.getString(R.string.s_appeal)    // 申诉返还余额
            5 -> typeData = ResUtil.getString(R.string.s_paypresent) // 充值赠送
            6 -> typeData = ResUtil.getString(R.string.s_ridepresent) // 骑行扣除赠送
            7 -> typeData = ResUtil.getString(R.string.s_dispatchpresent) // 调度费扣除赠送
            8 -> typeData = ResUtil.getString(R.string.s_appealpresent)  // 申诉返还赠送
            9 -> typeData = ResUtil.getString(R.string.s_extenpresent)  // 推广码赠送
            10 -> typeData = ResUtil.getString(R.string.s_giftpresent) // 礼品卡赠送
            11 -> typeData = ResUtil.getString(R.string.s_buyridecard) // 购买骑行卡
            14 -> typeData = ResUtil.getString(R.string.s_pay_sys) // 系统充值
            15 -> typeData = ResUtil.getString(R.string.s_deduct) // 系统扣款
            16 -> typeData = ResUtil.getString(R.string.s_newuser_present) // 新用户注册赠送
            17 -> typeData = ResUtil.getString(R.string.s_refun) // 退款
            18 -> typeData = ResUtil.getString(R.string.s_refund_fail) // 退款失败
            19 -> typeData = ResUtil.getString(R.string.s_refun) // 退款
            20 -> typeData = ResUtil.getString(R.string.s_refund_fail) // 退款失败
            21 -> typeData = ResUtil.getString(R.string.s_refund_ridecard_suc) // 购买骑行卡退款
        }
        return typeData
    }

    var screenWidth = 0
    var Scale = "1.0"
    var userAgentData = ""
    //获取User-Agent
    fun getUserAgent() : String{
        if(screenWidth <= 480){
        }else if(screenWidth <= 720){
            Scale = "2.0"
        }else{
            Scale = "3.0"
        }
        var net = NetUtils.getNetWorkType(ContextUtil.getContext())
//        MyLogUtil.Log("8888","===="+net)
        var IsVirtual = ""
        if(Glob.IsVirtual){
            IsVirtual = "Simulator "
        }
        userAgentData = "GoGo/"+ BuildConfig.VERSION_NAME.replace("v","")+" ("+IsVirtual+DeviceUtils.getBrand()+"; Android "+
                DeviceUtils.getSysVersion()+")"+" NetType/"+net+" Lang/"+ FlavorConfig.Local.language+" Scale/"+ Scale
//        MyLogUtil.Log("1111","=====userAgent ===="+userAgentData)
        return userAgentData
    }

    @RequiresApi(Build.VERSION_CODES.O)
    fun getSicData(sicData: String?): String? {
        var flag = 0xE03FD23A
        var tmps = ByteArray(0) // 对base64编码的字符串进行解码得到byte数组
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
////            tmps = Base64.getDecoder().decode(sicData);
//            tmps = Base64.getMimeDecoder().decode(sicData)
//        }
        tmps = Base64.getMimeDecoder().decode(sicData)

        while (flag > 0) {
            var i = 0
            val size = tmps.size
            while (i < size) {
                tmps[i] = (tmps[i].toInt() xor ((flag and 0xff).toInt())).toByte()
                i++
            }
            flag = flag shr 4
        }
        val data = String(tmps, Charsets.UTF_8)
//        MyLogUtil.Log("1111", "===============================sic data==$data")
        return data
    }

    //url 解析
    fun parseLink(link: String): URI? {
        try {
            return URI(link)
        } catch (e: URISyntaxException) {
            e.printStackTrace()
            return null
        }
    }
    fun getUrlParm(parsedUri : URI) : String{
        var parm_Str = parsedUri?.query.orEmpty().split("&").associate {
            val pair = it.split("=")
            pair[0] to pair.getOrElse(1) { "" }
        }
        return parm_Str.toString()
    }
    fun goAdAciton(act : Activity, title : String, parsedUri : URI,is_show_app_header : Int?,se_type : Int = 5){
        if (parsedUri != null) {
            //intent://order/gopay?money=50
//            MyLogUtil.Log("5555","=======Scheme: ${parsedUri.scheme}") // 输出 Scheme: https
//            MyLogUtil.Log("5555","=======Host: ${parsedUri.host}") // 输出 Host: www.example.com
//            MyLogUtil.Log("5555","=======Path: ${parsedUri.path}") // 输出 Path: /
//            MyLogUtil.Log("5555","=======parms: ${parsedUri.query}") // 输出 query
            when (parsedUri.host) {
                "opener" -> {
                    if (!Glob.isLogin) {
                        if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
                            MyToastUtil.toast(ResUtil.getString(R.string.s_local_err))
                            return
                        }

                        toast(ResUtil.getString(R.string.login_first))
                        FlavorConfig.appRoute.login()
                        return
                    }
                    var url = ""
                    if (parsedUri.query.isNullOrEmpty()){
                        url = parsedUri.path.substring(1,parsedUri.path.length)
                    }else{
                        url = parsedUri.path.substring(1,parsedUri.path.length)+"?"+parsedUri.query
                    }
                    MyLogUtil.Log("1111","===="+url)
//                    act.startActivity<WebActivity>(WebActivity.TITLE to title,
//                        WebActivity.URL to url)
                    if (is_show_app_header != null && is_show_app_header == 0){
                        act.startActivity<WebActionActivity>(WebActionActivity.TITLE to title,
                            WebActionActivity.URL to url, WebActionActivity.IsHideHead to true,WebActionActivity.se_type to se_type.toString())
                    }else{
                        act.startActivity<WebActionActivity>(WebActionActivity.TITLE to title,
                            WebActionActivity.URL to url, WebActionActivity.IsHideHead to false,WebActionActivity.se_type to se_type.toString())
                    }
                }
                "redirect" -> {
                    if (!Glob.isLogin) {
                        if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
                            MyToastUtil.toast(ResUtil.getString(R.string.s_local_err))
                            return
                        }

                        toast(ResUtil.getString(R.string.login_first))
                        FlavorConfig.appRoute.login()
                        return
                    }
                    when(parsedUri.path.substring(1,parsedUri.path.length)){
                        "ride_card" -> {
                            act.startActivity<RideCardNewActivity>()
                        }
                        "recharge" -> {
                            act.startActivity<ChargeNewActivity>()
                        }
                        "member_center" -> {
                            return
                            act.startActivity<MyMemberActivity>()
                        }
                        "member_info" -> {
                            act.startActivity<PersonActivity>()
                        }
                    }
                }
            }
            when (parsedUri.scheme) {
                "https" -> {
                    if (!Glob.isLogin) {
                        if (!LocationUtil.isGpsEnabledByPermiss() || LocationModel.lastLocation == null) {
                            MyToastUtil.toast(ResUtil.getString(R.string.s_local_err))
                            return
                        }

                        toast(ResUtil.getString(R.string.login_first))
                        FlavorConfig.appRoute.login()
                        return
                    }
                    when(parsedUri.path.substring(1,parsedUri.path.length)){
                        "myPoints" -> {
                            act.startActivity<WebActionActivity>(
                                WebActionActivity.TITLE to "",
                                WebActionActivity.URL to UrlDecodeUtil().getParm(
                                    FlavorConfig.NET.H5_URL+"myPoints"+"?locale="+
                                            FlavorConfig.Local.language), WebActionActivity.IsHideHead to (is_show_app_header != null && is_show_app_header == 0))

                        }
                    }
                }
            }
        }
    }

    fun getStr_small(data_str : String,size : Int) : SpannableStringBuilder {
        val spannableStringBuilder = SpannableStringBuilder(data_str)
        val sizeSpan = RelativeSizeSpan(0.55f) // 例如，0.5是原来的一半大小，2.0是原来的两倍大小
        spannableStringBuilder.setSpan(sizeSpan, spannableStringBuilder.length-size, spannableStringBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        return spannableStringBuilder
    }
    fun getPriceSmall(data_str : String) : SpannableStringBuilder{
        val spannableStringBuilder = SpannableStringBuilder(data_str)
        val sizeSpan = RelativeSizeSpan(0.6f) // 例如，0.5是原来的一半大小，2.0是原来的两倍大小
        spannableStringBuilder.setSpan(sizeSpan, spannableStringBuilder.length-2, spannableStringBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        return spannableStringBuilder
    }

    fun getFloat2Double(dataF : Double) : String{
        try {
//            MyLogUtil.Log("1111","==============="+String.format("%.2f", dataF))
            var data_Str = String.format(Locale.ENGLISH,"%,.2f", dataF)
            return data_Str
        }catch (e : NumberFormatException){
//            MyLogUtil.Log("1111","=======111========"+dataF.toString())
            return dataF.toString()
        }
    }
    fun getFloat2(dataF : Float) : String{
        try {
            val df = DecimalFormat("0.00")
            df.minimumFractionDigits = 2
            df.maximumFractionDigits = 2
            df.roundingMode = RoundingMode.HALF_UP
            return df.format(dataF)
        }catch (e : NumberFormatException){
            return dataF.toString()
        }
    }
    fun formatAmount(amount: Long): String {
        return String.format("%,d", amount)
    }

//    fun getLanguageName(name : I18nNameData): String{
//        var nameData = ""
//        when(FlavorConfig.Local.language){
//            LanguageUtil.LANG_US -> {
//                if (!name.en_us.isNullOrEmpty()){
//                    nameData = name.en_us.toString()
//                }else{
//                    nameData = getDefaultName(name)
//                }
//            }
//            LanguageUtil.LANG_ZH -> {
//                if (!name.zh_cn.isNullOrEmpty()){
//                    nameData = name.zh_cn.toString()
//                }else{
//                    nameData = getDefaultName(name)
//                }
//            }
//            LanguageUtil.LANG_TH -> {
//                if (!name.th_th.isNullOrEmpty()){
//                    nameData = name.th_th.toString()
//                }else{
//                    nameData = getDefaultName(name)
//                }
//            }
//        }
//        return nameData
//    }
    fun getDefaultName(name : I18nNameData): String{
        var nameData = ""
        if (!name.th_th.isNullOrEmpty()) nameData = name.th_th
        if (!name.zh_cn.isNullOrEmpty()) nameData = name.zh_cn
        if (!name.en_us.isNullOrEmpty()) nameData = name.en_us
        return nameData
    }

    /**
     * 根据语言获取手机 区号
     */
    fun getPhoneCode() : String{
        var code = ""
        if (LanguageUtil.isSuperLanuage){
//            MyLogUtil.Log("1111","=======isSuperLanuage=========="+LanguageUtil.isSuperLanuage)
            when(FlavorConfig.Local.language) {
                LanguageUtil.LANG_US -> {code = "+66"}
                LanguageUtil.LANG_ZH -> {code = "+86"}
                LanguageUtil.LANG_TH -> {code = "+66"}
                LanguageUtil.LANG_ID -> {code = "+62"}
                LanguageUtil.LANG_ML -> {code = "+60"}
                LanguageUtil.LANG_KR -> {code = "+82"}
            }
        }else{
//            MyLogUtil.Log("1111","=======isSuperLanuage=========="+LanguageUtil.isSuperLanuage)
            when(Glob.CurrencyLld) {
                "th" -> {code = "+66"}
                "ind" -> {code = "+62"}
                else -> {code = "+66"}
            }
        }

        if (code.isNullOrEmpty()) {
            return ""
        }
        return "+"+code.toInt()
    }
    fun checkPwdLength(pwd1 : String) : Boolean{
        if (pwd1.length < 8){
            return false
        }
        if (!containsDigitAndLetterOnly(pwd1)){
            return false
        }
        return true
    }
    fun checkPwd(pwd1 : String,pwd2 : String) : Boolean{
        if (!pwd1.equals(pwd2)){
            MyToastUtil.toast(ContextUtil.getContext().getString(R.string.s_pwddiff_error))
            return false
        }
        if (pwd1.length < 8){
            MyToastUtil.toast(ContextUtil.getContext().getString(R.string.s_pwdformat_error))
            return false
        }
        if (!containsDigitAndLetterOnly(pwd1)){
            MyToastUtil.toast(ContextUtil.getContext().getString(R.string.s_pwdformat_error))
            return false
        }
        return true
    }
    fun containsDigitAndLetterOnly(input: String): Boolean {
        // 正则表达式：必须包含至少一个数字和一个字母，不能有其他特殊字符
        val regex = "(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]+"
        return input.matches(regex.toRegex())
    }

    fun startNav(act: Activity, startpos: LatLng, endpos: LatLng, isWalk : Boolean){
        if (Glob.isGoogleServiceAvailable && Glob.isGoogleNetAvailable != null && Glob.isGoogleNetAvailable!!) {
            if (isWalk){
                navigationTo("",endpos.lat,endpos.lng,INavigation.NavigationType.WALKING)
            }else{
                navigationTo("",endpos.lat,endpos.lng,INavigation.NavigationType.RIDING)
            }
        }else{
            if (isWalk){
                startBaiduByWalk(act,startpos,endpos)
            }else{
                startBaiduByRide(act,startpos,endpos)
            }
        }
    }
    fun navigationTo(destName: String, destLat: Double, destLon: Double, navigationType: Int) {
        val context = ContextUtil.getContext()
        val googleMapPackageName = "com.google.android.apps.maps"
        val stringBuffer = StringBuffer("google.navigation:mode=").append(getGoogleNavigationValue(navigationType))
        stringBuffer.append("&q=").append(destLat).append(",").append(destLon)
        val intent = Intent("android.intent.action.VIEW", Uri.parse(stringBuffer.toString()))
        intent.setPackage(googleMapPackageName)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        if (AppUtils.isInstallApp(context, googleMapPackageName)) {
            context.startActivity(intent) //启动调用
        } else {
            Toast.makeText(context, R.string.google_map_not_install, Toast.LENGTH_SHORT).show()
        }
    }
    private fun getGoogleNavigationValue(navigationType: Int): String {
        //"d 为汽车 w 为行人 b 为自行车"
        if (navigationType == INavigation.NavigationType.WALKING) return "w"
        if (navigationType == INavigation.NavigationType.RIDING) return "b"
        return if (navigationType == INavigation.NavigationType.DRIVING) "d" else "b"
    }
    fun startBaiduByRide(act: Activity,startpos: LatLng, endpos: LatLng){
        // 获取导航控制类  引擎初始化
        BikeNavigateHelper.getInstance().initNaviEngine(act, object : IBEngineInitListener {
            override fun engineInitSuccess() {
                //起终点位置
                var startPt = BikeRouteNodeInfo()
                startPt.location = com.baidu.mapapi.model.LatLng(startpos.lat, startpos.lng)
                var endPt = BikeRouteNodeInfo()
                endPt.location = com.baidu.mapapi.model.LatLng(endpos.lat, endpos.lng)

                //构造BikeNaviLaunchParam.vehicle(0)默认的普通骑行导航
                var mBikeParam = BikeNaviLaunchParam().startNodeInfo(startPt).endNodeInfo(endPt).vehicle(0)
                //发起算路
                BikeNavigateHelper.getInstance()
                    .routePlanWithRouteNode(mBikeParam, object : IBRoutePlanListener {
                        override fun onRoutePlanStart() { }//执行算路开始的逻辑
                        override fun onRoutePlanSuccess() {
                            //算路成功  跳转至诱导页面
                            val intent: Intent = Intent(act, NaviActivity::class.java)
                            act.startActivity(intent)
                        }
                        override fun onRoutePlanFail(bikeRoutePlanError: BikeRoutePlanError) {
                            //执行算路失败的逻辑
                            MyToastUtil.toast(context.getString(R.string.s_nav_err_plan))
                        }
                    })
            }
            override fun engineInitFail() {
                //骑行导航初始化失败之后的回调
                MyToastUtil.toast(context.getString(R.string.s_nav_err_init))
            }
        })
    }
    fun startBaiduByWalk(act: Activity,startpos: LatLng, endpos: LatLng){
        // 获取导航控制类  引擎初始化
        BikeNavigateHelper.getInstance().initNaviEngine(act, object : IBEngineInitListener {
            override fun engineInitSuccess() {
                //起终点位置
                var startPt = WalkRouteNodeInfo()
                var endPt = WalkRouteNodeInfo()
                startPt.location = com.baidu.mapapi.model.LatLng(startpos.lat, startpos.lng)
                endPt.location = com.baidu.mapapi.model.LatLng(endpos.lat, endpos.lng)

                var mBikeParam = WalkNaviLaunchParam().startNodeInfo(startPt).endNodeInfo(endPt)
                //发起算路
                WalkNavigateHelper.getInstance()
                    .routePlanWithRouteNode(mBikeParam, object : IWRoutePlanListener {
                        override fun onRoutePlanStart() { }//执行算路开始的逻辑
                        override fun onRoutePlanSuccess() {
                            //算路成功  跳转至诱导页面
                            val intent: Intent = Intent(act, NaviActivity::class.java)
                            act.startActivity(intent)
                        }
                        override fun onRoutePlanFail(walkRoutePlanError: WalkRoutePlanError) {
                            //执行算路失败的逻辑
                            MyToastUtil.toast(context.getString(R.string.s_nav_err_plan))
                        }
                    })
            }
            override fun engineInitFail() {
                //骑行导航初始化失败之后的回调
                MyToastUtil.toast(context.getString(R.string.s_nav_err_init))
            }
        })
    }

    //获取本地国家码
    fun getLLd() : String{
        var lld = ""
        if (!SpUtil.Companion.getInstance().find(Constant.SpKey.SP_COUNTRY_sel).isNullOrEmpty()) {
            lld = SpUtil.Companion.getInstance().find(Constant.SpKey.SP_COUNTRY_sel)!!
        }
        return lld
    }

    //获取渠道
    fun getQuDao(): String? {
        var appInfo: ApplicationInfo? = null
        var msg: String? = ""
        try {
            appInfo = App.context.getPackageManager().getApplicationInfo(App.context.getPackageName(), PackageManager.GET_META_DATA)
            msg = appInfo.metaData.getInt("UMENG_CHANNEL").toString()
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return msg
    }

    //获取不同颜色字符串
    fun getSpanStr(unit : String,content : String,str_color : Int) : SpannableString{
        val spannableString = SpannableString(content)
        if (content.contains(unit.toString())){
            var data1 = spannableString.split(unit.toString())[0].length
            spannableString.setSpan(ForegroundColorSpan(ContextUtil.getContext().resources.getColor(str_color)), data1,
                data1+unit.toString().length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        return spannableString
    }
    //获取不同颜色字符串
    fun getSpanStr2(content : String,unit1 : String,unit2 : String,str_color1 : Int,str_color2 : Int) : SpannableString{
        val spannableString = SpannableString(content)
        if (content.contains(unit1.toString())){
            var data1 = spannableString.split(unit1.toString())[0].length
            spannableString.setSpan(ForegroundColorSpan(ContextUtil.getContext().resources.getColor(str_color1)), data1,
                data1+unit1.toString().length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        if (content.contains(unit2.toString())){
            var data1 = spannableString.split(unit2.toString())[0].length
            spannableString.setSpan(ForegroundColorSpan(ContextUtil.getContext().resources.getColor(str_color2)), data1,
                data1+unit2.toString().length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        return spannableString
    }
    fun concatenateSpannableStrings(vararg spannableStrings: SpannableString): SpannableString {
        val builder = StringBuilder()
        var currentIndex = 0
        spannableStrings.forEach { span ->
            builder.append(span)
            currentIndex += span.length
        }
        return SpannableString(builder)
    }




    fun determineAdvertisingInfo() {
        var gaid: String? = null
        try {
            check(Looper.myLooper() != Looper.getMainLooper()) { "Cannot be called from the main thread" }
            val advertisingIdInfo: AdvertisingIdClient.Info = AdvertisingIdClient.getAdvertisingIdInfo(ContextUtil.getContext())
            gaid = advertisingIdInfo.getId()
            if (!gaid.isNullOrEmpty()){
                CoroutinesUtil.launchMain {
                    MyToastUtil.toast(gaid!!)
                }
            }
            MyLogUtil.Log("7777","=========pangle    determineAdvertisingInfo==========="+ gaid)
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: GooglePlayServicesNotAvailableException) {
            e.printStackTrace()
        } catch (e: GooglePlayServicesRepairableException) {
            e.printStackTrace()
        }
    }
}