<?xml version="1.0" encoding="utf-8"?>
<!-- 根布局：整个非P免罚权益详情页的垂直布局容器 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:paddingTop="8dp">

    <!-- 顶部标题：会员权益解锁状态标题，默认隐藏，在特定条件下显示
         例如："白金会员权益使用" 或 其他会员等级的权益标题 -->
    <TextView
        android:id="@+id/tv_member2_unlock_status_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:textColor="@color/black_namal"
        android:textSize="14sp"
        android:textStyle="bold"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="0dp"
        tools:text="@string/s_vip_platinum_equity_use"
        android:visibility="gone"
        tools:visibility="visible"/>

    <!-- 主卡片容器：显示非P免罚图标和次数的蓝色圆形背景区域，正方形样式 -->
    <LinearLayout
        android:layout_width="240dp"
        android:layout_height="240dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"
        android:layout_gravity="center"
        android:background="@drawable/bg_benefit_card_placeholder" 
        android:orientation="vertical"
        android:gravity="center"
        android:paddingTop="24dp" 
        android:paddingBottom="12dp"> 

        <!-- 权益图标：显示非P免罚的图标，即禁止停车标志（P加斜线） -->
        <ImageView
            android:id="@+id/iv_non_parking_icon"
            android:layout_width="80dp" 
            android:layout_height="80dp"
            android:src="@drawable/ic_no_parking_placeholder"
            tools:ignore="ContentDescription" />

        <!-- 权益次数：显示用户拥有的非P免罚权益次数，例如"6次" -->
        <TextView
            android:id="@+id/tv_non_parking_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp" 
            android:textColor="@color/black_namal"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="2 @string/s_non_parking_benefit_count_suffix" />

    </LinearLayout>

    <!-- 格式化的权益描述：简洁描述权益内容，默认隐藏，可通过代码显示
         例如："会员期间累计可享受6次P点外还车免收调度费" -->
    <TextView
        android:id="@+id/tv_non_parking_formatted_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp" 
        android:gravity="center_horizontal"
        android:textColor="@color/c_838588" 
        android:textSize="13sp" 
        tools:text="会员期间累计可享受2次P点外还车免收调度费" 
        android:visibility="gone"
        tools:visibility="visible"/>

    <!-- 权益说明按钮：显示"权益说明"文本，点击可能会展开更多详细说明 -->
    <TextView
        android:id="@+id/tv_equity_explanation_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/s_vip_equity_title"
        android:textColor="@color/c_838588"
        android:textSize="14sp"
        android:visibility="visible"/>

    <!-- 权益详细描述：显示非P免罚权益的完整说明文本，界面底部较长描述 -->
    <TextView
        android:id="@+id/tv_non_parking_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:textColor="@color/c_838588" 
        android:textSize="14sp"
        tools:text="@string/s_non_parking_benefit_desc" />

    <!-- 添加弹性空间，用于控制底部bar位置 -->
    <Space
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />


    <!-- 底部横向bar：包含左右icon和蓝色文本，作为会员中心底部标识 -->
    <LinearLayout
        android:id="@+id/ll_bottom_bar_member2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="16dp"
        android:background="@android:color/transparent">

        <!-- 左侧装饰icon -->
        <ImageView
            android:id="@+id/iv_bottom_left_icon_member2"
            android:layout_width="80dp"
            android:layout_height="40dp"
            android:layout_marginEnd="10dp"
            android:src="@drawable/ic_dbhy_left"
            android:contentDescription="icon" />

        <!-- 底部蓝色文本：会员中心宣传语，支持多语言和换行 -->
        <TextView
            android:id="@+id/tv_bottom_text_member2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/s_vip_bottom_tip"
            android:textColor="@color/blue_namal"
            android:textSize="15sp"
            android:paddingStart="4dp"
            android:paddingEnd="4dp" />

        <!-- 右侧装饰icon -->
        <ImageView
            android:id="@+id/iv_bottom_right_icon_member2"
            android:layout_width="80dp"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:src="@drawable/ic_dbhy_right"
            android:contentDescription="icon" />
    </LinearLayout>

</LinearLayout>