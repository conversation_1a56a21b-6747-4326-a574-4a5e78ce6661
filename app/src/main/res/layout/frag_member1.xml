<?xml version="1.0" encoding="utf-8"?>
<!-- 根布局：会员专属券包页面的垂直布局容器 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingTop="8dp"
    android:paddingBottom="16dp">

    <!-- 内容区：顶部标题、优惠券列表、空状态容器 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- 顶部标题：会员权益解锁状态标题，默认隐藏，在特定条件下显示
             例如："白金会员权益使用"或其他会员等级的权益标题 -->
        <TextView
            android:id="@+id/tv_member1_unlock_status_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:textColor="@color/black_namal"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="0dp"
            tools:text="@string/s_vip_unlock_rights"
            android:visibility="gone"
            tools:visibility="visible"/>

        <!-- 优惠券列表：显示会员专属券包的RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_coupons_member1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:clipToPadding="false"
            android:paddingBottom="16dp"
            android:visibility="visible" />

        <!-- 空状态容器：当无可用优惠券时显示 -->
        <LinearLayout
            android:id="@+id/ll_empty_state_member1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <!-- 空状态图标 -->
            <ImageView
                android:id="@+id/iv_empty_box_member1"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_empty_box_placeholder"
                android:contentDescription="@string/s_no_content_available" />

            <!-- 空状态提示文本 -->
            <TextView
                android:id="@+id/tv_empty_text_member1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/s_no_content_available"
                android:textColor="@color/c_838588"
                android:textSize="14sp" />
        </LinearLayout>
    </FrameLayout>

    <!-- 新增：权益说明区域 -->
    <TextView
        android:id="@+id/tv_equity_desc_title_member1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text="@string/s_vip_equity_title"
        android:textColor="@color/c_838588"
        android:textSize="14sp"
        android:textStyle="bold"
        android:layout_marginTop="18dp"
        android:layout_marginBottom="4dp"
        android:visibility="visible"/>

    <!-- 权益说明内容：显示专属券包权益的详细说明 -->
    <TextView
        android:id="@+id/tv_equity_desc_content_member1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:textColor="@color/c_838588"
        android:textSize="14sp"
        android:lineSpacingExtra="2dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="8dp"
        android:gravity="center_horizontal"
        android:visibility="visible"
        tools:text="这里是专属券包权益的详细说明，由后端返回内容动态设置"/>

    <!-- 添加弹性空间，用于控制底部bar位置 -->
    <Space
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- 底部横向bar：包含左右icon和蓝色文本，作为会员中心底部标识 -->
    <LinearLayout
        android:id="@+id/ll_bottom_bar_member1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="16dp"
        android:background="@android:color/transparent">

        <!-- 左侧装饰icon -->
        <ImageView
            android:id="@+id/iv_bottom_left_icon_member1"
            android:layout_width="80dp"
            android:layout_height="40dp"
            android:layout_marginEnd="10dp"
            android:src="@drawable/ic_dbhy_left"
            android:contentDescription="icon" />

        <!-- 底部蓝色文本：会员中心宣传语，支持多语言和换行 -->
        <TextView
            android:id="@+id/tv_bottom_text_member1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/s_vip_bottom_tip"
            android:textColor="@color/blue_namal"
            android:textSize="15sp"
            android:paddingStart="4dp"
            android:paddingEnd="4dp" />

        <!-- 右侧装饰icon -->
        <ImageView
            android:id="@+id/iv_bottom_right_icon_member1"
            android:layout_width="80dp"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:src="@drawable/ic_dbhy_right"
            android:contentDescription="icon" />
    </LinearLayout>

</LinearLayout>