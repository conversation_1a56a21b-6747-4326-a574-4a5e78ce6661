<?xml version="1.0" encoding="utf-8"?>
<com.tbit.uqbike.roundview.RoundRelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="96dp"
    app:rv_backgroundColor="@color/white"
    app:rv_cornerRadius="10dp">
    <RelativeLayout
        android:id="@+id/rl_l"
        android:layout_width="96dp"
        android:layout_height="match_parent">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_invite_l_g"
            android:orientation="vertical">
            <ImageView
                android:layout_width="87dp"
                android:layout_height="55dp"
                android:layout_marginLeft="-25dp"
                android:layout_alignParentBottom="true"
                android:scaleType="fitXY"
                android:alpha="0.1"
                android:src="@drawable/ic_bike_invite"></ImageView>
            <LinearLayout
                android:id="@+id/ly_coupon_type1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_item_invite_amount"
                        android:paddingHorizontal="2dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/text_large"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        android:gravity="center"
                        tools:text="B4545"></TextView>
                    <TextView
                        android:id="@+id/tv_item_invite_unit"
                        android:paddingRight="2dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/text_smallest_10"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        android:gravity="center"
                        tools:text="THB"></TextView>
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_item_invite_amounthint"
                    android:paddingHorizontal="2dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/text_small"
                    android:textColor="@color/white"
                    android:gravity="center"
                    tools:text="B4545"></TextView>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ly_coupon_type2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:orientation="vertical">
                <TextView
                    android:id="@+id/tv_item_invite_typename"
                    android:paddingHorizontal="2dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/text_large"
                    android:textColor="@color/white"
                    android:gravity="center"
                    android:textStyle="bold"
                    tools:text="B4545"></TextView>
            </LinearLayout>
        </RelativeLayout>
    </RelativeLayout>

    <LinearLayout
        android:layout_toRightOf="@+id/rl_l"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@drawable/bg_invite_r_w">

        <LinearLayout
            android:layout_weight="1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="7dp"
            android:gravity="center_vertical"
            android:orientation="vertical">
            <TextView
                android:id="@+id/tv_item_invite_title"
                android:paddingHorizontal="2dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/text_default_s"
                android:textColor="@color/black_namal"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="B4545"></TextView>

            <TextView
                android:id="@+id/tv_item_invite_time"
                android:paddingHorizontal="2dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/text_small"
                android:textColor="@color/c_838588"
                tools:text="B4545"></TextView>

            <TextView
                android:id="@+id/tv_item_invite_super"
                android:paddingHorizontal="2dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:textSize="@dimen/text_small"
                android:textColor="@color/c_838588"
                android:text="@string/s_invite_usup"></TextView>
        </LinearLayout>
        <ImageView
            android:id="@+id/img_item_invite_sel"
            android:layout_marginHorizontal="7dp"
            android:layout_width="19dp"
            android:layout_height="19dp"
            android:src="@drawable/ic_sel_y"></ImageView>
    </LinearLayout>

    <com.tbit.uqbike.roundview.RoundLinearLayout
        android:layout_marginLeft="-5dp"
        android:layout_marginTop="-5dp"
        android:layout_toRightOf="@+id/rl_l"
        android:layout_width="10dp"
        android:layout_height="10dp"
        app:rv_backgroundColor="@color/bg_act_grey"
        app:rv_strokeWidth="1dp"
        app:rv_strokeColor="@color/c_line"
        app:rv_cornerRadius="10dp">
    </com.tbit.uqbike.roundview.RoundLinearLayout>

    <com.tbit.uqbike.roundview.RoundLinearLayout
        android:layout_marginLeft="-5dp"
        android:layout_marginBottom="-5dp"
        android:layout_toRightOf="@+id/rl_l"
        android:layout_alignParentBottom="true"
        android:layout_width="10dp"
        android:layout_height="10dp"
        app:rv_backgroundColor="@color/bg_act_grey"
        app:rv_strokeWidth="1dp"
        app:rv_strokeColor="@color/c_line"
        app:rv_cornerRadius="10dp">
    </com.tbit.uqbike.roundview.RoundLinearLayout>

    <!-- 右上角会员标识 -->
    <TextView
        android:id="@+id/tv_member_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/bg_member_tag_orange"
        android:text="@string/s_member_tag"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:paddingTop="2dp"
        android:paddingBottom="2dp"
        android:visibility="gone"
        android:gravity="center" />
</com.tbit.uqbike.roundview.RoundRelativeLayout>