<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="270dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/shape_round_white">
    <TextView
        android:id="@+id/mTvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="18dp"
        android:layout_marginRight="16dp"
        android:gravity="center"
        android:textStyle="bold"
        android:textColor="@color/black_namal"
        android:textSize="@dimen/text_large"
        tools:text="内容" />

    <ImageView
        android:visibility="gone"
        android:id="@+id/dig_img"
        android:layout_marginTop="15dp"
        android:layout_centerHorizontal="true"
        android:layout_below="@+id/mTvTitle"
        android:layout_width="100dp"
        android:layout_height="45dp"></ImageView>

    <TextView
        android:id="@+id/mTvSpecial"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/dig_img"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="13dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="25dp"
        android:gravity="center"
        android:textColor="@color/black_hint"
        android:textSize="@dimen/text_small"
        tools:visibility="visible"
        tools:text="内容" />

    <LinearLayout
        android:id="@+id/lay_middle"
        android:layout_width="match_parent"
        android:layout_height="42dp"
        android:layout_below="@+id/mTvSpecial"
        android:layout_marginHorizontal="@dimen/h_margin"
        android:layout_marginBottom="@dimen/v_margin"
        android:orientation="horizontal">

        <com.tbit.uqbike.roundview.RoundLinearLayout
            android:id="@+id/rl_left"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            app:rv_backgroundColor="#F7F8FA"
            app:rv_cornerRadius="25dp"
            android:layout_weight="1">
            <TextView
                android:id="@+id/mTvLeft"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="@string/cancel"
                android:textColor="@color/black_namal"
                android:textSize="@dimen/text_default" />
        </com.tbit.uqbike.roundview.RoundLinearLayout>

        <View
            android:id="@+id/vD"
            android:layout_width="10dp"
            android:layout_height="match_parent"
            android:background="#00000000" />

        <com.tbit.uqbike.roundview.RoundLinearLayout
            android:id="@+id/rl_right"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            app:rv_backgroundColor="@color/blue_namal"
            app:rv_cornerRadius="25dp"
            android:layout_weight="1">
            <TextView
                android:id="@+id/mTvRight"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="@string/ok"
                android:textColor="@color/white"
                android:textSize="@dimen/text_default" />
        </com.tbit.uqbike.roundview.RoundLinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ly_gop"
        android:visibility="gone"
        android:layout_below="@+id/lay_middle"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/v_margin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/go_p"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/blue_namal"
            android:textSize="@dimen/text_small"
            android:background="@drawable/underline_b"
            android:text="@string/s_go_p"></TextView>


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="3dp"
            android:textColor="@color/blue_namal"
            android:textSize="@dimen/text_small"
            android:text=">"></TextView>
    </LinearLayout>


</RelativeLayout>