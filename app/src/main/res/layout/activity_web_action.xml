<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <fragment
        android:id="@+id/read_media_permission_fragment"
        android:name="com.tbit.uqbike.fragment.ReadMediaPermissionFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        tools:ignore="MissingConstraints" />

    <fragment
        android:id="@+id/read_permission_fragment"
        android:name="com.tbit.uqbike.fragment.PermissionReadFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        tools:ignore="MissingConstraints" />

<!--    <include-->
<!--        android:id="@+id/layout_toolbar"-->
<!--        layout="@layout/layout_app_bar"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content" />-->

    <RelativeLayout
        android:id="@+id/toolbar"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="30dp"
        android:layout_width="match_parent"
        android:layout_height="50dp">
        <ImageView
            android:id="@+id/img_back"
            android:paddingLeft="13dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:src="@drawable/icon_back"></ImageView>

        <TextView
            android:id="@+id/toolbar_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_centerInParent="true"
            android:singleLine="true"
            tools:text="022000336"
            android:textColor="@color/black_namal"
            android:textSize="@dimen/text_title"
            android:textStyle="bold" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/ly_top"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:orientation="horizontal"></LinearLayout>

    <wendu.dsbridge.DWebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/ly_top"
        app:layout_constraintBottom_toBottomOf="parent"
        android:paddingStart="24dp"
        android:paddingEnd="24dp" />

</androidx.constraintlayout.widget.ConstraintLayout>
