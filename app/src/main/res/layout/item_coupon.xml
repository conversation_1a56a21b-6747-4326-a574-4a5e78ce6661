<?xml version="1.0" encoding="utf-8"?>
<!-- 优惠券列表item卡片的根布局，带圆角阴影 -->
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="12dp"
    android:layout_marginEnd="12dp"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    app:cardUseCompatPadding="false">

    <!-- 主体内容区域，使用ConstraintLayout实现复杂排版 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- 左侧彩色背景面板：根据券类型显示不同背景色 -->
        <View
            android:id="@+id/view_coupon_left_panel_bg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="@id/guideline_vertical"
            android:background="#F86125" />

        <!-- 右上内陷圆角 -->
        <View
            android:id="@+id/view_coupon_right_top_cut"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:background="@drawable/bg_coupon_cut_circle"
            android:layout_marginEnd="-6dp"
            android:layout_marginTop="-6dp"
            app:layout_constraintTop_toTopOf="@id/view_coupon_left_panel_bg"
            app:layout_constraintEnd_toEndOf="@id/view_coupon_left_panel_bg" />
        <!-- 右下内陷圆角 -->
        <View
            android:id="@+id/view_coupon_right_bottom_cut"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:background="@drawable/bg_coupon_cut_circle"
            android:layout_marginEnd="-6dp"
            android:layout_marginBottom="-6dp"
            app:layout_constraintBottom_toBottomOf="@id/view_coupon_left_panel_bg"
            app:layout_constraintEnd_toEndOf="@id/view_coupon_left_panel_bg" />

        <!-- 左侧橙色背景水印icon -->
        <ImageView
            android:id="@+id/iv_coupon_watermark_icon"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:src="@drawable/ic_bike_s"
            android:alpha="0.2"
            android:scaleType="fitCenter"
            android:tint="#33FFFFFF"
            app:layout_constraintStart_toStartOf="@id/view_coupon_left_panel_bg"
            app:layout_constraintBottom_toBottomOf="@id/view_coupon_left_panel_bg" />

        <!-- 垂直分割线，用于左右内容分区比例控制 -->
        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.3" />

        <!-- 水印状态标签：如"已使用" "已过期"，默认隐藏 -->
        <TextView
            android:id="@+id/tv_coupon_status_watermark"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/coupon_watermark_label_text_color"
            android:background="@drawable/bg_coupon_status_watermark"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:paddingTop="4dp"
            android:paddingBottom="4dp"
            android:visibility="gone"
            android:layout_marginStart="0dp"
            android:layout_marginTop="0dp"
            app:layout_constraintTop_toTopOf="@id/view_coupon_left_panel_bg"
            app:layout_constraintStart_toStartOf="@id/view_coupon_left_panel_bg"
            tools:text="已使用"
            tools:visibility="visible"/>

        <!-- 券面额数值：如"1.00" -->
        <TextView
            android:id="@+id/tv_coupon_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            android:textSize="30sp"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tv_coupon_threshold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_coupon_currency"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintVertical_bias="0.5"
            tools:text="1.00" />

        <!-- 券币种单位：如"THB" "元" -->
         <TextView
            android:id="@+id/tv_coupon_currency"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:layout_marginStart="2dp"
            app:layout_constraintBaseline_toBaselineOf="@id/tv_coupon_value"
            app:layout_constraintStart_toEndOf="@id/tv_coupon_value"
            app:layout_constraintEnd_toStartOf="@id/guideline_vertical"
            tools:text="THB" />

        <!-- 券门槛说明：如"无门槛" "满10元可用" -->
        <TextView
            android:id="@+id/tv_coupon_threshold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:layout_marginTop="2dp"
            app:layout_constraintTop_toBottomOf="@id/tv_coupon_value"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/guideline_vertical"
            tools:text="无门槛" />

        <!-- "权益说明 >"链接，点击可查看券规则，默认隐藏 -->
        <TextView
            android:id="@+id/tv_coupon_rules_link"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginBottom="16dp"
            android:text="权益说明 >"
            android:textColor="@color/text_gray_color"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/view_coupon_left_panel_bg"
            app:layout_constraintTop_toBottomOf="@id/tv_coupon_title" />

        <!-- 券标题：如"3天无限次骑行卡优惠券" -->
        <TextView
            android:id="@+id/tv_coupon_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/black_namal"
            android:textSize="15sp"
            android:textStyle="bold"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginStart="12dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="12dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@id/guideline_vertical"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="3天无限次骑行卡优惠券3天无限次骑行卡优惠券" />

        <!-- 券到期时间：如"今日23:59到期" -->
        <TextView
            android:id="@+id/tv_coupon_expiry"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/c_FF4F4F"
            android:textSize="12sp"
            android:layout_marginTop="4dp"
            app:layout_constraintTop_toBottomOf="@id/tv_coupon_title"
            app:layout_constraintStart_toStartOf="@id/tv_coupon_title"
            app:layout_constraintEnd_toEndOf="@id/tv_coupon_title"
            tools:text="今日23:59到期" />

        <!-- 券规则描述容器，包含规则文本和右箭头icon -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container_rules_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintTop_toBottomOf="@id/tv_coupon_expiry"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_coupon_title">

            <!-- 券规则文本：如"使用规则" -->
            <TextView
                android:id="@+id/tv_rules_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/s_rule_desc" 
                android:textColor="@color/c_838588"
                android:textSize="12sp"
                android:translationY="-2dp" 
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

            <!-- 右侧箭头icon，提示可点击查看更多规则 -->
            <ImageView
                android:id="@+id/iv_rules_arrow"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:src="@drawable/ic_arrow_right_gray"
                android:contentDescription="@string/s_rule_desc"
                android:scaleType="fitCenter"
                android:layout_marginStart="0dp"
                android:translationY="-1dp" 
                app:layout_constraintStart_toEndOf="@id/tv_rules_text"
                app:layout_constraintTop_toTopOf="@id/tv_rules_text"
                app:layout_constraintBottom_toBottomOf="@id/tv_rules_text" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- "立即使用"按钮，券可用时显示 -->
        <Button
            android:id="@+id/btn_coupon_use"
            android:layout_width="75dp"
            android:layout_height="30dp"
            android:background="@drawable/btn_outline_blue_background"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:text="@string/s_vip_coupon_btn_use" 
            android:textColor="@color/blue_namal"
            android:textSize="12sp"
            app:elevation="0dp"
            app:layout_constraintBottom_toBottomOf="@id/container_rules_description"
            app:layout_constraintEnd_toEndOf="@id/tv_coupon_title"
            app:layout_constraintTop_toTopOf="@id/container_rules_description"
            tools:visibility="visible" />

        <!-- "立即解锁"按钮，券锁定时显示 -->
        <Button
            android:id="@+id/btn_coupon_unlock"
            android:layout_width="75dp"
            android:layout_height="30dp"
            android:background="@drawable/btn_outline_gray_background"
            android:minWidth="0dp"
            android:minHeight="0dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:text="@string/s_vip_coupon_btn_lock" 
            android:textColor="@color/dark_grey"
            android:textSize="12sp"
            app:elevation="0dp"
            android:visibility="gone" 
            app:layout_constraintBottom_toBottomOf="@id/container_rules_description"
            app:layout_constraintEnd_toEndOf="@id/tv_coupon_title"
            app:layout_constraintTop_toTopOf="@id/container_rules_description"
            tools:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>