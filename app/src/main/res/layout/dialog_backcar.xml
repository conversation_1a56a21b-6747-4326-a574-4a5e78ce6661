<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.tbit.uqbike.roundview.RoundLinearLayout
        app:rv_cornerRadius="20dp"
        app:rv_backgroundColor="@color/white"
        android:layout_marginTop="24dp"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        android:layout_width="288dp"
        android:layout_height="wrap_content"
        android:paddingHorizontal="20dp"
        tools:ignore="MissingConstraints">

        <TextView
            android:id="@+id/tv_diag_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="1dp"
            android:layout_marginTop="20dp"
            android:paddingHorizontal="10dp"
            android:textSize="@dimen/text_large"
            android:textStyle="bold"
            android:textColor="@color/black_namal"
            android:text="@string/dialog_tip"></TextView>
        <TextView
            android:id="@+id/mTvSpecial"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:gravity="center"
            android:textColor="@color/black_hint"
            android:textSize="@dimen/text_default_s"
            tools:text="内容" />

        <com.tbit.uqbike.roundview.RoundLinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:rv_backgroundColor="@color/c_grey"
            app:rv_cornerRadius="8dp"
            android:layout_marginTop="10dp"
            android:paddingHorizontal="10dp"
            android:paddingVertical="8dp"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/tv_diog_backcar_payamount_hint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/black_hint"
                    android:textSize="@dimen/text_default_s"
                    android:textStyle="bold"
                    tools:text="支付费用(THB)"></TextView>

                <TextView
                    android:id="@+id/tv_diog_backcar_payamount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black_hint"
                    android:textSize="@dimen/text_default_s"
                    android:textStyle="bold"
                    tools:text="10"></TextView>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginVertical="7dp"
                android:background="@color/c_line"></View>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/tv_diog_backcar_rideamount_hint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/c_838588"
                    android:textSize="@dimen/text_small"
                    tools:text="支付费用(THB)"></TextView>

                <TextView
                    android:id="@+id/tv_diog_backcar_rideamount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/c_838588"
                    android:textSize="@dimen/text_small"
                    android:textStyle="bold"
                    tools:text="10"></TextView>
            </LinearLayout>
            <LinearLayout
                android:visibility="gone"
                android:id="@+id/ly_diog_backcar_couponamount"
                android:layout_marginTop="3dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/tv_diog_backcar_couponamount_hint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/c_F86125"
                    android:textSize="@dimen/text_small"
                    tools:text="支付费用(THB)"></TextView>

                <TextView
                    android:id="@+id/tv_diog_backcar_couponamount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/c_F86125"
                    android:textSize="@dimen/text_smallest_10"
                    android:textStyle="bold"
                    tools:text="10"></TextView>
                <ImageView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_marginLeft="2dp"
                    android:tint="@color/c_838588"
                    android:src="@drawable/ic_go"></ImageView>
            </LinearLayout>
            <LinearLayout
                android:layout_marginTop="7dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/tv_diog_backcar_dispamount_hint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/c_838588"
                    android:textSize="@dimen/text_small"
                    tools:text="支付费用(THB)"></TextView>

                <TextView
                    android:id="@+id/tv_diog_backcar_dispamount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/c_838588"
                    android:textSize="@dimen/text_small"
                    android:textStyle="bold"
                    tools:text="10"></TextView>
                
                <TextView
                    android:id="@+id/tv_diog_backcar_dispamount_tag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="6dp"
                    android:textColor="@color/c_E7544C"
                    android:textSize="@dimen/text_smallest_10"
                    android:background="@drawable/bg_tag_rounded"
                    android:paddingLeft="6dp"
                    android:paddingRight="6dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:visibility="gone"
                    tools:text="已免除" />
            </LinearLayout>

        </com.tbit.uqbike.roundview.RoundLinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal">
            <com.tbit.uqbike.roundview.RoundTextView
                android:id="@+id/tv_close"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:rv_backgroundColor="@color/c_grey"
                app:rv_cornerRadius="25dp"
                android:paddingHorizontal="10dp"
                android:textSize="@dimen/text_default"
                android:textColor="@color/black_namal"
                android:gravity="center"
                android:text="@string/cancel"></com.tbit.uqbike.roundview.RoundTextView>
            <View
                android:layout_width="10dp"
                android:layout_height="1dp"></View>
            <com.tbit.uqbike.roundview.RoundTextView
                android:id="@+id/tv_sure"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:rv_backgroundColor="@color/blue_namal"
                app:rv_cornerRadius="25dp"
                android:paddingHorizontal="10dp"
                android:textSize="@dimen/text_default"
                android:textColor="@color/white"
                android:gravity="center"
                android:text="@string/s_backcar"></com.tbit.uqbike.roundview.RoundTextView>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ly_gop"
            android:visibility="gone"
            android:layout_below="@+id/lay_middle"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/v_margin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/go_p"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/blue_namal"
                android:textSize="@dimen/text_small"
                android:background="@drawable/underline_b"
                android:text="@string/s_go_p"></TextView>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="3dp"
                android:textColor="@color/blue_namal"
                android:textSize="@dimen/text_small"
                android:text=">"></TextView>
        </LinearLayout>

    </com.tbit.uqbike.roundview.RoundLinearLayout>

    <ImageView
        android:id="@+id/img_diag_ts"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_centerHorizontal="true"
        android:layout_width="49dp"
        android:layout_height="49dp"
        android:src="@drawable/ic_diag_ts"></ImageView>
</RelativeLayout>