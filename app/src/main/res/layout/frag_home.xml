<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <fragment
        android:id="@+id/fragment_camera_permission"
        android:name="com.tbit.uqbike.fragment.CameraPermissionFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <fragment
        android:id="@+id/main_upda_fragment"
        android:name="com.tbit.uqbike.fragment.MainBusinessFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.tbit.uqbike.widget.CustomNestedScrollView
        android:id="@+id/nscl"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#F2F4F8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/frag_home"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <fragment
                android:id="@+id/main_ad_fragment"
                android:name="com.tbit.uqbike.fragment.HomeAdFragment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                tools:layout="@layout/fragment_home_ad" />

            <LinearLayout
                android:id="@+id/layout_ad_area"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="-20dp"
                android:background="@drawable/bg_home_bom"
                android:orientation="vertical"
                android:paddingHorizontal="16dp">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="14dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <com.tbit.uqbike.widget.RoundRelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@color/transparent">

                            <fragment
                                android:id="@+id/main_map_fragment"
                                android:name="com.tbit.uqbike.fragment.MainMapFragment"
                                android:layout_width="match_parent"
                                android:layout_height="140dp"
                                tools:layout="@layout/fragment_main_map" />

                            <RelativeLayout
                                android:id="@+id/rl_map"
                                android:layout_width="352dp"
                                android:layout_height="119dp">

                                <ImageView
                                    android:id="@+id/image_big"
                                    android:layout_width="50dp"
                                    android:layout_height="50dp"
                                    android:layout_alignParentRight="true"
                                    android:layout_alignParentBottom="true"
                                    android:src="@drawable/ic_fd" />
                            </RelativeLayout>
                        </com.tbit.uqbike.widget.RoundRelativeLayout>

                        <RelativeLayout
                            android:id="@+id/ry_scan"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent">

                            <LinearLayout
                                android:id="@+id/ly_scan"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:paddingBottom="20dp">

                                <RelativeLayout
                                    android:id="@+id/rl_scanner"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginHorizontal="20dp"
                                    android:layout_marginTop="13dp">

                                    <LinearLayout
                                        android:id="@+id/ly_uriding"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:background="@drawable/selector_shape_scanner"
                                        android:backgroundTint="@color/blue_namal"
                                        android:gravity="center">

                                        <ImageView
                                            android:id="@+id/image_scanner"
                                            android:layout_width="18dp"
                                            android:layout_height="18dp"
                                            android:layout_marginBottom="3dp"
                                            android:src="@drawable/ic_scanner" />

                                        <TextView
                                            android:id="@+id/text_scanner"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginLeft="5dp"
                                            android:layout_marginBottom="3dp"
                                            android:layout_toRightOf="@id/image_scanner"
                                            android:gravity="center"
                                            android:text="@string/scan_unlock"
                                            android:textColor="@color/white"
                                            android:textSize="@dimen/text_large"
                                            android:textStyle="bold" />
                                    </LinearLayout>

                                    <LinearLayout
                                        android:id="@+id/ly_riding_btn"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:backgroundTint="@color/blue_namal"
                                        android:gravity="center_vertical"
                                        android:orientation="horizontal"
                                        android:paddingBottom="13dp"
                                        android:visibility="gone">

                                        <LinearLayout
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_weight="1"
                                            android:orientation="vertical">

                                            <LinearLayout
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:gravity="center_vertical"
                                                android:orientation="horizontal">

                                                <ImageView
                                                    android:layout_width="23dp"
                                                    android:layout_height="23dp"
                                                    android:src="@drawable/ic_che"></ImageView>

                                                <TextView
                                                    android:id="@+id/tv_riding_title"
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:layout_marginLeft="10dp"
                                                    android:textColor="@color/black_namal"
                                                    android:textSize="@dimen/text_default_s"
                                                    tools:text="骑行中"></TextView>
                                            </LinearLayout>

                                            <TextView
                                                android:id="@+id/tv_riding_cont"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_marginTop="3dp"
                                                android:textColor="@color/black_namal"
                                                android:textSize="@dimen/text_small"
                                                tools:text="已骑行2分钟，骑行费用9THB"></TextView>
                                        </LinearLayout>


                                        <com.tbit.uqbike.roundview.RoundTextView
                                            android:id="@+id/rv_seeOrder"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginLeft="10dp"
                                            android:paddingHorizontal="25dp"
                                            android:paddingVertical="6dp"
                                            android:textColor="@color/white"
                                            android:textSize="@dimen/text_default_s"
                                            app:rv_backgroundColor="@color/blue_namal"
                                            app:rv_cornerRadius="25dp"
                                            tools:text="查看"></com.tbit.uqbike.roundview.RoundTextView>
                                    </LinearLayout>
                                </RelativeLayout>

                                <LinearLayout
                                    android:id="@+id/ly_bom_menu"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:orientation="horizontal">

                                    <LinearLayout
                                        android:id="@+id/ly_menu_balance"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:orientation="vertical"
                                        android:paddingHorizontal="2dp">

                                        <ImageView
                                            android:layout_width="23dp"
                                            android:layout_height="23dp"
                                            android:src="@drawable/ic_menu_qbye"></ImageView>

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="1dp"
                                            android:gravity="center"
                                            android:text="@string/s_wallet_recharge"
                                            android:textColor="@color/black_namal"
                                            android:textSize="@dimen/text_small"></TextView>
                                    </LinearLayout>

                                    <LinearLayout
                                        android:id="@+id/ly_menu_hotline"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:orientation="vertical"
                                        android:paddingHorizontal="2dp">

                                        <ImageView
                                            android:layout_width="23dp"
                                            android:layout_height="23dp"
                                            android:src="@drawable/ic_menu_kf"></ImageView>

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="1dp"
                                            android:gravity="center"
                                            android:text="@string/contact_customer_service"
                                            android:textColor="@color/black_namal"
                                            android:textSize="@dimen/text_small"></TextView>
                                    </LinearLayout>

                                    <LinearLayout
                                        android:id="@+id/ly_menu_cost"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:orientation="vertical"
                                        android:paddingHorizontal="2dp">

                                        <ImageView
                                            android:layout_width="23dp"
                                            android:layout_height="23dp"
                                            android:src="@drawable/ic_menu_jf"></ImageView>

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="1dp"
                                            android:gravity="center"
                                            android:text="@string/billing_rules"
                                            android:textColor="@color/black_namal"
                                            android:textSize="@dimen/text_small"></TextView>
                                    </LinearLayout>

                                    <LinearLayout
                                        android:id="@+id/ly_menu_more"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:gravity="center"
                                        android:orientation="vertical"
                                        android:paddingHorizontal="2dp">

                                        <ImageView
                                            android:layout_width="23dp"
                                            android:layout_height="23dp"
                                            android:src="@drawable/ic_menu_gd"></ImageView>

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="1dp"
                                            android:gravity="center"
                                            android:text="@string/s_more_gn"
                                            android:textColor="@color/black_namal"
                                            android:textSize="@dimen/text_small"></TextView>
                                    </LinearLayout>
                                </LinearLayout>

                                <ImageView
                                    android:id="@+id/im_image0"
                                    android:layout_width="match_parent"
                                    android:layout_height="80dp"
                                    android:layout_marginTop="10dp"
                                    android:paddingHorizontal="0dp"
                                    android:scaleType="fitXY"
                                    tools:visibility="invisible"></ImageView>

                                <ImageView
                                    android:id="@+id/im_image1"
                                    android:layout_width="match_parent"
                                    android:layout_height="80dp"
                                    android:layout_marginTop="10dp"
                                    android:paddingHorizontal="0dp"
                                    android:scaleType="fitXY"
                                    tools:visibility="invisible"></ImageView>

                                <ImageView
                                    android:id="@+id/im_image2"
                                    android:layout_width="match_parent"
                                    android:layout_height="80dp"
                                    android:layout_marginTop="10dp"
                                    android:paddingHorizontal="0dp"
                                    android:scaleType="fitXY"
                                    tools:visibility="invisible"></ImageView>

                                <ImageView
                                    android:id="@+id/im_image3"
                                    android:layout_width="match_parent"
                                    android:layout_height="80dp"
                                    android:layout_marginTop="10dp"
                                    android:paddingHorizontal="0dp"
                                    android:scaleType="fitXY"
                                    tools:visibility="invisible"></ImageView>

                                <ImageView
                                    android:id="@+id/im_image4"
                                    android:layout_width="match_parent"
                                    android:layout_height="80dp"
                                    android:layout_marginTop="10dp"
                                    android:paddingHorizontal="0dp"
                                    android:scaleType="fitXY"
                                    tools:visibility="invisible"></ImageView>

                                <FrameLayout
                                    android:id="@+id/layout_h5_area"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:visibility="gone"
                                    android:layout_marginTop="12dp"
                                    android:layout_marginBottom="12dp" />
                            </LinearLayout>
                        </RelativeLayout>
                    </LinearLayout>


                </RelativeLayout>
            </LinearLayout>


        </LinearLayout>

    </com.tbit.uqbike.widget.CustomNestedScrollView>

    <fragment
        android:id="@+id/main_adfull_fragment"
        android:name="com.tbit.uqbike.fragment.MainAdFullFragment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>