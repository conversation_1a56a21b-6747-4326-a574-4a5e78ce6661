<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="20dp"
    android:gravity="center">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="分享返利功能测试"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="40dp"
        android:textColor="#333333" />

    <Button
        android:id="@+id/btn_test_local_html"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:text="测试本地HTML页面"
        android:textSize="16sp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/primary_btn_shape"
        android:textColor="@android:color/white" />

    <Button
        android:id="@+id/btn_test_dialog_directly"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:text="直接测试分享弹窗"
        android:textSize="16sp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/white_btn_shape"
        android:textColor="#333333" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="说明：\n1. 第一个按钮测试H5页面调用原生分享\n2. 第二个按钮直接测试分享弹窗UI"
        android:textSize="14sp"
        android:textColor="#666666"
        android:layout_marginTop="20dp"
        android:gravity="center" />

</LinearLayout>
