<?xml version="1.0" encoding="utf-8"?>
<!-- 根布局：骑行卡延时权益详情页的垂直布局容器 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:paddingTop="8dp">

    <!-- 顶部标题：会员权益解锁状态标题，默认隐藏，在特定条件下显示
         例如："黄金会员权益使用"或其他会员等级的权益标题 -->
    <TextView
        android:id="@+id/tv_member3_unlock_status_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:textColor="@color/black_namal"
        android:textSize="14sp"
        android:textStyle="bold"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="0dp"
        tools:text="@string/s_vip_gold_equity_use"
        android:visibility="gone"
        tools:visibility="visible"/>

    <!-- 主卡片容器：显示骑行卡图标和延时时间的蓝色圆形背景区域，正方形样式 -->
    <LinearLayout
        android:layout_width="240dp"
        android:layout_height="240dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"
        android:layout_gravity="center"
        android:background="@drawable/bg_benefit_card_placeholder" 
        android:orientation="vertical"
        android:gravity="center"
        android:paddingTop="24dp" 
        android:paddingBottom="12dp"> 

        <!-- 权益图标：显示骑行卡延时的图标（蓝色骑行卡图标） -->
        <ImageView
            android:id="@+id/iv_ride_card_icon"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@drawable/ic_qxk" 
            tools:ignore="ContentDescription" />

        <!-- 权益时间：显示骑行卡延时的时间值，例如"+20分钟/次"，粗体显示，较大字号 -->
        <TextView
            android:id="@+id/tv_ride_card_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp" 
            android:textColor="@color/black_namal"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="+20 @string/s_vip_minute_times" />

    </LinearLayout>

    <!-- 格式化的权益描述：简洁描述骑行卡延时的权益内容，默认隐藏，可通过代码显示
         例如："会员期间使用骑行卡骑行，每次免费骑行时间增加20分钟" -->
    <TextView
        android:id="@+id/tv_ride_card_formatted_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:gravity="center_horizontal"
        android:textColor="@color/c_838588"
        android:textSize="14sp"
        tools:text="@string/s_vip_ridecard_desc"
        android:visibility="gone"
        tools:visibility="visible" />

    <!-- 权益说明按钮：显示"权益说明"文本，点击可能会展开更多详细说明 -->
    <TextView
        android:id="@+id/tv_equity_explanation_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/s_vip_equity_title"
        android:textColor="@color/c_838588"
        android:textSize="14sp"
        android:visibility="visible"/>

    <!-- 权益详细描述：显示骑行卡延时权益的完整说明文本，卡片下方灰色描述文字 -->
    <TextView
        android:id="@+id/tv_ride_card_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:textColor="@color/c_838588"
        android:textSize="14sp"
        tools:text="@string/s_ride_card_benefit_desc" />

    <!-- 添加弹性空间，用于控制底部bar位置 -->
    <Space
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- 底部横向bar：包含左右icon和蓝色文本，作为会员中心底部标识 -->
    <LinearLayout
        android:id="@+id/ll_bottom_bar_member3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="16dp"
        android:background="@android:color/transparent">

        <!-- 左侧装饰icon -->
        <ImageView
            android:id="@+id/iv_bottom_left_icon_member3"
            android:layout_width="80dp"
            android:layout_height="40dp"
            android:layout_marginEnd="10dp"
            android:src="@drawable/ic_dbhy_left"
            android:contentDescription="icon" />

        <!-- 底部蓝色文本：会员中心宣传语，支持多语言和换行 -->
        <TextView
            android:id="@+id/tv_bottom_text_member3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/s_vip_bottom_tip"
            android:textColor="@color/blue_namal"
            android:textSize="15sp"
            android:paddingStart="4dp"
            android:paddingEnd="4dp" />

        <!-- 右侧装饰icon -->
        <ImageView
            android:id="@+id/iv_bottom_right_icon_member3"
            android:layout_width="80dp"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:src="@drawable/ic_dbhy_right"
            android:contentDescription="icon" />
    </LinearLayout>

</LinearLayout>