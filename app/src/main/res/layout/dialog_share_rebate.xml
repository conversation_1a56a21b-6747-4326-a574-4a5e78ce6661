<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_dialog_simple_bg"
    android:padding="20dp">

    <!-- 关闭按钮 -->
    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/ic_close_black_24dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="32dp"
        android:text="@string/s_share_rebate_title"
        android:textColor="@color/black_namal"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/iv_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 副标题 -->
    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/s_share_rebate_subtitle"
        android:textColor="@color/c_838588"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <!-- 佣金层级容器 -->
    <LinearLayout
        android:id="@+id/ll_rebate_levels"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:orientation="vertical"
        android:background="@drawable/shape_round_grey"
        android:backgroundTint="@color/bg_act"
        android:padding="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_subtitle">

        <!-- 佣金层级项目会动态添加到这里 -->

    </LinearLayout>

    <!-- 总收益 -->
    <TextView
        android:id="@+id/tv_total_earnings"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/shape_round_primary"
        android:backgroundTint="@color/c_E00000"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_rebate_levels"
        tools:text="你的总收益 21%" />

    <!-- 分享按钮容器 -->
    <LinearLayout
        android:id="@+id/ll_share_buttons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_total_earnings">

        <!-- WhatsApp -->
        <LinearLayout
            android:id="@+id/ll_whatsapp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground"
            android:padding="12dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_whatsapp"
                android:background="@drawable/shape_ellipse"
                android:backgroundTint="@color/c_00C420"
                android:padding="12dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/s_share_rebate_whatsapp"
                android:textColor="@color/black_namal"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- Line -->
        <LinearLayout
            android:id="@+id/ll_line"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground"
            android:padding="12dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_line"
                android:background="@drawable/shape_ellipse"
                android:backgroundTint="@color/c_00C420"
                android:padding="12dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/s_share_rebate_line"
                android:textColor="@color/black_namal"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- Facebook -->
        <LinearLayout
            android:id="@+id/ll_facebook"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground"
            android:padding="12dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_facebook"
                android:background="@drawable/shape_ellipse"
                android:backgroundTint="@color/blue_namal"
                android:padding="12dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/s_share_rebate_facebook"
                android:textColor="@color/black_namal"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- 复制链接 -->
        <LinearLayout
            android:id="@+id/ll_copy_link"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?android:attr/selectableItemBackground"
            android:padding="12dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_copy"
                android:background="@drawable/shape_ellipse"
                android:backgroundTint="@color/c_838588"
                android:padding="12dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/s_share_rebate_copy_link"
                android:textColor="@color/black_namal"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
