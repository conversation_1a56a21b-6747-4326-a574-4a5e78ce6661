<?xml version="1.0" encoding="utf-8"?>
<!-- 根布局：整个会员中心页面的容器 -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    android:background="@color/bg_act"
    android:orientation="vertical">
    
    <!-- 滚动容器：允许整个内容区域可滚动 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsv"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        
        <!-- 内容容器：包含所有会员信息和权益内容 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            
            <!-- 背景图片：会员卡片的背景图，根据会员等级变化 -->
            <ImageView
                android:id="@+id/img_member_bg"
                android:layout_width="match_parent"
                android:layout_height="440dp"></ImageView>
                
            <!-- 主内容容器：垂直排列所有会员中心内容 -->
            <LinearLayout
                android:id="@+id/cl_head"
                android:layout_marginTop="80dp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <!-- 会员卡片轮播：显示会员卡片（如青铜、白银、黄金、白金会员卡） -->
                <com.zhpan.bannerview.BannerViewPager
                    app:layout_constraintTop_toBottomOf="@+id/title_my"
                    android:id="@+id/banner_view"
                    android:layout_width="match_parent"
                    android:layout_height="200dp" />

                <!-- 会员权益标题区域：显示"xx会员权益"文本及两侧装饰 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">
                    <!-- 左侧装饰线 -->
                    <ImageView
                        android:id="@+id/img_qy_l"
                        android:layout_width="71dp"
                        android:layout_height="11dp"
                        android:scaleType="fitXY"
                        android:src="@drawable/ic_zs_qt"></ImageView>

                    <!-- 会员权益标题文本 -->
                    <TextView
                        android:id="@+id/tv_member_qy"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/c_5F6B7E"
                        android:layout_marginHorizontal="10dp"
                        android:textSize="@dimen/text_default_s"
                        tools:text="青铜会员权益"></TextView>

                    <!-- 右侧装饰线 -->
                    <ImageView
                        android:id="@+id/img_qy_r"
                        android:layout_width="71dp"
                        android:layout_height="11dp"
                        android:scaleType="fitXY"
                        android:src="@drawable/ic_zs_qt1"></ImageView>
                </LinearLayout>

                <!-- 权益图标区域：显示三个权益图标及其数量（专属券包、非P免罚、骑行卡延时） -->
                <LinearLayout
                    android:layout_marginTop="13dp"
                    android:layout_marginHorizontal="15dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_horizontal">
                    
                    <!-- 专属券包权益图标区域 -->
                    <LinearLayout
                        android:id="@+id/ly_member_zsqb"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">
                        <!-- 图标容器 -->
                        <RelativeLayout
                            android:layout_width="40dp"
                            android:layout_height="40dp">
                            <!-- 主图标 -->
                            <ImageView
                                android:id="@+id/img_member_zsqb"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:src="@drawable/ic_qt_qb"></ImageView>
                            <!-- 右下角的特殊标记 -->
                            <ImageView
                                android:id="@+id/img_member_zsqb_s"
                                android:layout_width="14dp"
                                android:layout_height="14dp"
                                android:layout_alignParentBottom="true"
                                android:layout_alignParentRight="true"
                                android:src="@drawable/ic_s_bj"></ImageView>
                        </RelativeLayout>
                        <!-- 权益名称 -->
                        <TextView
                            android:id="@+id/tv_member1"
                            android:layout_marginTop="6dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black_namal"
                            android:textSize="@dimen/text_default_s"
                            android:text="@string/s_zsqb"></TextView>

                        <!-- 权益数量 -->
                        <TextView
                            android:layout_marginTop="4dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/tv_member_zsqb_count"
                            android:textColor="@color/c_838588"
                            android:textSize="@dimen/text_small"
                            tools:text="5张"></TextView>
                        <!-- 选中指示器箭头 -->
                        <ImageView
                            android:id="@+id/iv_arrow_zsqb"
                            android:layout_width="38dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_xz"
                            android:visibility="visible"></ImageView>
                    </LinearLayout>
                    
                    <!-- 非P免罚权益图标区域 -->
                    <LinearLayout
                        android:id="@+id/ly_member_fpmf"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">
                        <!-- 图标容器 -->
                        <RelativeLayout
                            android:layout_width="40dp"
                            android:layout_height="40dp">
                            <!-- 主图标 -->
                            <ImageView
                                android:id="@+id/img_member_fpmf"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:src="@drawable/ic_fp_bj"></ImageView>
                            <!-- 右下角的特殊标记 -->
                            <ImageView
                                android:id="@+id/img_member_fpmf_s"
                                android:layout_width="14dp"
                                android:layout_height="14dp"
                                android:layout_alignParentBottom="true"
                                android:layout_alignParentRight="true"
                                android:src="@drawable/ic_s_bj"></ImageView>
                        </RelativeLayout>
                        <!-- 权益名称 -->
                        <TextView
                            android:id="@+id/tv_member2"
                            android:layout_marginTop="6dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black_namal"
                            android:textSize="@dimen/text_default_s"
                            android:text="@string/s_fpmf"></TextView>

                        <!-- 权益数量 -->
                        <TextView
                            android:layout_marginTop="4dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/tv_member_fpmf_count"
                            android:textColor="@color/c_838588"
                            android:textSize="@dimen/text_small"
                            tools:text="6次"></TextView>
                        <!-- 选中指示器箭头 -->
                        <ImageView
                            android:id="@+id/iv_arrow_fpmf"
                            android:layout_width="38dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_xz"
                            android:visibility="invisible"></ImageView>
                    </LinearLayout>
                    
                    <!-- 骑行卡延时权益图标区域 -->
                    <LinearLayout
                        android:id="@+id/ly_member_qxk"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal"
                        android:orientation="vertical">
                        <!-- 图标容器 -->
                        <RelativeLayout
                            android:layout_width="40dp"
                            android:layout_height="40dp">
                            <!-- 主图标 -->
                            <ImageView
                                android:id="@+id/img_member_qxk"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:src="@drawable/ic_qxk_bj"></ImageView>
                            <!-- 右下角的特殊标记 -->
                            <ImageView
                                android:id="@+id/img_member_qxk_s"
                                android:layout_width="14dp"
                                android:layout_height="14dp"
                                android:layout_alignParentBottom="true"
                                android:layout_alignParentRight="true"
                                android:src="@drawable/ic_s_bj"></ImageView>
                        </RelativeLayout>
                        <!-- 权益名称 -->
                        <TextView
                            android:id="@+id/tv_member3"
                            android:layout_marginTop="6dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black_namal"
                            android:textSize="@dimen/text_default_s"
                            android:text="@string/s_qxkys"></TextView>

                        <!-- 权益数量 -->
                        <TextView
                            android:layout_marginTop="4dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/tv_member_qxk_count"
                            android:textColor="@color/c_838588"
                            android:textSize="@dimen/text_small"
                            android:text="@string/min"
                            tools:text="1分钟/次" />
                        <!-- 选中指示器箭头 -->
                        <ImageView
                            android:id="@+id/iv_arrow_qxk"
                            android:layout_width="38dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_xz"
                            android:visibility="invisible"></ImageView>
                    </LinearLayout>
                </LinearLayout>

                <!-- 权益详情区域：加载不同的Fragment（如frag_member1/2/3.xml）展示具体权益内容 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="-8dp"
                    android:background="@drawable/bg_home_bom">
                    <!-- ViewPager2：用于切换不同权益的详情页面 -->
                    <androidx.viewpager2.widget.ViewPager2
                        android:id="@+id/vp"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"/>
                </LinearLayout>

            </LinearLayout>


        </RelativeLayout>

    </androidx.core.widget.NestedScrollView>
    
    <!-- 顶部标题栏：显示标题和返回按钮，固定在顶部 -->
    <RelativeLayout
        android:id="@+id/rv_member_title"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:paddingBottom="10dp">
        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="29dp"
            android:layout_height="29dp"
            android:layout_gravity="clip_vertical"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="10dp"
            android:src="@drawable/icon_back"></ImageView>

        <!-- 页面标题：会员中心 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black_namal"
            android:layout_alignParentBottom="true"
            android:textSize="@dimen/text_title"
            android:textStyle="bold"
            android:layout_centerHorizontal="true"
            android:text="@string/s_member"></TextView>

        <!-- 规则按钮：点击查看会员规则 -->
        <TextView
            android:id="@+id/tv_rule"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black_namal"
            android:layout_alignParentBottom="true"
            android:textSize="@dimen/text_default_s"
            android:layout_marginRight="@dimen/h_margin"
            android:layout_alignParentRight="true"
            android:text="@string/s_rule"></TextView>
    </RelativeLayout>

</RelativeLayout>
