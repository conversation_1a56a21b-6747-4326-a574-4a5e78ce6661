<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 会员卡片背景图片：根据会员等级显示不同背景，填充整个卡片区域 -->
    <ImageView
        android:id="@+id/banner_image"
        android:layout_marginTop="30dp"
        android:layout_marginBottom="5dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY" />

    <!-- 会员卡片高光装饰图：右上角高光效果，增强卡片质感 -->
    <ImageView
        android:id="@+id/banner_image_hg"
        android:layout_alignParentRight="true"
        android:layout_marginRight="26dp"
        android:layout_marginTop="18dp"
        android:layout_width="115dp"
        android:layout_height="115dp"
        android:scaleType="centerCrop" />

    <!-- 会员卡片底部信息区域：垂直排列会员名称、有效期、里程、升级提示等 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:paddingBottom="30dp"
        android:layout_marginHorizontal="20dp"
        android:gravity="center_vertical">
        <!-- 会员名称：如"黄金会员" -->
        <TextView
            android:id="@+id/item_tv_member_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textColor="@color/black_namal"
            android:textSize="@dimen/text_largestMax"
            android:text="黄金会员" />
        <!-- 会员有效期：如"有效期2025.03.03-2025.04.02" -->
        <TextView
            android:id="@+id/item_tv_member_time"
            android:layout_marginTop="10dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black_namal"
            android:textSize="@dimen/text_smallest_10"
            android:text="有效期2025.03.03-2025.04.02" />
        <!-- 会员里程信息区域：显示当前里程和问号说明 -->
        <LinearLayout
            android:layout_marginTop="15dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">
            <!-- 当前里程文本：如"里程15.56km" -->
            <TextView
                android:id="@+id/item_tv_member_disance"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:textColor="@color/black_namal"
                android:textSize="@dimen/text_smallest_10"
                android:text="里程15.56km" />
            <!-- 里程说明问号icon：点击弹出说明 -->
            <ImageView
                android:id="@+id/item_img_member_ques"
                android:layout_marginLeft="7dp"
                android:layout_width="17dp"
                android:layout_height="17dp"
                android:src="@drawable/ic_qt" />
        </LinearLayout>

        <!-- 升级提示区域：显示距离下一等级的里程和去骑行按钮 -->
        <LinearLayout
            android:layout_marginTop="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">
            <!-- 距离下一等级提示文本：如"距离下一等级还差20.00km" -->
            <TextView
                android:id="@+id/item_tv_member_level"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black_namal"
                android:textSize="@dimen/text_smallest_10"
                android:text="距离下一等级还差20.00km" />
            <!-- 分隔线装饰 -->
            <View
                android:layout_width="1dp"
                android:layout_height="10dp"
                android:layout_marginHorizontal="5dp"
                android:background="@color/white50" />

            <!-- 去骑行按钮区域：包含"去骑行"文本和右侧箭头icon -->
            <LinearLayout
                android:id="@+id/ll_go_ride_clickable_area"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="8dp">
                <!-- "去骑行"按钮文本 -->
                <TextView
                    android:id="@+id/item_tv_member_level_ride"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black_namal"
                    android:textSize="@dimen/text_smallest_10"
                    android:text="@string/s_nextlevel_go" />
                <!-- 右侧箭头icon -->
                <ImageView
                    android:id="@+id/item_img_member_go"
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:layout_marginStart="4dp"
                    android:src="@drawable/ic_qt" />
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

    <!-- 卡片底部描述文本区域：可用于显示会员卡片的补充说明或提示 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:gravity="center">
        <TextView
            android:id="@+id/tv_describe"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:paddingHorizontal="1dp"
            android:paddingTop="1dp"
            android:paddingBottom="1dp"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />
    </LinearLayout>
</RelativeLayout>