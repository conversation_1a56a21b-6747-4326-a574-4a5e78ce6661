<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog_bom"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:paddingHorizontal="@dimen/activity_horizontal_margin"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/s_order_endtime"
            android:layout_weight="1"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_838588" />
        <com.tbit.uqbike.roundview.RoundTextView
            android:id="@+id/rv_ridrecord_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_838588"
            android:textStyle="bold"
            tools:text="111111111"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingHorizontal="@dimen/activity_horizontal_margin"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/order_no"
            android:layout_weight="1"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_838588" />
        <TextView
            android:id="@+id/rv_ridrecord_no"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_default_s"
            android:textStyle="bold"
            android:textColor="@color/c_838588"
            tools:text="11"/>
        <ImageView
            android:id="@+id/img_ridrecord_copy"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginLeft="7dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/ic_copy"></ImageView>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingHorizontal="@dimen/activity_horizontal_margin"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/s_order_state"
            android:layout_weight="1"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_838588" />
        <TextView
            android:id="@+id/rv_ridrecord_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_default_s"
            android:textStyle="bold"
            android:textColor="@color/c_838588"
            tools:text="11"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:paddingHorizontal="@dimen/activity_horizontal_margin"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/s_riding_length_h"
            android:layout_weight="1"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_838588" />
        <TextView
            android:id="@+id/tv_ridrecord_length"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_default_s"
            android:textStyle="bold"
            android:textColor="@color/black_namal"
            tools:text="11"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingHorizontal="@dimen/activity_horizontal_margin"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/s_riding_time_h"
            android:layout_weight="1"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_838588" />
        <TextView
            android:id="@+id/tv_ridrecord_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_default_s"
            android:textStyle="bold"
            android:textColor="@color/black_namal"
            tools:text="11"/>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/ly_ridcard"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingHorizontal="@dimen/activity_horizontal_margin"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/s_riding_card_h"
            android:layout_weight="1"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_838588" />
        <TextView
            android:id="@+id/tv_ridrecord_card"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_default_s"
            android:textStyle="bold"
            android:textColor="@color/black_namal"
            tools:text="-10"/>
        <TextView
            android:id="@+id/tv_ridrecord_card_member"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_F86125"
            android:visibility="gone"
            tools:text="（会员+20）"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingHorizontal="@dimen/activity_horizontal_margin"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_ridrecord_cost_explain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/s_riding_time_cost"
            android:layout_weight="1"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_838588" />
        <TextView
            android:id="@+id/tv_ridrecord_cost"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_default_s"
            android:textStyle="bold"
            android:textColor="@color/black_namal"
            tools:text="11"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingHorizontal="@dimen/activity_horizontal_margin"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_ridrecord_coupon_explain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="优惠券抵扣"
            android:layout_weight="1"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_838588" />
        <ImageView
            android:id="@+id/img_recorddetail_coupon"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/ic_up"></ImageView>
        <TextView
            android:id="@+id/tv_ridrecord_coupon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="3dp"
            android:textSize="@dimen/text_default_s"
            android:textStyle="bold"
            android:textColor="@color/black_namal"
            tools:text="11"/>
    </LinearLayout>
    <com.tbit.uqbike.roundview.RoundLinearLayout
        android:visibility="gone"
        android:id="@+id/ry_recorddetail_coupon"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginHorizontal="@dimen/activity_horizontal_margin"
        app:rv_backgroundColor="@color/c_grey"
        app:rv_cornerRadius="7dp"
        android:paddingVertical="5dp"
        android:paddingHorizontal="7dp"
        android:orientation="vertical"></com.tbit.uqbike.roundview.RoundLinearLayout>
    <LinearLayout
        android:id="@+id/ly_recode_dispatch"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingHorizontal="@dimen/activity_horizontal_margin"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_ridrecord_dispatch_explain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/black_namal"
            android:layout_weight="1"
            tools:text="调度费(¥)"/>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">
            <TextView
                android:id="@+id/tv_ridrecord_dispatch_member"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/text_default_s"
                android:textColor="@color/c_F86125"
                android:visibility="gone"
                android:layout_marginEnd="4dp"
                tools:text="会员免罚"/>
            <TextView
                android:id="@+id/tv_ridrecord_dispatch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:textSize="@dimen/text_default_s"
                android:textColor="@color/black_namal"
                tools:text="11"/>
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingHorizontal="@dimen/activity_horizontal_margin"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_ridrecord_allcost"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/s_riding_allcost"
            android:layout_weight="1"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_838588" />

        <TextView
            android:id="@+id/tv_ridrecord_costnum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:text=""
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/black_namal"/>
    </LinearLayout>


    <LinearLayout
        android:id="@+id/ly_ridrecord_balance"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="25dp"
        android:paddingHorizontal="@dimen/activity_horizontal_margin"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_ridrecord_balancecost"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/s_record_balancecast"
            android:layout_weight="1"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_838588" />

        <TextView
            android:id="@+id/tv_ridrecord_balancenum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:text=""
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/black_namal"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ly_ridrecord_present"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingHorizontal="@dimen/activity_horizontal_margin"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_ridrecord_presentcost"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/s_record_balancecast"
            android:layout_weight="1"
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/c_838588" />

        <TextView
            android:id="@+id/tv_ridrecord_presentnum"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:text=""
            android:textSize="@dimen/text_default_s"
            android:textColor="@color/black_namal"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ly_paybom"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/h_margin"
        android:paddingHorizontal="@dimen/h_margin"
        android:paddingVertical="10dp"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:background="@drawable/bg_grad_b"
        android:layout_marginTop="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/tv_ridrecord_allcost1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="待支付金额"
                android:layout_weight="1"
                android:textSize="@dimen/text_default_s"
                android:textColor="@color/blue_namal" />

            <TextView
                android:id="@+id/tv_ridrecord_costnum1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:text=""
                android:textSize="@dimen/text_default_s"
                android:textColor="@color/blue_namal"/>
        </LinearLayout>

    </LinearLayout>

    <com.tbit.uqbike.roundview.RoundTextView
        android:visibility="gone"
        android:id="@+id/btn_riding_ok"
        android:layout_marginTop="14dp"
        android:layout_marginHorizontal="@dimen/h_margin"
        android:paddingVertical="@dimen/v_margin_s"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:textSize="@dimen/text_large"
        android:textStyle="bold"
        android:textColor="@color/white"
        app:rv_backgroundColor="@color/blue_namal"
        app:rv_cornerRadius="25dp"
        android:gravity="center"
        android:text="@string/pay"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="35dp"></LinearLayout>
</LinearLayout>