<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">GOGO</string>
    <string name="quit_double_check_tips">Press again to exit the app</string>
    <string name="please_wait">Please wait...</string>
    <string name="network_error">Network Error</string>
    <string name="dialog_tip">Tip</string>
    <string name="ok">OK</string>
    <string name="confirm">OK</string>
    <string name="cancel">Cancel</string>
    <string name="i_know">I know</string>
    <string name="baidu_map_not_install">No Baidu map client installed</string>
    <string name="gaode_map_not_install">No Gaode map client installed</string>
    <string name="google_map_not_install">No Google map client installed</string>
    <string name="errcode_success">Pay successfully</string>
    <string name="permission_denied">Permission denied</string>
    <string name="permission_never_ask_again">Permission is no longer requested</string>
    <string name="allow">Allow</string>
    <string name="deny">Deny</string>
    <string name="permission_rationale">App requires location permission</string>
    <string name="camera_permission_rationale">App requires Camera permission</string>
    <string name="ble_permission_rationale">App requires Bluetooth permission</string>
    <string name="media_permission_rationale">App  requires the permission to read media files</string>
    <string name="ble_not_opened">Bluetooth is not turned on</string>
    <string name="ble_not_supported">Phone does not support low-power Bluetooth</string>
    <string name="device_not_founded">Device not found</string>
    <string name="motion_state">bike in motion</string>
    <string name="disconnected">Bluetooth not connected</string>
    <string name="timeout">Timeout</string>
    <string name="illegal_command">Illegal command</string>
    <string name="illegal_key">Illegal key</string>
    <string name="ble_op_failed">Bluetooth operation failed</string>
    <string name="ble_un_support">The device does not support Bluetooth BLE</string>
    <string name="ctrl_ble_disable">Bluetooth is not enabled, Bluetooth function will not work properly, whether to enable Bluetooth?</string>
    <string name="ctrl_location_disable">GPS positioning is not turned on, Bluetooth function will not work properly, whether to turn on positioning?</string>
    <string name="location_disable">GPS positioning is not turned on, whether to turn on positioning?</string>
    <string name="open">Open</string>
    <string name="operation_timeout">Operation timeout</string>
    <string name="scan">Scan code</string>
    <string name="camera_error">Camera error</string>
    <string name="open_flashlight">Turn on flashlight</string>
    <string name="close_flashlight">Turn off flashlight</string>
    <string name="scan_tips">Aim at the QR code on the bike</string>
    <string name="qr_code_invalid_hint">Invalid QR code!</string>
    <string name="new_version">New version</string>
    <string name="check_new_version">Get new version</string>
    <string name="about_version_new">It\'s already the latest version</string>
    <string name="download_download">Download</string>
    <string name="quit">Exit</string>
    <string name="download_update">Upgrade</string>
    <string name="download_later">Not upgrading</string>
    <string name="download_downloading">Downloading, please wait...</string>
    <string name="download_success">Download successfully. Preparing to install.</string>
    <string name="download_fail">Sorry, download failed</string>
    <string name="download_tip">New version found</string>
    <string name="skip">%1$ds</string>
    <string name="s_skip">Skip</string>
    <string name="login_first">Please login first</string>
    <string name="login">Login</string>
    <string name="one_pass_login">Login by one click</string>
    <string name="other_way_to_login">Other ways to login</string>
    <string name="lazy_login">Don\'t login yet</string>
    <string name="click_to_login">Click to login</string>
    <string name="logout">Log out account</string>
    <string name="logout_tip">Are you sure you want to log out?</string>
    <string name="country_code">Country code</string>
    <string name="plz_choose_country_code">Please select country code</string>
    <string name="phone_number_title">Cell phone number</string>
    <string name="phone_number_hint">Please enter your cell phone number</string>
    <string name="auth_code_title">Verification code</string>
    <string name="auth_code_hint">Please enter the verification code</string>
    <string name="get_auth_code">Get code</string>
    <string name="version_code_title">Version number:</string>
    <string name="phone_number_can_not_be_empty">Cell phone number cannot be empty</string>
    <string name="phone_num_not_meet_the_rule">The format of the cell phone number is wrong</string>
    <string name="auth_code_can_not_be_empty">Verification code cannot be empty</string>
    <string name="second_with_unit">%1$ds</string>
    <string name="register_agree">I have read and agree to the 《User Agreement》 and 《Privacy Policy》.</string>
    <string name="user_agreement">User Agreement</string>
    <string name="auth_code_sent">SMS verification code has been sent</string>
    <string name="login_success">Login successfully</string>
    <string name="name">Name</string>
    <string name="verify">Verify</string>
    <string name="scan_unlock">Scan the code to unlock the bike</string>
    <string name="common_problem">FAQ</string>
    <string name="mileage_with_unit">%1$s km</string>
    <string name="online_service">Online customer service</string>
    <string name="search">Search</string>
    <string name="search_destination">Search destination</string>
    <string name="current_address_with_title">You are here: %1$s</string>
    <string name="scan_to_ride">Scan code to use the bike</string>
    <string name="manual_input_title">Enter the number to unlock the bike</string>
    <string name="manual_input_tips">Please enter the bike number correctly</string>
    <string name="terminal_did_not_upload_data">No data has been uploaded to the terminal.</string>
    <string name="bike_is_riding">The bike is being ridden</string>
    <string name="bike_status">bike status</string>
    <string name="remaining_battery">Remaining power</string>
    <string name="remaining_mileage">Riding distance</string>
    <string name="change_one">Change</string>
    <string name="unlock">Unlock</string>
    <string name="borrow_bike">Borrow bike</string>
    <string name="borrow_hint">Please ride in the red operating area and return your bike to point p</string>
    <string name="out_of_station_hint">Illegal return will be charged %1$s dispatch fee</string>
    <string name="no_with_title">NO:%1$s</string>
    <string name="remaining_mileage_info">Ride within %1$s km</string>
    <string name="billing_rules_info">Within %1$d mins %2$s, beyond %3$s/%4$d mins</string>
    <string name="unlocking">Unlocking</string>
    <string name="bike_is_unlocked">bike is unlocked</string>
    <string name="riding">Riding</string>
    <string name="keep_going">Continue to use the bike</string>
    <string name="return_bike">Return bike</string>
    <string name="keep_going_with_stop_time">Continue to use the bike (parked for %1$d mins)</string>
    <string name="bike_machine_no">Bike number:</string>
    <string name="mileage_remain">Mileage</string>
    <string name="mileage_remain_with_unit">Mileage (km)</string>
    <string name="mileage">Riding mileage</string>
    <string name="mileage_unit">km</string>
    <string name="mileage_unit_km_format">%1$s km</string>
    <string name="time">Bicycle time</string>
    <string name="time_unit_minute">min</string>
    <string name="time_unit_minute_format">%1$s min</string>
    <string name="fee">Estimated amount</string>
    <string name="fee_unit">￥</string>
    <string name="fee_unit_format">%1$s %2$s</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="in_operation">Operation in progress</string>
    <string name="confirm_return_bike">Confirm to return the bike?</string>
    <string name="total_cast_title">Total cost (%1$s)</string>
    <string name="rdie_time_title">Ride duration (min)</string>
    <string name="ride_distance_title">Ride distance (km)</string>
    <string name="dialog_content_money_not_enough">Please recharge your account with %1$s before returning the bike.</string>
    <string name="return_failed">Return bike failed</string>
    <string name="return_failed_tip">Please contact customer service if you failed to return the bike!</string>
    <string name="contact_customer_service">Contact us</string>
    <string name="navigation">Navigation</string>
    <string name="payment_method">Payment methods</string>
    <string name="pay">Make Payment</string>
    <string name="money_unit">Yuan</string>
    <string name="money_with_unit">%1$s%2$s</string>
    <string name="recharge_amount">Please select the amount</string>
    <string name="charge_customize">Enter  %1$s</string>
    <string name="confirm_recharge">Go recharge</string>
    <string name="check_update">Check for updates</string>
    <string name="is_last_version">It\'s already the latest version</string>
    <string name="log_out">Logout</string>
    <string name="terms_of_service">User Agreement</string>
    <string name="ride_record">Ride record</string>
    <string name="ride_card_short_ad">Ride for more!</string>
    <string name="click_to_buy">Click to open</string>
    <string name="wallet">My wallet</string>
    <string name="gift_card_title">My gift cards</string>
    <string name="gift_card_exchange">Gift card exchange</string>
    <string name="gift_card_exchange_success">Gift card exchange successfully</string>
    <string name="use_time_with_title">Exchange time: %1$s</string>
    <string name="please_input_gift_card_no">Please enter gift card number</string>
    <string name="expiration_date_with_title">Validity period: %1$s</string>
    <string name="min">mins</string>
    <string name="transaction_detail_title">Order history</string>
    <string name="recharge">Recharge</string>
    <string name="no_more_tips">No more content</string>
    <string name="record_detail_title">Trip information</string>
    <string name="billing_rules">Billing rules</string>
    <string name="no_locating_points">The ride time is too short to check the track.</string>
    <string name="travel_consumption">Riding fee</string>
    <string name="machine_no">Bike number</string>
    <string name="riding_time">Riding time</string>
    <string name="remark">Remark</string>
    <string name="distance_unit">km</string>
    <string name="cost_appeal">Fee appeal</string>
    <string name="short_order_tint">This order is for a short period of time</string>
    <string name="is_any_problem_with_bike">Is there something wrong with the bike?</string>
    <string name="do_not_let_go">The handlebars don\'t move.</string>
    <string name="destroyed_bike">It\'s been destroyed.</string>
    <string name="no_problem">There\'s no problem.</string>
    <string name="report_other_fault">Is the problem not included? Click to go to troubleshooting ></string>
    <string name="short_order_feedback_title">End order within two mins</string>
    <string name="buy_notes">Purchase information</string>
    <string name="effectiving">In effect</string>
    <string name="expired">Expired</string>
    <string name="not_active">Not in effect</string>
    <string name="ride_card">Ride card</string>
    <string name="ride_card_time_count">Free ride within %1$d mins in a single ride</string>
    <string name="ride_card_max_ride">Available rides: %1$d rides(%2$s)</string>
    <string name="ride_card_notice">1.Refund is not supported after purchase, please confirm before purchase. \n2.Can only be used during the effective period of the package, invalid after the expiration date, invalid after the maximum number of times of use. \n3.Riding membership does not support stacking purchases, you need to use up or expire before you can buy again. \n4. If there is a riding limit, after exceeding the riding limit, you can only use the balance to make the payment. \n5. If there is a riding limit, it is invalid after exceeding the riding limit.</string>
    <string name="site_apply_title">Parking spot application</string>
    <string name="site_apply_reason_not_null">It\'s easier to apply for a parking spot by entering a reason!</string>
    <string name="site_apply_positioning">In the process of locating...</string>
    <string name="site_apply_application_received">Your application has been received and will be processed as soon as possible.</string>
    <string name="submit">Submit</string>
    <string name="apply_reason_with_limit">Reason for application</string>
    <string name="apply_address">Location of returning address</string>
    <string name="feedback_fault">Fault reporting</string>
    <string name="feedback_bike">Report bike</string>
    <string name="scan_or_input_machine_no">Scan the code or manually enter the bike number</string>
    <string name="please_select_fault_type">Please select the type of fault</string>
    <string name="fault_brakes">Brake does not work</string>
    <string name="add_image">Add a picture</string>
    <string name="fault_remark_hint">Please add a description</string>
    <string name="machine_no_not_null">Bike number cannot be empty</string>
    <string name="feedback">Feedback</string>
    <string name="feedback_reason">Question type</string>
    <string name="please_select_reason_first">Please select the type of question first</string>
    <string name="feedback_hint">Please enter your feedback</string>
    <string name="others">Other</string>
    <string name="thanks_for_feedback">Thank you for your feedback!</string>
    <string name="cost_appeal_tips">If you have a complaint about this order, please submit the relevant information.</string>
    <string name="appeal_reason">Appeal reason</string>
    <string name="description_remarks">Description remark</string>
    <string name="enter_desc_remark_hint">Please enter your description</string>
    <string name="supple_pic">Add a picture</string>
    <string name="submit_appeal">Submit a cn appeal</string>
    <string name="please_select_appeal_reason_first">Please select the reason for your appeal first</string>
    <string name="submit_success">Submit successfully</string>
    <string name="my_message">Message</string>
    <string name="have_read_message">Read</string>
    <string name="unread_message">Unread</string>
    <string name="message_is_empty">No message</string>
    <string name="find_bike">locate your bike</string>
    <string name="type">Type</string>
    <string name="str_time">Time</string>
    <string name="str_want_to_appeal">I want to complain</string>
    <string name="order_no">Order No.</string>
    <string name="str_place">Location</string>
    <string name="riding_track">Track</string>
    <string name="uncivilized_riding_type">Type of uncivilized riding</string>
    <string name="snapshot_pic">Photo</string>
    <string name="appeal_content">Complaint content</string>
    <string name="civilized_riding_appeal_hint">Please enter your appeal content</string>
    <string name="unmatched_type_str_hint">--</string>
    <string name="data_is_null">Data is null</string>
    <string name="load_place_failed">Location acquisition failed</string>
    <string name="op_timeout">Operation timeout</string>
    <string name="op_failed">Operation failed</string>
    <string name="op_success">%1$s success</string>
    <string name="send_success">%1$s command sent successfully</string>
    <string name="op_failed_with_reason">%1$s failure-%2$s</string>
    <string name="unlock_helmet">Open helmet lock</string>
    <string name="lock_helmet">Close helmet lock</string>
    <string name="lock">Lock</string>
    <string name="country_code_format">(+%1$s)</string>
    <string name="choose_country_code_hint">Please select country code</string>
    <string name="back">Return</string>
    <string name="cs_reply">Customer service response</string>
    <string name="str_privacy_policy">《Privacy Policy》</string>
    <string name="str_service_terms">《User Agreement》</string>
    <string name="str_bind_success">Binding successfully</string>
    <string name="str_pay_success">Pay successfully</string>
    <string name="str_pay_failed">Payment failure</string>
    <string name="str_read_and_agree">I have read and agree</string>
    <string name="str_read_and_continue">I have read and continue</string>
    <string name="str_sign_btn_tipes">Submit signature (scroll to bottom)</string>
    <string name="str_please_sign">Please sign</string>
    <string name="str_line_pay_jump_link_empty">Line Pay jump link is empty</string>
    <string name="str_line_pay_config_error">Line pay data is incorrect</string>
    <string name="str_lianlian_pay_config_error">Lianlian payment data is incorrect</string>
    <string name="str_bind_phone">Binding cell phone number</string>
    <string name="str_pay_card_num_format">---- ---- ---- %1$s</string>
    <string name="str_google_sign_in_cancel">Google login canceled</string>
    <string name="str_google_sign_in_failed">Google login failed</string>
    <string name="str_google_signing_in">Google login in progress</string>
    <string name="str_tick_privacy_and_agreement_tips">Please read and agree</string>
    <string name="str_bind_phone_hint">Please bind the cell phone number</string>
    <string name="str_enter_invite_info_success">Fill in the invitation information successfully</string>
    <string name="str_google_net_not_available_tips">Currently can not access google maps related network or delay is large, please make sure the network environment is smooth.</string>
    <string name="str_balance_pay">Balance payment</string>
    <string name="str_unsupport_pay">This payment method is not supported</string>
    <string name="str_can_ride_duration_format">Accumulated riding:%1$d mins</string>
    <string name="str_buy_ride_minute">Purchase</string>
    <string name="str_recharge_balance">Recharge balance</string>
    <string name="str_gps_open_hint">If GPS is not turned on, the map function will not work properly, please turn it on.</string>
    <string name="str_lianlian_pay">Payment</string>
    <string name="str_copy">Copy</string>
    <string name="str_call">Call</string>
    <string name="str_customer_service">Customer service</string>
    <string name="str_copy_success">Copy successfully</string>
    <string name="str_not_get_paypal_config">Paypal payment configuration not obtained</string>
    <string name="s_bick_dispatch">Improper parking will incur %1$s dispatch fee</string>
    <string name="s_bike_price">Within %1$s mins %2$s, beyond %3$s/1 mins</string>
    <string name="s_openclock">Unlocking in progress...</string>
    <string name="s_clock_suc">Unlocked successfully</string>
    <string name="s_clock_fail">Unlocking failed</string>
    <string name="s_riding_hint">Please drive the bike to the P-point area to return it.</string>
    <string name="s_riding_time_h">Riding time (mins)</string>
    <string name="s_riding_length_h">Riding mileage (km)</string>
    <string name="s_riding_batterylife_h">Battery life (km)</string>
    <string name="s_riding_amount">Real time fee</string>
    <string name="s_riding_searchp">Finding P-points</string>
    <string name="s_riding_p_hint">Bike has been stationary for %1$s, and will trigger temporary parking after %2$s of mins.</string>
    <string name="s_riding_end_hint">Bike has been stationary for %1$s, and the order will be automatically terminated after %2$s mins.</string>
    <string name="s_riding_uwallet_hint">Your balance is not sufficient, it would be power outage after %1$s, please recharge in a timely manner!</string>
    <string name="s_riding_nwallet_hint">Your balance is depleted, the bike has been disconnected from the power, %1$s after the imminent end of the ride, please recharge in a timely manner!</string>
    <string name="s_riding_repay_p">Bikes are not in P-points, parking will be charged %1$s dispatch fee</string>
    <string name="s_riding_repay_jt">bikes in the no-parking zone, parking will be charged %1$s dispatch fee</string>
    <string name="s_riding_repay_yj">bikes not in the operation area, parking will be charged %1$s dispatch fee</string>
    <string name="s_riding_repay">Are you sure to return the bike?</string>
    <string name="s_riding_qx">Continue to ride</string>
    <string name="s_charge_title">Secure recharge</string>
    <string name="s_pay_hint">Recharge instructions</string>
    <string name="s_pay">Payment</string>
    <string name="s_gopay">Go to pay</string>
    <string name="s_order_npay">To be paid</string>
    <string name="s_order_comp">Completed</string>
    <string name="s_riding_card_h">Deduction of riding card duration (mins)</string>
    <string name="s_riding_time_cost">Hourly fee</string>
    <string name="s_riding_dispatch_cost">Dispatch fee</string>
    <string name="s_riding_allcost">Total fee</string>
    <string name="s_cast">Fee</string>
    <string name="s_record_balancecast">Balance deduction</string>
    <string name="s_feedback_explain">Troubleshooting</string>
    <string name="s_msginfo">Message details</string>
    <string name="s_order">Order:</string>
    <string name="s_pay_cannel">Confirm cancel payment</string>
    <string name="s_myridecard">My ride card</string>
    <string name="s_wallbuy">Wallet purchase</string>
    <string name="s_paybuy">Payment purchase</string>
    <string name="s_haveridecard_nobuy">Riding card already exists and cannot be purchased</string>
    <string name="s_ridecard_expense">Ride card fees</string>
    <string name="s_walletbalance">Wallet balance</string>
    <string name="s_ubalance">(Insufficient balance)</string>
    <string name="s_sure_buy">Confirm to purchase</string>
    <string name="s_seekinfo">View details</string>
    <string name="s_giftbalance">Gift balance</string>
    <string name="s_nuse">Unavailable</string>
    <string name="s_gift">Gift</string>
    <string name="s_balance_run">Balance flow</string>
    <string name="s_area_use">Currently available</string>
    <string name="s_area_uuse">Currently unavailable</string>
    <string name="s_walletamount">Wallet amount</string>
    <string name="s_persentamount">Gift amount</string>
    <string name="s_ndata">No data available</string>
    <string name="s_input_cardpwd">Please input the card password</string>
    <string name="s_extend">Promotion code</string>
    <string name="s_hint_giftcard">Scan the code or manually enter the gift card number</string>
    <string name="s_hint_extend">Scan the code or manually enter the promotion code</string>
    <string name="s_buy_suc">Purchase successfully</string>
    <string name="s_shortrecord_hint">This order has been using the bike for a short period of time, are there any problems with the bike?</string>
    <string name="s_n_agree">Disagree</string>
    <string name="s_agree">Agree</string>
    <string name="s_n_agree_w">Still disagree</string>
    <string name="s_agree_w">Agree and continue</string>
    <string name="s_dialog_plicytitle">GOGO requires you to agree to the User Agreement and the Privacy Policy</string>
    <string name="s_dialog_plicycont">If you do not agree to the 《user agreement》 and 《privacy policy》, you can only visit the map page.</string>
    <string name="s_have_nopayorder">There is an unpaid ride order, please complete the payment first</string>
    <string name="s_haveriding">There are currently trips in progress</string>
    <string name="s_input_carnum">Please enter the bike number</string>
    <string name="s_sure_openclock">Confirm to unlock</string>
    <string name="s_nusercar_hint">Please unlock the bike only when your available balance reaches %1$s.</string>
    <string name="s_opengps_hint">Please turn on the location first</string>
    <string name="s_opengo">Go to open</string>
    <string name="s_areaout">This location is outside the operation area</string>
    <string name="s_beginner">Beginner\'s guide ></string>
    <string name="s_set">Settings</string>
    <string name="s_upda">Updates</string>
    <string name="s_changelang">Switch language</string>
    <string name="s_person"></string>
    <string name="s_balance">Balance</string>
    <string name="s_signout">Logout account</string>
    <string name="s_please_login">Hello,\n please login first</string>
    <string name="s_otherlogin">Other login methods</string>
    <string name="s_sure_signout">Confirm to cancel</string>
    <string name="s_losecard">View expired cards</string>
    <string name="s_free_ridealone">Free riding mins</string>
    <string name="s_onlearea_use">Only for %1$s use</string>
    <string name="s_unit_day">Days</string>
    <string name="s_gift_day">Send %1$s days as gift</string>
    <string name="s_unit_times">Times</string>
    <string name="s_gift_times">Send %1$s times as gift</string>
    <string name="s_Remaining_time">Remaining riding time</string>
    <string name="s_ulimit_time">Unlimited time</string>
    <string name="s_remaining_day_num">Maximum number of rides remaining in a single day</string>
    <string name="s_ulimit_num">Unlimited times</string>
    <string name="s_feedback_time">Feedback time</string>
    <string name="s_feedback_type">Feedback type</string>
    <string name="s_feecback_cont">Feedback content</string>
    <string name="s_appeal_time">Appeal time</string>
    <string name="s_ride_order">Riding order</string>
    <string name="s_appeal_reason">Appeal reason</string>
    <string name="s_handresult">Processing result</string>
    <string name="s_send">Send</string>
    <string name="s_creatorder_time">Order creation time</string>
    <string name="s_ridecard_name">Riding card name</string>
    <string name="s_tobepay">Amount to be paid</string>
    <string name="s_paycomp">Paid</string>
    <string name="s_paytime">Payment time</string>
    <string name="s_paymoney">Payment amount</string>
    <string name="s_canldown">Canceled</string>
    <string name="s_canl_time">Cancellation time</string>
    <string name="s_price">Sales price</string>
    <string name="s_rech_wallet">Recharge wallet amount</string>
    <string name="s_start_time">Start time</string>
    <string name="s_end_time">End time</string>
    <string name="s_buyridecard">Purchase riding card</string>
    <string name="s_pay_canl">Payment cancellation</string>
    <string name="s_order_shorttime">Long-distance ride orders during short period</string>
    <string name="s_type_ride">Riding</string>
    <string name="s_dispatch_cost">Dispatch fee</string>
    <string name="s_appeal">Appeals</string>
    <string name="s_paypresent">Recharge gift</string>
    <string name="s_ridepresent">Ride minus gift</string>
    <string name="s_dispatchpresent">Dispatch fee deduction gift</string>
    <string name="s_appealpresent">Appeal refund gift</string>
    <string name="s_extenpresent">Promotional code gift</string>
    <string name="s_giftpresent">Gift card present</string>
    <string name="s_lose_card">Invalid card</string>
    <string name="s_scan_fail">Failure to scan code</string>
    <string name="s_scanfail_input">Code scanning failed, the QR code may have problems,\n please manually enter the bike number to unlock the door</string>
    <string name="s_scan_input">Go to enter the code</string>
    <string name="s_scan_ugogo">This is not the QR code of GOGO</string>
    <string name="s_scan_usave">There may be security risks, please scan the official QR code</string>
    <string name="s_sumit_await">Submit successfully, please wait for the audit results</string>
    <string name="s_usignout">Your account has a balance and cannot be canceled</string>
    <string name="s_sumitimg_fail">Image upload failed, please re-select!</string>
    <string name="s_pay_sel">Please select the required recharge items</string>
    <string name="s_ques_sel">Please select the question</string>
    <string name="s_order_null">Order number is empty</string>
    <string name="s_lang_cn">Simplified Chinese</string>
    <string name="s_lang_en">English</string>
    <string name="s_lang_th">Thai</string>
    <string name="s_login_black">Account login is limited, if you have any questions, please contact customer service</string>
    <string name="s_login_signout">Your account cancellation review in progress, can not Login, please contact customer service</string>
    <string name="s_pic_sel">Select from your phone\'s photo albums</string>
    <string name="s_quit_sure">OK to log out?</string>
    <string name="s_stop_time">Stopped %1$s mins</string>
    <string name="s_paying">Payment processing...</string>
    <string name="s_bike_pricebycard">Ride card single mins consumed, charge %1$s/mins</string>
    <string name="s_sumit_suc">You have submitted the relevant information</string>
    <string name="s_udevice">The current device is not an unlocked device and cannot be returned.</string>
    <string name="s_upay_state">Payment pending</string>
    <string name="uexpiration_date_with_title">Expiration time：%1$s</string>
    <string name="s_allbalance">Total available balance</string>
    <string name="s_super_pay">Available payment methods</string>
    <string name="s_super_pay_hint">The following payment methods are currently available, you do not need to choose manually</string>
    <string name="s_order_pay">Order payment</string>
    <string name="s_info_hint">Detailed description</string>
    <string name="s_gobuy">Go to buy</string>
    <string name="s_scan_car_hint">Please aim at the QR code on the bike</string>
    <string name="s_car_exit">Existing bikes</string>
    <string name="s_car_unit">Bikes</string>
    <string name="s_auth_title">Service Agreement and Rules for Handling Personal Information</string>
    <string name="s_auth_get">GOGO would like to obtain your:</string>
    <string name="s_auth_ble">Bluetooth permission</string>
    <string name="s_auth_ble_cont">For opening and closing the lock of the bike, assisting the motorcycle to open and close the lock, and assisting in positioning;</string>
    <string name="s_auth_carm">Camera opermission</string>
    <string name="s_auth_carm_cont">Used for scanning the code to use the bicycle, uploading photos of faulty vehicles, and using the face recognition function to determine that the account user is you;</string>
    <string name="s_auth_mic">Microphone permission</string>
    <string name="s_auth_mic_cont">For voice content recognition and voice input;</string>
    <string name="s_auth_local">Location information permission</string>
    <string name="s_auth_local_cont">Used to determine whether your current location is in a parking point (P-point), operation area, no-parking area, or power-off circle; used to find neighboring bicycles and motorcycles;</string>
    <string name="s_auth_msg">New message notification permission</string>
    <string name="s_auth_msg_cont">Used to provide you with orders, discounts, service changes;</string>
    <string name="s_auth_photo">Album permission</string>
    <string name="s_auth_photo_cont">For uploading pictures and videos;</string>
    <string name="s_auth_dev">Device identification code</string>
    <string name="s_auth_dev_cont">Used to determine user\'s identity and maintain account security;</string>
    <string name="s_auth_car">Vehicle loading during riding</string>
    <string name="s_auth_car_cont">For preventing illegal loading;</string>
    <string name="s_auth_cont">You acknowledge that this pop-up window will not directly open the relevant permissions, and we will separately seek your consent for the opening of specific permnissions. \nPlease read the Service Agreement, Platform Rules and Personal Information Processing Rules carefully to confirm your consent. If you are a minor under 14 years of age, please ensure that your guardian carefully read and understand the changes or add the agreement; and if your guardian does not agree or can not fully understand any of the terms, please stop the subsequent operation, and you can contact us you will not be able to continue to use the service. If you or your guardian click on the "Agree", it will be deemed that your guardian fully agrees and understands all the terms and conditions.</string>
    <string name="s_ride_alltime">Total riding time:</string>
    <string name="s_ride_day_maxnum">Max rides per day</string>
    <string name="s_suc_giftcard">Submit successfully, obtaining %1$s Amount</string>
    <string name="s_sel_area">Please select area</string>
    <string name="s_ride_service">Use riding services</string>
    <string name="s_price_all">total</string>
    <string name="s_per_carm_title">GOGO wants to access your camera</string>
    <string name="s_per_carm_cont">Users can scan the code to unlock, take pictures of broken down cars, provide feedback and other functions that require access to the camera.</string>
    <string name="s_p_inarea">Please apply within the operation area</string>
    <string name="s_stop_ls">Temporary stop</string>
    <string name="s_phone">Mobile</string>
    <string name="s_email">Email</string>
    <string name="s_input_email">Please input your email</string>
    <string name="s_input_pwd">Please input your password</string>
    <string name="s_login_pwd">Password login</string>
    <string name="s_forgetpwd">Forgot password?</string>
    <string name="s_no_account">No account?</string>
    <string name="s_register">Go to register</string>
    <string name="s_login_code">Code login</string>
    <string name="s_gologin">Go to login</string>
    <string name="s_input_pwd_new">Please input  8-20 characters</string>
    <string name="s_input_pwd_sure">Please confirm your password again</string>
    <string name="s_reg_account">Account registration</string>
    <string name="s_pwdformat_error">Wrong password format</string>
    <string name="s_pwddiff_error">Two passwords are inconsistent</string>
    <string name="s_fixpwd">Change password</string>
    <string name="s_input_oldpwd">Please input the old password</string>
    <string name="s_pwdlength_error">Input 8-20 characters, including numbers and letters</string>
    <string name="s_nav_err_plan">Route calculation failed, please reopen</string>
    <string name="s_nav_err_init">Navigation initialization failed</string>
    <string name="s_email_error">Email error</string>
    <string name="s_fix_suc">Change successfully</string>
    <string name="s_go_p">Go to the nearest parking zone</string>
    <string name="s_pay_user">User recharge</string>
    <string name="s_pay_sys">System recharge</string>
    <string name="s_deduct">Deduction</string>
    <string name="s_arreashint">You have owed %1$s. If the arrears reach %2$s, the order will be terminated. Please recharge in time</string>
    <string name="s_arreas_tip">Arrears reached %1$s during cycling, the order will be closed</string>
    <string name="s_ride_tip">Riding reminder</string>
    <string name="s_account_exit">Have account?</string>
    <string name="s_noapp">No app installed</string>
    <string name="s_err_sys">sys_error</string>
    <string name="s_ble_hint">Turning on Bluetooth will help to open and close the lock faster</string>
    <string name="s_ble_title">GOGO want to use Bluetooth</string>
    <string name="s_ble_cont">GOGO needs to turn on your Bluetooth to have the lock and unlock function</string>
    <string name="s_canlfail_openble">Lock failed, turning on Bluetooth will help close the lock faster</string>
    <string name="s_err_data">Data anomalies</string>
    <string name="s_open_ble_pre">GOGO needs to enable your nearby device permission to provide the lock switch function</string>
    <string name="s_ble_hint_new">Turn on Bluetooth, faster to open and close the lock, make riding more convenient</string>
    <string name="s_ble_hint_main">Turn on Bluetooth, open and close the lock faster</string>
    <string name="s_newuser_tv1">Congratulations to you</string>
    <string name="s_newuser_obat">Get riding experience bonus</string>
    <string name="s_new_go">Ride now</string>
    <string name="s_newuser_save">Deposited to your wallet - Gift balance</string>
    <string name="s_newuser_present">New user registration gift</string>
    <string name="s_refund_resq">refund request</string>
    <string name="s_refund_reason">refund reason</string>
    <string name="s_refun">refund</string>
    <string name="s_refun_record">refund record</string>
    <string name="s_exchange">exchange</string>
    <string name="s_order_endtime">order end time</string>
    <string name="s_order_state">order status</string>
    <string name="s_copy">copied</string>
    <string name="s_balance_cost">wallet balance deduction</string>
    <string name="s_present_cost">gift balance deduction</string>
    <string name="s_exchange_paymoney">Top-up payment amount</string>
    <string name="s_ridecare_exchange_title">riding card redemption</string>
    <string name="s_input_ridecardnum">enter riding card serial number</string>
    <string name="s_pay_yh_fail">Please do not use Bank APP to pay, otherwise the recharge will fail.</string>
    <string name="s_exchange_suc">Redemption successful</string>
    <string name="s_ridecard_time">Riding card validity period</string>
    <string name="s_ride_freetime">Free riding time</string>
    <string name="s_unit_hour">hour</string>
    <string name="s_datedown">%1$s expire</string>
    <string name="s_free_ridetime">Free riding time : until %1$s</string>
    <string name="s_use_y">Used</string>
    <string name="s_paynum_err">The Top-up amount exceeds the limit, please re-enter</string>
    <string name="s_refund_hint">Wallet Refund Information</string>
    <string name="s_refund_money">Refundable wallet amount</string>
    <string name="s_refund_explan">Refund instructions</string>
    <string name="s_refund_explandinfo">Detailed refund information</string>
    <string name="s_refund_person_title">Fill in the refund bank information</string>
    <string name="s_sel_khh">Please select the bank</string>
    <string name="s_ipt_username">Please fill in account name</string>
    <string name="s_ipt_usernum">Please fill in account number</string>
    <string name="s_sure_refund">Confirmed to refund?</string>
    <string name="s_think">think again</string>
    <string name="s_dialog_refund_cont">Once approved, the refund amount is expected to be returned to the original payment method within 3 working days. If multiple recharges are made, they will be refunded separately, so please check it carefully.</string>
    <string name="s_dialog_refund_title">Submission successful, refund under review</string>
    <string name="s_isrefund">refunded</string>
    <string name="s_refunt_input_err">Do not enter spaces or unusual characters.</string>
    <string name="s_refund_n">No refund amount available</string>
    <string name="s_balance_ridecard">remaining balance to purchase riding card</string>
    <string name="s_sure_refund_balance">Confirm refund</string>
    <string name="s_refund_fail">Refund failed</string>
    <string name="s_check_u">Awaiting review</string>
    <string name="s_suc">Successful</string>
    <string name="s_fail">Fail</string>
    <string name="s_refund_applemoney">Amount of refund requested:</string>
    <string name="s_checktime">Review time:</string>
    <string name="s_refund_deposit">Bank:</string>
    <string name="s_refund_name">Account name:</string>
    <string name="s_refund_account">Account number:</string>
    <string name="s_refund_remark">process result:</string>
    <string name="s_refund_appletime">application time:</string>
    <string name="s_isrefund_ing">Refunding</string>
    <string name="s_refund_ridecard_suc">Refund for purchasing riding card</string>
    <string name="s_refund_balance">Wallet Refund</string>
    <string name="s_refund_ridecard">Riding Card Refund</string>
    <string name="s_refund_apple_explain">Instructions for applying for a refund</string>
    <string name="s_openble_hint">Locking failed. Please try to turn on Bluetooth before returning the bike</string>
    <string name="s_lineservice">fail to return the bike, please contact customer service</string>
    <string name="s_goline">to contact</string>
    <string name="s_pay_setmy">customize</string>
    <string name="s_refundmsg_applemoney">Request refund for wallet amount</string>
    <string name="s_refundmsg_applecard">Request refund for riding card</string>
    <string name="s_refundmsg_cardmoney">Card amount</string>
    <string name="s_refundmsg_pay">Purchase payment method</string>
    <string name="s_refundmsg_back">Refund Path</string>
    <string name="s_refundmsg_appletime">application time</string>
    <string name="s_refundmsg_reason">Reason for refund request</string>
    <string name="s_refund_expired">expired</string>
    <string name="s_exchange_limit">Unable to use bank APP to recharge amount less than 30%1$s</string>
    <string name="s_refund_n_riding">Your have riding order in progress and cannot be refunded. Please go to "Ride Record" to end the order first</string>
    <string name="s_refresh">Refresh</string>
    <string name="s_pay_wait">Waiting for payment...</string>
    <string name="s_returnfail_ble">Bluetooth lock failed</string>
    <string name="s_input_y">（Required）</string>
    <string name="appeal_reason_sel">Select a reason for appeal</string>
    <string name="s_cost_remark_title">Appeal reason remark</string>
    <string name="s_cost_img_title">Please upload %1$s photos according to the example requirements</string>
    <string name="s_cost_img_explain">Photo exampl</string>
    <string name="s_cost_img_explain_hint">Please strictly follow the example requirements when uploading the image, as it will help in passing the review</string>
    <string name="s_input_remark">Please enter a remark on the reason for the appeal</string>
    <string name="s_sumit_pic">Please upload the photo</string>
    <string name="s_sumit_pic_err">Please upload the required number of photos as specified</string>
    <string name="s_login_newhint">Register for a GOGO account upon first login with the verification code</string>
    <string name="s_setpwd">Set password</string>
    <string name="s_stop_title">Temporary vehicle locking will continue to be billed</string>
    <string name="s_stop_hint">The maximum lock duration is %1$s minutes; if exceeded, the vehicle will be automatically locked and returned</string>
    <string name="s_stoping">Vehicle is being temporarily locked…</string>
    <string name="s_stopcen_hint">The remaining time for temporary vehicle lock is %1$s; the vehicle will be automatically returned after the countdown ends</string>
    <string name="s_usecar_title">First time riding a GOGO e-bike</string>
    <string name="s_usecar_hint">Let\'s see how to use the bike</string>
    <string name="s_usecar_new1">Please ride within the service area</string>
    <string name="s_usecar_new2">The vehicle will be powered off if ridden outside the operating area</string>
    <string name="s_usecar_new_unit1">powered off</string>
    <string name="s_usecar_new3">Please return the vehicle at the P point on the map</string>
    <string name="s_usecar_new4">If the vehicle is not returned at the \'P\' point, GOGO will charge an additional dispatch fee</string>
    <string name="s_usecar_new_unit2">charge an additional dispatch fee</string>
    <string name="s_usecar_stop1">Temporary vehicle lock is not the same as returning the vehicle</string>
    <string name="s_usecar_stop2">The vehicle will continue to incur charges. Please pay attention to the locking time</string>
    <string name="s_usecar_stop3">If you are not using the vehicle, please promptly select \'Return Vehicle\'</string>
    <string name="s_usecar_stop4">If the temporary vehicle lock exceeds %1$s, the vehicle will be automatically returned If it is not returned at the \'P\' point, an additional dispatch fee will be charged</string>
    <string name="s_usecar_stop_unit1">dispatch fee will be charged</string>
    <string name="s_free_ridealone_new">Free for each use</string>
    <string name="s_ride_freetime_new">Free</string>
    <string name="s_ridecard_hint1">The ride card can only be used to deduct fare charges</string>
    <string name="s_ridecard_hint2">Timing starts from the first ride after purchasing this card and continues without interruption</string>
    <string name="s_ridecard_cost">Charges will apply according to %1$s after exceeding %2$s</string>
    <string name="s_ridecard_time_new">Validity period</string>
    <string name="s_free_ridealone_new1">One-time free ride</string>
    <string name="s_ride_alltime_new">Total number of rides</string>
    <string name="s_infomore">More details</string>
    <string name="s_riding_back">Return bike</string>
    <string name="temporary_stop">Temporary vehicle lock</string>
    <string name="s_aut_ym_title">Umeng+SDK Privacy Terms</string>
    <string name="s_aut_ym_hint">Conduct APP operation statistics and analysis, collect and analyze APP crash information, push marketing messages and order information during riding to users in a timely manner.</string>
    <string name="s_riding_tophint">Please return the bicycle to P Point</string>
    <string name="s_pay_xyhint">By clicking on recharge payment means you agree to 《the recharge agreement》</string>
    <string name="s_pay_exchange_xy">《the recharge agreement》</string>
    <string name="s_lang_id">Indonesia</string>
    <string name="s_lang_ml">Malaysia</string>
    <string name="s_change_country">Switching country</string>
    <string name="s_sel_rental">Choose an electric bike rental package</string>
    <string name="s_rental">rent now</string>
    <string name="s_power_fish">after the battery swap is completed</string>
    <string name="s_power_fish_hint">please fill in the battery replacement code provided by the staff to resume riding</string>
    <string name="s_carpic_hint">please take a sample photo of the drop-off point and the vehicle below</string>
    <string name="s_clockcar">lock bike</string>
    <string name="s_rentaling">on lease</string>
    <string name="s_function">Report Issue</string>
    <string name="s_lookcar">find a bike</string>
    <string name="s_cz_hint">please pay attention to the return time, if you do not overtime, you will also be deducted from the wallet at %1$s unril the wallet fee is deducted</string>
    <string name="s_cz_hint1">please return the bike in time, if you do not overtime, you will be deducted from the wallet at %1$s nuntil the wallet fee is deducted and the bike will be automatically returned</string>
    <string name="s_changepower">contact for battery swapping</string>
    <string name="s_picrereturn">take photos and renturn the bike</string>
    <string name="s_picreturn_hint">follow the example to take photos of the left and right sides of the bike</string>
    <string name="s_picreturn_hint_l">please take a photo of the left side of the bike </string>
    <string name="s_picreturn_img_hint">follow the examplero take a photo</string>
    <string name="s_picreturn_hint_r">please take a photo of the right side of the bike </string>
    <string name="s_cash">deposit</string>
    <string name="s_unit_s">seconds</string>
    <string name="s_sureorder">confirm order page</string>
    <string name="s_wallet_pay">wallet balance top-up</string>
    <string name="s_ridingrental_reture_hint1">please return the bike to 【%1$s】before %2$s</string>
    <string name="s_ridingrental_reture_hint2">there is only %1$s left in the remaining free time, go to【%2$s】to renturn the bike</string>
    <string name="s_ridingrental_reture_hint3">go to 【%1$s】to return the bike</string>
    <string name="s_overtime">timed out</string>
    <string name="s_riding_repay_p_rental">bike is not within the designated P point, the mandatory return of the bike will be charged at %1$s dispatch fee</string>
    <string name="s_riding_repay_jt_rental">bike is in the no-parking zone, the mandatory return of the bike will be subject to a %1$s dispatch fee</string>
    <string name="s_riding_repay_yj_rental">bike is not in the operating area, the mandatory return of the vehicle will be subject to a %1$s dispatch fee</string>
    <string name="s_riding_repay_rental">are you sure want to return the bike now?</string>
    <string name="s_riding_back_rental">mandatory return the bike</string>
    <string name="s_p_cont">go to the designated P point to return it</string>
    <string name="s_p_disp">there is a %1$s dispatch fee for returning the bike here</string>
    <string name="scan_lease">scan QR code to rent bike</string>
    <string name="s_power_kf">Please contact the staff to replace the battery</string>
    <string name="s_dialog_wxtx_tip">Friendly reminder</string>
    <string name="s_ridingnow">Directly riding</string>
    <string name="s_power_dig_title">Are you sure you want to contact us for battery replacement now? Please read the following carefully:</string>
    <string name="s_power_dig_con1">1. After clicking OK, the bike will enter the battery replacement state and will not be able to ride. It is estimated that the staff will arrive at the site within 1 hour to complete the battery replacement before you can continue riding.</string>
    <string name="s_power_dig_con21">1. After payment is completed, the battery will be replaced and you will not be able to ride. It is expected that staff will arrive on site within 1 hour to complete the battery replacement before you can continue riding</string>
    <string name="s_power_dig_unit1">The vehicle will lose power</string>
    <string name="s_power_con2">2. You still have %1$s free battery replacement opportunities. Please go to the pick-up point and click: Contact for battery replacement, otherwise we will charge an additional [%2$s] battery replacement fee</string>
    <string name="s_power_dig_con3">2. This battery replacement will cost [%1$s]</string>
    <string name="s_needpay">Need to pay</string>
    <string name="s_power_amout">Battery replacement fee</string>
    <string name="s_power_amout_wall">Wallet balance can be deducted</string>
    <string name="s_power_amout_parent">Gift balance can be deducted</string>
    <string name="s_car_uclock">Unlocked</string>
    <string name="s_car_lock">Locked</string>
    <string name="s_car_power">Battery replacement</string>
    <string name="s_power_ing">Waiting for battery replacement, temporarily unable to ride</string>
    <string name="s_car_back">return bike</string>
    <string name="s_power_comp">The battery replacement is expected to be completed within 1 hour</string>
    <string name="s_surepay">Confirm payment</string>
    <string name="s_dig_buycard">Buy a cycling card to ride, more favorable and more money</string>
    <string name="s_dig_ucard">Buy a riding card to ride, more favorable and more money</string>
    <string name="s_priceinfo">Amount Details</string>
    <string name="s_fxs">Distributor entry</string>
    <string name="s_card_off">%1$soff</string>
    <string name="s_refund_yj">Refund of deposit</string>
    <string name="s_power_clock">Please lock the car before contacting for battery replacement</string>
    <string name="s_wate_nyj">Only used for deposit-free use</string>
    <string name="s_invite">Invitation courtesy</string>
    <string name="s_invite_suc">Invitation successful</string>
    <string name="s_msg_invite">The user you invited completes the ride and the reward has been deposited into your wallet - gift balance</string>
    <string name="s_invite_amount">Reward amount</string>
    <string name="s_msginfo_invite">The 【%1$s】users you invited have completed the ride and the 【%2$s】gift amount has been deposited into your wallet-gift balance</string>
    <string name="s_reward_other">Get "%1$s" + "%2$s pieces %3$s"</string>
    <string name="s_award_ride">Get "%1$spieces%2$s"</string>
    <string name="s_accept">Happy to accept</string>
    <string name="s_newuser_gift_suc">Successfully received the new user gift pack</string>
    <string name="s_invite_sel">Select Coupon</string>
    <string name="s_discount">discount</string>
    <string name="s_get">get</string>
    <string name="s_ridemoney">riding credit</string>
    <string name="s_get_ridecard">%1$s piece riding card</string>
    <string name="s_get_invite">%1$s piece coupon</string>
    <string name="s_congratulations">Congratulations on getting it</string>
    <string name="s_getsuc">Congratulations on receiving  it successfully</string>
    <string name="s_today">today</string>
    <string name="s_invite_ismin">Full %1$s can be used</string>
    <string name="s_invite_umin">No threshold for discount</string>
    <string name="s_coupons">coupons</string>
    <string name="s_expired_num">%1$s piece almost expires</string>
    <string name="s_coup_y">discounted</string>
    <string name="s_coup_dis">Coupon discount</string>
    <string name="s_uuse_coupon">Do not use discount</string>
    <string name="s_invite_usup">Cannot be used with other coupons at the same time</string>
    <string name="s_coupon_u_other">Cannot be used with other coupons at the same time</string>
    <string name="s_coupon_u_min">The usage threshold of this coupon is greater than the total amount after the discount</string>
    <string name="s_payamount">payment amount</string>
    <string name="s_coupon_amount_dis">Coupon discount for riding fees</string>
    <string name="s_payresult">Purchase Results</string>
    <string name="s_buy_fail">Purchase failed</string>
    <string name="s_go_ride">go ride</string>
    <string name="s_lang_hy">korean</string>
    <string name="s_coupon_zero">The total discount amount exceeds the total fees</string>
    <string name="s_msg_invitetype">The friend you invited has completed the ride, and the reward has been received. Please check it in time.</string>
    <string name="s_chargeresult">Payment result</string>
    <string name="s_mingxi">Details</string>
    <string name="s_more_gn">More functions</string>
    <string name="s_win">Congratulations on winning</string>
    <string name="s_award">Bonus</string>
    <string name="s_notify_hint">Turn on notification permissions to receive important notifications from GOGO as soon as possible</string>
    <string name="s_appdown">This QR code is the download QR code for GOGO APP</string>
    <string name="s_msg_notifyhint">Turn on push notifications to not miss any messages</string>
    <string name="s_backcar">Return</string>
    <string name="s_backcarnow">Are you sure you want to return the tram now? If you confirm to return it, this rental will end and you will need to pay again for subsequent use of the car</string>
    <string name="s_backcar_before">Hello, there are still %1$s hours before the return time of the order. If you return it, this rental will end and the remaining fee will not be refunded.</string>
    <string name="s_endorder_now">Are you sure you want to return the car now and end the order?</string>
    <string name="s_returncar_pic_hint">Please take a full-body photo of the vehicle according to the example</string>
    <string name="s_net_err">Poor network connection</string>
    <string name="s_net_rset">Please try to reconnect your network</string>
    <string name="s_goset">Go to settings</string>
    <string name="s_err_local">Failed to obtain location</string>
    <string name="s_err_local_set">Please check if your phone\'s location service is enabled and grant GOGO location permission</string>
    <string name="s_u_local">Failed to retrieve location information</string>
    <string name="s_main_u_local">The location was not obtained, please check the system settings</string>
    <string name="s_main_u_net">Network abnormality, please check the network settings</string>
    <string name="s_usecar_zn">User guide</string>
    <string name="s_see">View</string>
    <string name="s_rental_end">End Rental</string>
    <string name="s_riding_end">End Ride</string>
    <string name="s_riding_has">Ridden</string>
    <string name="s_cost_overtime">Overtime Fee</string>
    <string name="s_rental_has">Rented</string>
    <string name="s_leavemsg">Inquiry</string>
    <string name="s_person_info">Profile</string>
    <string name="s_date_year">Year</string>
    <string name="s_date_month">Month</string>
    <string name="s_data_day">Day</string>
    <string name="s_selcountry">Select country or region</string>
    <string name="s_avater">Avatar</string>
    <string name="s_account">Account</string>
    <string name="s_birthdate">Birthdate</string>
    <string name="s_sex">Gender</string>
    <string name="s_country">Country or region</string>
    <string name="s_identity">Role</string>
    <string name="s_thirdaccount">Third-party Account</string>
    <string name="s_sumit_record">Report History</string>
    <string name="s_fault_type">Issue Type</string>
    <string name="s_record_info">Details</string>
    <string name="s_question_deal">Issue Resolved?</string>
    <string name="s_deal_y">Resolved</string>
    <string name="s_deal_n">Unresolved</string>
    <string name="s_deal_appraise">Rate Service</string>
    <string name="s_appraise_xx">Rate with Stars</string>
    <string name="s_appraise_edit_hint">Any Feedback?</string>
    <string name="s_sumitneed">Fill Required Fields</string>
    <string name="s_selorder_cost">Select Order to Appeal</string>
    <string name="s_costrecord">Appeal History</string>
    <string name="s_appraisal">Rate Now</string>
    <string name="s_leavemsg_s">Message</string>
    <string name="s_leavemsg_dialog">Message Box</string>
    <string name="s_input_hint">Enter Here</string>
    <string name="s_phonenum">Phone Number</string>
    <string name="s_costinfo">Appeal Details</string>
    <string name="s_comp_person">Complete Profile</string>
    <string name="s_cost_type">Appeal Type</string>
    <string name="s_cost_remark">Appeal Note</string>
    <string name="s_cost_pic">Appeal Image</string>
    <string name="s_cost_rental_upay">Pending Rental Fee</string>
    <string name="s_cost_upay">Pending Riding Fee</string>
    <string name="s_local_hint1">Location permission is very important</string>
    <string name="s_local_hint2">It helps us provide more accurate cycling location services and ensures your safety</string>
    <string name="s_getlocal">Allow access to location</string>
    <string name="s_local_err">Location information not obtained. Please check if the network is abnormal</string>
    <string name="s_check_local">Location information not obtained. Please check if location services are enabled and authorized for GOGO</string>
    <string name="s_menu_home">Home</string>
    <string name="s_menu_act">Campaigns</string>
    <string name="s_menu_mine">Profile</string>
    <string name="s_jf">My Points</string>
    <string name="s_member">Membership Hub</string>
    <string name="s_rule">Rules</string>
    <string name="s_zsqb">Exclusive Coupons</string>
    <string name="s_fpmf">No Non-P Fee</string>
    <string name="s_qxkys">Ride Pass Extension</string>
    <string name="s_nextlevel_go">Ride to Upgrade</string>
    <string name="s_lc">Miles</string>
    <string name="s_member_rules">Membership Rules</string>
    <string name="s_points">Points</string>
    <string name="s_view_benefits">View Benefits</string>
    <string name="s_bronze_tier">Bronze Tier</string>
    <string name="s_silver_benefits">Silver Tier</string>
    <string name="s_gold_benefits">Gold Tier</string>
    <string name="s_platinum_benefits">Platinum Tier</string>
    <string name="s_black_benefits">Black Tier</string>
    <string name="s_activity_center">Activity Center</string>
    <string name="s_rideorder_prePay">Estimated Fare</string>
    <string name="s_wallet_recharge">Wallet Recharge</string>
    <string name="s_wait_for_updates">Wait for updates</string>
    <string name="s_have_riding_order">There is an active ride in progress; scanning the code is unavailable</string>
    <string name="s_have_wait_pay_order">There is currently an order pending payment and QR code scanning is unavailable</string>
    <string name="no_more_data">No more data</string>
    <string name="s_vipcenter">Membership Hub</string>
    <string name="s_vip_bronze_equity">Bronze Member Benefits</string>
    <string name="s_vip_sliver_equity">Silver Benefits</string>
    <string name="s_vip_gold_equity">Gold Benefits</string>
    <string name="s_vip_platinum_equity">Platinum Benefits</string>
    <string name="s_vip_blackgold_equity">Black Tier Benefits</string>
    <string name="s_vipCoupon_num">%1$s Passes</string>
    <string name="s_nop_num">%1$s Times</string>
    <string name="s_rideCard_delay_minute">%1$s Minutes</string>
    <string name="s_vip_mile">%1$s Miles</string>
    <string name="s_vip_mile_standard">Mileage Requirement</string>
    <string name="s_vip_keepLevel">Keep Riding to Maintain Tier</string>
    <string name="s_vip_nextLevelforMile">Need %1$s km More for Next Tier</string>
    <string name="s_vip_nextleveUpdate">Ride %1$s km to Upgrade!</string>
    <string name="s_vip_mile_tips">Last 30 Days Mileage</string>
    <string name="s_vip_bottom_tip">GOGO Member\nEnjoy Great Riding</string>
    <string name="s_vip_equity_title">Terms &amp; Benefits</string>
    <string name="s_vip_minute_times">Minutes/Ride</string>
    <string name="s_vip_ridecard_desc">+%d Free Minutes per Ride (Active Tier)</string>
    <string name="s_vip_nop_desc">%d Free Non-P Returns (Active Tier)</string>
    <string name="s_vip_unlock_rights">Unlocked Benefits</string>
    <string name="s_normal_nodata">No Data</string>
    <string name="s_vip_coupon_btn_lock">Locked</string>
    <string name="s_vip_coupon_btn_use">Use Now</string>
    <string name="s_vip_coupon_btn_expire">Expired</string>
    <string name="s_vip_coupon_btn_used">Used</string>
    <string name="s_ridepage_noP_tips">Member Benefit: Return Anywhere, No Fee</string>
    <string name="s_rideorder_payFree">Fee Waived (Members)</string>
    <string name="s_rideorder_exchange">Redeem Now</string>
    <string name="s_vip_times">Times</string>
    <string name="s_vip_bronze_equity_use">Bronze Tier Unlocks Access</string>
    <string name="s_vip_sliver_equity_use">Silver Tier Unlocks Access</string>
    <string name="s_vip_gold_equity_use">Gold Tier Unlocks Access</string>
    <string name="s_vip_platinum_equity_use">Platinum Tier Unlocks Access</string>
    <string name="s_vip_blackgold_equity_use">Black Tier Unlocks Access</string>
    <string name="s_ride_point_exchange">Free Redemption in Points Mall</string>
    <string name="s_page_home">Home</string>
    <string name="s_page_activity">Campaigns</string>
    <string name="s_page_message">Messages</string>
    <string name="s_page_mine">Profile</string>
    <string name="s_mine_points">My Points</string>
    <string name="s_vip_bronze">Bronze Tier</string>
    <string name="s_vip_sliver">Silver Tier</string>
    <string name="s_vip_gold">Gold Tier</string>
    <string name="s_vip_platinum">Platinum Tier</string>
    <string name="s_vip_blackgold">Black Tier</string>
    <string name="s_vip_right_rule">Rules</string>
    <string name="s_vip_rule_title">Membership Rules</string>
    <string name="s_vip_coupon_title">Exclusive Coupons</string>
    <string name="s_vip_nop_title">No Non-P Fee</string>
    <string name="s_vip_ridecard_title">Ride Pass Extension</string>
    <string name="s_rideOrder_noScan">Active Ride - Scan Disabled</string>
    <string name="s_rideorder_points">Points</string>
    <string name="s_vip_look_equity">View Benefits</string>
    <string name="s_vip_goride_update">Ride to upgrade</string>
    <string name="s_vip_upgrade_milestone_needed">Ride %1$s km to upgrade</string>
    <string name="s_vip_locked_equity">Unlocked Benefits</string>
    <string name="s_rule_desc">Rule Description</string>
    <string name="s_go_use">Use</string>
    <string name="s_no_content_available">No content available</string>
    <string name="s_member_unlock_benefit">Unlockable by Platinum Members</string> 
    <string name="s_ride_card_benefit_time_suffix">min/ride</string>
    <string name="s_ride_card_benefit_desc">During membership, each ride card usage grants an additional 20 minutes of free ride time.</string>
    <string name="benefit_unit_piece">pcs</string>
    <string name="benefit_unit_time">Times</string>
    <string name="benefit_unit_day">Days</string>
    <string name="s_no_threshold">No threshold</string>
    <string name="s_update_google_play">Please update Google Play services and try again</string>
    <string name="s_no_sim_card">No SIM card detected</string>

    <!-- Coupon Display Strings -->
    <string name="s_go_to_use">Use</string>
    <string name="s_currency_thb">THB</string>
    <string name="s_time_unit_min">min</string>

    <!-- Coupon Expiry Format Strings -->
    <string name="s_today_time_expires">Today %1$s expires</string>
    <string name="s_date_time_expires">%1$s %2$s expires</string>

    <!-- Remove or check s_datedown and s_today if they are now redundant or need modification -->
    <!-- Example: If s_datedown was "%1$s到期" or "到期" -->
    <!-- <string name="s_datedown">%1$s expires</string> -->
    <!-- <string name="s_today">Today</string> -->

    <string name="s_impunity_tip">Member Exclusive: The current vehicle is not in a P point, no dispatch fee will be charged, you can return it directly.</string>

    <string name="s_member_tag">Member</string>

</resources>
