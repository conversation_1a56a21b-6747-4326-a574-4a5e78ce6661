<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.tbit.uqbike">

    <uses-sdk tools:overrideLibrary="             com.google.firebase.firebase_analytics,             com.google.firebase.messaging,             com.google.android.gms.measurement.api,             com.google.firebase.measurement,             com.google.android.gms.measurement.sdk,             com.google.android.gms.cloudmessaging,             com.google.firebase.measurement_impl,             com.google.firebase.analytics.connector,             com.google.android.gms.measurement.sdk.api,             com.google.android.gms.measurement_base" />

    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" /> <!-- Android12 的蓝牙权限 如果您的应用与已配对的蓝牙设备通信或者获取当前手机蓝牙是否打开 -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" /> <!-- Android12 的蓝牙权限 如果您的应用查找蓝牙设备（如蓝牙低功耗 (BLE) 外围设备） -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" /> <!-- Android12 的蓝牙权限 如果您的应用使当前设备可被其他蓝牙设备检测到 -->
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission
        android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" /> <!-- Android13读取媒体文件权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" /> <!-- 这个权限用于进行网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- 这个权限用于访问GPS定位 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 这个权限用于获取wifi的获取权限，wifi信息会用来进行网络定位 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- SD卡读取权限，用户写入离线定位数据 -->
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" /> <!-- 适配安卓12&11获取当前已安装的所有应用列表 -->
    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
    </queries>
    <queries package="${applicationId}">
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
        <intent>
            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
        </intent>
    </queries>

    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <application
        android:name=".App"
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup">
        <activity
            android:name=".activity.MyMemberActivity"
            android:exported="false" />
        <activity
            android:name=".activity.MyMember"
            android:exported="false" />
        <activity
            android:name=".activity.HomeActivity"
            android:exported="false"
            android:launchMode="singleTask" />
        <activity
            android:name=".activity.CostInfoActivity"
            android:exported="false" />
        <activity
            android:name=".activity.FaultInfoActivity"
            android:exported="false" />
        <activity
            android:name=".activity.PersonActivity"
            android:exported="false" />
        <activity
            android:name=".activity.OrderCostRecordActivity"
            android:exported="false" />
        <activity
            android:name=".activity.LeaveMsgRecordActivity"
            android:exported="false" />
        <activity
            android:name=".activity.CostRecordActivity"
            android:exported="false" />
        <activity
            android:name=".activity.FaultRecordActivity"
            android:exported="false" />
        <activity
            android:name=".activity.RideCardPayResultActivity"
            android:exported="false" />
        <activity
            android:name=".activity.SelInviteActivity"
            android:exported="false" />
        <activity
            android:name=".activity.WebActionActivity"
            android:excludeFromRecents="false"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activity.ShareRebateTestActivity"
            android:exported="false" />
        <activity
            android:name=".activity.PicReturnActivity"
            android:exported="false" />
        <activity
            android:name=".activity.CountrySelActivity"
            android:exported="false" />
        <activity
            android:name=".activity.UserCarActivity"
            android:exported="false" />
        <activity
            android:name=".activity.MessageRefundDetailActity"
            android:exported="false" />
        <activity
            android:name=".activity.RefundPersonInfoActivity"
            android:exported="false" />
        <activity
            android:name=".activity.RefunRecordActivity"
            android:exported="false" />
        <activity
            android:name=".activity.RefunReasonActivity"
            android:exported="false" />
        <activity
            android:name=".activity.RefundActivity"
            android:exported="false" />
        <activity
            android:name=".activity.FixPwdActivity"
            android:exported="false" />
        <activity
            android:name=".activity.RegisterActivity"
            android:exported="false" />
        <activity
            android:name=".activity.NaviActivity"
            android:exported="false" />
        <activity
            android:name=".activity.LanguageActivity"
            android:exported="false" />
        <activity
            android:name=".activity.AuthActivity"
            android:exported="false" />
        <activity
            android:name=".activity.SignOutActivity"
            android:exported="false" />
        <activity
            android:name=".activity.ExchangeActivity"
            android:exported="false" />
        <activity
            android:name=".activity.PersentInfoActivity"
            android:exported="false" />
        <activity
            android:name=".activity.WalletPersentActivity"
            android:exported="false" />
        <activity
            android:name=".activity.WalletBalanceActivity"
            android:exported="false" />
        <activity
            android:name=".activity.LoseRideCardActivity"
            android:exported="false" />
        <activity
            android:name=".activity.MyRideCardActivity"
            android:exported="false" />
        <activity
            android:name=".activity.SearchPActivity"
            android:exported="false" />
        <activity
            android:name=".activity.ChargeNewActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activity.RideCardNewActivity"
            android:exported="false" />

        <meta-data
            android:name="design_width_in_dp"
            android:value="360" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="640" /> <!-- baidu -->
        <meta-data
            android:name="com.baidu.lbsapi.API_KEY"
            android:value="${BMAP_KEY}" />

        <service
            android:name="com.baidu.location.f"
            android:enabled="true"
            android:process=":remote" /> <!-- baidu -->
        <!-- gaode -->
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="${AMAP_KEY}" /> <!-- <service -->
        <!-- android:name="com.amap.api.location.APSService" -->
        <!-- android:exported="false" /> &lt;!&ndash; gaode &ndash;&gt; -->
        <!-- google -->
        <!-- <uses-library -->
        <!-- android:name="org.apache.http.legacy" -->
        <!-- android:required="false" /> -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_api_key" />
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" /> <!-- google -->
        <!-- paypal -->
        <!--
 <activity android:name="com.braintreepayments.api.BraintreeBrowserSwitchActivity"
            android:launchMode="singleTask"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="${applicationId}.braintree" />
            </intent-filter>
        </activity>
        -->
        <!-- paypal -->
        <!-- 友盟 -->
        <!-- 配置APP KEY -->
        <!-- <meta-data -->
        <!-- android:name="UMENG_APPKEY" -->
        <!-- android:value="${UMENG_APPKEY}" /> &lt;!&ndash; 配置渠道 &ndash;&gt; -->
        <meta-data
            android:name="UMENG_CHANNEL"
            android:value="${APP_CHANNEL}" /> <!-- 友盟 -->
        <!-- &lt;!&ndash; 一键登录 &ndash;&gt; -->
        <!-- 移动登录类名 -->
        <!-- <activity -->
        <!-- android:name="com.cmic.sso.wy.activity.LoginAuthActivity" -->
        <!-- android:configChanges="keyboardHidden|orientation|screenSize" -->
        <!-- android:exported="false" -->
        <!-- android:launchMode="singleTop" -->
        <!-- android:screenOrientation="behind" -->
        <!-- android:theme="@style/Theme.ActivityDialogStyle" -->
        <!-- tools:replace="android:screenOrientation,android:configChanges" /> &lt;!&ndash; 联通登录类名 &ndash;&gt; -->
        <!-- <activity -->
        <!-- android:name="com.sdk.mobile.manager.login.cucc.OauthActivity" -->
        <!-- android:configChanges="keyboardHidden|orientation|screenSize" -->
        <!-- android:exported="false" -->
        <!-- android:launchMode="singleTop" -->
        <!-- android:screenOrientation="behind" -->
        <!-- android:theme="@style/Theme.ActivityDialogStyle" -->
        <!-- tools:replace="android:screenOrientation,android:theme" /> &lt;!&ndash; 电信登录类名 &ndash;&gt; -->
        <!-- <activity -->
        <!-- android:name="com.netease.nis.quicklogin.ui.YDQuickLoginActivity" -->
        <!-- android:configChanges="keyboardHidden|orientation|screenSize" -->
        <!-- android:exported="false" -->
        <!-- android:launchMode="singleTop" -->
        <!-- android:screenOrientation="behind" -->
        <!-- android:theme="@style/Theme.ActivityDialogStyle" /> &lt;!&ndash; 一键登录 &ndash;&gt; -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <activity
            android:name=".activity.SplashActivity"
            android:exported="true"
            android:noHistory="true"
            android:theme="@style/SpalTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="gogoapp" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.MainActivity"
            android:exported="false"
            android:launchMode="singleTask" />
        <activity
            android:name=".activity.LoginActivity"
            android:exported="false" />
        <activity
            android:name=".activity.ScanForBorrowActivity"
            android:exported="false" />
        <activity
            android:name=".activity.InputForBorrowActivity"
            android:exported="false" />
        <activity
            android:name=".activity.NameAuthActivity"
            android:exported="false" />
        <activity
            android:name=".activity.SearchParkPointActivity"
            android:exported="false" />
        <activity
            android:name=".activity.MineActivity"
            android:exported="false" />
        <activity
            android:name=".activity.WalletActivity"
            android:exported="false" />
        <activity
            android:name=".activity.GiftCardActivity"
            android:exported="false" />
        <activity
            android:name=".activity.CouponActivity"
            android:exported="false" />
        <activity
            android:name=".activity.TransactionDetailActivity"
            android:exported="false" />
        <activity
            android:name=".activity.RidingRecordActivity"
            android:exported="false" />
        <activity
            android:name=".activity.RecordDetailActivity"
            android:exported="false"
            android:launchMode="singleTop" />
        <activity
            android:name=".activity.ApplyForSitesActivity"
            android:exported="false" />
        <activity
            android:name=".activity.FeedbackActivity"
            android:exported="false" />
        <activity
            android:name=".activity.FeedbackFaultActivity"
            android:exported="false" />
        <activity
            android:name=".activity.ScanMachineNoActivity"
            android:exported="false" />
        <activity
            android:name=".activity.MessageActivity"
            android:exported="false" />
        <activity
            android:name=".activity.MessageDetailActivity"
            android:exported="false" />
        <activity
            android:name=".activity.OnePassLoginSuccessActivity"
            android:exported="false" />
        <activity
            android:name=".activity.WebActivity"
            android:exported="false" />
        <activity
            android:name=".activity.CostAppealActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activity.CivilizedRidingDetailActivity"
            android:exported="false" />
        <activity
            android:name=".activity.CivilizedRidingAppealActivity"
            android:exported="false" />
        <activity
            android:name=".activity.CivilizedRidingRecordActivity"
            android:exported="false" />
        <activity
            android:name=".activity.BindPhoneActivity"
            android:exported="false" />
        <activity
            android:name=".activity.SplashLoadingActivity"
            android:exported="false" />
        <activity
            android:name=".activity.SetActivity"
            android:exported="false" /> <!-- FCM 关闭自动初始化 start -->
        <meta-data
            android:name="firebase_messaging_auto_init_enabled"
            android:value="false" />
        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="false" /> <!-- FCM end -->
        <!-- Honor start -->
        <meta-data
            android:name="com.hihonor.push.app_id"
            android:value="104459245" /> <!-- android:value="此处改为荣耀后台真实参数" /> -->
        <!-- Honor end -->
        <!-- 魅族 start -->
        <!-- push应用定义消息receiver声明 -->
        <!-- 已移除 MfrMzMessageReceiver 相关配置 -->
        <!-- 魅族 end -->
        <service
            android:name=".push.MyCustomMessageService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-9859096297951223~9123532772" />
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
            android:value="true" />
    </application>

</manifest>