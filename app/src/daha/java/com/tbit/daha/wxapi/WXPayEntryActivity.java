package com.tbit.daha.wxapi;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import com.tbit.smartbike.pay.wxpay.WxPayResultDispatcher;
import com.tbit.uqbike.R;
import com.tbit.uqbike.base.BaseActivity;
import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

public class WXPayEntryActivity extends BaseActivity implements IWXAPIEventHandler {

	private IWXAPI api;
	private String app_id;//微信开发后台申请的app_id

	@Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		//这里可以不填写
//        setContentView(R.layout.pay_result);
		app_id = getString(R.string.wx_appid);
		api = WXAPIFactory.createWXAPI(this, app_id);
		try {
			api.handleIntent(getIntent(), this);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	protected void onNewIntent(Intent intent) {
		super.onNewIntent(intent);
		setIntent(intent);
		api.handleIntent(intent, this);
	}

	@Override
	public void onReq(BaseReq req) {
	}

	/**
	 * 处理结果回调
	 *
	 * @param resp
	 */
	@Override
	public void onResp(BaseResp resp) {
		if (resp.getType() == ConstantsAPI.COMMAND_PAY_BY_WX) {
			Log.i("ddd", "onResp: "+resp.errStr);
			WxPayResultDispatcher.INSTANCE.dispatchResult(resp.errCode);
		}

		finish();
	}
}