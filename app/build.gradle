apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
//apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'
//apply plugin: 'click-anti-shake'
//apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.sensorsdata.analytics.android'

android {
    compileSdkVersion 34
    defaultConfig {
        applicationId "com.tbit.uqbike"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 10000
        versionName "1.0.0"
        flavorDimensions "tbit"
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters "armeabi-v7a","arm64-v8a","x86","x86_64"//,"armeabi-v7a","x86","mips"
        }
    }
    buildFeatures {
        viewBinding true
    }
    signingConfigs {
        debug {
            storeFile file("signing.jks")
            storePassword "123456"
            keyAlias "signing"
            keyPassword "123456"
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            manifestPlaceholders = [
                    UMENG_APPKEY : "5e44f4b84ca357e11900017d",
            ]
        }

        release {
            signingConfig signingConfigs.debug
//            minifyEnabled false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            manifestPlaceholders = [
                    UMENG_APPKEY : "5e44f4b84ca357e11900017d",
            ]
        }
    }

    productFlavors {
        uqbike {
            applicationId "com.tbit.uqbike"
            versionCode 20100
            versionName "v2.1.0"
            manifestPlaceholders = [
                    AMAP_KEY: "f47657f11c069603338e68e65667d9d4",
                    APP_CHANNEL: "uqbike"
            ]
        }

        uqbike_overseas {
            applicationId "com.tbit.uqbike"
            versionCode 20100
            versionName "v2.1.0"
            manifestPlaceholders = [
                    AMAP_KEY: "f47657f11c069603338e68e65667d9d4",
                    APP_CHANNEL: "uqbike_overseas"
            ]
        }

        uqbike_bilishi {
            applicationId "com.tbit.uqbikebls"
            versionCode 10004
            versionName "v1.0.2"
            manifestPlaceholders = [
                    AMAP_KEY: "a351a44fb9f7cb8be97dcc0ca0fcbf60",
                    APP_CHANNEL: "uqbike_bilishi"
            ]
        }

        uqbike_international {
            applicationId "com.tbit.uqbikejp"
            versionCode 10200
            versionName "v1.2.0"
            manifestPlaceholders = [
                    AMAP_KEY: "7d5940e7e3e070fe20681c52204d01b7",
                    APP_CHANNEL: "uqbike_international"
            ]
        }

        uqbike_japan {
            applicationId "com.tbit.uqbikejp"
            versionCode 10100
            versionName "v1.1.0"
            manifestPlaceholders = [
                    AMAP_KEY: "7d5940e7e3e070fe20681c52204d01b7",
                    APP_CHANNEL: "uqbike_japan"
            ]
        }


        uqbike_thailand {
            applicationId "com.tbit.uqbiketh"
            versionCode 200065
            versionName "v2.6.5.03"
            manifestPlaceholders = [
                    AMAP_KEY: "c40bf538e8661d45ebced206323ea104",
                    BMAP_KEY: "fwTRzsMNPPzKVXBFf7uYHVWe0YRI351B",//APK 包
                    APP_CHANNEL: "1"
                    //BMAP_KEY: "oCkKAt5KM4pTfqYrf3l4CHSXB8sfGVKc",//谷歌上架包
                    //APP_CHANNEL: "2"
            ]
        }

        uqbike_show {
            applicationId "com.tbit.uqbike"
            versionCode 102007
            versionName "v1.2.0.7"
            manifestPlaceholders = [
                    AMAP_KEY: "f47657f11c069603338e68e65667d9d4",
                    BMAP_KEY: "fwTRzsMNPPzKVXBFf7uYHVWe0YRI351B",
                    APP_CHANNEL: "uqbike_show"
            ]

//            signingConfigs {
//                debug {
//                    storeFile file("./android.keystore")
//                    storePassword "123456"
//                    keyAlias "android.keystore"
//                    keyPassword "123456"
//                }
//            }
        }

        daha {
            applicationId "com.tbit.daha"
            versionCode 20100
            versionName "v2.1.0"
            manifestPlaceholders = [
                    AMAP_KEY : "f35a0e1eecf290d56cb5c151576f5361",
                    APP_CHANNEL: "daha"
            ]
        }
        bundle{
            language {
                enableSplit = false
            }
        }

    }
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }
}

//clickAntiShake{
//    antiMillis = 500
//    matchJarName ""
//}

dependencies {
    implementation project(':preview')
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
//    implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"
    implementation "org.jetbrains.anko:anko:$anko_version"
    implementation 'androidx.appcompat:appcompat:1.3.0'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.core:core-ktx:1.6.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'androidx.gridlayout:gridlayout:1.0.0'
    implementation 'com.android.support:multidex:1.0.3'
    implementation files('libs\\umeng-agoo-accs-3.4.2.7.12.jar')
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'

    // stetho
    implementation 'com.facebook.stetho:stetho:1.5.0'
    implementation 'com.facebook.stetho:stetho-okhttp3:1.5.0'
    //引入rxjava
    implementation 'io.reactivex.rxjava2:rxjava:2.1.7'
    implementation 'io.reactivex.rxjava2:rxkotlin:2.2.0'
    //引入Log拦截器，方便DEBUG模式输出log信息
    implementation 'com.squareup.okhttp3:logging-interceptor:4.8.0'

    //引入okhttp
    implementation 'com.squareup.okhttp3:okhttp:4.9.1'

    //引入retrofit
    implementation 'com.squareup.retrofit2:retrofit:2.3.0'
    implementation ('com.squareup.retrofit2:converter-simplexml:2.2.0') {
        exclude module: 'stax'
        exclude module: 'stax-api'
        exclude module: 'xpp3'
    }
    //引入rxjava适配器，方便rxjava与retrofit的结合
    implementation 'com.jakewharton.retrofit:retrofit2-rxjava2-adapter:1.0.0'
    //引入json转换器，方便将返回的数据转换为json格式
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    //引入rxandroid
    implementation 'io.reactivex.rxjava2:rxandroid:2.0.1'
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    //引入bga-zxing
    implementation 'com.google.zxing:core:3.3.3'
    implementation 'com.github.bingoogolapple.BGAQRCode-Android:zxing:1.3.8'
//    implementation 'com.github.yankaibang.BGAQRCode-Android:zxing:a1b00e4f69'
//    implementation 'com.github.yankaibang.BGAQRCode-Android:qrcodecore:a1b00e4f69'
    //引入permissionsdispatcher
    implementation("com.github.hotchemi:permissionsdispatcher:3.2.0") {
        exclude group: 'com.android.support'
    }
    kapt "com.github.hotchemi:permissionsdispatcher-processor:3.2.0"
    // apollo
    implementation 'com.github.lsxiao.Apollo:core:1.0.1-fix'
    kapt "com.github.lsxiao.Apollo:processor:1.0.1-fix"
    //高德地图
//    implementation 'com.amap.api:navi-3dmap:9.3.0_3dmap9.3.0'
    //google map
//    implementation 'com.google.android.gms:play-services-maps:18.0.2'
    implementation 'com.google.android.gms:play-services-maps:19.0.0'
    implementation 'com.google.android.gms:play-services-location:19.0.1'
//    implementation 'com.google.android.gms:play-services-location:21.3.0'

//    implementation("com.google.android.gms:play-services-maps:15.0.1") {
//        exclude group: 'com.android.support'
//    }
//    implementation("com.google.android.gms:play-services-location:15.0.1") {
//        exclude group: 'com.android.support'
//    }
    implementation("com.google.maps:google-maps-services:0.19.0") {
        exclude group: 'com.android.support'
    }
    implementation 'org.slf4j:slf4j-simple:1.7.25'
    //谷歌登录
    implementation 'com.google.android.gms:play-services-auth:20.2.0'
    //TabLayout
    implementation 'com.flyco.tablayout:FlycoTabLayout_Lib:2.1.2@aar'
    //轮播图
    implementation 'com.youth.banner:banner:1.4.10'
    //glide
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'
    //状态页面
    implementation 'com.github.lsxiao:capa:1.0.4'
    // Stripe Android SDK
    implementation 'com.stripe:stripe-android:20.5.0'

    //协程库
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.5.0'
    //支持安卓主线程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1'
    //支持将Rx代码转为协程
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-rx2:1.3.9"
    implementation 'com.github.JessYanCoding:AndroidAutoSize:v1.2.1'
    implementation 'org.greenrobot:eventbus:3.3.1'
    implementation 'com.aliyun.dpa:oss-android-sdk:+'
    implementation 'io.github.shenzhen2017:easyfloat:1.0.2'

    //集成FCM推送
    implementation 'com.umeng.umsdk:fcm-umengaccs:+'
    implementation platform('com.google.firebase:firebase-bom:32.4.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging'

    //集成荣耀推送
    implementation 'com.umeng.umsdk:honor-umengaccs:+'
    implementation 'com.umeng.umsdk:honor-push:+'

    //集成魅族推送
    //implementation 'com.umeng.umsdk:meizu-umengaccs:+'
    //implementation 'com.umeng.umsdk:meizu-push:+'

    //集成OPPO推送
    implementation 'com.umeng.umsdk:oppo-umengaccs:+'
    implementation 'com.umeng.umsdk:oppo-push:+'

    implementation 'com.vip:edit:1.0.4'
    implementation project(':dsbridge')
    implementation 'top.zibin:Luban:1.1.8'

    // 添加 Sensors Analytics SDK依赖
    implementation 'com.sensorsdata.analytics.android:SensorsAnalyticsSDK:6.8.3'

    // SmartRefresh 下拉刷新控件
    implementation  'io.github.scwang90:refresh-layout-kernel:3.0.0-alpha'      //核心必须依赖
    implementation  'io.github.scwang90:refresh-header-material:3.0.0-alpha'    //谷歌刷新头
    implementation  'io.github.scwang90:refresh-footer-ball:3.0.0-alpha'        //球脉冲加载

    //pangle SDK
    implementation 'com.pangle.global:ads-sdk:*******'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'

    implementation 'com.facebook.android:audience-network-sdk:6.+'

    //admob SDK
    implementation 'com.google.android.gms:play-services-ads:23.6.0'

    implementation 'com.github.zhpanvip:bannerviewpager:3.5.12'

    // EventBus (添加)
    implementation 'org.greenrobot:eventbus:3.3.1'

}
